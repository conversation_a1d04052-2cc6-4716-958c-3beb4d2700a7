# GoAdmin 第三方API接口文档

## 快速开始

GoAdmin 提供基于签名认证的第三方API，支持安全的卡密充值操作。

### 1. 获取API密钥

联系管理员获取您的API密钥：
- **SecretId**: `AKID1234567890abcdef1234567890ab` (示例)
- **SecretKey**: `1234567890abcdef...` (保密)

### 2. 接口调用

**充值接口**: `POST /api/v1/third/recharge`

**请求格式**:
```json
{
  "target_card_key": "目标卡密",
  "new_card_key": "充值卡密"
}
```

**请求头**:
```
X-Secret-Id: 您的SecretId
X-Timestamp: Unix时间戳
X-Signature: 计算的签名
Content-Type: application/json
```

## 编程语言示例

### Python (推荐)

```python
import hmac
import hashlib
import time
import urllib.parse
import requests

def generate_signature(method, uri, params, secret_key, timestamp):
    params['timestamp'] = str(timestamp)
    sorted_params = sorted(params.items())
    query_string = '&'.join([f"{urllib.parse.quote(k)}={urllib.parse.quote(v)}" for k, v in sorted_params])
    string_to_sign = f"{method.upper()}\n{uri}\n{query_string}"
    return hmac.new(secret_key.encode(), string_to_sign.encode(), hashlib.sha256).hexdigest()

def recharge_card(secret_id, secret_key, target_card, new_card):
    params = {"target_card_key": target_card, "new_card_key": new_card}
    timestamp = int(time.time())
    signature = generate_signature("POST", "/api/v1/third/recharge", params, secret_key, timestamp)
    
    headers = {
        'X-Secret-Id': secret_id,
        'X-Timestamp': str(timestamp),
        'X-Signature': signature,
        'Content-Type': 'application/json'
    }
    
    response = requests.post('https://your-domain.com/api/v1/third/recharge', headers=headers, json=params)
    return response.json()

# 使用示例
result = recharge_card("your-secret-id", "your-secret-key", "TARGET_CARD", "SOURCE_CARD")
print(result)
```

### JavaScript/Node.js

```javascript
const crypto = require('crypto');
const https = require('https');

function generateSignature(method, uri, params, secretKey, timestamp) {
    params.timestamp = timestamp.toString();
    const sortedKeys = Object.keys(params).sort();
    const queryString = sortedKeys.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join('&');
    const stringToSign = `${method.toUpperCase()}\n${uri}\n${queryString}`;
    return crypto.createHmac('sha256', secretKey).update(stringToSign).digest('hex');
}

async function rechargeCard(secretId, secretKey, targetCard, newCard) {
    const params = { target_card_key: targetCard, new_card_key: newCard };
    const timestamp = Math.floor(Date.now() / 1000);
    const signature = generateSignature('POST', '/api/v1/third/recharge', params, secretKey, timestamp);
    
    const response = await fetch('https://your-domain.com/api/v1/third/recharge', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Secret-Id': secretId,
            'X-Timestamp': timestamp.toString(),
            'X-Signature': signature
        },
        body: JSON.stringify(params)
    });
    
    return await response.json();
}

// 使用示例
rechargeCard('your-secret-id', 'your-secret-key', 'TARGET_CARD', 'SOURCE_CARD')
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

### Java

```java
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;

public class GoAdminClient {
    private String generateSignature(String method, String uri, Map<String, String> params, String secretKey, long timestamp) throws Exception {
        params.put("timestamp", String.valueOf(timestamp));
        List<String> sortedKeys = new ArrayList<>(params.keySet());
        Collections.sort(sortedKeys);
        
        StringBuilder queryString = new StringBuilder();
        for (int i = 0; i < sortedKeys.size(); i++) {
            String key = sortedKeys.get(i);
            String value = params.get(key);
            if (i > 0) queryString.append("&");
            queryString.append(URLEncoder.encode(key, StandardCharsets.UTF_8))
                      .append("=")
                      .append(URLEncoder.encode(value, StandardCharsets.UTF_8));
        }
        
        String stringToSign = method.toUpperCase() + "\n" + uri + "\n" + queryString.toString();
        
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }
    
    // 使用OkHttp或其他HTTP客户端发送请求...
}
```

### Go

```go
package main

import (
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "fmt"
    "net/url"
    "sort"
    "strings"
    "time"
    // 添加HTTP客户端代码...
)

func generateSignature(method, uri string, params map[string]string, secretKey string, timestamp int64) string {
    params["timestamp"] = fmt.Sprintf("%d", timestamp)
    
    var keys []string
    for k := range params {
        keys = append(keys, k)
    }
    sort.Strings(keys)
    
    var parts []string
    for _, key := range keys {
        value := params[key]
        encodedKey := url.QueryEscape(key)
        encodedValue := url.QueryEscape(value)
        parts = append(parts, fmt.Sprintf("%s=%s", encodedKey, encodedValue))
    }
    queryString := strings.Join(parts, "&")
    
    stringToSign := fmt.Sprintf("%s\n%s\n%s", strings.ToUpper(method), uri, queryString)
    
    h := hmac.New(sha256.New, []byte(secretKey))
    h.Write([]byte(stringToSign))
    return hex.EncodeToString(h.Sum(nil))
}

func main() {
    // 使用示例
    params := map[string]string{
        "target_card_key": "TARGET_CARD",
        "new_card_key":    "SOURCE_CARD",
    }
    timestamp := time.Now().Unix()
    signature := generateSignature("POST", "/api/v1/third/recharge", params, "your-secret-key", timestamp)
    fmt.Printf("签名: %s\n", signature)
}
```

## 错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 200 | 成功 | - |
| 401 | 签名验证失败 | 检查SecretId和签名算法 |
| 1001 | 卡密不存在 | 检查卡密是否正确 |
| 1006 | 时间戳过期 | 检查系统时间同步 |
| 1007 | 权限不足 | 联系管理员检查权限 |

## 注意事项

1. **时间同步**: 确保系统时间准确，误差不超过5分钟
2. **HTTPS**: 生产环境必须使用HTTPS
3. **密钥安全**: SecretKey不能泄露或硬编码在客户端
4. **错误处理**: 实现重试机制处理网络错误

## 技术支持

如有问题，请联系技术支持或查看完整文档：[详细API文档](docs/third-party-api.md)
