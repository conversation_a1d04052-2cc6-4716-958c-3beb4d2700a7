# 第三方API接口文档

## 概述

第三方API采用基于签名的安全认证机制，类似于阿里云、腾讯云等主流云服务商的API设计。通过SecretId和SecretKey进行身份认证，使用HMAC-SHA256算法生成请求签名，确保API调用的安全性。

## 认证机制

### 1. 获取API密钥

联系管理员为您创建API密钥，您将获得：
- **SecretId**: 用于标识API密钥（可公开传输）
- **SecretKey**: 用于生成签名（需要保密，不可传输）

### 2. 签名算法

#### 步骤1：构造待签名字符串
```
StringToSign = HTTPMethod + "\n" + URI + "\n" + CanonicalQueryString
```

其中：
- `HTTPMethod`: HTTP请求方法（大写），如 `POST`
- `URI`: 请求路径，如 `/api/v1/third/recharge`
- `CanonicalQueryString`: 规范化的查询字符串（包含请求参数和时间戳）

#### 步骤2：生成规范化查询字符串
1. 将所有请求参数（包括时间戳）按参数名进行字典序排序
2. 对参数名和参数值进行URL编码
3. 按照 `key1=value1&key2=value2` 的格式拼接

#### 步骤3：生成签名
```
Signature = HEX(HMAC-SHA256(SecretKey, StringToSign))
```

#### 步骤4：设置请求头
```
X-Secret-Id: {SecretId}
X-Timestamp: {Unix时间戳}
X-Signature: {生成的签名}
Content-Type: application/json
```

### 3. 时间戳验证

- 时间戳有效期为5分钟
- 请确保客户端时间与服务器时间同步

## API接口

### 充值接口

**接口地址**: `POST /api/v1/third/recharge`

**功能说明**: 使用指定卡密为目标卡密进行充值

**请求参数**:
```json
{
  "target_card_key": "目标卡密",
  "new_card_key": "充值卡密"
}
```

**安全限制**:
- 需要具有充值权限

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "充值成功"
  }
}
```

**错误响应**:
```json
{
  "code": 1001,
  "message": "卡密不存在"
}
```

### 查询卡密详情接口

**接口地址**: `POST /api/v1/third/cardkey/detail`

**功能说明**: 查询指定卡密的使用时间和到期时间。

**请求参数**:
```json
{
  "card_key": "要查询的卡密"
}
```

**安全限制**:
- 需要通过签名认证（同充值接口）

**响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "card_key": "xxxxxx",
    "used_at": "2024-06-01 12:00:00",
    "expired_at": "2024-07-01 12:00:00"
  }
}
```

**错误响应**:
```json
{
  "code": 1001,
  "message": "卡密不存在"
}
```

## 错误码说明

| 错误码 | 说明 | 触发场景 | 处理建议 |
|--------|------|----------|----------|
| 0 | 成功 | 请求处理成功 | - |
| 1000 | 一般错误 | 服务器内部错误 | 重试或联系技术支持 |
| 1001 | 无效的请求参数 | 请求参数格式错误或缺少必需参数 | 检查请求参数格式和必填字段 |
| 1002 | 未授权 | 缺少认证头、SecretId不存在、签名验证失败、时间戳过期 | 检查SecretId、签名算法和系统时间 |
| 2001 | 卡密不存在 | 目标卡密或充值卡密不存在 | 检查卡密是否正确 |
| 2002 | 卡密已使用 | 充值卡密已被使用 | 使用未使用的卡密 |
| 2003 | 卡密已过期 | 充值卡密已过期 | 使用有效期内的卡密 |
| 2004 | 卡密已禁用 | 卡密被管理员禁用 | 联系管理员解除禁用 |
| 6001 | 禁止访问 | API密钥无充值权限 | 联系管理员配置充值权限 |

### 常见错误场景详解

#### 1002 未授权错误的具体情况：

**缺少认证头**：
```json
{
  "code": 1002,
  "message": "缺少必要的认证头"
}
```
- 原因：请求头中缺少 `X-Secret-Id`、`X-Timestamp` 或 `X-Signature`
- 解决：确保所有必需的请求头都已设置

**无效的时间戳**：
```json
{
  "code": 1002,
  "message": "无效的时间戳"
}
```
- 原因：时间戳格式错误或无法解析
- 解决：使用正确的Unix时间戳格式

**无效的API密钥**：
```json
{
  "code": 1002,
  "message": "无效的API密钥"
}
```
- 原因：SecretId不存在、已禁用或已过期
- 解决：检查SecretId是否正确，联系管理员确认状态

**签名验证失败**：
```json
{
  "code": 1002,
  "message": "签名验证失败"
}
```
- 原因：签名算法错误、时间戳超过5分钟有效期、参数不匹配
- 解决：检查签名算法实现，确保系统时间同步

#### 充值相关错误：

**权限解析失败**：
```json
{
  "code": 1002,
  "message": "权限解析失败"
}
```
- 原因：API密钥的权限配置格式错误
- 解决：联系管理员重新配置权限

**无充值权限**：
```json
{
  "code": 6001,
  "message": "无充值权限"
}
```
- 原因：API密钥未配置充值权限
- 解决：联系管理员为API密钥添加充值权限

## 编程语言示例

### Python示例

```python
import hmac
import hashlib
import time
import urllib.parse
import requests

def generate_signature(method, uri, params, secret_key, timestamp):
    params['timestamp'] = str(timestamp)
    sorted_params = sorted(params.items())
    query_string = '&'.join([f"{urllib.parse.quote(k)}={urllib.parse.quote(v)}" for k, v in sorted_params])
    string_to_sign = f"{method.upper()}\n{uri}\n{query_string}"
    return hmac.new(secret_key.encode(), string_to_sign.encode(), hashlib.sha256).hexdigest()

def recharge_card(secret_id, secret_key, target_card, new_card):
    params = {"target_card_key": target_card, "new_card_key": new_card}
    timestamp = int(time.time())
    signature = generate_signature("POST", "/api/v1/third/recharge", params, secret_key, timestamp)
    
    headers = {
        'X-Secret-Id': secret_id,
        'X-Timestamp': str(timestamp),
        'X-Signature': signature,
        'Content-Type': 'application/json'
    }
    
    response = requests.post('https://your-domain.com/api/v1/third/recharge', headers=headers, json=params)
    return response.json()

# 使用示例
result = recharge_card("your-secret-id", "your-secret-key", "TARGET_CARD", "SOURCE_CARD")
print(result)
```

### JavaScript/Node.js

```javascript
const crypto = require('crypto');
const https = require('https');

function generateSignature(method, uri, params, secretKey, timestamp) {
    params.timestamp = timestamp.toString();
    const sortedKeys = Object.keys(params).sort();
    const queryString = sortedKeys.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join('&');
    const stringToSign = `${method.toUpperCase()}\n${uri}\n${queryString}`;
    return crypto.createHmac('sha256', secretKey).update(stringToSign).digest('hex');
}

async function rechargeCard(secretId, secretKey, targetCard, newCard) {
    const params = { target_card_key: targetCard, new_card_key: newCard };
    const timestamp = Math.floor(Date.now() / 1000);
    const signature = generateSignature('POST', '/api/v1/third/recharge', params, secretKey, timestamp);
    
    const response = await fetch('https://your-domain.com/api/v1/third/recharge', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Secret-Id': secretId,
            'X-Timestamp': timestamp.toString(),
            'X-Signature': signature
        },
        body: JSON.stringify(params)
    });
    
    return await response.json();
}

// 使用示例
rechargeCard('your-secret-id', 'your-secret-key', 'TARGET_CARD', 'SOURCE_CARD')
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

### Go

```go
package main

import (
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "fmt"
    "net/url"
    "sort"
    "strings"
    "time"
    // 添加HTTP客户端代码...
)

func generateSignature(method, uri string, params map[string]string, secretKey string, timestamp int64) string {
    params["timestamp"] = fmt.Sprintf("%d", timestamp)
    
    var keys []string
    for k := range params {
        keys = append(keys, k)
    }
    sort.Strings(keys)
    
    var parts []string
    for _, key := range keys {
        value := params[key]
        encodedKey := url.QueryEscape(key)
        encodedValue := url.QueryEscape(value)
        parts = append(parts, fmt.Sprintf("%s=%s", encodedKey, encodedValue))
    }
    queryString := strings.Join(parts, "&")
    
    stringToSign := fmt.Sprintf("%s\n%s\n%s", strings.ToUpper(method), uri, queryString)
    
    h := hmac.New(sha256.New, []byte(secretKey))
    h.Write([]byte(stringToSign))
    return hex.EncodeToString(h.Sum(nil))
}

func main() {
    // 使用示例
    params := map[string]string{
        "target_card_key": "TARGET_CARD",
        "new_card_key":    "SOURCE_CARD",
    }
    timestamp := time.Now().Unix()
    signature := generateSignature("POST", "/api/v1/third/recharge", params, "your-secret-key", timestamp)
    fmt.Printf("签名: %s\n", signature)
}
```

### Java示例

```java
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.*;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import com.fasterxml.jackson.databind.ObjectMapper;

public class GoAdminClient {
    private final String secretId;
    private final String secretKey;
    private final String baseUrl;
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;

    public GoAdminClient(String secretId, String secretKey, String baseUrl) {
        this.secretId = secretId;
        this.secretKey = secretKey;
        this.baseUrl = baseUrl;
        this.httpClient = HttpClient.newHttpClient();
        this.objectMapper = new ObjectMapper();
    }

    private String generateSignature(String method, String uri, Map<String, String> params, long timestamp)
            throws NoSuchAlgorithmException, InvalidKeyException {
        // 添加时间戳
        params.put("timestamp", String.valueOf(timestamp));

        // 排序参数
        List<String> sortedKeys = new ArrayList<>(params.keySet());
        Collections.sort(sortedKeys);

        // 构造查询字符串
        StringBuilder queryString = new StringBuilder();
        for (int i = 0; i < sortedKeys.size(); i++) {
            String key = sortedKeys.get(i);
            String value = params.get(key);
            if (i > 0) {
                queryString.append("&");
            }
            queryString.append(URLEncoder.encode(key, StandardCharsets.UTF_8))
                      .append("=")
                      .append(URLEncoder.encode(value, StandardCharsets.UTF_8));
        }

        // 构造待签名字符串
        String stringToSign = method.toUpperCase() + "\n" + uri + "\n" + queryString.toString();

        // 生成签名
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));

        // 转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString();
    }

    public Map<String, Object> recharge(String targetCardKey, String newCardKey)
            throws Exception {
        String method = "POST";
        String uri = "/api/v1/third/recharge";

        Map<String, String> params = new HashMap<>();
        params.put("target_card_key", targetCardKey);
        params.put("new_card_key", newCardKey);

        long timestamp = Instant.now().getEpochSecond();
        String signature = generateSignature(method, uri, params, timestamp);

        // 构造请求体
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("target_card_key", targetCardKey);
        requestBody.put("new_card_key", newCardKey);
        String jsonBody = objectMapper.writeValueAsString(requestBody);

        // 创建HTTP请求
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(baseUrl + uri))
                .header("Content-Type", "application/json")
                .header("X-Secret-Id", secretId)
                .header("X-Timestamp", String.valueOf(timestamp))
                .header("X-Signature", signature)
                .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                .build();

        // 发送请求
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        // 解析响应
        return objectMapper.readValue(response.body(), Map.class);
    }

    public static void main(String[] args) {
        try {
            GoAdminClient client = new GoAdminClient(
                "your-secret-id",
                "your-secret-key",
                "https://your-domain.com"
            );

            Map<String, Object> result = client.recharge("TARGET_CARD", "SOURCE_CARD");
            System.out.println("充值结果: " + result);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

### 浏览器JavaScript示例

```javascript
class GoAdminWebClient {
    constructor(secretId, secretKey, baseUrl) {
        this.secretId = secretId;
        this.secretKey = secretKey;
        this.baseUrl = baseUrl;
    }

    async generateSignature(method, uri, params, timestamp) {
        // 添加时间戳
        params.timestamp = timestamp.toString();

        // 排序参数
        const sortedKeys = Object.keys(params).sort();

        // 构造查询字符串
        const queryString = sortedKeys
            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
            .join('&');

        // 构造待签名字符串
        const stringToSign = `${method.toUpperCase()}\n${uri}\n${queryString}`;

        // 使用Web Crypto API生成签名
        const encoder = new TextEncoder();
        const keyData = encoder.encode(this.secretKey);
        const messageData = encoder.encode(stringToSign);

        const cryptoKey = await crypto.subtle.importKey(
            'raw',
            keyData,
            { name: 'HMAC', hash: 'SHA-256' },
            false,
            ['sign']
        );

        const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);

        // 转换为十六进制字符串
        const hashArray = Array.from(new Uint8Array(signature));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

        return hashHex;
    }

    async recharge(targetCardKey, newCardKey) {
        const method = 'POST';
        const uri = '/api/v1/third/recharge';

        const params = {
            target_card_key: targetCardKey,
            new_card_key: newCardKey
        };

        const timestamp = Math.floor(Date.now() / 1000);
        const signature = await this.generateSignature(method, uri, params, timestamp);

        const response = await fetch(this.baseUrl + uri, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-Secret-Id': this.secretId,
                'X-Timestamp': timestamp.toString(),
                'X-Signature': signature
            },
            body: JSON.stringify(params)
        });

        return await response.json();
    }
}

// 使用示例
async function rechargeExample() {
    const client = new GoAdminWebClient(
        'your-secret-id',
        'your-secret-key',
        'https://your-domain.com'
    );

    try {
        const result = await client.recharge('TARGET_CARD', 'SOURCE_CARD');
        console.log('充值结果:', result);
    } catch (error) {
        console.error('充值失败:', error);
    }
}
```

## 注意事项

1. **时间同步**: 确保客户端时间与服务器时间同步，时间戳误差不能超过5分钟
2. **签名安全**: SecretKey必须保密，不能在客户端代码中硬编码
3. **HTTPS**: 生产环境必须使用HTTPS协议
4. **错误处理**: 实现适当的错误处理和重试机制
5. **参数验证**: 在发送请求前验证参数的有效性

## 测试工具

您可以使用以下curl命令测试API：

```bash
# 注意：实际使用时需要正确计算签名
curl -X POST 'https://your-domain.com/api/v1/third/recharge' \
  -H 'X-Secret-Id: your-secret-id' \
  -H 'X-Timestamp: 1703123456' \
  -H 'X-Signature: calculated-signature' \
  -H 'Content-Type: application/json' \
  -d '{
    "target_card_key": "TARGET_CARD",
    "new_card_key": "SOURCE_CARD"
  }'
```

## 签名算法详解

SDK使用与阿里云、腾讯云相同的签名算法：

1. **构造待签名字符串**: `METHOD\nURI\nCanonicalQueryString`
2. **生成签名**: `HMAC-SHA256(SecretKey, StringToSign)`
3. **请求头**:
   - `X-Secret-Id`: SecretID（公开）
   - `X-Timestamp`: Unix时间戳
   - `X-Signature`: 生成的签名

## 技术支持

如有问题，请联系技术支持团队。
