#!/bin/bash

# GoAdmin 部署脚本
# 用于快速部署 GoAdmin 系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装 $1"
        exit 1
    fi
}

# 检查环境
check_environment() {
    log_info "检查环境依赖..."
    
    # 检查 Go
    check_command "go"
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go 版本: $GO_VERSION"
    
    # 检查 Node.js
    check_command "node"
    NODE_VERSION=$(node --version)
    log_info "Node.js 版本: $NODE_VERSION"
    
    # 检查 npm
    check_command "npm"
    NPM_VERSION=$(npm --version)
    log_info "npm 版本: $NPM_VERSION"
    
    # 检查 MySQL
    check_command "mysql"
    MYSQL_VERSION=$(mysql --version | awk '{print $5}' | sed 's/,//')
    log_info "MySQL 版本: $MYSQL_VERSION"
    
    log_success "环境检查完成"
}

# 配置数据库
setup_database() {
    log_info "配置数据库..."
    
    read -p "请输入 MySQL root 密码: " -s MYSQL_ROOT_PASSWORD
    echo
    
    read -p "请输入数据库名称 (默认: goadmin): " DB_NAME
    DB_NAME=${DB_NAME:-goadmin}
    
    # 创建数据库
    mysql -u root -p$MYSQL_ROOT_PASSWORD -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log_success "数据库 $DB_NAME 创建成功"
    else
        log_error "数据库创建失败"
        exit 1
    fi
    
    # 导入表结构
    log_info "导入数据库表结构..."
    mysql -u root -p$MYSQL_ROOT_PASSWORD $DB_NAME < deploy/create_tables.sql 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log_success "数据库表结构导入成功"
    else
        log_error "数据库表结构导入失败"
        exit 1
    fi
}

# 配置文件
setup_config() {
    log_info "配置系统..."
    
    if [ ! -f "config.yaml" ]; then
        if [ -f "config.yaml.example" ]; then
            cp config.yaml.example config.yaml
            log_info "已复制配置文件模板，请编辑 config.yaml 文件"
            
            # 自动替换数据库配置
            if [ ! -z "$DB_NAME" ]; then
                sed -i "s/database: \"goadmin\"/database: \"$DB_NAME\"/" config.yaml
                log_info "已自动配置数据库名称"
            fi
            
            log_warning "请手动编辑 config.yaml 文件，配置数据库密码等信息"
            read -p "配置完成后按回车继续..."
        else
            log_error "配置文件模板不存在"
            exit 1
        fi
    else
        log_info "配置文件已存在，跳过配置"
    fi
}

# 构建后端
build_backend() {
    log_info "构建后端..."
    
    # 下载依赖
    go mod tidy
    
    # 构建
    go build -ldflags="-s -w" -o goadmin main.go
    
    if [ $? -eq 0 ]; then
        log_success "后端构建成功"
    else
        log_error "后端构建失败"
        exit 1
    fi
}

# 构建前端
build_frontend() {
    log_info "构建前端..."
    
    cd web
    
    # 安装依赖
    npm install
    
    # 构建
    npm run build
    
    if [ $? -eq 0 ]; then
        log_success "前端构建成功"
    else
        log_error "前端构建失败"
        exit 1
    fi
    
    cd ..
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    # 检查端口是否被占用
    PORT=$(grep "port:" config.yaml | head -1 | awk '{print $2}' | tr -d '"')
    PORT=${PORT:-8080}
    
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口 $PORT 已被占用"
        read -p "是否要停止现有服务并重启？(y/n): " RESTART
        if [ "$RESTART" = "y" ] || [ "$RESTART" = "Y" ]; then
            pkill -f "./goadmin" || true
            sleep 2
        else
            log_info "跳过启动服务"
            return
        fi
    fi
    
    # 启动服务
    nohup ./goadmin > goadmin.log 2>&1 &
    
    sleep 3
    
    if pgrep -f "./goadmin" > /dev/null; then
        log_success "服务启动成功，端口: $PORT"
        log_info "日志文件: goadmin.log"
        log_info "访问地址: http://localhost:$PORT"
    else
        log_error "服务启动失败，请检查日志文件 goadmin.log"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "GoAdmin 部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -c, --check    仅检查环境"
    echo "  -d, --db       仅配置数据库"
    echo "  -b, --build    仅构建项目"
    echo "  -s, --start    仅启动服务"
    echo "  --backend      仅构建后端"
    echo "  --frontend     仅构建前端"
    echo ""
    echo "示例:"
    echo "  $0              # 完整部署"
    echo "  $0 -c           # 检查环境"
    echo "  $0 -d           # 配置数据库"
    echo "  $0 -b           # 构建项目"
}

# 主函数
main() {
    echo "========================================"
    echo "       GoAdmin 自动部署脚本"
    echo "========================================"
    echo ""
    
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--check)
            check_environment
            exit 0
            ;;
        -d|--db)
            setup_database
            exit 0
            ;;
        -b|--build)
            build_backend
            build_frontend
            exit 0
            ;;
        --backend)
            build_backend
            exit 0
            ;;
        --frontend)
            build_frontend
            exit 0
            ;;
        -s|--start)
            start_service
            exit 0
            ;;
        "")
            # 完整部署流程
            check_environment
            setup_database
            setup_config
            build_backend
            build_frontend
            start_service
            
            echo ""
            log_success "部署完成！"
            echo "========================================"
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
