<template>
  <div class="app-wrapper">
    <div class="sidebar" :class="{ 'is-collapse': isCollapse }">
      <div class="logo-container">
        <el-icon class="logo-icon" v-if="!isCollapse"><Monitor /></el-icon>
        <span class="logo-text" v-if="!isCollapse">管理系统</span>
      </div>
      <el-scrollbar>
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          router
          class="menu"
        >
          <el-menu-item 
            v-for="route in menuRoutes" 
            :key="route.path" 
            :index="'/' + route.path"
          >
            <el-icon v-if="route.meta?.icon">
              <component :is="route.meta.icon"/>
            </el-icon>
            <template #title>
              <span>{{ route.meta?.title }}</span>
            </template>
          </el-menu-item>
        </el-menu>
      </el-scrollbar>
    </div>
    
    <div class="main-container">
      <div class="header">
        <div class="left">
          <el-icon 
            class="collapse-btn"
            @click="toggleSidebar"
          >
            <Expand v-if="isCollapse" />
            <Fold v-else />
          </el-icon>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="route.meta?.title">{{ route.meta.title }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="right">
          <el-tooltip content="全屏" placement="bottom">
            <el-icon class="action-icon" @click="toggleFullScreen">
              <FullScreen />
            </el-icon>
          </el-tooltip>
          <el-dropdown @command="handleCommand" trigger="click">
            <div class="user-dropdown">
              <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
              <span class="username">管理员</span>
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>个人信息
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>系统设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div class="main">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  ArrowDown, 
  Expand, 
  Fold, 
  FullScreen,
  User,
  Setting,
  SwitchButton,
  Monitor
} from '@element-plus/icons-vue'
import { routes } from '../router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const isCollapse = ref(false)

// 计算当前激活的菜单项
const activeMenu = computed(() => route.path)

// Filter visible routes
const menuRoutes = computed(() => {
  const mainRoute = routes.find(route => route.path === '/')
  if (!mainRoute?.children) return []
  return mainRoute.children.filter(route => !route.meta?.hidden)
})

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const toggleFullScreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中')
      break
    case 'logout':
      localStorage.removeItem('token')
      router.push('/login')
      break
  }
}
</script>

<style scoped>
.app-wrapper {
  height: 100vh;
  display: flex;
  background-color: var(--el-bg-color);
}

.sidebar {
  width: 240px;
  background: var(--el-menu-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;
  transition: width 0.3s;
}

.sidebar.is-collapse {
  width: 64px;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  overflow: hidden;
}

.logo-icon {
  font-size: 24px;
  color: var(--el-color-primary);
  margin-right: 12px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.menu {
  flex: 1;
  border-right: none;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 60px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collapse-btn {
  font-size: 20px;
  cursor: pointer;
  color: var(--el-text-color-regular);
  transition: color 0.3s;
}

.collapse-btn:hover {
  color: var(--el-color-primary);
}

.right {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-icon {
  font-size: 20px;
  cursor: pointer;
  color: var(--el-text-color-regular);
  transition: color 0.3s;
}

.action-icon:hover {
  color: var(--el-color-primary);
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: var(--el-fill-color-light);
}

.username {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.main {
  flex: 1;
  padding: 20px;
  background: var(--el-bg-color-page);
  overflow: auto;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

:deep(.el-menu-item) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style> 