import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'

// Create axios instance
const api = axios.create({
  baseURL: '/admin/v1',
  timeout: 10000
})

// Request interceptor
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  response => {
    // 处理业务逻辑错误
    if (response.data.code !== 0) {
      // 特殊处理token失效的情况
      if (response.data.code === 1002) { // token invalid
        localStorage.removeItem('token')
        localStorage.removeItem('admin')
        router.push('/login')
      }
      
      // 将所有非零状态码视为错误，统一拒绝promise
      return Promise.reject(new Error(response.data.message || '请求失败'))
    }
    
    return response.data
  },
  error => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          localStorage.removeItem('token')
          localStorage.removeItem('admin')
          router.push('/login')
          break
        default:
          ElMessage.error(error.response.data.message || '请求失败')
      }
    }
    return Promise.reject(error)
  }
)

export interface LoginData {
  username: string
  password: string
}

export interface Device {
  device_id: string
  status: number
  last_login_at: string | null
  created_at: string
}

export interface CardKey {
  id: number
  card_key: string
  status: number
  type: number
  value: number
  max_devices: number
  created_at: string
  expired_at: string | null
  used_at: string | null
  parent_id: string | null
  recharge_time: string | null
  parent_card?: CardKey | null
  recharged_cards?: CardKey[] | null
  devices: Device[]
}

export interface Admin {
  id: number
  username: string
  status: number
  last_login: string | null
  created_at: string
}

export interface PageParams {
  page: number
  page_size: number
}

export interface DashboardStats {
  totalCardKeys: number
  usedCardKeys: number
  expiredCardKeys: number
  totalBindings: number
  activeDevices: number
  dailyStats: Array<{
    date: string
    count: number
  }>
}

export interface CardKeyListParams extends PageParams {
  status?: number
  type?: number
  card_key?: string
}

export interface Script {
  id: number
  script_type: string
  branch: string
  file_name: string
  updated_at: string
}

export interface ThirdAPIKey {
  id: number
  secret_id: string
  secret_key: string
  name: string
  status: number
  permissions: string
  last_used_at: string | null
  expires_at: string | null
  created_at: string
  updated_at: string
}

export interface APIKeyPermissions {
  recharge: boolean
  query: boolean
}

export interface CreateAPIKeyData {
  name: string
  permissions: APIKeyPermissions
  expires_at?: string
}

export interface APIKeyListParams extends PageParams {
  name?: string
  status?: number
}

// API functions
export default {
  // Auth
  login: (data: LoginData) => api.post('/auth/login', data),

  // Admin
  getAdminList: (params: PageParams) => api.post('/admins/list', params),
  createAdmin: (data: { username: string; password: string }) => api.post('/admins/create', data),
  updateAdmin: (id: number, data: Partial<Admin>) => api.post('/admins/update', { id, ...data }),
  deleteAdmin: (id: number) => api.post('/admins/delete', { id }),
  updateAdminStatus: (id: number, status: number) => api.post('/admins/status', { id, status }),

  // CardKey
  getCardKeyList: (params: CardKeyListParams) => api.post('/cardkeys/list', params),
  generateCardKey: (data: { type: number; value: number; prefix?: string; length: number; count: number }) => api.post('/cardkeys/create', data),
  deleteCardKey: (cardKeys: string | string[]) => api.post('/cardkeys/delete', { card_key: Array.isArray(cardKeys) ? cardKeys[0] : cardKeys }),
  getCardKeyWithDevices: (cardKey: string, params: PageParams) => api.post('/cardkeys/detail', { card_key: cardKey, ...params }),
  rechargeCardKey: (targetCardKey: string, newCardKey: string) => api.post('/cardkeys/recharge', { target_card_key: targetCardKey, new_card_key: newCardKey }),
  unbindDevice: (cardKey: string, deviceIds: string[]) => api.post('/bindings/unbind', { card_key: cardKey, device_ids: deviceIds }),
  unbindAllDevices: (cardKey: string) => api.post('/bindings/unbind_all', { card_key: cardKey }),

  getStats() {
    return api.get<DashboardStats>('/stats')
  },

  // 获取Hamibot统计数据
  getHamibotStats() {
    return api.get('/stats/hamibot')
  },

  // 获取Hamibot设备统计数据
  getHamibotRobotStats() {
    return api.get('/stats/hamibot/robots')
  },

  // 系统配置相关接口
  getConfigList: (params: any) => api.post('/configs/list', params),
  setConfig: (data: any) => api.post('/configs/set', data),
  deleteConfig: (key: string) => api.post('/configs/delete', { key }),

  // 脚本管理相关接口
  getScriptList() {
    return api.get<Script[]>('/github/scripts')
  },

  updateScript(data: { script_type: string; branch: string }) {
    return api.post('/github/scripts/update', data)
  },

  // 第三方API密钥管理
  getAPIKeyList: (params: APIKeyListParams) =>
    api.post<{ list: ThirdAPIKey[]; total: number; page: number; page_size: number }>('/api-keys/list', params),

  createAPIKey: (data: CreateAPIKeyData) =>
    api.post<ThirdAPIKey>('/api-keys/create', data),

  deleteAPIKey: (secretId: string) =>
    api.post<any>('/api-keys/delete', { secret_id: secretId }),

  updateAPIKeyStatus: (secretId: string, status: number) =>
    api.post<any>('/api-keys/update-status', { secret_id: secretId, status })
}