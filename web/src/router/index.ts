import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '../layout/index.vue'

export const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/index.vue'),
    meta: { 
      title: '登录',
      hidden: true,
      requiresAuth: false 
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/dashboard/index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'DataLine',
          requiresAuth: true
        }
      },
      {
        path: 'admin',
        name: 'Admin',
        component: () => import('../views/admin/AdminList.vue'),
        meta: {
          title: '管理员管理',
          icon: 'User',
          requiresAuth: true
        }
      },
      {
        path: 'cardkey',
        name: '<PERSON><PERSON><PERSON>',
        component: () => import('../views/cardkey/CardKeyList.vue'),
        meta: {
          title: '卡密管理',
          icon: 'Ticket',
          requiresAuth: true
        }
      },
      {
        path: 'system/config',
        name: 'SystemConfig',
        component: () => import('../views/system/Config.vue'),
        meta: {
          title: '系统配置',
          icon: 'Setting',
          requiresAuth: true
        }
      },
      {
        path: 'script',
        name: 'Script',
        component: () => import('../views/script/index.vue'),
        meta: {
          title: '脚本管理',
          icon: 'Document',
          requiresAuth: true
        }
      },
      {
        path: 'api-keys',
        name: 'APIKeys',
        component: () => import('../views/apikey/APIKeyList.vue'),
        meta: {
          title: 'API密钥管理',
          icon: 'Key',
          requiresAuth: true
        }
      },
      {
        path: 'api-test',
        name: 'APITest',
        component: () => import('../views/apikey/APIKeyTest.vue'),
        meta: {
          title: 'API测试',
          icon: 'Monitor',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/dashboard',
    meta: { hidden: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  
  // 如果是登录页面
  if (to.path === '/login') {
    if (token) {
      // 已登录，重定向到首页或之前的页面
      next(from.path === '/login' ? '/dashboard' : from.path)
    } else {
      // 未登录，允许访问登录页
      next()
    }
    return
  }

  // 需要认证但没有token，跳转到登录页
  if (to.meta.requiresAuth && !token) {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  next()
})

export default router 