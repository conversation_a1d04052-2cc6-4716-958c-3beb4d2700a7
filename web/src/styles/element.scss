// Element Plus 自定义样式
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #409eff,
    ),
    'success': (
      'base': #67c23a,
    ),
    'warning': (
      'base': #e6a23c,
    ),
    'danger': (
      'base': #f56c6c,
    ),
    'info': (
      'base': #909399,
    ),
  ),
  $border-radius: (
    'base': 4px,
    'small': 2px,
    'round': 20px,
    'circle': 100%,
  ),
  $font-size: (
    'extra-large': 20px,
    'large': 18px,
    'medium': 16px,
    'base': 14px,
    'small': 13px,
    'extra-small': 12px,
  )
);

// 自定义组件样式
.el-button {
  font-weight: 500;
  
  &.is-plain {
    border-width: 1px;
  }
}

.el-input {
  .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
    
    &:hover {
      box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
    }
    
    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
}

.el-card {
  border: none;
  box-shadow: var(--el-box-shadow-light);
  border-radius: var(--el-border-radius-base);
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-light);
  }
}

.el-table {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-header-bg-color: var(--el-fill-color-light);
  
  .el-table__header th {
    font-weight: 600;
    background-color: var(--el-table-header-bg-color);
  }
}

.el-dialog {
  border-radius: var(--el-border-radius-base);
  
  .el-dialog__header {
    margin: 0;
    padding: 20px;
    border-bottom: 1px solid var(--el-border-color-light);
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    padding: 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
}

.el-menu {
  border-right: none;
  
  .el-menu-item {
    &.is-active {
      background-color: var(--el-color-primary-light-9);
    }
  }
}

.el-pagination {
  justify-content: flex-end;
  margin-top: 20px;
} 