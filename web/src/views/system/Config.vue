<template>
  <div class="config-list">
    <div class="toolbar">
      <el-button type="primary" plain @click="handleAdd">
        添加配置
        <el-icon class="el-icon--right"><Plus /></el-icon>
      </el-button>
    </div>

    <el-card class="list-card" shadow="never">
      <el-table :data="configs" stripe>
        <el-table-column prop="key" label="配置键" min-width="150" />
        <el-table-column prop="value" label="配置值" min-width="200">
          <template #default="{ row }">
            <div class="value-content">
              <pre v-if="row.type !== 'STRING'" class="code-block" v-html="formatValue(row.value, row.type)"></pre>
              <span v-else>{{ row.value }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="值类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getValueTypeTag(row.type)" size="small">{{ getValueTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="desc" label="描述" min-width="150" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
            <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 配置编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加配置' : '编辑配置'"
      width="650px"
      @close="resetForm"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="配置键" prop="key">
          <el-input v-model="form.key" :disabled="dialogType === 'edit'" placeholder="请输入配置键" />
        </el-form-item>
        <el-form-item label="值类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio-button label="STRING">字符串</el-radio-button>
            <el-radio-button label="JSON">JSON</el-radio-button>
            <el-radio-button label="YAML">YAML</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="配置值" prop="value">
          <div class="value-editor">
            <el-input
              v-if="form.type === 'STRING'"
              v-model="form.value"
              placeholder="请输入配置值"
            />
            <div v-else class="code-editor">
              <div class="editor-toolbar">
                <span class="editor-label">{{ form.type }} 编辑器</span>
                <el-button
                  v-if="form.type === 'JSON'"
                  link
                  type="primary"
                  size="small"
                  @click="formatJsonValue"
                >格式化</el-button>
              </div>
              <el-input
                v-model="form.value"
                type="textarea"
                :autosize="{ minRows: 6, maxRows: 12 }"
                :placeholder="getValuePlaceholder(form.type)"
                class="code-textarea"
              />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input v-model="form.desc" placeholder="请输入配置描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import api from '@/api'

interface Config {
  id: number
  key: string
  value: string
  type: 'STRING' | 'JSON' | 'YAML'
  desc: string
}

// 分页参数
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const configs = ref<Config[]>([])

// 表单相关
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const formRef = ref<FormInstance>()
const form = reactive({
  key: '',
  value: '',
  type: 'STRING' as const,
  desc: ''
})

// 表单校验规则
const rules = {
  key: [{ required: true, message: '请输入配置键', trigger: 'blur' }],
  value: [{ required: true, message: '请输入配置值', trigger: 'blur' }],
  type: [{ required: true, message: '请选择值类型', trigger: 'change' }]
}

// 获取值类型标签
const getValueTypeLabel = (type: string) => {
  switch (type) {
    case 'JSON':
      return 'JSON'
    case 'YAML':
      return 'YAML'
    default:
      return '字符串'
  }
}

// 获取值类型标签样式
const getValueTypeTag = (type: string) => {
  switch (type) {
    case 'JSON':
      return 'success'
    case 'YAML':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取值占位符
const getValuePlaceholder = (type: string) => {
  switch (type) {
    case 'JSON':
      return '请输入有效的 JSON 格式内容'
    case 'YAML':
      return '请输入有效的 YAML 格式内容'
    default:
      return '请输入配置值'
  }
}

// 格式化JSON值
const formatJsonValue = () => {
  try {
    const parsed = JSON.parse(form.value)
    form.value = JSON.stringify(parsed, null, 2)
  } catch (error) {
    ElMessage.warning('当前内容不是有效的 JSON 格式')
  }
}

// 格式化配置值
const formatValue = (value: string, type: string) => {
  try {
    if (type === 'JSON') {
      const parsed = JSON.parse(value)
      const formatted = JSON.stringify(parsed, null, 2)
      return syntaxHighlight(formatted)
    } else if (type === 'YAML') {
      return value // YAML格式已经是易读的格式
    }
    return value
  } catch (error) {
    return value
  }
}

// JSON语法高亮
const syntaxHighlight = (json: string) => {
  json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')
  return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, (match) => {
    let cls = 'number'
    if (/^"/.test(match)) {
      if (/:$/.test(match)) {
        cls = 'key'
      } else {
        cls = 'string'
      }
    } else if (/true|false/.test(match)) {
      cls = 'boolean'
    } else if (/null/.test(match)) {
      cls = 'null'
    }
    return `<span class="json-${cls}">${match}</span>`
  })
}

// 加载配置列表
const loadConfigs = async () => {
  try {
    const response = await api.getConfigList({
      page: page.value,
      page_size: pageSize.value
    })
    configs.value = response.data.items
    total.value = response.data.total
  } catch (error) {
    console.error('Failed to load configs:', error)
    ElMessage.error('加载配置列表失败')
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.key = ''
  form.value = ''
  form.type = 'STRING'
  form.desc = ''
}

// 处理添加配置
const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
}

// 处理编辑配置
const handleEdit = (row: Config) => {
  dialogType.value = 'edit'
  Object.assign(form, row)
  dialogVisible.value = true
}

// 处理删除配置
const handleDelete = (row: Config) => {
  ElMessageBox.confirm('确定要删除该配置吗？', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await api.deleteConfig(row.key)
      ElMessage.success('删除成功')
      loadConfigs()
    } catch (error) {
      console.error('Failed to delete config:', error)
      ElMessage.error('删除失败')
    }
  })
}

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await api.setConfig(form)
        ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
        dialogVisible.value = false
        loadConfigs()
      } catch (error) {
        console.error('Failed to save config:', error)
        ElMessage.error(dialogType.value === 'add' ? '添加失败' : '更新失败')
      }
    }
  })
}

// 处理分页
const handleSizeChange = () => {
  page.value = 1
  loadConfigs()
}

const handleCurrentChange = () => {
  loadConfigs()
}

// 初始加载
loadConfigs()
</script>

<style scoped>
.config-list {
  padding: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.list-card {
  background: #fff;
  border-radius: 8px;
}

.value-content {
  max-height: 200px;
  overflow-y: auto;
}

.code-block {
  margin: 0;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.value-editor {
  width: 100%;
}

.code-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.editor-label {
  color: #606266;
  font-size: 13px;
}

.code-textarea {
  :deep(.el-textarea__inner) {
    border: none;
    border-radius: 0;
    font-family: monospace;
    padding: 12px;
  }
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table__header th) {
  font-weight: 600;
  color: #606266;
}

/* JSON语法高亮样式 */
:deep(.json-string) {
  color: #22863a;
}

:deep(.json-number) {
  color: #005cc5;
}

:deep(.json-boolean) {
  color: #005cc5;
}

:deep(.json-null) {
  color: #005cc5;
}

:deep(.json-key) {
  color: #d73a49;
}
</style> 