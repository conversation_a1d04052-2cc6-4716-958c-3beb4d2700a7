<template>
  <div class="api-key-list">
    <div class="header">
      <h2>第三方API密钥管理</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建API密钥
      </el-button>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="密钥名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入密钥名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadAPIKeys">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="apiKeys" v-loading="loading" stripe>
        <el-table-column prop="secret_id" label="SecretId" width="280">
          <template #default="{ row }">
            <div class="secret-id-cell">
              <span class="secret-id">{{ row.secret_id }}</span>
              <el-button
                type="text"
                size="small"
                @click="copyToClipboard(row.secret_id)"
                class="copy-btn"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="name" label="密钥名称" width="150" />
        
        <el-table-column label="权限" width="120">
          <template #default="{ row }">
            <div class="permissions">
              <el-tag v-if="getPermissions(row.permissions).recharge" size="small" type="success">
                充值
              </el-tag>
              <el-tag v-if="getPermissions(row.permissions).query" size="small" type="info">
                查询
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="last_used_at" label="最后使用" width="160">
          <template #default="{ row }">
            {{ row.last_used_at ? formatDate(row.last_used_at) : '从未使用' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="expires_at" label="过期时间" width="160">
          <template #default="{ row }">
            {{ row.expires_at ? formatDate(row.expires_at) : '永不过期' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="text"
              size="small"
              @click="showSecretKey(row)"
            >
              查看密钥
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="toggleStatus(row)"
              :class="row.status === 1 ? 'danger-text' : 'success-text'"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="text"
              size="small"
              class="danger-text"
              @click="deleteAPIKey(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadAPIKeys"
          @current-change="loadAPIKeys"
        />
      </div>
    </el-card>

    <!-- 创建API密钥对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建API密钥"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="密钥名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入密钥名称" />
        </el-form-item>
        
        <el-form-item label="权限配置" prop="permissions">
          <el-checkbox-group v-model="selectedPermissions">
            <el-checkbox label="recharge">充值权限</el-checkbox>
            <el-checkbox label="query">查询权限</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="过期时间">
          <el-date-picker
            v-model="createForm.expires_at"
            type="datetime"
            placeholder="选择过期时间（可选）"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreate" :loading="creating">创建</el-button>
      </template>
    </el-dialog>

    <!-- 查看密钥对话框 -->
    <el-dialog
      v-model="showSecretDialog"
      title="API密钥详情"
      width="600px"
    >
      <div v-if="currentAPIKey" class="secret-detail">
        <div class="secret-item">
          <label>SecretId:</label>
          <div class="secret-value">
            <code>{{ currentAPIKey.secret_id }}</code>
            <el-button
              type="text"
              size="small"
              @click="copyToClipboard(currentAPIKey.secret_id)"
            >
              <el-icon><DocumentCopy /></el-icon>
            </el-button>
          </div>
        </div>
        
        <div class="secret-item">
          <label>SecretKey:</label>
          <div class="secret-value">
            <code>{{ currentAPIKey.secret_key }}</code>
            <el-button
              type="text"
              size="small"
              @click="copyToClipboard(currentAPIKey.secret_key)"
            >
              <el-icon><DocumentCopy /></el-icon>
            </el-button>
          </div>
        </div>
        
        <el-alert
          title="安全提醒"
          type="warning"
          :closable="false"
          show-icon
        >
          <p>请妥善保管您的SecretKey，不要在客户端代码中硬编码。</p>
          <p>SecretKey仅在创建时显示一次，请及时保存。</p>
        </el-alert>
      </div>
      
      <template #footer>
        <el-button type="primary" @click="showSecretDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, DocumentCopy } from '@element-plus/icons-vue'
import api, { type ThirdAPIKey, type CreateAPIKeyData, type APIKeyPermissions } from '@/api'

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const apiKeys = ref<ThirdAPIKey[]>([])
const showCreateDialog = ref(false)
const showSecretDialog = ref(false)
const currentAPIKey = ref<ThirdAPIKey | null>(null)
const createFormRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  status: undefined as number | undefined
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 创建表单
const createForm = reactive<CreateAPIKeyData>({
  name: '',
  permissions: {
    recharge: false,
    query: false
  },
  expires_at: undefined
})

const selectedPermissions = ref<string[]>([])

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入密钥名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 加载API密钥列表
const loadAPIKeys = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...searchForm
    }

    const response = await api.getAPIKeyList(params)
    apiKeys.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载API密钥列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.name = ''
  searchForm.status = undefined
  pagination.page = 1
  loadAPIKeys()
}

// 创建API密钥
const handleCreate = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()

    // 构建权限对象
    createForm.permissions = {
      recharge: selectedPermissions.value.includes('recharge'),
      query: selectedPermissions.value.includes('query')
    }

    // 检查是否至少选择了一个权限
    if (!createForm.permissions.recharge && !createForm.permissions.query) {
      ElMessage.warning('请至少选择一个权限')
      return
    }

    creating.value = true
    const response = await api.createAPIKey(createForm)

    ElMessage.success('API密钥创建成功')
    showCreateDialog.value = false

    // 显示新创建的密钥
    currentAPIKey.value = response.data
    showSecretDialog.value = true

    // 重置表单
    resetCreateForm()

    // 刷新列表
    loadAPIKeys()
  } catch (error) {
    ElMessage.error('创建API密钥失败')
  } finally {
    creating.value = false
  }
}

// 重置创建表单
const resetCreateForm = () => {
  createForm.name = ''
  createForm.permissions = { recharge: false, query: false }
  createForm.expires_at = undefined
  selectedPermissions.value = []
  createFormRef.value?.resetFields()
}

// 查看密钥详情
const showSecretKey = (apiKey: ThirdAPIKey) => {
  currentAPIKey.value = apiKey
  showSecretDialog.value = true
}

// 切换状态
const toggleStatus = async (apiKey: ThirdAPIKey) => {
  const newStatus = apiKey.status === 1 ? 2 : 1
  const action = newStatus === 1 ? '启用' : '禁用'

  try {
    await ElMessageBox.confirm(
      `确定要${action}此API密钥吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.updateAPIKeyStatus(apiKey.secret_id, newStatus)
    ElMessage.success(`${action}成功`)
    loadAPIKeys()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除API密钥
const deleteAPIKey = async (apiKey: ThirdAPIKey) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除API密钥 "${apiKey.name}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.deleteAPIKey(apiKey.secret_id)
    ElMessage.success('删除成功')
    loadAPIKeys()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 解析权限
const getPermissions = (permissionsStr: string): APIKeyPermissions => {
  try {
    return JSON.parse(permissionsStr)
  } catch {
    return { recharge: false, query: false }
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 监听创建对话框关闭
const handleCreateDialogClose = () => {
  resetCreateForm()
}

// 监听创建对话框的关闭事件
watch(showCreateDialog, (newVal) => {
  if (!newVal) {
    handleCreateDialogClose()
  }
})

// 页面加载时获取数据
onMounted(() => {
  loadAPIKeys()
})
</script>

<style scoped>
.api-key-list {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.secret-id-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.secret-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
}

.copy-btn {
  padding: 4px;
  min-height: auto;
}

.permissions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.danger-text {
  color: #f56c6c !important;
}

.success-text {
  color: #67c23a !important;
}

.secret-detail {
  padding: 10px 0;
}

.secret-item {
  margin-bottom: 20px;
}

.secret-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #303133;
}

.secret-value {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.secret-value code {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #606266;
  word-break: break-all;
  background: none;
  padding: 0;
}

.el-alert {
  margin-top: 20px;
}

.el-alert p {
  margin: 4px 0;
}
</style>
