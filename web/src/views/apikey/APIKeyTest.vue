<template>
  <div class="api-key-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>第三方API测试</span>
        </div>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="SecretId">
          <el-input v-model="testForm.secretId" placeholder="请输入SecretId" />
        </el-form-item>
        
        <el-form-item label="SecretKey">
          <el-input v-model="testForm.secretKey" type="password" placeholder="请输入SecretKey" />
        </el-form-item>
        
        <el-form-item label="目标卡密">
          <el-input v-model="testForm.targetCardKey" placeholder="请输入目标卡密" />
        </el-form-item>
        
        <el-form-item label="充值卡密">
          <el-input v-model="testForm.newCardKey" placeholder="请输入充值卡密" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testRecharge" :loading="testing">
            测试充值
          </el-button>
          <el-button @click="generateSignature">
            生成签名
          </el-button>
        </el-form-item>
      </el-form>
      
      <el-divider>签名信息</el-divider>
      
      <div v-if="signatureInfo" class="signature-info">
        <div class="info-item">
          <label>时间戳:</label>
          <code>{{ signatureInfo.timestamp }}</code>
        </div>
        <div class="info-item">
          <label>待签名字符串:</label>
          <pre>{{ signatureInfo.stringToSign }}</pre>
        </div>
        <div class="info-item">
          <label>生成的签名:</label>
          <code>{{ signatureInfo.signature }}</code>
        </div>
      </div>
      
      <el-divider>测试结果</el-divider>
      
      <div v-if="testResult" class="test-result">
        <el-alert
          :title="testResult.success ? '测试成功' : '测试失败'"
          :type="testResult.success ? 'success' : 'error'"
          :description="testResult.message"
          show-icon
          :closable="false"
        />
        
        <div v-if="testResult.response" class="response-detail">
          <h4>响应详情:</h4>
          <pre>{{ JSON.stringify(testResult.response, null, 2) }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const testing = ref(false)

const testForm = reactive({
  secretId: '',
  secretKey: '',
  targetCardKey: '',
  newCardKey: ''
})

const signatureInfo = ref<{
  timestamp: number
  stringToSign: string
  signature: string
} | null>(null)

const testResult = ref<{
  success: boolean
  message: string
  response?: any
} | null>(null)

// 使用Web Crypto API生成HMAC-SHA256签名
const generateHmacSha256 = async (message: string, key: string): Promise<string> => {
  const encoder = new TextEncoder()
  const keyData = encoder.encode(key)
  const messageData = encoder.encode(message)

  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  )

  const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData)
  const hashArray = Array.from(new Uint8Array(signature))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

// 生成签名
const generateSignature = async () => {
  if (!testForm.secretKey) {
    ElMessage.warning('请输入SecretKey')
    return
  }
  
  const method = 'POST'
  const uri = '/api/v1/third/recharge'
  const timestamp = Math.floor(Date.now() / 1000)
  
  const params = {
    target_card_key: testForm.targetCardKey,
    new_card_key: testForm.newCardKey,
    timestamp: timestamp.toString()
  }
  
  // 对参数进行排序
  const sortedKeys = Object.keys(params).sort()
  
  // 构造查询字符串
  const queryString = sortedKeys
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key as keyof typeof params])}`)
    .join('&')
  
  // 构造待签名字符串
  const stringToSign = `${method}\n${uri}\n${queryString}`

  // 生成签名 (使用Web Crypto API)
  const signature = await generateHmacSha256(stringToSign, testForm.secretKey)
  
  signatureInfo.value = {
    timestamp,
    stringToSign,
    signature
  }
  
  ElMessage.success('签名生成成功')
}

// 测试充值
const testRecharge = async () => {
  if (!testForm.secretId || !testForm.secretKey || !testForm.targetCardKey || !testForm.newCardKey) {
    ElMessage.warning('请填写完整的测试信息')
    return
  }
  
  testing.value = true
  testResult.value = null
  
  try {
    // 生成签名
    await generateSignature()
    
    if (!signatureInfo.value) {
      throw new Error('签名生成失败')
    }
    
    // 构造请求
    const requestData = {
      target_card_key: testForm.targetCardKey,
      new_card_key: testForm.newCardKey
    }
    
    const headers = {
      'Content-Type': 'application/json',
      'X-Secret-Id': testForm.secretId,
      'X-Timestamp': signatureInfo.value.timestamp.toString(),
      'X-Signature': signatureInfo.value.signature
    }
    
    // 发送请求
    const response = await fetch('/api/v1/third/recharge', {
      method: 'POST',
      headers,
      body: JSON.stringify(requestData)
    })
    
    const result = await response.json()
    
    testResult.value = {
      success: result.code === 0,
      message: result.message || '请求完成',
      response: result
    }
    
    if (result.code === 0) {
      ElMessage.success('充值测试成功')
    } else {
      ElMessage.error(`充值测试失败: ${result.message}`)
    }
    
  } catch (error) {
    testResult.value = {
      success: false,
      message: error instanceof Error ? error.message : '未知错误',
      response: null
    }
    ElMessage.error('测试请求失败')
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.api-key-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.signature-info {
  margin: 20px 0;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #303133;
}

.info-item code {
  display: block;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  word-break: break-all;
}

.info-item pre {
  padding: 12px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
}

.test-result {
  margin: 20px 0;
}

.response-detail {
  margin-top: 15px;
}

.response-detail h4 {
  margin-bottom: 10px;
  color: #303133;
}

.response-detail pre {
  padding: 12px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
}
</style>
