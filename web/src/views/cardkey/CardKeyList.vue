<template>
  <div class="cardkey-list">
    <div class="toolbar">
      <el-button type="primary" @click="handleGenerate" plain>
        <el-icon><Plus /></el-icon>
        生成卡密
      </el-button>
      <el-button type="danger" :disabled="!selectedKeys.length" @click="handleBatchDelete" plain>
        <el-icon><Delete /></el-icon>
        批量删除
      </el-button>
      <el-button type="primary" :disabled="!selectedKeys.length" @click="handleCopySelected" plain>
        <el-icon><Document /></el-icon>
        复制选中
      </el-button>
      <div class="search-area">
        <el-select v-model="searchStatus" placeholder="状态" clearable @change="handleSearch">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input
          v-model="searchKey"
          placeholder="搜索卡密"
          clearable
          @keyup.enter="handleSearch"
          @clear="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <el-card class="list-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="cardKeyList"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="card_key" label="卡密" min-width="180" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === 1 ? 'success' : 'warning'" effect="light">
              {{ row.type === 1 ? '时间卡' : '次数卡' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="value" label="值" width="100">
          <template #default="{ row }">
            {{ row.type === 1 ? `${row.value}天` : `${row.value}次` }}
          </template>
        </el-table-column>
        <el-table-column prop="max_devices" label="最大设备数" width="100">
          <template #default="{ row }">
            {{ row.max_devices }}台
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" effect="light">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="used_at" label="使用时间" width="180">
          <template #default="{ row }">
            {{ row.used_at ? formatDateTime(row.used_at) : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="expired_at" label="过期时间" width="180">
          <template #default="{ row }">
            {{ row.type === 1 ? formatDateTime(row.expired_at) : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="parent_id" label="目标卡密" width="180">
          <template #default="{ row }">
            {{ row.parent_id }}
          </template>
        </el-table-column>
        <el-table-column prop="recharge_time" label="充值时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.recharge_time)}}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
            <el-button type="success" link @click="handleRecharge(row)">充值</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <el-dialog
      v-model="dialogVisible"
      title="生成卡密"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="卡密类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio :label="1">时间卡</el-radio>
            <el-radio :label="2">次数卡</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="form.type === 1 ? '有效天数' : '可用次数'" prop="value">
          <el-input-number
            v-model="form.value"
            :min="1"
            :max="999999"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="前缀" prop="prefix">
          <el-input 
            v-model="form.prefix" 
            placeholder="选填，生成的卡密将带有此前缀"
            style="width: 240px;"
          />
        </el-form-item>
        <el-form-item label="卡密长度" prop="length">
          <el-input-number
            v-model="form.length"
            :min="6"
            :max="32"
            :step="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="生成数量" prop="count">
          <el-input-number
            v-model="form.count"
            :min="1"
            :max="1000"
            :step="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="最大设备数" prop="max_devices">
          <el-input-number v-model="form.max_devices" :min="1" :max="100" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="devicesDialogVisible"
      :title="'卡密详情 - ' + currentCardKey?.card_key"
      width="800px"
      destroy-on-close
    >
      <!-- 卡密充值信息 -->
      <el-card class="recharge-info-card" shadow="never" style="margin-bottom: 20px;">
        <template #header>
          <div class="card-header">
            <span>充值信息</span>
          </div>
        </template>
        
        <div v-if="currentCardKey?.parent_card" class="recharge-section">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="卡密">
              {{ currentCardKey.parent_card.card_key }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(currentCardKey.parent_card.status)" effect="light">
                {{ getStatusText(currentCardKey.parent_card.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="类型">
              <el-tag :type="currentCardKey.parent_card.type === 1 ? 'success' : 'warning'" effect="light">
                {{ currentCardKey.parent_card.type === 1 ? '时间卡' : '次数卡' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="充值时间">
              {{ formatDateTime(currentCardKey.recharge_time) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
      
        <div v-if="currentCardKey?.recharged_cards && currentCardKey.recharged_cards.length > 0" class="recharge-section">
          <el-table :data="currentCardKey.recharged_cards" style="width: 100%" border>
            <el-table-column prop="card_key" label="来源卡密" min-width="180">
              <template #default="{ row }">
                <el-tooltip :content="row.card_key" placement="top" :show-after="500">
                  <span>{{ row.card_key }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="row.type === 1 ? 'success' : 'warning'" effect="light">
                  {{ row.type === 1 ? '时间卡' : '次数卡' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" effect="light">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="recharge_time" label="充值时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.recharge_time) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <div v-if="!currentCardKey?.parent_card && (!currentCardKey?.recharged_cards || currentCardKey.recharged_cards.length === 0)" class="no-recharge-info">
          <el-empty description="暂无充值相关信息" />
        </div>
      </el-card>
      
      <!-- 设备管理 -->
      <el-card class="device-management-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>设备管理</span>
            <div>
              <el-button 
                type="danger" 
                :disabled="!selectedDevices.length" 
                @click="handleBatchUnbind"
                size="small"
              >
                <el-icon><Delete /></el-icon>
                批量解绑
              </el-button>
              <el-button 
                type="danger" 
                @click="handleUnbindAll"
                size="small"
              >
                <el-icon><Delete /></el-icon>
                解绑所有设备
              </el-button>
            </div>
          </div>
        </template>

      <el-table
        v-loading="deviceListLoading"
        :data="deviceList"
        style="width: 100%"
        @selection-change="handleDeviceSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="device_id" label="设备ID" min-width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tooltip :content="row.device_id" placement="top" :show-after="500">
              <span>{{ row.device_id }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="在线状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === DeviceStatus.ONLINE ? 'success' : 'info'" effect="light">
              {{ row.status === DeviceStatus.ONLINE ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_active" label="最近活跃" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.last_active) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="绑定时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                type="danger"
                size="small"
                @click="handleUnbindDevice(row)"
              >
                解绑
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination" style="margin-top: 20px; padding: 16px; border-top: 1px solid #f0f0f0;">
        <el-pagination
          v-model:current-page="deviceCurrentPage"
          v-model:page-size="devicePageSize"
          :page-sizes="[10, 20, 50]"
          :total="deviceTotal"
          layout="total, sizes, prev, pager, next"
          @size-change="handleDevicePageSizeChange"
          @current-change="handleDevicePageChange"
        />
      </div>
    </el-card>
    </el-dialog>

    <el-dialog
      v-model="resultDialogVisible"
      title="生成的卡密"
      width="600px"
      destroy-on-close
    >
      <div class="card-keys-container">
        <div v-for="(key, index) in generatedKeys" :key="index" class="card-key-item">
          <span>{{ key }}</span>
          <el-button type="primary" link @click="copyText(key)">复制</el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resultDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyAllKeys">复制全部</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 以卡充卡对话框 -->
    <el-dialog
      v-model="rechargeDialogVisible"
      title="以卡充卡"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="rechargeFormRef"
        :model="rechargeForm"
        :rules="rechargeRules"
        label-width="120px"
      >
        <el-form-item label="目标卡密" prop="targetCardKey">
          <el-input v-model="rechargeForm.targetCardKey" disabled placeholder="请选择要充值的卡密" />
        </el-form-item>
        <el-form-item label="充值卡密" prop="newCardKey">
          <el-input v-model="rechargeForm.newCardKey" placeholder="请输入用于充值的卡密" />
        </el-form-item>
        <el-alert
          title="注意: 充值后，新卡密将被标记为已禁用状态。"
          type="warning"
          :closable="false"
          style="margin-bottom: 10px;"
        />
        <el-alert
          title="充值要求: 新卡密与目标卡密的类型和最大设备数必须相同，否则充值将失败。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        <el-alert
          v-if="rechargeError"
          :title="rechargeError"
          type="error"
          :closable="true"
          @close="rechargeError = ''"
          style="margin-bottom: 20px;"
        />
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rechargeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRechargeSubmit">确认充值</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus, Delete, Document, Search } from '@element-plus/icons-vue'
import api from '../../api'
import type { CardKey, Device } from '../../api'
import { CardKeyStatus, DeviceStatus } from '../../constants'
import dayjs from 'dayjs'

const loading = ref(false)
const cardKeyList = ref<CardKey[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const devicesDialogVisible = ref(false)
const formRef = ref<FormInstance>()
const selectedKeys = ref<CardKey[]>([])
const currentCardKey = ref<CardKey | null>(null)
const resultDialogVisible = ref(false)
const generatedKeys = ref<string[]>([])
const searchKey = ref('')
const searchStatus = ref<number>()
const rechargeDialogVisible = ref(false)
const rechargeFormRef = ref<FormInstance>()
const rechargeForm = reactive({
  targetCardKey: '',
  newCardKey: ''
})
const rechargeError = ref('')

const form = reactive({
  type: 1,
  value: 1,
  prefix: '',
  length: 16,
  count: 1,
  max_devices: 1
})

const rules = {
  type: [{ required: true, message: '请选择卡密类型', trigger: 'change' }],
  value: [{ required: true, message: '请输入值', trigger: 'blur' }],
  length: [{ required: true, message: '请输入卡密长度', trigger: 'blur' }],
  count: [
    { required: true, message: '请输入生成数量', trigger: 'blur' },
    { type: 'number' as const, min: 1, max: 1000, message: '生成数量必须在1-1000之间', trigger: 'blur' }
  ],
  max_devices: [
    { required: true, message: '请输入最大设备数', trigger: 'blur' },
    { type: 'number' as const, min: 1, max: 100, message: '最大设备数必须在1-100之间', trigger: 'blur' }
  ]
}

const rechargeRules = {
  newCardKey: [
    { required: true, message: '请输入充值卡密', trigger: 'blur' }
  ]
}

const formatDateTime = (date: string | null) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const getStatusType = (status: number) => {
  switch (status) {
    case CardKeyStatus.UNUSED:
      return 'info'
    case CardKeyStatus.USED:
      return 'success'
    case CardKeyStatus.EXPIRED:
      return 'warning'
    case CardKeyStatus.DISABLED:
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: number) => {
  switch (status) {
    case CardKeyStatus.UNUSED:
      return '未使用'
    case CardKeyStatus.USED:
      return '已使用'
    case CardKeyStatus.EXPIRED:
      return '已过期'
    case CardKeyStatus.DISABLED:
      return '已禁用'
    default:
      return '未知'
  }
}

const statusOptions = [
  { value: CardKeyStatus.UNUSED, label: '未使用' },
  { value: CardKeyStatus.USED, label: '已使用' },
  { value: CardKeyStatus.EXPIRED, label: '已过期' },
  { value: CardKeyStatus.DISABLED, label: '已禁用' }
]

const loadData = async () => {
  loading.value = true
  try {
    const response = await api.getCardKeyList({
      page: currentPage.value,
      page_size: pageSize.value,
      card_key: searchKey.value,
      status: searchStatus.value
    })
    cardKeyList.value = response.data.items
    total.value = response.data.total
  } catch (error) {
    console.error('Failed to load card key list:', error)
    ElMessage.error('加载卡密列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection: CardKey[]) => {
  selectedKeys.value = selection
}

const handleEdit = async (row: CardKey) => {
  currentCardKey.value = row
  deviceCurrentPage.value = 1
  devicePageSize.value = 10
  devicesDialogVisible.value = true
  loadDeviceList()
}

const handleUnbindDevice = async (device: any) => {
  try {
    await ElMessageBox.confirm('确定要解绑该设备吗？', '提示', {
      type: 'warning'
    })
    await api.unbindDevice(currentCardKey.value!.card_key, [device.device_id])
    ElMessage.success('解绑成功')
    // 重新加载设备列表和卡密列表
    loadDeviceList()
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to unbind device:', error)
      ElMessage.error('解绑失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedKeys.value.length} 个卡密吗？`, '提示', {
      type: 'warning'
    })
    const cardKeys = selectedKeys.value.map(key => key.card_key)
    await api.deleteCardKey(cardKeys[0])
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete card keys:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadData()
}

const handleGenerate = () => {
  form.type = 1
  form.value = 30
  form.prefix = ''
  form.length = 10
  form.count = 1
  form.max_devices = 1
  dialogVisible.value = true
}

const handleDelete = async (row: CardKey) => {
  try {
    await ElMessageBox.confirm('确定要删除该卡密吗？', '提示', {
      type: 'warning'
    })
    await api.deleteCardKey(row.card_key)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete card key:', error)
      ElMessage.error('删除失败')
    }
  }
}

const copyText = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (err) {
    console.error('Failed to copy:', err)
    ElMessage.error('复制失败')
  }
}

const copyAllKeys = async () => {
  try {
    await navigator.clipboard.writeText(generatedKeys.value.join('\n'))
    ElMessage.success('全部复制成功')
  } catch (err) {
    console.error('Failed to copy all:', err)
    ElMessage.error('复制失败')
  }
}

const handleCopySelected = async () => {
  try {
    const selectedCardKeys = selectedKeys.value.map(key => key.card_key).join('\n')
    await navigator.clipboard.writeText(selectedCardKeys)
    ElMessage.success('选中卡密复制成功')
  } catch (err) {
    console.error('Failed to copy selected:', err)
    ElMessage.error('复制失败')
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await api.generateCardKey(form)
        const cardKeys = response.data.card_keys
        
        generatedKeys.value = cardKeys
        dialogVisible.value = false
        resultDialogVisible.value = true
        loadData()
      } catch (error) {
        console.error('Failed to generate card key:', error)
        ElMessage.error('生成失败')
      }
    }
  })
}

// 添加设备列表分页相关的响应式变量
const deviceList = ref<Device[]>([])
const deviceListLoading = ref(false)
const deviceCurrentPage = ref(1)
const devicePageSize = ref(10)
const deviceTotal = ref(0)

// 加载设备列表数据
const loadDeviceList = async () => {
  if (!currentCardKey.value) return
  
  deviceListLoading.value = true
  try {
    const response = await api.getCardKeyWithDevices(currentCardKey.value.card_key, {
      page: deviceCurrentPage.value,
      page_size: devicePageSize.value
    })
    currentCardKey.value = response.data.card
    deviceList.value = response.data.bindings
    deviceTotal.value = response.data.total
  } catch (error) {
    console.error('Failed to load device list:', error)
    ElMessage.error('加载设备列表失败')
  } finally {
    deviceListLoading.value = false
  }
}

const handleDevicePageSizeChange = (val: number) => {
  devicePageSize.value = val
  loadDeviceList()
}

const handleDevicePageChange = (val: number) => {
  deviceCurrentPage.value = val
  loadDeviceList()
}

const selectedDevices = ref<Device[]>([])

const handleDeviceSelectionChange = (selection: Device[]) => {
  selectedDevices.value = selection
}

const handleBatchUnbind = async () => {
  if (!currentCardKey.value || !selectedDevices.value.length) return

  try {
    await ElMessageBox.confirm(`确定要解绑选中的 ${selectedDevices.value.length} 个设备吗？`, '提示', {
      type: 'warning'
    })
    
    await api.unbindDevice(
      currentCardKey.value.card_key,
      selectedDevices.value.map(device => device.device_id)
    )
    
    ElMessage.success('批量解绑成功')
    loadDeviceList()
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to batch unbind devices:', error)
      ElMessage.error('批量解绑失败')
    }
  }
}

const handleUnbindAll = async () => {
  if (!currentCardKey.value) return

  try {
    await ElMessageBox.confirm(`确定要解绑所有设备吗？`, '提示', {
      type: 'warning'
    })
    
    await api.unbindAllDevices(currentCardKey.value.card_key)
    
    ElMessage.success('全部解绑成功')
    loadDeviceList()
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to unbind all devices:', error)
      ElMessage.error('全部解绑失败')
    }
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

const handleRecharge = (row: CardKey) => {
  rechargeForm.targetCardKey = row.card_key
  rechargeForm.newCardKey = ''
  rechargeError.value = ''
  rechargeDialogVisible.value = true
}

const handleRechargeSubmit = async () => {
  if (!rechargeFormRef.value) return

  await rechargeFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await api.rechargeCardKey(rechargeForm.targetCardKey, rechargeForm.newCardKey)
        // API返回的响应已经是处理过的，只有成功才会到这里
        ElMessage.success('卡密充值成功')
        rechargeDialogVisible.value = false
        loadData() // 重新加载卡密列表
      } catch (error: any) {
        console.error('Failed to recharge card key:', error)
        // 记录错误信息到对话框中显示
        rechargeError.value = error.message || '卡密充值失败'
        // 也显示一个消息通知
        ElMessage.error(error.message || '卡密充值失败')
      }
    }
  })
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.cardkey-list {
  padding: 20px;
}

.toolbar {
  margin-bottom: 16px;
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-area {
  margin-left: auto;
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-area :deep(.el-input) {
  width: 240px;
}

.search-area :deep(.el-select) {
  width: 120px;
}

.list-card {
  background: #fff;
  border-radius: 8px;
}

.list-card :deep(.el-card__body) {
  padding: 0;
}

.list-card :deep(.el-table) {
  border-radius: 8px 8px 0 0;
}

.list-card :deep(.el-table th.el-table__cell) {
  background-color: #f5f7fa;
  font-weight: 600;
}

.pagination {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  background: #fff;
  border-radius: 0 0 8px 8px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-button--link) {
  padding: 4px 8px;
}

:deep(.el-tag) {
  border-radius: 4px;
}

.card-keys-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
}

.card-key-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.card-key-item:last-child {
  border-bottom: none;
}

.card-key-item span {
  font-family: monospace;
  font-size: 14px;
}

/* 卡密详情对话框样式 */
.recharge-info-card,
.device-management-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recharge-section {
  margin-bottom: 20px;
}

.recharge-section h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 15px;
  color: #606266;
}

.no-recharge-info {
  padding: 20px 0;
}

:deep(.el-descriptions) {
  margin-bottom: 16px;
}

:deep(.el-descriptions__label) {
  width: 100px;
  font-weight: bold;
}
</style>