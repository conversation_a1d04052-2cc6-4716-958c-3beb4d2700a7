<template>
  <div class="admin-list">
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd" plain>
        <el-icon><Plus /></el-icon>
        添加管理员
      </el-button>
    </div>

    <el-card class="list-card" shadow="never">
      <el-table :data="adminList" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'" effect="light">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button :type="row.status === 1 ? 'warning' : 'success'" link @click="handleStatusChange(row)">
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加管理员' : '编辑管理员'"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" :disabled="dialogType === 'edit'" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import api from '../../api'
import type { Admin } from '../../api'
import dayjs from 'dayjs'

const loading = ref(false)
const adminList = ref<Admin[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const formRef = ref<FormInstance>()

const form = ref({
  id: undefined as number | undefined,
  username: '',
  password: ''
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

const formatDateTime = (date: string | null) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const loadData = async () => {
  loading.value = true
  try {
    const response = await api.getAdminList({
      page: currentPage.value,
      page_size: pageSize.value
    })
    adminList.value = response.data.items
    total.value = response.data.total
  } catch (error) {
    console.error('Failed to load admin list:', error)
    ElMessage.error('加载管理员列表失败')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadData()
}

const handleAdd = () => {
  dialogType.value = 'add'
  form.value = {
    id: undefined,
    username: '',
    password: ''
  }
  dialogVisible.value = true
}

const handleEdit = (row: Admin) => {
  dialogType.value = 'edit'
  form.value = {
    id: row.id,
    username: row.username,
    password: ''
  }
  dialogVisible.value = true
}

const handleStatusChange = async (row: Admin) => {
  try {
    await api.updateAdminStatus(row.id, row.status === 1 ? 2 : 1)
    ElMessage.success('状态更新成功')
    loadData()
  } catch (error) {
    console.error('Failed to update admin status:', error)
    ElMessage.error('更新状态失败')
  }
}

const handleDelete = async (row: Admin) => {
  try {
    await ElMessageBox.confirm('确定要删除该管理员吗？', '提示', {
      type: 'warning'
    })
    await api.deleteAdmin(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete admin:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          const { username, password } = form.value
          await api.createAdmin({ username, password })
          ElMessage.success('添加成功')
        } else {
          const updateData: any = { id: form.value.id }
          if (form.value.password) {
            updateData.password = form.value.password
          }
          await api.updateAdmin(form.value.id!, updateData)
          ElMessage.success('更新成功')
        }
        dialogVisible.value = false
        loadData()
      } catch (error) {
        console.error('Failed to submit admin:', error)
        ElMessage.error(dialogType.value === 'add' ? '添加失败' : '更新失败')
      }
    }
  })
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.admin-list {
  padding: 20px;
}

.toolbar {
  margin-bottom: 16px;
  display: flex;
  gap: 12px;
}

.list-card {
  background: #fff;
  border-radius: 8px;
}

.list-card :deep(.el-card__body) {
  padding: 0;
}

.list-card :deep(.el-table) {
  border-radius: 8px 8px 0 0;
}

.list-card :deep(.el-table th.el-table__cell) {
  background-color: #f5f7fa;
  font-weight: 600;
}

.pagination {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  background: #fff;
  border-radius: 0 0 8px 8px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-button--link) {
  padding: 4px 8px;
}

:deep(.el-tag) {
  border-radius: 4px;
}
</style> 