<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>总卡密数</span>
            <el-icon><Ticket /></el-icon>
          </div>
        </template>
        <div class="card-value">{{ stats.totalCardKeys }}</div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>已使用卡密</span>
            <el-icon><Check /></el-icon>
          </div>
        </template>
        <div class="card-value">{{ stats.usedCardKeys }}</div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>过期卡密</span>
            <el-icon><Timer /></el-icon>
          </div>
        </template>
        <div class="card-value">{{ stats.expiredCardKeys }}</div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>可用绑定数</span>
            <el-icon><Link /></el-icon>
          </div>
        </template>
        <div class="card-value">{{ stats.availableBindings }}</div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>绑定设备总数</span>
            <el-icon><Monitor /></el-icon>
          </div>
        </template>
        <div class="card-value">{{ stats.totalBindings }}</div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>当前在线设备</span>
            <el-icon><Connection /></el-icon>
          </div>
        </template>
        <div class="card-value">{{ stats.activeDevices }}</div>
      </el-card>
    </div>

    <!-- Hamibot活跃用户趋势图 -->
    <el-card class="chart-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>近30天Hamibot活跃用户趋势</span>
          <el-icon><TrendCharts /></el-icon>
        </div>
      </template>
      <div class="chart-container" ref="hamibotChartRef"></div>
    </el-card>

    <!-- Hamibot机器人活跃用户趋势图 -->
    <el-card class="chart-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>近30天Hamibot机器人活跃用户趋势</span>
          <el-icon><TrendCharts /></el-icon>
        </div>
      </template>
      <div class="chart-container" ref="hamibotRobotChartRef"></div>
    </el-card>

    <!-- 活跃设备趋势图 -->
    <el-card class="chart-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>近30天活跃设备趋势</span>
          <el-icon><TrendCharts /></el-icon>
        </div>
      </template>
      <div class="chart-container" ref="deviceChartRef"></div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Ticket, Check, Timer, Monitor, Connection, TrendCharts, Link } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import api from '@/api'

interface DailyStat {
  date: string
  count: number
}

interface Stats {
  totalCardKeys: number
  usedCardKeys: number
  expiredCardKeys: number
  totalBindings: number
  activeDevices: number
  dailyStats: DailyStat[]
  availableBindings: number
}

interface HamibotStats {
  date: string
  plan_name: string
  count: number
}

interface HamibotRobotStats {
  date: string
  count: number
}

const stats = ref<Stats>({
  totalCardKeys: 0,
  usedCardKeys: 0,
  expiredCardKeys: 0,
  totalBindings: 0,
  activeDevices: 0,
  dailyStats: [],
  availableBindings: 0
})

const hamibotStats = ref<HamibotStats[]>([])
const hamibotRobotStats = ref<HamibotRobotStats[]>([])

const deviceChartRef = ref<HTMLElement>()
const hamibotChartRef = ref<HTMLElement>()
const hamibotRobotChartRef = ref<HTMLElement>()
let deviceChart: echarts.ECharts | null = null
let hamibotChart: echarts.ECharts | null = null
let hamibotRobotChart: echarts.ECharts | null = null

const loadData = async () => {
  try {
    // 并行加载所有接口的数据
    const [statsResponse, hamibotResponse, hamibotRobotResponse] = await Promise.all([
      api.getStats(),
      api.getHamibotStats(),
      api.getHamibotRobotStats()
    ])

    // 处理设备统计数据
    if (statsResponse.data) {
      stats.value = {
        ...stats.value,
        ...statsResponse.data,
        dailyStats: statsResponse.data.dailyStats || []
      }
    }
    initDeviceChart()

    // 处理Hamibot统计数据
    if (hamibotResponse.data) {
      hamibotStats.value = hamibotResponse.data
    }
    initHamibotChart()

    // 处理Hamibot机器人统计数据
    if (hamibotRobotResponse.data) {
      hamibotRobotStats.value = hamibotRobotResponse.data
    }
    initHamibotRobotChart()
  } catch (error) {
    console.error('Failed to load stats:', error)
    ElMessage.error('加载统计数据失败')
    // 保持默认值
    stats.value = {
      totalCardKeys: 0,
      usedCardKeys: 0,
      expiredCardKeys: 0,
      totalBindings: 0,
      activeDevices: 0,
      dailyStats: [],
      availableBindings: 0
    }
    hamibotStats.value = []
    hamibotRobotStats.value = []
    initDeviceChart()
    initHamibotChart()
    initHamibotRobotChart()
  }
}

const initDeviceChart = () => {
  if (!deviceChartRef.value) return

  deviceChart = echarts.init(deviceChartRef.value)
  
  const dailyStats = stats.value.dailyStats || []
  const dates = dailyStats.map(item => item.date)

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any[]) {
        const data = params[0]
        return `${data.name}<br/>${data.seriesName}：${data.value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 30,
        formatter: (value: string) => value
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '活跃设备数',
        type: 'line',
        data: dailyStats.map(item => item.count),
        smooth: true,
        showSymbol: false,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2
        },
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(64,158,255,0.3)'
            },
            {
              offset: 1,
              color: 'rgba(64,158,255,0.1)'
            }
          ])
        }
      }
    ]
  }

  deviceChart.setOption(option)
}

const initHamibotChart = () => {
  if (!hamibotChartRef.value) return

  hamibotChart = echarts.init(hamibotChartRef.value)
  
  // 获取所有唯一的日期和计划名称
  const dates = Array.from(new Set(hamibotStats.value.map(item => item.date))).sort((a, b) => {
    const [aMonth, aDay] = a.split('-').map(Number)
    const [bMonth, bDay] = b.split('-').map(Number)
    return aMonth === bMonth ? aDay - bDay : aMonth - bMonth
  })
  const planNames = Array.from(new Set(hamibotStats.value.map(item => item.plan_name)))

  // 为每个计划生成一个系列
  const series = planNames.map(plan => {
    return {
      name: plan || '未知计划',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: dates.map(date => {
        const stat = hamibotStats.value.find(item => item.date === date && item.plan_name === plan)
        return stat ? stat.count : 0
      })
    }
  })

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any[]) {
        let result = `${params[0].name}<br/>`
        let total = 0
        params.forEach((param: any) => {
          const value = param.value || 0
          total += value
          result += `${param.seriesName}：${value}<br/>`
        })
        result += `总计：${total}`
        return result
      }
    },
    legend: {
      data: planNames.map(plan => plan || '未知计划'),
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates,
      axisLabel: {
        rotate: 30,
        formatter: (value: string) => value
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series
  }

  hamibotChart.setOption(option)
}

const initHamibotRobotChart = () => {
  if (!hamibotRobotChartRef.value) return

  hamibotRobotChart = echarts.init(hamibotRobotChartRef.value)
  
  const dates = hamibotRobotStats.value.map(item => item.date)
  const data = hamibotRobotStats.value.map(item => item.count)

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any[]) {
        const data = params[0]
        return `${data.name}<br/>${data.seriesName}：${data.value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 30,
        formatter: (value: string) => value
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '活跃机器人数',
        type: 'line',
        data: data,
        smooth: true,
        showSymbol: false,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2
        },
        itemStyle: {
          color: '#67C23A'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(103,194,58,0.3)'
            },
            {
              offset: 1,
              color: 'rgba(103,194,58,0.1)'
            }
          ])
        }
      }
    ]
  }

  hamibotRobotChart.setOption(option)
}

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  deviceChart?.resize()
  hamibotChart?.resize()
  hamibotRobotChart?.resize()
}

onMounted(() => {
  loadData()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  deviceChart?.dispose()
  hamibotChart?.dispose()
  hamibotRobotChart?.dispose()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  color: #606266;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  text-align: center;
  padding: 10px 0;
}

.chart-card {
  background: #fff;
  border-radius: 8px;
}

.chart-container {
  height: 400px;
  width: 100%;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 