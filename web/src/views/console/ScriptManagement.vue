<template>
  <div class="script-management">
    <div class="page-header">
      <h2>脚本管理</h2>
      <div class="header-actions">
        <el-select
          v-model="typeFilter"
          placeholder="脚本类型"
          style="width: 120px"
          @change="loadScripts"
          clearable
        >
          <el-option label="AutoX" value="autox" />
          <el-option label="Hamibot" value="hamibot" />
          <el-option label="其他" value="other" />
        </el-select>
        <el-select
          v-model="statusFilter"
          placeholder="状态"
          style="width: 100px"
          @change="loadScripts"
          clearable
        >
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="2" />
        </el-select>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索脚本名称或描述"
          style="width: 300px"
          @keyup.enter="loadScripts"
        >
          <template #append>
            <el-button @click="loadScripts" :icon="Search" />
          </template>
        </el-input>
      </div>
    </div>

    <el-card>
      <el-table
        :data="scripts"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="脚本名称" width="200" />
        <el-table-column prop="description" label="描述" width="250" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)">
              {{ row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="owner.username" label="所有者" width="150" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 1"
              type="warning"
              size="small"
              @click="updateScriptStatus(row.id, 2)"
            >
              禁用
            </el-button>
            <el-button
              v-else
              type="success"
              size="small"
              @click="updateScriptStatus(row.id, 1)"
            >
              启用
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="viewScriptDetails(row)"
            >
              详情
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteScript(row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadScripts"
          @current-change="loadScripts"
        />
      </div>
    </el-card>

    <!-- 脚本详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="脚本详情"
      width="800px"
      :before-close="handleDetailClose"
    >
      <div v-if="selectedScript" class="script-details">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="脚本ID">
                {{ selectedScript.id }}
              </el-descriptions-item>
              <el-descriptions-item label="脚本名称">
                {{ selectedScript.name }}
              </el-descriptions-item>
              <el-descriptions-item label="脚本类型">
                <el-tag :type="getTypeColor(selectedScript.type)">
                  {{ selectedScript.type }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="selectedScript.status === 1 ? 'success' : 'danger'">
                  {{ selectedScript.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="所有者">
                {{ selectedScript.owner?.username || '未知' }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDate(selectedScript.created_at) }}
              </el-descriptions-item>
              <el-descriptions-item label="描述" :span="2">
                {{ selectedScript.description || '无描述' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane label="脚本内容" name="content">
            <el-input
              v-model="selectedScript.content"
              type="textarea"
              :rows="20"
              readonly
              placeholder="脚本内容"
            />
          </el-tab-pane>
          <el-tab-pane label="配置信息" name="config">
            <pre class="config-content">{{ formatConfig(selectedScript.config) }}</pre>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { scriptApi } from '@/api/script'
import { formatDate } from '@/utils/date'

// 响应式数据
const scripts = ref([])
const loading = ref(false)
const searchKeyword = ref('')
const typeFilter = ref('')
const statusFilter = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const detailDialogVisible = ref(false)
const selectedScript = ref(null)
const activeTab = ref('info')

// 获取类型颜色
const getTypeColor = (type) => {
  const colors = {
    'autox': 'primary',
    'hamibot': 'success',
    'other': 'info'
  }
  return colors[type] || 'info'
}

// 格式化配置
const formatConfig = (config) => {
  if (!config) return '无配置'
  try {
    return JSON.stringify(JSON.parse(config), null, 2)
  } catch {
    return config
  }
}

// 加载脚本列表
const loadScripts = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      keyword: searchKeyword.value,
      type: typeFilter.value
    }
    if (statusFilter.value !== null) {
      params.status = statusFilter.value
    }

    const response = await scriptApi.getScriptList(params)
    scripts.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('加载脚本列表失败')
  } finally {
    loading.value = false
  }
}

// 更新脚本状态
const updateScriptStatus = async (scriptId, status) => {
  const action = status === 1 ? '启用' : '禁用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}该脚本吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await scriptApi.updateScriptStatus({
      id: scriptId,
      status: status
    })

    ElMessage.success(`${action}脚本成功`)
    loadScripts()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}脚本失败`)
    }
  }
}

// 删除脚本
const deleteScript = async (scriptId) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该脚本吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await scriptApi.deleteScript({ id: scriptId })
    ElMessage.success('脚本删除成功')
    loadScripts()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除脚本失败')
    }
  }
}

// 查看脚本详情
const viewScriptDetails = async (script) => {
  try {
    const response = await scriptApi.getScriptById(script.id)
    selectedScript.value = response.data
    detailDialogVisible.value = true
    activeTab.value = 'info'
  } catch (error) {
    ElMessage.error('获取脚本详情失败')
  }
}

// 关闭详情对话框
const handleDetailClose = () => {
  detailDialogVisible.value = false
  selectedScript.value = null
}

// 初始化
onMounted(() => {
  loadScripts()
})
</script>

<style scoped>
.script-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.script-details {
  padding: 20px 0;
}

.config-content {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
}
</style>
