<template>
  <div class="device-management">
    <div class="page-header">
      <h2>设备管理</h2>
      <div class="header-actions">
        <el-select
          v-model="statusFilter"
          placeholder="设备状态"
          style="width: 120px"
          @change="loadDevices"
          clearable
        >
          <el-option label="离线" :value="0" />
          <el-option label="在线" :value="1" />
          <el-option label="忙碌" :value="2" />
        </el-select>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索设备ID或名称"
          style="width: 300px"
          @keyup.enter="loadDevices"
        >
          <template #append>
            <el-button @click="loadDevices" :icon="Search" />
          </template>
        </el-input>
      </div>
    </div>

    <el-card>
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value">{{ onlineDeviceCount }}</div>
                <div class="stat-label">在线设备</div>
              </div>
              <el-icon class="stat-icon online"><Monitor /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value">{{ total }}</div>
                <div class="stat-label">总设备数</div>
              </div>
              <el-icon class="stat-icon total"><Cpu /></el-icon>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <el-table
        :data="devices"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="device_id" label="设备ID" width="200" />
        <el-table-column prop="name" label="设备名称" width="150" />
        <el-table-column prop="type" label="设备类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === 'android' ? 'success' : 'primary'">
              {{ row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="user.username" label="所属用户" width="150" />
        <el-table-column prop="ip" label="IP地址" width="150" />
        <el-table-column prop="version" label="版本" width="100" />
        <el-table-column prop="last_active" label="最后活跃" width="180">
          <template #default="{ row }">
            {{ row.last_active ? formatDate(row.last_active) : '从未活跃' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="danger"
              size="small"
              @click="deleteDevice(row.device_id)"
            >
              删除
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="viewDeviceDetails(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadDevices"
          @current-change="loadDevices"
        />
      </div>
    </el-card>

    <!-- 设备详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="设备详情"
      width="600px"
    >
      <div v-if="selectedDevice" class="device-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备ID">
            {{ selectedDevice.device_id }}
          </el-descriptions-item>
          <el-descriptions-item label="设备名称">
            {{ selectedDevice.name }}
          </el-descriptions-item>
          <el-descriptions-item label="设备类型">
            <el-tag :type="selectedDevice.type === 'android' ? 'success' : 'primary'">
              {{ selectedDevice.type }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedDevice.status)">
              {{ getStatusText(selectedDevice.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="所属用户">
            {{ selectedDevice.user?.username || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ selectedDevice.ip }}
          </el-descriptions-item>
          <el-descriptions-item label="用户代理" :span="2">
            {{ selectedDevice.user_agent }}
          </el-descriptions-item>
          <el-descriptions-item label="版本">
            {{ selectedDevice.version }}
          </el-descriptions-item>
          <el-descriptions-item label="最后活跃">
            {{ selectedDevice.last_active ? formatDate(selectedDevice.last_active) : '从未活跃' }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间" :span="2">
            {{ formatDate(selectedDevice.created_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Monitor, Cpu } from '@element-plus/icons-vue'
import { deviceApi } from '@/api/device'
import { formatDate } from '@/utils/date'

// 响应式数据
const devices = ref([])
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const onlineDeviceCount = ref(0)
const detailDialogVisible = ref(false)
const selectedDevice = ref(null)

// 获取状态类型
const getStatusType = (status) => {
  const types = { 0: 'info', 1: 'success', 2: 'warning' }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = { 0: '离线', 1: '在线', 2: '忙碌' }
  return texts[status] || '未知'
}

// 加载设备列表
const loadDevices = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      keyword: searchKeyword.value
    }
    if (statusFilter.value !== null) {
      params.status = statusFilter.value
    }

    const response = await deviceApi.getDeviceList(params)
    devices.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('加载设备列表失败')
  } finally {
    loading.value = false
  }
}

// 加载在线设备数量
const loadOnlineDeviceCount = async () => {
  try {
    const response = await deviceApi.getOnlineDeviceCount()
    onlineDeviceCount.value = response.data.count
  } catch (error) {
    console.error('加载在线设备数量失败')
  }
}

// 删除设备
const deleteDevice = async (deviceId) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该设备吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deviceApi.deleteDevice({ device_id: deviceId })
    ElMessage.success('设备删除成功')
    loadDevices()
    loadOnlineDeviceCount()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除设备失败')
    }
  }
}

// 查看设备详情
const viewDeviceDetails = (device) => {
  selectedDevice.value = device
  detailDialogVisible.value = true
}

// 初始化
onMounted(() => {
  loadDevices()
  loadOnlineDeviceCount()
})
</script>

<style scoped>
.device-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  opacity: 0.3;
}

.stat-icon.online {
  color: #67c23a;
}

.stat-icon.total {
  color: #409eff;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.device-details {
  padding: 20px 0;
}
</style>
