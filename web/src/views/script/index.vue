<template>
  <div class="script-manager">
    <div class="toolbar">
      <el-button type="primary" @click="loadScripts" plain>
        <el-icon><Refresh /></el-icon>
        刷新列表
      </el-button>
    </div>

    <el-card class="list-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="scriptList"
        stripe
      >
        <el-table-column prop="script_type" label="脚本类型" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getScriptTypeTag(row.script_type)" effect="light">
              {{ getScriptTypeLabel(row.script_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="branch" label="分支" width="100">
          <template #default="{ row }">
            <el-tag :type="row.branch === 'online' ? 'success' : 'warning'" effect="light">
              {{ row.branch }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="file_name" label="文件名" min-width="200" />
        <el-table-column prop="updated_at" label="最后更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              :loading="row.updating"
              @click="handleUpdate(row)"
            >
              更新
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import api from '@/api'
import type { Script } from '@/api'
import dayjs from 'dayjs'

interface ExtendedScript extends Script {
  updating?: boolean
}

const loading = ref(false)
const scriptList = ref<ExtendedScript[]>([])

const formatDateTime = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const getScriptTypeTag = (type: string) => {
  switch (type) {
    case 'hamibot':
      return 'success'
    case 'autojs':
      return 'primary'
    case 'autojs_login':
      return 'info'
    case 'bot':
      return 'warning'
    case 'botui':
      return 'danger'
    default:
      return 'info'
  }
}

const getScriptTypeLabel = (type: string) => {
  return type
}

const loadScripts = async () => {
  loading.value = true
  try {
    const response = await api.getScriptList()
    scriptList.value = response.data
  } catch (error) {
    console.error('Failed to load script list:', error)
    ElMessage.error('加载脚本列表失败')
  } finally {
    loading.value = false
  }
}

const handleUpdate = async (row: ExtendedScript) => {
  row.updating = true
  try {
    await api.updateScript({
      script_type: row.script_type,
      branch: row.branch
    })
    ElMessage.success('更新成功')
    loadScripts()
  } catch (error) {
    console.error('Failed to update script:', error)
    ElMessage.error('更新失败')
  } finally {
    row.updating = false
  }
}

onMounted(() => {
  loadScripts()
})
</script>

<style scoped>
.script-manager {
  padding: 20px;
}

.toolbar {
  margin-bottom: 16px;
  display: flex;
  gap: 12px;
}

.list-card {
  background: #fff;
  border-radius: 8px;
}

.list-card :deep(.el-card__body) {
  padding: 0;
}

.list-card :deep(.el-table) {
  border-radius: 8px 8px 0 0;
}

.list-card :deep(.el-table th.el-table__cell) {
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.el-button--link) {
  padding: 4px 8px;
}

:deep(.el-tag) {
  border-radius: 4px;
}
</style> 