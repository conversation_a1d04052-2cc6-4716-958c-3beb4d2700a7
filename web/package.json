{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.4.0", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "pinia": "^2.3.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^22.15.21", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "axios": "^1.9.0", "element-plus": "^2.9.10", "sass": "^1.89.0", "sass-embedded": "^1.89.1", "typescript": "~5.6.2", "vite": "^6.3.3", "vue-tsc": "^2.2.0"}}