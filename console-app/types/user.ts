// 用户相关类型定义

export interface User {
  id: number
  username: string
  email?: string
  nickname: string
  avatar?: string
  status: number // 1-正常 2-禁用
  last_login_at?: string
  created_at: string
  updated_at: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  user: User
  token: string
  refresh_token: string
}

export interface RegisterRequest {
  username: string
  password: string
  email?: string
}

export interface RefreshTokenRequest {
  refresh_token: string
}

export interface ChangePasswordRequest {
  old_password: string
  new_password: string
}

export interface UserListResponse {
  list: User[]
  total: number
  page: number
  page_size: number
}
