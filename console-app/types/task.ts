// 任务相关类型定义

import type { User } from './user'
import type { <PERSON>rip<PERSON> } from './script'
import type { Device } from './device'

export interface Task {
  id: number
  name: string
  description?: string
  type: string // script, batch, schedule
  script_id: number
  script?: Script
  target_devices: string // JSON格式的设备ID列表
  config: string // JSON格式的配置参数
  status: number // 0-待执行 1-执行中 2-已完成 3-失败 4-已取消
  progress: number // 执行进度 0-100
  created_by: number
  creator?: User
  created_at: string
  updated_at: string
  started_at?: string
  completed_at?: string
}

export interface TaskResult {
  id: number
  task_id: number
  task?: Task
  device_id: number
  device?: Device
  status: number // 0-待执行 1-执行中 2-成功 3-失败
  output?: string
  error?: string
  started_at?: string
  completed_at?: string
  created_at: string
  updated_at: string
}

export interface CreateTaskRequest {
  name: string
  description?: string
  type?: string
  script_id: number
  target_devices: number[]
  config?: Record<string, any>
}

export interface StartTaskRequest {
  id: number
}

export interface CancelTaskRequest {
  id: number
}

export interface UpdateTaskProgressRequest {
  id: number
  progress: number
}

export interface UpdateTaskResultRequest {
  task_id: number
  device_id: number
  status: number
  output?: string
  error?: string
}

export interface TaskListResponse {
  list: Task[]
  total: number
  page: number
  page_size: number
}

// 任务状态枚举
export enum TaskStatus {
  PENDING = 0,
  RUNNING = 1,
  COMPLETED = 2,
  FAILED = 3,
  CANCELLED = 4
}

// 任务类型枚举
export enum TaskType {
  SCRIPT = 'script',
  BATCH = 'batch',
  SCHEDULE = 'schedule'
}

// 任务结果状态枚举
export enum TaskResultStatus {
  PENDING = 0,
  RUNNING = 1,
  SUCCESS = 2,
  FAILED = 3
}
