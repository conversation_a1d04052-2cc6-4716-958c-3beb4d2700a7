import { useUserStore } from '@/stores/user'

// 请求配置接口
interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  params?: any
  header?: Record<string, string>
  timeout?: number
}

// 响应接口
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 基础配置
const BASE_URL = process.env.NODE_ENV === 'development' ? 'http://localhost:8080' : ''
const TIMEOUT = 10000

// 请求拦截器
const requestInterceptor = (config: RequestConfig) => {
  // 添加基础URL
  if (!config.url.startsWith('http')) {
    config.url = BASE_URL + config.url
  }

  // 设置默认请求头
  config.header = {
    'Content-Type': 'application/json',
    ...config.header
  }

  // 添加认证令牌
  const userStore = useUserStore()
  if (userStore.token) {
    config.header.Authorization = `Bearer ${userStore.token}`
  }

  // 设置超时时间
  config.timeout = config.timeout || TIMEOUT

  console.log('请求配置:', config)
  return config
}

// 响应拦截器
const responseInterceptor = async (response: any, config: RequestConfig) => {
  console.log('响应数据:', response)

  // 请求成功
  if (response.statusCode === 200) {
    const data: ApiResponse = response.data

    // 业务成功
    if (data.code === 200) {
      return Promise.resolve(data)
    }

    // 业务失败
    if (data.code === 401) {
      // 令牌过期，尝试刷新
      const userStore = useUserStore()
      const refreshSuccess = await userStore.refreshAccessToken()
      
      if (refreshSuccess) {
        // 刷新成功，重新发起请求
        return request(config)
      } else {
        // 刷新失败，跳转登录
        uni.showToast({
          title: '登录已过期，请重新登录',
          icon: 'none'
        })
        userStore.logout()
        return Promise.reject(new Error('登录已过期'))
      }
    }

    // 其他业务错误
    uni.showToast({
      title: data.message || '请求失败',
      icon: 'none'
    })
    return Promise.reject(new Error(data.message || '请求失败'))
  }

  // HTTP错误
  let errorMessage = '网络错误'
  switch (response.statusCode) {
    case 400:
      errorMessage = '请求参数错误'
      break
    case 401:
      errorMessage = '未授权访问'
      break
    case 403:
      errorMessage = '禁止访问'
      break
    case 404:
      errorMessage = '请求地址不存在'
      break
    case 500:
      errorMessage = '服务器内部错误'
      break
    case 502:
      errorMessage = '网关错误'
      break
    case 503:
      errorMessage = '服务不可用'
      break
    case 504:
      errorMessage = '网关超时'
      break
    default:
      errorMessage = `网络错误 ${response.statusCode}`
  }

  uni.showToast({
    title: errorMessage,
    icon: 'none'
  })
  return Promise.reject(new Error(errorMessage))
}

// 主请求函数
export const request = <T = any>(config: RequestConfig): Promise<ApiResponse<T>> => {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const finalConfig = requestInterceptor(config)

    // 发起请求
    uni.request({
      url: finalConfig.url,
      method: finalConfig.method || 'GET',
      data: finalConfig.data,
      header: finalConfig.header,
      timeout: finalConfig.timeout,
      success: async (response) => {
        try {
          const result = await responseInterceptor(response, config)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        console.error('请求失败:', error)
        
        let errorMessage = '网络连接失败'
        if (error.errMsg) {
          if (error.errMsg.includes('timeout')) {
            errorMessage = '请求超时'
          } else if (error.errMsg.includes('fail')) {
            errorMessage = '网络连接失败'
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none'
        })
        reject(new Error(errorMessage))
      }
    })
  })
}

// 便捷方法
export const get = <T = any>(url: string, params?: any, config?: Partial<RequestConfig>) => {
  return request<T>({
    url,
    method: 'GET',
    params,
    ...config
  })
}

export const post = <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) => {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...config
  })
}

export const put = <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) => {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...config
  })
}

export const del = <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) => {
  return request<T>({
    url,
    method: 'DELETE',
    data,
    ...config
  })
}
