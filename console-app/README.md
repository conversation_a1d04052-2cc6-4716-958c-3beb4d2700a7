# 群控系统控制台

一个基于 uniapp 开发的多端群控系统控制台应用，支持 H5、小程序、APP 等多个平台。

## 功能特性

### 🔐 用户系统
- 用户注册/登录
- JWT 令牌认证
- 自动令牌刷新
- 个人资料管理
- 密码修改

### 📱 设备管理
- 设备注册/删除
- 设备状态监控（在线/离线/忙碌）
- 设备分组管理
- 设备详情查看
- 实时状态更新

### 📝 脚本管理
- 脚本创建/编辑
- 脚本配置管理
- 脚本模板支持
- 代码格式化
- 脚本克隆功能

### 🎯 任务管理
- 任务创建/执行
- 任务进度监控
- 任务结果查看
- 批量设备任务
- 任务统计分析

### 📊 数据监控
- 实时数据仪表盘
- 设备状态统计
- 任务执行统计
- 系统运行状态

## 技术栈

- **框架**: Vue 3 + TypeScript + uniapp
- **状态管理**: Pinia
- **构建工具**: Vite
- **UI组件**: uni-ui
- **样式**: SCSS
- **网络请求**: uni.request 封装
- **多端支持**: H5、小程序、APP

## 项目结构

```
console-app/
├── api/                    # API 接口层
│   ├── user.ts            # 用户相关接口
│   ├── device.ts          # 设备相关接口
│   ├── script.ts          # 脚本相关接口
│   └── task.ts            # 任务相关接口
├── pages/                  # 页面文件
│   ├── login/             # 登录页面
│   ├── register/          # 注册页面
│   ├── dashboard/         # 仪表盘
│   ├── scripts/           # 脚本管理
│   ├── devices/           # 设备管理
│   ├── tasks/             # 任务管理
│   └── profile/           # 个人中心
├── stores/                 # Pinia 状态管理
│   ├── user.ts            # 用户状态
│   ├── device.ts          # 设备状态
│   ├── script.ts          # 脚本状态
│   └── task.ts            # 任务状态
├── types/                  # TypeScript 类型定义
│   ├── user.ts            # 用户类型
│   ├── device.ts          # 设备类型
│   ├── script.ts          # 脚本类型
│   └── task.ts            # 任务类型
├── utils/                  # 工具函数
│   ├── request.ts         # 网络请求封装
│   └── index.ts           # 通用工具函数
├── styles/                 # 全局样式
│   └── global.scss        # 全局样式文件
├── static/                 # 静态资源
├── App.vue                 # 主应用组件
├── main.ts                 # 应用入口
├── pages.json              # 页面配置
├── manifest.json           # 应用配置
└── package.json            # 依赖配置
```

## 开发指南

### 环境要求

- Node.js >= 16
- npm >= 8 或 yarn >= 1.22
- HBuilderX 或 VS Code

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发运行

```bash
# H5 开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# APP 开发
npm run dev:app
```

### 构建打包

```bash
# H5 构建
npm run build:h5

# 微信小程序构建
npm run build:mp-weixin

# APP 构建
npm run build:app
```

## 核心功能说明

### 1. 认证系统

应用采用 JWT 令牌认证，支持自动刷新令牌机制：

- 登录成功后获取 access_token 和 refresh_token
- 请求拦截器自动添加认证头
- 响应拦截器处理令牌过期，自动刷新
- 刷新失败时自动跳转登录页

### 2. 状态管理

使用 Pinia 进行状态管理，按功能模块划分：

- `useUserStore`: 用户信息、登录状态
- `useDeviceStore`: 设备列表、设备状态
- `useScriptStore`: 脚本管理、脚本配置
- `useTaskStore`: 任务管理、任务执行

### 3. 网络请求

封装了统一的请求工具，支持：

- 请求/响应拦截器
- 自动错误处理
- 令牌自动刷新
- 请求重试机制

### 4. 多端适配

项目支持多端运行：

- **H5**: 响应式设计，支持桌面和移动端
- **小程序**: 微信小程序原生体验
- **APP**: 原生应用性能

## API 接口

### 用户相关
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `GET /api/v1/user/profile` - 获取用户资料
- `PUT /api/v1/user/profile` - 更新用户资料

### 设备相关
- `POST /api/v1/user/devices/register` - 注册设备
- `GET /api/v1/user/devices/list` - 获取设备列表
- `PUT /api/v1/user/devices/status` - 更新设备状态
- `DELETE /api/v1/user/devices` - 删除设备

### 脚本相关
- `POST /api/v1/user/scripts` - 创建脚本
- `GET /api/v1/user/scripts/list` - 获取脚本列表
- `PUT /api/v1/user/scripts` - 更新脚本
- `DELETE /api/v1/user/scripts` - 删除脚本

### 任务相关
- `POST /api/v1/user/tasks` - 创建任务
- `GET /api/v1/user/tasks/list` - 获取任务列表
- `PUT /api/v1/user/tasks/start` - 启动任务
- `PUT /api/v1/user/tasks/cancel` - 取消任务

## 部署说明

### H5 部署

1. 构建项目：`npm run build:h5`
2. 将 `dist/build/h5` 目录部署到 Web 服务器

### 小程序部署

1. 构建项目：`npm run build:mp-weixin`
2. 使用微信开发者工具打开 `dist/build/mp-weixin` 目录
3. 上传代码到微信小程序后台

### APP 部署

1. 构建项目：`npm run build:app`
2. 使用 HBuilderX 打开项目
3. 发行为原生 APP

## 贡献指南

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱: <EMAIL>
- 项目地址: https://github.com/example/console-app
