<template>
  <view class="register-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <uni-icons type="back" size="20" color="#fff" />
        </view>
        <text class="navbar-title">注册账号</text>
        <view class="navbar-placeholder"></view>
      </view>
    </view>

    <!-- 注册表单 -->
    <view class="register-form">
      <!-- 头部信息 -->
      <view class="header-section">
        <text class="welcome-text">创建新账号</text>
        <text class="desc-text">加入群控系统，开始管理您的设备</text>
      </view>

      <!-- 表单 -->
      <view class="form-section">
        <view class="form-item">
          <view class="input-wrapper">
            <uni-icons type="person" size="20" color="#8f8f94" />
            <input
              v-model="formData.username"
              class="form-input"
              type="text"
              placeholder="请输入用户名（3-20个字符）"
              :disabled="loading"
              maxlength="20"
            />
          </view>
          <view v-if="errors.username" class="error-text">{{ errors.username }}</view>
        </view>

        <view class="form-item">
          <view class="input-wrapper">
            <uni-icons type="email" size="20" color="#8f8f94" />
            <input
              v-model="formData.email"
              class="form-input"
              type="text"
              placeholder="请输入邮箱（可选）"
              :disabled="loading"
            />
          </view>
          <view v-if="errors.email" class="error-text">{{ errors.email }}</view>
        </view>

        <view class="form-item">
          <view class="input-wrapper">
            <uni-icons type="locked" size="20" color="#8f8f94" />
            <input
              v-model="formData.password"
              class="form-input"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码（至少6个字符）"
              :disabled="loading"
            />
            <uni-icons
              :type="showPassword ? 'eye-slash' : 'eye'"
              size="20"
              color="#8f8f94"
              @click="showPassword = !showPassword"
            />
          </view>
          <view v-if="errors.password" class="error-text">{{ errors.password }}</view>
        </view>

        <view class="form-item">
          <view class="input-wrapper">
            <uni-icons type="locked" size="20" color="#8f8f94" />
            <input
              v-model="formData.confirmPassword"
              class="form-input"
              :type="showConfirmPassword ? 'text' : 'password'"
              placeholder="请确认密码"
              :disabled="loading"
              @confirm="handleRegister"
            />
            <uni-icons
              :type="showConfirmPassword ? 'eye-slash' : 'eye'"
              size="20"
              color="#8f8f94"
              @click="showConfirmPassword = !showConfirmPassword"
            />
          </view>
          <view v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</view>
        </view>

        <view class="form-item">
          <view class="agreement-wrapper">
            <checkbox-group @change="onAgreementChange">
              <checkbox :checked="agreed" color="#007aff" />
            </checkbox-group>
            <text class="agreement-text">
              我已阅读并同意
              <text class="link-text" @click="showAgreement">《用户协议》</text>
              和
              <text class="link-text" @click="showPrivacy">《隐私政策》</text>
            </text>
          </view>
        </view>

        <view class="form-item">
          <button
            class="register-btn"
            :class="{ 'btn-loading': loading }"
            :disabled="loading || !canSubmit"
            @click="handleRegister"
          >
            <uni-icons v-if="loading" type="spinner-cycle" size="20" color="#fff" class="loading-icon" />
            {{ loading ? '注册中...' : '注册' }}
          </button>
        </view>

        <view class="form-actions">
          <text class="link-text" @click="goToLogin">已有账号？立即登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useUserStore } from '@/stores/user'
import { validateEmail, showError, showSuccess } from '@/utils/index'

// 状态栏高度
const statusBarHeight = ref(0)

// 表单数据
const formData = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 错误信息
const errors = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 控制状态
const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const agreed = ref(false)

// 用户状态管理
const userStore = useUserStore()

// 计算属性
const canSubmit = computed(() => {
  return (
    formData.value.username.trim() &&
    formData.value.password.trim() &&
    formData.value.confirmPassword.trim() &&
    agreed.value &&
    !Object.values(errors).some(error => error)
  )
})

// 获取系统信息
onMounted(() => {
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight || 0
    }
  })
})

// 验证表单
const validateForm = () => {
  // 清空错误信息
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })

  let isValid = true

  // 验证用户名
  if (!formData.value.username.trim()) {
    errors.username = '请输入用户名'
    isValid = false
  } else if (formData.value.username.length < 3) {
    errors.username = '用户名至少3个字符'
    isValid = false
  } else if (formData.value.username.length > 20) {
    errors.username = '用户名最多20个字符'
    isValid = false
  } else if (!/^[a-zA-Z0-9_]+$/.test(formData.value.username)) {
    errors.username = '用户名只能包含字母、数字和下划线'
    isValid = false
  }

  // 验证邮箱（可选）
  if (formData.value.email && !validateEmail(formData.value.email)) {
    errors.email = '请输入正确的邮箱格式'
    isValid = false
  }

  // 验证密码
  if (!formData.value.password) {
    errors.password = '请输入密码'
    isValid = false
  } else if (formData.value.password.length < 6) {
    errors.password = '密码至少6个字符'
    isValid = false
  } else if (formData.value.password.length > 50) {
    errors.password = '密码最多50个字符'
    isValid = false
  }

  // 验证确认密码
  if (!formData.value.confirmPassword) {
    errors.confirmPassword = '请确认密码'
    isValid = false
  } else if (formData.value.password !== formData.value.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
    isValid = false
  }

  return isValid
}

// 处理注册
const handleRegister = async () => {
  if (!validateForm() || loading.value) return

  if (!agreed.value) {
    showError('请先同意用户协议和隐私政策')
    return
  }

  loading.value = true

  try {
    const success = await userStore.register(
      formData.value.username,
      formData.value.password,
      formData.value.email || undefined
    )
    
    if (success) {
      showSuccess('注册成功，请登录')
      
      // 延迟跳转到登录页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      showError('注册失败，请稍后重试')
    }
  } catch (error) {
    console.error('注册错误:', error)
    showError('注册失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 同意协议变化
const onAgreementChange = (e: any) => {
  agreed.value = e.detail.value.length > 0
}

// 显示用户协议
const showAgreement = () => {
  uni.showModal({
    title: '用户协议',
    content: '这里是用户协议的内容...',
    showCancel: false
  })
}

// 显示隐私政策
const showPrivacy = () => {
  uni.showModal({
    title: '隐私政策',
    content: '这里是隐私政策的内容...',
    showCancel: false
  })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 跳转到登录页面
const goToLogin = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-back,
.navbar-placeholder {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.register-form {
  padding: 60rpx 60rpx 120rpx;
  margin-top: 88rpx;
}

.header-section {
  text-align: center;
  margin-bottom: 80rpx;
  margin-top: 60rpx;
}

.welcome-text {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.desc-text {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.form-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
}

.form-item {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 0 30rpx;
  height: 96rpx;
}

.form-input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  margin-left: 20rpx;
  margin-right: 20rpx;
}

.error-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #ff6b6b;
  padding-left: 30rpx;
}

.agreement-wrapper {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
}

.agreement-text {
  flex: 1;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  margin-left: 20rpx;
}

.link-text {
  color: #fff;
  text-decoration: underline;
}

.register-btn {
  width: 100%;
  height: 96rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &:not(:disabled):active {
    background: #0056cc;
    transform: scale(0.98);
  }

  &:disabled {
    background: #ccc;
    color: #999;
  }

  &.btn-loading {
    background: #0056cc;
  }
}

.loading-icon {
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.form-actions {
  text-align: center;
  margin-top: 40rpx;
}
</style>
