<template>
  <view class="profile-page">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-header">
        <view class="user-avatar">
          <image 
            v-if="userInfo?.avatar" 
            :src="userInfo.avatar" 
            class="avatar-image"
            mode="aspectFill"
          />
          <view v-else class="avatar-placeholder">
            <uni-icons type="person" size="40" color="#fff" />
          </view>
        </view>
        <view class="user-info">
          <text class="user-name">{{ userInfo?.nickname || userInfo?.username }}</text>
          <text class="user-email">{{ userInfo?.email || '未设置邮箱' }}</text>
        </view>
        <view class="user-status">
          <view class="status-dot active"></view>
          <text class="status-text">在线</text>
        </view>
      </view>
      
      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-value">{{ userStats.devices }}</text>
          <text class="stat-label">设备</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ userStats.scripts }}</text>
          <text class="stat-label">脚本</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ userStats.tasks }}</text>
          <text class="stat-label">任务</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ userStats.onlineTime }}</text>
          <text class="stat-label">在线时长</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-sections">
      <!-- 账户管理 -->
      <view class="menu-section">
        <view class="section-header">
          <text class="section-title">账户管理</text>
        </view>
        <view class="menu-list">
          <view class="menu-item" @click="editProfile">
            <view class="menu-icon">
              <uni-icons type="person" size="20" color="#007aff" />
            </view>
            <text class="menu-text">个人资料</text>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#8f8f94" />
            </view>
          </view>
          
          <view class="menu-item" @click="changePassword">
            <view class="menu-icon">
              <uni-icons type="locked" size="20" color="#f0ad4e" />
            </view>
            <text class="menu-text">修改密码</text>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#8f8f94" />
            </view>
          </view>
          
          <view class="menu-item" @click="accountSecurity">
            <view class="menu-icon">
              <uni-icons type="gear" size="20" color="#4cd964" />
            </view>
            <text class="menu-text">账户安全</text>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#8f8f94" />
            </view>
          </view>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="menu-section">
        <view class="section-header">
          <text class="section-title">应用设置</text>
        </view>
        <view class="menu-list">
          <view class="menu-item" @click="notificationSettings">
            <view class="menu-icon">
              <uni-icons type="sound" size="20" color="#dd524d" />
            </view>
            <text class="menu-text">通知设置</text>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#8f8f94" />
            </view>
          </view>
          
          <view class="menu-item" @click="themeSettings">
            <view class="menu-icon">
              <uni-icons type="color" size="20" color="#8e44ad" />
            </view>
            <text class="menu-text">主题设置</text>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#8f8f94" />
            </view>
          </view>
          
          <view class="menu-item" @click="languageSettings">
            <view class="menu-icon">
              <uni-icons type="chatboxes" size="20" color="#2ecc71" />
            </view>
            <text class="menu-text">语言设置</text>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#8f8f94" />
            </view>
          </view>
        </view>
      </view>

      <!-- 帮助与支持 -->
      <view class="menu-section">
        <view class="section-header">
          <text class="section-title">帮助与支持</text>
        </view>
        <view class="menu-list">
          <view class="menu-item" @click="helpCenter">
            <view class="menu-icon">
              <uni-icons type="help" size="20" color="#3498db" />
            </view>
            <text class="menu-text">帮助中心</text>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#8f8f94" />
            </view>
          </view>
          
          <view class="menu-item" @click="feedback">
            <view class="menu-icon">
              <uni-icons type="email" size="20" color="#e67e22" />
            </view>
            <text class="menu-text">意见反馈</text>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#8f8f94" />
            </view>
          </view>
          
          <view class="menu-item" @click="aboutApp">
            <view class="menu-icon">
              <uni-icons type="info" size="20" color="#95a5a6" />
            </view>
            <text class="menu-text">关于应用</text>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#8f8f94" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="logout">
        <uni-icons type="loop" size="20" color="#dd524d" />
        <text>退出登录</text>
      </button>
    </view>

    <!-- 个人资料编辑弹窗 -->
    <uni-popup ref="profilePopup" type="center">
      <view class="profile-dialog">
        <view class="dialog-header">
          <text class="dialog-title">编辑个人资料</text>
          <view class="dialog-close" @click="hideProfileDialog">
            <uni-icons type="clear" size="20" color="#8f8f94" />
          </view>
        </view>
        <view class="dialog-content">
          <view class="form-item">
            <text class="form-label">昵称</text>
            <input
              v-model="profileForm.nickname"
              class="form-input"
              type="text"
              placeholder="请输入昵称"
              maxlength="20"
            />
          </view>
          <view class="form-item">
            <text class="form-label">邮箱</text>
            <input
              v-model="profileForm.email"
              class="form-input"
              type="text"
              placeholder="请输入邮箱"
            />
          </view>
        </view>
        <view class="dialog-actions">
          <button class="dialog-btn cancel" @click="hideProfileDialog">取消</button>
          <button class="dialog-btn confirm" @click="saveProfile">保存</button>
        </view>
      </view>
    </uni-popup>

    <!-- 修改密码弹窗 -->
    <uni-popup ref="passwordPopup" type="center">
      <view class="password-dialog">
        <view class="dialog-header">
          <text class="dialog-title">修改密码</text>
          <view class="dialog-close" @click="hidePasswordDialog">
            <uni-icons type="clear" size="20" color="#8f8f94" />
          </view>
        </view>
        <view class="dialog-content">
          <view class="form-item">
            <text class="form-label">当前密码</text>
            <input
              v-model="passwordForm.oldPassword"
              class="form-input"
              type="password"
              placeholder="请输入当前密码"
            />
          </view>
          <view class="form-item">
            <text class="form-label">新密码</text>
            <input
              v-model="passwordForm.newPassword"
              class="form-input"
              type="password"
              placeholder="请输入新密码"
            />
          </view>
          <view class="form-item">
            <text class="form-label">确认密码</text>
            <input
              v-model="passwordForm.confirmPassword"
              class="form-input"
              type="password"
              placeholder="请再次输入新密码"
            />
          </view>
        </view>
        <view class="dialog-actions">
          <button class="dialog-btn cancel" @click="hidePasswordDialog">取消</button>
          <button class="dialog-btn confirm" @click="savePassword">保存</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useDeviceStore } from '@/stores/device'
import { useScriptStore } from '@/stores/script'
import { useTaskStore } from '@/stores/task'
import { validateEmail, showError, showSuccess, showConfirm } from '@/utils/index'

// 状态管理
const userStore = useUserStore()
const deviceStore = useDeviceStore()
const scriptStore = useScriptStore()
const taskStore = useTaskStore()

// 弹窗引用
const profilePopup = ref()
const passwordPopup = ref()

// 表单数据
const profileForm = ref({
  nickname: '',
  email: ''
})

const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 计算属性
const userInfo = computed(() => userStore.userInfo)

const userStats = computed(() => ({
  devices: deviceStore.deviceStats.total,
  scripts: scriptStore.scriptStats.total,
  tasks: taskStore.taskStats.total,
  onlineTime: '24小时' // 这里应该从API获取真实数据
}))

// 页面加载
onMounted(() => {
  loadUserStats()
})

// 加载用户统计数据
const loadUserStats = async () => {
  try {
    await Promise.all([
      deviceStore.getUserDevices(),
      scriptStore.getUserScripts(),
      taskStore.getUserTasks()
    ])
  } catch (error) {
    console.error('加载用户统计失败:', error)
  }
}

// 菜单操作
const editProfile = () => {
  profileForm.value = {
    nickname: userInfo.value?.nickname || '',
    email: userInfo.value?.email || ''
  }
  profilePopup.value?.open()
}

const changePassword = () => {
  passwordForm.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
  passwordPopup.value?.open()
}

const accountSecurity = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

const notificationSettings = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

const themeSettings = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

const languageSettings = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

const helpCenter = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

const feedback = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

const aboutApp = () => {
  uni.showModal({
    title: '关于应用',
    content: '群控系统控制台 v1.0.0\n\n一个强大的多端设备管理平台',
    showCancel: false
  })
}

// 弹窗操作
const hideProfileDialog = () => {
  profilePopup.value?.close()
}

const hidePasswordDialog = () => {
  passwordPopup.value?.close()
}

const saveProfile = async () => {
  // 验证邮箱格式
  if (profileForm.value.email && !validateEmail(profileForm.value.email)) {
    showError('请输入正确的邮箱格式')
    return
  }

  try {
    const success = await userStore.updateProfile({
      nickname: profileForm.value.nickname,
      email: profileForm.value.email
    })

    if (success) {
      showSuccess('个人资料更新成功')
      hideProfileDialog()
    }
  } catch (error) {
    showError('更新个人资料失败')
  }
}

const savePassword = async () => {
  // 验证表单
  if (!passwordForm.value.oldPassword) {
    showError('请输入当前密码')
    return
  }

  if (!passwordForm.value.newPassword) {
    showError('请输入新密码')
    return
  }

  if (passwordForm.value.newPassword.length < 6) {
    showError('新密码至少6个字符')
    return
  }

  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    showError('两次输入的密码不一致')
    return
  }

  try {
    const success = await userStore.changePassword(
      passwordForm.value.oldPassword,
      passwordForm.value.newPassword
    )

    if (success) {
      showSuccess('密码修改成功')
      hidePasswordDialog()
    }
  } catch (error) {
    showError('修改密码失败')
  }
}

// 退出登录
const logout = async () => {
  try {
    const confirmed = await showConfirm('确定要退出登录吗？')
    if (!confirmed) return

    await userStore.logout()
  } catch (error) {
    showError('退出登录失败')
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  color: #fff;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-email {
  font-size: 26rpx;
  opacity: 0.8;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  
  &.active {
    background: #4cd964;
  }
}

.status-text {
  font-size: 24rpx;
  opacity: 0.8;
}

.user-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.menu-sections {
  margin-bottom: 30rpx;
}

.menu-section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.menu-list {
  padding: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8f9fa;
  }
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(0, 122, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  opacity: 0.5;
}

.logout-section {
  padding: 0 20rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #fff;
  border: 2rpx solid #dd524d;
  border-radius: 16rpx;
  color: #dd524d;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  transition: all 0.3s;

  &:active {
    background: #dd524d;
    color: #fff;
  }
}

.profile-dialog,
.password-dialog {
  background: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-width: 90vw;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.dialog-close {
  padding: 10rpx;
}

.dialog-content {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
}

.dialog-actions {
  display: flex;
  border-top: 2rpx solid #f5f5f5;
}

.dialog-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  font-size: 30rpx;
  background: #fff;

  &.cancel {
    color: #8f8f94;
    border-right: 2rpx solid #f5f5f5;
  }

  &.confirm {
    color: #007aff;
  }

  &:active {
    background: #f5f5f5;
  }
}
</style>
