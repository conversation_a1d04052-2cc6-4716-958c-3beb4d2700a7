<template>
  <view class="script-edit">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <uni-icons type="back" size="20" color="#333" />
        </view>
        <text class="navbar-title">{{ isEdit ? '编辑脚本' : '新建脚本' }}</text>
        <view class="navbar-action" @click="saveScript">
          <text class="save-text" :class="{ disabled: !canSave }">保存</text>
        </view>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-container">
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">脚本名称 *</text>
          <input
            v-model="formData.name"
            class="form-input"
            type="text"
            placeholder="请输入脚本名称"
            maxlength="100"
          />
        </view>

        <view class="form-item">
          <text class="form-label">脚本类型</text>
          <picker
            :value="typeIndex"
            :range="typeOptions"
            range-key="label"
            @change="onTypeChange"
          >
            <view class="picker-input">
              <text>{{ typeOptions[typeIndex].label }}</text>
              <uni-icons type="down" size="14" color="#8f8f94" />
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="form-label">脚本描述</text>
          <textarea
            v-model="formData.description"
            class="form-textarea"
            placeholder="请输入脚本描述（可选）"
            maxlength="500"
            :show-count="true"
          />
        </view>
      </view>

      <view class="form-section">
        <view class="section-header">
          <text class="section-title">脚本内容</text>
          <view class="section-actions">
            <view class="action-btn" @click="showTemplates">
              <uni-icons type="document" size="16" color="#007aff" />
              <text>模板</text>
            </view>
            <view class="action-btn" @click="formatCode">
              <uni-icons type="gear" size="16" color="#007aff" />
              <text>格式化</text>
            </view>
          </view>
        </view>
        <view class="code-editor">
          <textarea
            v-model="formData.content"
            class="code-textarea"
            placeholder="请输入脚本代码..."
            :auto-height="true"
            :show-count="false"
          />
        </view>
      </view>

      <!-- 快速插入工具栏 -->
      <view class="toolbar">
        <scroll-view class="toolbar-scroll" scroll-x>
          <view class="toolbar-items">
            <view 
              v-for="tool in codeTools" 
              :key="tool.key"
              class="toolbar-item"
              @click="insertCode(tool)"
            >
              <text>{{ tool.label }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 模板选择弹窗 -->
    <uni-popup ref="templatePopup" type="bottom">
      <view class="template-sheet">
        <view class="template-header">
          <text class="template-title">选择模板</text>
          <view class="template-close" @click="hideTemplates">
            <uni-icons type="clear" size="20" color="#8f8f94" />
          </view>
        </view>
        <view class="template-list">
          <view 
            v-for="template in templates" 
            :key="template.id"
            class="template-item"
            @click="useTemplate(template)"
          >
            <view class="template-info">
              <text class="template-name">{{ template.name }}</text>
              <text class="template-desc">{{ template.description }}</text>
            </view>
            <uni-icons type="right" size="16" color="#8f8f94" />
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useScriptStore } from '@/stores/script'
import { showError, showSuccess, showConfirm } from '@/utils/index'

// 状态栏高度
const statusBarHeight = ref(0)

// 路由参数
const scriptId = ref<number | null>(null)
const isEdit = computed(() => !!scriptId.value)

// 状态管理
const scriptStore = useScriptStore()

// 表单数据
const formData = ref({
  name: '',
  description: '',
  content: '',
  type: 'autox'
})

// 类型选项
const typeIndex = ref(0)
const typeOptions = [
  { label: 'AutoX', value: 'autox' },
  { label: 'Hamibot', value: 'hamibot' },
  { label: '其他', value: 'other' }
]

// 代码工具
const codeTools = [
  { key: 'log', label: 'log', code: 'console.log();' },
  { key: 'click', label: 'click', code: 'click();' },
  { key: 'sleep', label: 'sleep', code: 'sleep(1000);' },
  { key: 'toast', label: 'toast', code: 'toast("");' },
  { key: 'if', label: 'if', code: 'if () {\n    \n}' },
  { key: 'for', label: 'for', code: 'for (let i = 0; i < 10; i++) {\n    \n}' },
  { key: 'function', label: 'function', code: 'function name() {\n    \n}' }
]

// 模板数据
const templates = [
  {
    id: 1,
    name: 'AutoX 基础模板',
    description: '包含基本的 AutoX 脚本结构',
    content: `// AutoX 基础脚本模板
auto.waitFor();

console.log("脚本开始执行");

// 在这里编写你的脚本逻辑

console.log("脚本执行完成");`
  },
  {
    id: 2,
    name: 'Hamibot 基础模板',
    description: '包含基本的 Hamibot 脚本结构',
    content: `// Hamibot 基础脚本模板
console.show();

console.log("脚本开始执行");

// 在这里编写你的脚本逻辑

console.log("脚本执行完成");`
  },
  {
    id: 3,
    name: '点击操作模板',
    description: '包含常用的点击操作示例',
    content: `// 点击操作模板
auto.waitFor();

// 等待元素出现并点击
let element = text("按钮").findOne(5000);
if (element) {
    element.click();
    console.log("点击成功");
} else {
    console.log("未找到元素");
}

// 坐标点击
click(500, 1000);
sleep(1000);`
  }
]

// 弹窗引用
const templatePopup = ref()

// 计算属性
const canSave = computed(() => {
  return formData.value.name.trim() && formData.value.content.trim()
})

// 获取系统信息
onMounted(() => {
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight || 0
    }
  })

  // 获取路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options as any
  
  if (options.id) {
    scriptId.value = parseInt(options.id)
    loadScript()
  }
})

// 加载脚本数据
const loadScript = async () => {
  if (!scriptId.value) return

  try {
    const script = await scriptStore.getScriptById(scriptId.value)
    if (script) {
      formData.value = {
        name: script.name,
        description: script.description || '',
        content: script.content,
        type: script.type
      }

      // 设置类型索引
      const typeIdx = typeOptions.findIndex(opt => opt.value === script.type)
      if (typeIdx > -1) {
        typeIndex.value = typeIdx
      }
    }
  } catch (error) {
    showError('加载脚本失败')
  }
}

// 类型变化
const onTypeChange = (e: any) => {
  typeIndex.value = e.detail.value
  formData.value.type = typeOptions[typeIndex.value].value
}

// 保存脚本
const saveScript = async () => {
  if (!canSave.value) {
    showError('请填写必要信息')
    return
  }

  try {
    if (isEdit.value && scriptId.value) {
      // 更新脚本
      const success = await scriptStore.updateScript({
        id: scriptId.value,
        name: formData.value.name,
        description: formData.value.description,
        content: formData.value.content
      })

      if (success) {
        showSuccess('脚本更新成功')
        setTimeout(() => {
          uni.navigateBack()
        }, 1000)
      }
    } else {
      // 创建脚本
      const success = await scriptStore.createScript({
        name: formData.value.name,
        description: formData.value.description,
        content: formData.value.content,
        type: formData.value.type
      })

      if (success) {
        showSuccess('脚本创建成功')
        setTimeout(() => {
          uni.navigateBack()
        }, 1000)
      }
    }
  } catch (error) {
    showError(isEdit.value ? '更新脚本失败' : '创建脚本失败')
  }
}

// 插入代码
const insertCode = (tool: any) => {
  const textarea = formData.value.content
  const cursorPos = textarea.length // 简化处理，插入到末尾
  
  formData.value.content = textarea.slice(0, cursorPos) + tool.code + textarea.slice(cursorPos)
}

// 格式化代码
const formatCode = () => {
  // 简单的代码格式化
  let code = formData.value.content
  
  // 基本的缩进处理
  const lines = code.split('\n')
  let indentLevel = 0
  const formattedLines = lines.map(line => {
    const trimmed = line.trim()
    
    if (trimmed.includes('}')) {
      indentLevel = Math.max(0, indentLevel - 1)
    }
    
    const formatted = '    '.repeat(indentLevel) + trimmed
    
    if (trimmed.includes('{')) {
      indentLevel++
    }
    
    return formatted
  })
  
  formData.value.content = formattedLines.join('\n')
  showSuccess('代码格式化完成')
}

// 显示模板
const showTemplates = () => {
  templatePopup.value?.open()
}

// 隐藏模板
const hideTemplates = () => {
  templatePopup.value?.close()
}

// 使用模板
const useTemplate = async (template: any) => {
  hideTemplates()
  
  if (formData.value.content.trim()) {
    const confirmed = await showConfirm('使用模板将覆盖当前内容，确定继续吗？')
    if (!confirmed) return
  }
  
  formData.value.content = template.content
  showSuccess('模板应用成功')
}

// 返回
const goBack = async () => {
  // 检查是否有未保存的更改
  if (formData.value.name || formData.value.content) {
    const confirmed = await showConfirm('有未保存的更改，确定要离开吗？')
    if (!confirmed) return
  }
  
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.script-edit {
  background: #f5f5f5;
  min-height: 100vh;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border-bottom: 2rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-back,
.navbar-action {
  width: 120rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.save-text {
  font-size: 32rpx;
  color: #007aff;
  
  &.disabled {
    color: #ccc;
  }
}

.form-container {
  padding: 20rpx;
  margin-top: 88rpx;
  padding-bottom: 120rpx;
}

.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.form-item {
  margin-bottom: 30rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  line-height: 1.5;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.section-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 15rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #007aff;
}

.code-editor {
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  background: #fff;
}

.code-textarea {
  width: 100%;
  min-height: 400rpx;
  padding: 20rpx;
  font-size: 26rpx;
  font-family: 'Courier New', monospace;
  line-height: 1.6;
  background: #fff;
  border: none;
}

.toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 2rpx solid #e5e5e5;
  padding: 20rpx 0;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.toolbar-scroll {
  height: 60rpx;
}

.toolbar-items {
  display: flex;
  padding: 0 20rpx;
  gap: 20rpx;
}

.toolbar-item {
  flex-shrink: 0;
  padding: 15rpx 25rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #333;
  
  &:active {
    background: #e5e5e5;
  }
}

.template-sheet {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  padding-bottom: env(safe-area-inset-bottom);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.template-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.template-close {
  padding: 10rpx;
}

.template-list {
  max-height: 60vh;
  overflow-y: auto;
}

.template-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
  
  &:active {
    background: #f5f5f5;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.template-info {
  flex: 1;
}

.template-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.template-desc {
  font-size: 24rpx;
  color: #8f8f94;
}
</style>
