<template>
  <view class="scripts-page">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <uni-icons type="search" size="18" color="#8f8f94" />
        <input
          v-model="searchKeyword"
          class="search-input"
          type="text"
          placeholder="搜索脚本名称或描述"
          @input="onSearchInput"
        />
        <view v-if="searchKeyword" class="search-clear" @click="clearSearch">
          <uni-icons type="clear" size="16" color="#8f8f94" />
        </view>
      </view>
      <view class="filter-bar">
        <picker
          :value="typeFilterIndex"
          :range="typeOptions"
          range-key="label"
          @change="onTypeFilterChange"
        >
          <view class="filter-item">
            <text>{{ typeOptions[typeFilterIndex].label }}</text>
            <uni-icons type="down" size="14" color="#8f8f94" />
          </view>
        </picker>
        <picker
          :value="statusFilterIndex"
          :range="statusOptions"
          range-key="label"
          @change="onStatusFilterChange"
        >
          <view class="filter-item">
            <text>{{ statusOptions[statusFilterIndex].label }}</text>
            <uni-icons type="down" size="14" color="#8f8f94" />
          </view>
        </picker>
      </view>
    </view>

    <!-- 脚本列表 -->
    <view class="script-list">
      <view 
        v-for="script in scripts" 
        :key="script.id"
        class="script-item"
        @click="goToScriptDetail(script)"
      >
        <view class="script-header">
          <view class="script-info">
            <text class="script-name">{{ script.name }}</text>
            <view class="script-meta">
              <view class="meta-item">
                <view 
                  class="type-tag" 
                  :class="getTypeClass(script.type)"
                >
                  {{ script.type }}
                </view>
              </view>
              <view class="meta-item">
                <view 
                  class="status-tag" 
                  :class="script.status === 1 ? 'active' : 'inactive'"
                >
                  {{ script.status === 1 ? '启用' : '禁用' }}
                </view>
              </view>
            </view>
          </view>
          <view class="script-actions" @click.stop>
            <uni-icons 
              type="more-filled" 
              size="20" 
              color="#8f8f94"
              @click="showActionSheet(script)"
            />
          </view>
        </view>
        
        <view v-if="script.description" class="script-desc">
          {{ script.description }}
        </view>
        
        <view class="script-footer">
          <text class="script-time">{{ formatRelativeTime(script.updated_at) }}</text>
          <text class="script-owner">{{ script.owner?.username || '未知' }}</text>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more" @click="loadMore">
        <uni-icons v-if="loadingMore" type="spinner-cycle" size="20" color="#8f8f94" class="loading-icon" />
        <text>{{ loadingMore ? '加载中...' : '加载更多' }}</text>
      </view>

      <!-- 空状态 -->
      <view v-if="scripts.length === 0 && !loading" class="empty-state">
        <uni-icons type="document" size="60" color="#ccc" />
        <text class="empty-text">暂无脚本</text>
        <button class="empty-btn" @click="goToCreateScript">创建第一个脚本</button>
      </view>
    </view>

    <!-- 浮动按钮 -->
    <view class="fab" @click="goToCreateScript">
      <uni-icons type="plus" size="24" color="#fff" />
    </view>

    <!-- 操作菜单 -->
    <uni-popup ref="actionPopup" type="bottom">
      <view class="action-sheet">
        <view class="action-header">
          <text class="action-title">{{ selectedScript?.name }}</text>
        </view>
        <view class="action-list">
          <view class="action-item" @click="editScript">
            <uni-icons type="compose" size="20" color="#007aff" />
            <text>编辑脚本</text>
          </view>
          <view class="action-item" @click="configScript">
            <uni-icons type="gear" size="20" color="#f0ad4e" />
            <text>脚本配置</text>
          </view>
          <view class="action-item" @click="cloneScript">
            <uni-icons type="loop" size="20" color="#4cd964" />
            <text>克隆脚本</text>
          </view>
          <view class="action-item" @click="toggleScriptStatus">
            <uni-icons 
              :type="selectedScript?.status === 1 ? 'eye-slash' : 'eye'" 
              size="20" 
              :color="selectedScript?.status === 1 ? '#f0ad4e' : '#4cd964'" 
            />
            <text>{{ selectedScript?.status === 1 ? '禁用' : '启用' }}</text>
          </view>
          <view class="action-item danger" @click="deleteScript">
            <uni-icons type="trash" size="20" color="#dd524d" />
            <text>删除脚本</text>
          </view>
        </view>
        <view class="action-cancel" @click="hideActionSheet">
          <text>取消</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onPullDownRefresh, onReachBottom } from 'vue'
import { useScriptStore } from '@/stores/script'
import { 
  formatRelativeTime, 
  debounce, 
  showError, 
  showSuccess, 
  showConfirm 
} from '@/utils/index'

// 状态管理
const scriptStore = useScriptStore()

// 响应式数据
const scripts = ref<any[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索和筛选
const searchKeyword = ref('')
const typeFilterIndex = ref(0)
const statusFilterIndex = ref(0)

const typeOptions = [
  { label: '全部类型', value: '' },
  { label: 'AutoX', value: 'autox' },
  { label: 'Hamibot', value: 'hamibot' },
  { label: '其他', value: 'other' }
]

const statusOptions = [
  { label: '全部状态', value: '' },
  { label: '启用', value: 1 },
  { label: '禁用', value: 2 }
]

// 操作相关
const selectedScript = ref<any>(null)
const actionPopup = ref()

// 获取类型样式类
const getTypeClass = (type: string) => {
  const classMap: Record<string, string> = {
    'autox': 'type-autox',
    'hamibot': 'type-hamibot',
    'other': 'type-other'
  }
  return classMap[type] || 'type-other'
}

// 加载脚本列表
const loadScripts = async (refresh = false) => {
  if (refresh) {
    currentPage.value = 1
    hasMore.value = true
    scripts.value = []
  }

  if (loading.value || loadingMore.value || !hasMore.value) return

  try {
    if (refresh) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      keyword: searchKeyword.value,
      type: typeOptions[typeFilterIndex.value].value,
      status: statusOptions[statusFilterIndex.value].value
    }

    const success = await scriptStore.getScriptList(params)
    
    if (success) {
      const newScripts = scriptStore.scripts
      if (refresh) {
        scripts.value = newScripts
      } else {
        scripts.value.push(...newScripts)
      }

      hasMore.value = newScripts.length === pageSize.value
      if (hasMore.value) {
        currentPage.value++
      }
    }
  } catch (error) {
    showError('加载脚本列表失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 搜索输入处理
const onSearchInput = debounce(() => {
  loadScripts(true)
}, 500)

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  loadScripts(true)
}

// 类型筛选变化
const onTypeFilterChange = (e: any) => {
  typeFilterIndex.value = e.detail.value
  loadScripts(true)
}

// 状态筛选变化
const onStatusFilterChange = (e: any) => {
  statusFilterIndex.value = e.detail.value
  loadScripts(true)
}

// 加载更多
const loadMore = () => {
  loadScripts(false)
}

// 显示操作菜单
const showActionSheet = (script: any) => {
  selectedScript.value = script
  actionPopup.value?.open()
}

// 隐藏操作菜单
const hideActionSheet = () => {
  actionPopup.value?.close()
  selectedScript.value = null
}

// 页面跳转
const goToScriptDetail = (script: any) => {
  uni.navigateTo({
    url: `/pages/scripts/edit?id=${script.id}`
  })
}

const goToCreateScript = () => {
  uni.navigateTo({
    url: '/pages/scripts/edit'
  })
}

// 操作方法
const editScript = () => {
  hideActionSheet()
  uni.navigateTo({
    url: `/pages/scripts/edit?id=${selectedScript.value.id}`
  })
}

const configScript = () => {
  hideActionSheet()
  uni.navigateTo({
    url: `/pages/scripts/config?id=${selectedScript.value.id}`
  })
}

const cloneScript = async () => {
  hideActionSheet()
  
  try {
    const success = await scriptStore.cloneScript(
      selectedScript.value.id,
      `${selectedScript.value.name}_副本`
    )
    
    if (success) {
      showSuccess('脚本克隆成功')
      loadScripts(true)
    }
  } catch (error) {
    showError('克隆脚本失败')
  }
}

const toggleScriptStatus = async () => {
  hideActionSheet()
  
  const newStatus = selectedScript.value.status === 1 ? 2 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  try {
    const confirmed = await showConfirm(`确定要${action}该脚本吗？`)
    if (!confirmed) return

    const success = await scriptStore.updateScriptStatus(selectedScript.value.id, newStatus)
    
    if (success) {
      showSuccess(`${action}脚本成功`)
      loadScripts(true)
    }
  } catch (error) {
    showError(`${action}脚本失败`)
  }
}

const deleteScript = async () => {
  hideActionSheet()
  
  try {
    const confirmed = await showConfirm('确定要删除该脚本吗？此操作不可恢复。')
    if (!confirmed) return

    const success = await scriptStore.deleteScript(selectedScript.value.id)
    
    if (success) {
      showSuccess('脚本删除成功')
      loadScripts(true)
    }
  } catch (error) {
    showError('删除脚本失败')
  }
}

// 下拉刷新
onPullDownRefresh(async () => {
  await loadScripts(true)
  uni.stopPullDownRefresh()
})

// 上拉加载
onReachBottom(() => {
  loadMore()
})

// 页面加载
onMounted(() => {
  loadScripts(true)
})
</script>

<style lang="scss" scoped>
.scripts-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.search-section {
  background: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 0 30rpx;
  height: 70rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  margin-left: 20rpx;
}

.search-clear {
  padding: 10rpx;
}

.filter-bar {
  display: flex;
  gap: 20rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  color: #333;
}

.script-list {
  padding: 0 20rpx;
}

.script-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
  }
}

.script-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.script-info {
  flex: 1;
}

.script-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.script-meta {
  display: flex;
  gap: 15rpx;
}

.meta-item {
  display: flex;
  align-items: center;
}

.type-tag,
.status-tag {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
}

.type-tag {
  &.type-autox {
    background: #007aff;
  }

  &.type-hamibot {
    background: #4cd964;
  }

  &.type-other {
    background: #8f8f94;
  }
}

.status-tag {
  &.active {
    background: #4cd964;
  }

  &.inactive {
    background: #dd524d;
  }
}

.script-actions {
  padding: 10rpx;
}

.script-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.script-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #8f8f94;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  font-size: 28rpx;
  color: #8f8f94;
}

.loading-icon {
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-text {
  display: block;
  margin: 30rpx 0;
  font-size: 28rpx;
  color: #8f8f94;
}

.empty-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.fab {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  background: #007aff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.3);
  z-index: 100;

  &:active {
    transform: scale(0.95);
  }
}

.action-sheet {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
}

.action-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #f5f5f5;
}

.action-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.action-list {
  padding: 20rpx 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  font-size: 30rpx;
  color: #333;

  &:active {
    background: #f5f5f5;
  }

  &.danger {
    color: #dd524d;
  }

  uni-icons {
    margin-right: 20rpx;
  }
}

.action-cancel {
  padding: 25rpx 30rpx;
  text-align: center;
  font-size: 30rpx;
  color: #8f8f94;
  border-top: 2rpx solid #f5f5f5;

  &:active {
    background: #f5f5f5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
