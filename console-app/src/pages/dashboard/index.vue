<template>
  <view class="dashboard">
    <!-- 顶部统计卡片 -->
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stat-card" @click="goToDevices">
          <view class="stat-icon online">
            <uni-icons type="monitor" size="24" color="#4cd964" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ dashboardData.devices?.stats?.online || 0 }}</text>
            <text class="stat-label">在线设备</text>
          </view>
        </view>

        <view class="stat-card" @click="goToTasks">
          <view class="stat-icon running">
            <uni-icons type="gear" size="24" color="#007aff" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ dashboardData.tasks?.stats?.running || 0 }}</text>
            <text class="stat-label">运行任务</text>
          </view>
        </view>

        <view class="stat-card" @click="goToScripts">
          <view class="stat-icon scripts">
            <uni-icons type="document" size="24" color="#f0ad4e" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ dashboardData.scripts?.total || 0 }}</text>
            <text class="stat-label">脚本数量</text>
          </view>
        </view>

        <view class="stat-card">
          <view class="stat-icon completed">
            <uni-icons type="checkmarkempty" size="24" color="#4cd964" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ dashboardData.tasks?.stats?.completed || 0 }}</text>
            <text class="stat-label">完成任务</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快速操作 -->
    <view class="quick-actions">
      <view class="section-header">
        <text class="section-title">快速操作</text>
      </view>
      <view class="action-grid">
        <view class="action-item" @click="goToCreateTask">
          <view class="action-icon">
            <uni-icons type="plus" size="20" color="#007aff" />
          </view>
          <text class="action-text">创建任务</text>
        </view>
        <view class="action-item" @click="goToCreateScript">
          <view class="action-icon">
            <uni-icons type="compose" size="20" color="#4cd964" />
          </view>
          <text class="action-text">新建脚本</text>
        </view>
        <view class="action-item" @click="goToDeviceGroups">
          <view class="action-icon">
            <uni-icons type="list" size="20" color="#f0ad4e" />
          </view>
          <text class="action-text">设备分组</text>
        </view>
        <view class="action-item" @click="goToMonitor">
          <view class="action-icon">
            <uni-icons type="eye" size="20" color="#dd524d" />
          </view>
          <text class="action-text">实时监控</text>
        </view>
      </view>
    </view>

    <!-- 设备状态 -->
    <view class="device-section">
      <view class="section-header">
        <text class="section-title">设备状态</text>
        <text class="section-more" @click="goToDevices">查看全部</text>
      </view>
      <view class="device-list">
        <view 
          v-for="device in recentDevices" 
          :key="device.id"
          class="device-item"
          @click="goToDeviceDetail(device)"
        >
          <view class="device-info">
            <view class="device-name">{{ device.name }}</view>
            <view class="device-id">{{ device.device_id }}</view>
          </view>
          <view class="device-status">
            <view 
              class="status-dot" 
              :class="getStatusClass(device.status)"
            ></view>
            <text class="status-text">{{ getDeviceStatusText(device.status) }}</text>
          </view>
        </view>
        <view v-if="recentDevices.length === 0" class="empty-state">
          <uni-icons type="info" size="40" color="#ccc" />
          <text class="empty-text">暂无设备数据</text>
        </view>
      </view>
    </view>

    <!-- 最近任务 -->
    <view class="task-section">
      <view class="section-header">
        <text class="section-title">最近任务</text>
        <text class="section-more" @click="goToTasks">查看全部</text>
      </view>
      <view class="task-list">
        <view 
          v-for="task in recentTasks" 
          :key="task.id"
          class="task-item"
          @click="goToTaskDetail(task)"
        >
          <view class="task-info">
            <view class="task-name">{{ task.name }}</view>
            <view class="task-time">{{ formatRelativeTime(task.created_at) }}</view>
          </view>
          <view class="task-status">
            <view 
              class="status-tag" 
              :style="{ backgroundColor: getTaskStatusColor(task.status) }"
            >
              {{ getTaskStatusText(task.status) }}
            </view>
          </view>
        </view>
        <view v-if="recentTasks.length === 0" class="empty-state">
          <uni-icons type="info" size="40" color="#ccc" />
          <text class="empty-text">暂无任务数据</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onPullDownRefresh } from 'vue'
import { useUserStore } from '@/stores/user'
import { useDeviceStore } from '@/stores/device'
import { userApi } from '@/api/user'
import { 
  formatRelativeTime, 
  getDeviceStatusText, 
  getTaskStatusText, 
  getTaskStatusColor,
  showError 
} from '@/utils/index'

// 状态管理
const userStore = useUserStore()
const deviceStore = useDeviceStore()

// 响应式数据
const loading = ref(false)
const dashboardData = ref<any>({})

// 计算属性
const recentDevices = computed(() => {
  return dashboardData.value.devices?.list?.slice(0, 5) || []
})

const recentTasks = computed(() => {
  return dashboardData.value.tasks?.recent?.slice(0, 5) || []
})

// 获取设备状态样式类
const getStatusClass = (status: number) => {
  const classMap: Record<number, string> = {
    0: 'offline',
    1: 'online',
    2: 'busy'
  }
  return classMap[status] || 'offline'
}

// 加载仪表盘数据
const loadDashboardData = async () => {
  try {
    loading.value = true
    const response = await userApi.getDashboard()
    dashboardData.value = response.data
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
    showError('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 页面跳转方法
const goToDevices = () => {
  uni.switchTab({ url: '/pages/devices/list' })
}

const goToTasks = () => {
  uni.switchTab({ url: '/pages/tasks/list' })
}

const goToScripts = () => {
  uni.switchTab({ url: '/pages/scripts/list' })
}

const goToCreateTask = () => {
  uni.navigateTo({ url: '/pages/tasks/create' })
}

const goToCreateScript = () => {
  uni.navigateTo({ url: '/pages/scripts/edit' })
}

const goToDeviceGroups = () => {
  uni.navigateTo({ url: '/pages/devices/group' })
}

const goToMonitor = () => {
  uni.navigateTo({ url: '/pages/monitor/index' })
}

const goToDeviceDetail = (device: any) => {
  uni.navigateTo({ 
    url: `/pages/devices/detail?id=${device.device_id}` 
  })
}

const goToTaskDetail = (task: any) => {
  uni.navigateTo({ 
    url: `/pages/tasks/detail?id=${task.id}` 
  })
}

// 下拉刷新
onPullDownRefresh(async () => {
  await loadDashboardData()
  uni.stopPullDownRefresh()
})

// 页面加载
onMounted(() => {
  loadDashboardData()
})
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.stats-section {
  margin-bottom: 30rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
  }
}

.stat-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;

  &.online {
    background: rgba(76, 217, 100, 0.1);
  }

  &.running {
    background: rgba(0, 122, 255, 0.1);
  }

  &.scripts {
    background: rgba(240, 173, 78, 0.1);
  }

  &.completed {
    background: rgba(76, 217, 100, 0.1);
  }
}

.stat-content {
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #8f8f94;
}

.quick-actions {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 28rpx;
  color: #007aff;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
  }
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(0, 122, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15rpx;
}

.action-text {
  font-size: 24rpx;
  color: #333;
}

.device-section,
.task-section {
  margin-bottom: 30rpx;
}

.device-list,
.task-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.device-item,
.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8f9fa;
  }
}

.device-info,
.task-info {
  flex: 1;
}

.device-name,
.task-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.device-id,
.task-time {
  font-size: 24rpx;
  color: #8f8f94;
}

.device-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 10rpx;

  &.online {
    background: #4cd964;
  }

  &.offline {
    background: #8f8f94;
  }

  &.busy {
    background: #f0ad4e;
  }
}

.status-text {
  font-size: 24rpx;
  color: #8f8f94;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
}

.empty-state {
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-text {
  display: block;
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #8f8f94;
}
</style>
