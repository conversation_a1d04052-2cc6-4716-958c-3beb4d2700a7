<template>
  <view class="login-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <text class="navbar-title">群控系统</text>
      </view>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- Logo -->
      <view class="logo-section">
        <image class="logo" src="/static/logo.png" mode="aspectFit" />
        <text class="app-name">群控系统控制台</text>
        <text class="app-desc">多端设备统一管理平台</text>
      </view>

      <!-- 表单 -->
      <view class="form-section">
        <view class="form-item">
          <view class="input-wrapper">
            <uni-icons type="person" size="20" color="#8f8f94" />
            <input
              v-model="formData.username"
              class="form-input"
              type="text"
              placeholder="请输入用户名"
              :disabled="loading"
            />
          </view>
        </view>

        <view class="form-item">
          <view class="input-wrapper">
            <uni-icons type="locked" size="20" color="#8f8f94" />
            <input
              v-model="formData.password"
              class="form-input"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              :disabled="loading"
              @confirm="handleLogin"
            />
            <uni-icons
              :type="showPassword ? 'eye-slash' : 'eye'"
              size="20"
              color="#8f8f94"
              @click="showPassword = !showPassword"
            />
          </view>
        </view>

        <view class="form-item">
          <button
            class="login-btn"
            :class="{ 'btn-loading': loading }"
            :disabled="loading || !canSubmit"
            @click="handleLogin"
          >
            <uni-icons v-if="loading" type="spinner-cycle" size="20" color="#fff" class="loading-icon" />
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </view>

        <view class="form-actions">
          <text class="link-text" @click="goToRegister">还没有账号？立即注册</text>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer">
      <text class="footer-text">© 2024 群控系统. All rights reserved.</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { showError, showSuccess } from '@/utils/index'

// 状态栏高度
const statusBarHeight = ref(0)

// 表单数据
const formData = ref({
  username: '',
  password: ''
})

// 控制状态
const loading = ref(false)
const showPassword = ref(false)

// 用户状态管理
const userStore = useUserStore()

// 计算属性
const canSubmit = computed(() => {
  return formData.value.username.trim() && formData.value.password.trim()
})

// 获取系统信息
onMounted(() => {
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight || 0
    }
  })

  // 检查是否已登录
  if (userStore.isAuthenticated) {
    uni.reLaunch({
      url: '/pages/dashboard/index'
    })
  }
})

// 处理登录
const handleLogin = async () => {
  if (!canSubmit.value || loading.value) return

  // 基础验证
  if (formData.value.username.length < 3) {
    showError('用户名至少3个字符')
    return
  }

  if (formData.value.password.length < 6) {
    showError('密码至少6个字符')
    return
  }

  loading.value = true

  try {
    const success = await userStore.login(formData.value.username, formData.value.password)
    
    if (success) {
      showSuccess('登录成功')
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/dashboard/index'
        })
      }, 1000)
    } else {
      showError('登录失败，请检查用户名和密码')
    }
  } catch (error) {
    console.error('登录错误:', error)
    showError('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 跳转到注册页面
const goToRegister = () => {
  uni.navigateTo({
    url: '/pages/register/index'
  })
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.login-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 60rpx 60rpx 120rpx;
  margin-top: 88rpx;
}

.logo-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.form-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
}

.form-item {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 0 30rpx;
  height: 96rpx;
}

.form-input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  margin-left: 20rpx;
  margin-right: 20rpx;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &:not(:disabled):active {
    background: #0056cc;
    transform: scale(0.98);
  }

  &:disabled {
    background: #ccc;
    color: #999;
  }

  &.btn-loading {
    background: #0056cc;
  }
}

.loading-icon {
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.form-actions {
  text-align: center;
  margin-top: 40rpx;
}

.link-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: underline;
}

.footer {
  padding: 40rpx;
  text-align: center;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}
</style>
