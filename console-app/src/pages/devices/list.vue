<template>
  <view class="devices-page">
    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stat-card online">
          <view class="stat-icon">
            <uni-icons type="monitor" size="20" color="#4cd964" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ deviceStats.online }}</text>
            <text class="stat-label">在线</text>
          </view>
        </view>
        
        <view class="stat-card offline">
          <view class="stat-icon">
            <uni-icons type="monitor" size="20" color="#8f8f94" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ deviceStats.offline }}</text>
            <text class="stat-label">离线</text>
          </view>
        </view>
        
        <view class="stat-card busy">
          <view class="stat-icon">
            <uni-icons type="gear" size="20" color="#f0ad4e" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ deviceStats.busy }}</text>
            <text class="stat-label">忙碌</text>
          </view>
        </view>
        
        <view class="stat-card total">
          <view class="stat-icon">
            <uni-icons type="list" size="20" color="#007aff" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ deviceStats.total }}</text>
            <text class="stat-label">总数</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 搜索和筛选 -->
    <view class="search-section">
      <view class="search-bar">
        <uni-icons type="search" size="18" color="#8f8f94" />
        <input
          v-model="searchKeyword"
          class="search-input"
          type="text"
          placeholder="搜索设备ID或名称"
          @input="onSearchInput"
        />
        <view v-if="searchKeyword" class="search-clear" @click="clearSearch">
          <uni-icons type="clear" size="16" color="#8f8f94" />
        </view>
      </view>
      <view class="filter-bar">
        <picker
          :value="statusFilterIndex"
          :range="statusOptions"
          range-key="label"
          @change="onStatusFilterChange"
        >
          <view class="filter-item">
            <text>{{ statusOptions[statusFilterIndex].label }}</text>
            <uni-icons type="down" size="14" color="#8f8f94" />
          </view>
        </picker>
        <view class="filter-item" @click="goToDeviceGroups">
          <uni-icons type="list" size="16" color="#007aff" />
          <text>分组</text>
        </view>
      </view>
    </view>

    <!-- 设备列表 -->
    <view class="device-list">
      <view 
        v-for="device in devices" 
        :key="device.id"
        class="device-item"
        @click="goToDeviceDetail(device)"
      >
        <view class="device-header">
          <view class="device-info">
            <text class="device-name">{{ device.name }}</text>
            <text class="device-id">{{ device.device_id }}</text>
          </view>
          <view class="device-status">
            <view 
              class="status-dot" 
              :class="getStatusClass(device.status)"
            ></view>
            <text class="status-text">{{ getDeviceStatusText(device.status) }}</text>
          </view>
        </view>
        
        <view class="device-meta">
          <view class="meta-item">
            <uni-icons type="location" size="14" color="#8f8f94" />
            <text>{{ device.ip || '未知IP' }}</text>
          </view>
          <view class="meta-item">
            <uni-icons type="gear" size="14" color="#8f8f94" />
            <text>{{ device.type }}</text>
          </view>
          <view class="meta-item">
            <uni-icons type="clock" size="14" color="#8f8f94" />
            <text>{{ formatRelativeTime(device.last_active) }}</text>
          </view>
        </view>
        
        <view class="device-actions" @click.stop>
          <view 
            class="action-btn"
            :class="{ disabled: device.status === 0 }"
            @click="createTask(device)"
          >
            <uni-icons type="plus" size="16" color="#007aff" />
            <text>任务</text>
          </view>
          <view class="action-btn" @click="showDeviceActions(device)">
            <uni-icons type="more-filled" size="16" color="#8f8f94" />
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more" @click="loadMore">
        <uni-icons v-if="loadingMore" type="spinner-cycle" size="20" color="#8f8f94" class="loading-icon" />
        <text>{{ loadingMore ? '加载中...' : '加载更多' }}</text>
      </view>

      <!-- 空状态 -->
      <view v-if="devices.length === 0 && !loading" class="empty-state">
        <uni-icons type="monitor" size="60" color="#ccc" />
        <text class="empty-text">暂无设备</text>
        <button class="empty-btn" @click="registerDevice">注册设备</button>
      </view>
    </view>

    <!-- 浮动按钮 -->
    <view class="fab" @click="registerDevice">
      <uni-icons type="plus" size="24" color="#fff" />
    </view>

    <!-- 设备操作菜单 -->
    <uni-popup ref="actionPopup" type="bottom">
      <view class="action-sheet">
        <view class="action-header">
          <text class="action-title">{{ selectedDevice?.name }}</text>
          <text class="action-subtitle">{{ selectedDevice?.device_id }}</text>
        </view>
        <view class="action-list">
          <view class="action-item" @click="viewDeviceDetail">
            <uni-icons type="eye" size="20" color="#007aff" />
            <text>查看详情</text>
          </view>
          <view 
            class="action-item"
            :class="{ disabled: selectedDevice?.status === 0 }"
            @click="createTaskForDevice"
          >
            <uni-icons type="plus" size="20" color="#4cd964" />
            <text>创建任务</text>
          </view>
          <view class="action-item" @click="editDeviceName">
            <uni-icons type="compose" size="20" color="#f0ad4e" />
            <text>修改名称</text>
          </view>
          <view class="action-item danger" @click="deleteDevice">
            <uni-icons type="trash" size="20" color="#dd524d" />
            <text>删除设备</text>
          </view>
        </view>
        <view class="action-cancel" @click="hideDeviceActions">
          <text>取消</text>
        </view>
      </view>
    </uni-popup>

    <!-- 设备注册弹窗 -->
    <uni-popup ref="registerPopup" type="center">
      <view class="register-dialog">
        <view class="dialog-header">
          <text class="dialog-title">注册设备</text>
        </view>
        <view class="dialog-content">
          <view class="form-item">
            <text class="form-label">设备ID</text>
            <input
              v-model="registerForm.deviceId"
              class="form-input"
              type="text"
              placeholder="请输入设备唯一ID"
            />
          </view>
          <view class="form-item">
            <text class="form-label">设备名称</text>
            <input
              v-model="registerForm.name"
              class="form-input"
              type="text"
              placeholder="请输入设备名称"
            />
          </view>
          <view class="form-item">
            <text class="form-label">设备类型</text>
            <picker
              :value="registerForm.typeIndex"
              :range="deviceTypes"
              range-key="label"
              @change="onDeviceTypeChange"
            >
              <view class="picker-input">
                <text>{{ deviceTypes[registerForm.typeIndex].label }}</text>
                <uni-icons type="down" size="14" color="#8f8f94" />
              </view>
            </picker>
          </view>
        </view>
        <view class="dialog-actions">
          <button class="dialog-btn cancel" @click="hideRegisterDialog">取消</button>
          <button 
            class="dialog-btn confirm" 
            :disabled="!canRegister"
            @click="confirmRegister"
          >
            注册
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onPullDownRefresh, onReachBottom } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { 
  formatRelativeTime, 
  getDeviceStatusText, 
  debounce, 
  showError, 
  showSuccess, 
  showConfirm 
} from '@/utils/index'

// 状态管理
const deviceStore = useDeviceStore()

// 响应式数据
const devices = ref<any[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索和筛选
const searchKeyword = ref('')
const statusFilterIndex = ref(0)

const statusOptions = [
  { label: '全部状态', value: '' },
  { label: '在线', value: 1 },
  { label: '离线', value: 0 },
  { label: '忙碌', value: 2 }
]

// 设备类型
const deviceTypes = [
  { label: 'Android', value: 'android' },
  { label: 'iOS', value: 'ios' }
]

// 操作相关
const selectedDevice = ref<any>(null)
const actionPopup = ref()
const registerPopup = ref()

// 注册表单
const registerForm = ref({
  deviceId: '',
  name: '',
  typeIndex: 0
})

// 计算属性
const deviceStats = computed(() => deviceStore.deviceStats)

const canRegister = computed(() => {
  return registerForm.value.deviceId.trim() && registerForm.value.name.trim()
})

// 获取状态样式类
const getStatusClass = (status: number) => {
  const classMap: Record<number, string> = {
    0: 'offline',
    1: 'online',
    2: 'busy'
  }
  return classMap[status] || 'offline'
}

// 加载设备列表
const loadDevices = async (refresh = false) => {
  if (refresh) {
    currentPage.value = 1
    hasMore.value = true
    devices.value = []
  }

  if (loading.value || loadingMore.value || !hasMore.value) return

  try {
    if (refresh) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    const success = await deviceStore.getUserDevices()
    
    if (success) {
      const newDevices = deviceStore.devices
      if (refresh) {
        devices.value = newDevices
      } else {
        devices.value.push(...newDevices)
      }

      hasMore.value = newDevices.length === pageSize.value
      if (hasMore.value) {
        currentPage.value++
      }
    }
  } catch (error) {
    showError('加载设备列表失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 搜索输入处理
const onSearchInput = debounce(() => {
  // 本地搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    devices.value = deviceStore.devices.filter(device => 
      device.name.toLowerCase().includes(keyword) ||
      device.device_id.toLowerCase().includes(keyword)
    )
  } else {
    devices.value = deviceStore.devices
  }
}, 300)

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  devices.value = deviceStore.devices
}

// 状态筛选变化
const onStatusFilterChange = (e: any) => {
  statusFilterIndex.value = e.detail.value
  const status = statusOptions[statusFilterIndex.value].value
  
  if (status === '') {
    devices.value = deviceStore.devices
  } else {
    devices.value = deviceStore.devices.filter(device => device.status === status)
  }
}

// 加载更多
const loadMore = () => {
  loadDevices(false)
}

// 页面跳转
const goToDeviceDetail = (device: any) => {
  uni.navigateTo({
    url: `/pages/devices/detail?id=${device.device_id}`
  })
}

const goToDeviceGroups = () => {
  uni.navigateTo({
    url: '/pages/devices/group'
  })
}

const createTask = (device: any) => {
  if (device.status === 0) {
    showError('设备离线，无法创建任务')
    return
  }
  
  uni.navigateTo({
    url: `/pages/tasks/create?deviceId=${device.device_id}`
  })
}

// 设备操作
const showDeviceActions = (device: any) => {
  selectedDevice.value = device
  actionPopup.value?.open()
}

const hideDeviceActions = () => {
  actionPopup.value?.close()
  selectedDevice.value = null
}

const viewDeviceDetail = () => {
  hideDeviceActions()
  goToDeviceDetail(selectedDevice.value)
}

const createTaskForDevice = () => {
  hideDeviceActions()
  createTask(selectedDevice.value)
}

const editDeviceName = () => {
  hideDeviceActions()
  
  uni.showModal({
    title: '修改设备名称',
    editable: true,
    placeholderText: selectedDevice.value.name,
    success: async (res) => {
      if (res.confirm && res.content) {
        // 这里应该调用更新设备名称的API
        showSuccess('设备名称修改成功')
        loadDevices(true)
      }
    }
  })
}

const deleteDevice = async () => {
  hideDeviceActions()
  
  try {
    const confirmed = await showConfirm('确定要删除该设备吗？此操作不可恢复。')
    if (!confirmed) return

    const success = await deviceStore.deleteDevice(selectedDevice.value.device_id)
    
    if (success) {
      showSuccess('设备删除成功')
      loadDevices(true)
    }
  } catch (error) {
    showError('删除设备失败')
  }
}

// 设备注册
const registerDevice = () => {
  registerForm.value = {
    deviceId: '',
    name: '',
    typeIndex: 0
  }
  registerPopup.value?.open()
}

const hideRegisterDialog = () => {
  registerPopup.value?.close()
}

const onDeviceTypeChange = (e: any) => {
  registerForm.value.typeIndex = e.detail.value
}

const confirmRegister = async () => {
  if (!canRegister.value) return

  try {
    const success = await deviceStore.registerDevice({
      device_id: registerForm.value.deviceId,
      name: registerForm.value.name,
      type: deviceTypes[registerForm.value.typeIndex].value
    })

    if (success) {
      showSuccess('设备注册成功')
      hideRegisterDialog()
      loadDevices(true)
    }
  } catch (error) {
    showError('设备注册失败')
  }
}

// 下拉刷新
onPullDownRefresh(async () => {
  await loadDevices(true)
  uni.stopPullDownRefresh()
})

// 上拉加载
onReachBottom(() => {
  loadMore()
})

// 页面加载
onMounted(() => {
  loadDevices(true)
})
</script>

<style lang="scss" scoped>
.devices-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.stats-section {
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15rpx;
}

.stat-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stat-icon {
  margin-bottom: 10rpx;
}

.stat-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #8f8f94;
}

.search-section {
  background: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 0 30rpx;
  height: 70rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  margin-left: 20rpx;
}

.search-clear {
  padding: 10rpx;
}

.filter-bar {
  display: flex;
  gap: 20rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  color: #333;
}

.device-list {
  padding: 0 20rpx;
}

.device-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
  }
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.device-id {
  font-size: 24rpx;
  color: #8f8f94;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;

  &.online {
    background: #4cd964;
  }

  &.offline {
    background: #8f8f94;
  }

  &.busy {
    background: #f0ad4e;
  }
}

.status-text {
  font-size: 24rpx;
  color: #8f8f94;
}

.device-meta {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #8f8f94;
}

.device-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #007aff;
  
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }

  &:active {
    background: #e5e5e5;
  }
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  font-size: 28rpx;
  color: #8f8f94;
}

.loading-icon {
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-text {
  display: block;
  margin: 30rpx 0;
  font-size: 28rpx;
  color: #8f8f94;
}

.empty-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.fab {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  background: #007aff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.3);
  z-index: 100;

  &:active {
    transform: scale(0.95);
  }
}

.action-sheet {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
}

.action-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #f5f5f5;
}

.action-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.action-subtitle {
  font-size: 24rpx;
  color: #8f8f94;
}

.action-list {
  padding: 20rpx 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  font-size: 30rpx;
  color: #333;

  &:active {
    background: #f5f5f5;
  }

  &.danger {
    color: #dd524d;
  }

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }

  uni-icons {
    margin-right: 20rpx;
  }
}

.action-cancel {
  padding: 25rpx 30rpx;
  text-align: center;
  font-size: 30rpx;
  color: #8f8f94;
  border-top: 2rpx solid #f5f5f5;

  &:active {
    background: #f5f5f5;
  }
}

.register-dialog {
  background: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-width: 90vw;
}

.dialog-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #f5f5f5;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.dialog-content {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
}

.dialog-actions {
  display: flex;
  border-top: 2rpx solid #f5f5f5;
}

.dialog-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  font-size: 30rpx;
  background: #fff;

  &.cancel {
    color: #8f8f94;
    border-right: 2rpx solid #f5f5f5;
  }

  &.confirm {
    color: #007aff;

    &:disabled {
      color: #ccc;
    }
  }

  &:active {
    background: #f5f5f5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
