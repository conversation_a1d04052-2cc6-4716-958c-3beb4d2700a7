// 脚本相关类型定义

import type { User } from './user'

export interface Script {
  id: number
  name: string
  description?: string
  content: string
  config: string // JSON格式的配置参数
  type: string // autox, hamibot, other
  status: number // 1-启用 2-禁用
  owner_id: number
  owner?: User
  created_at: string
  updated_at: string
}

export interface CreateScriptRequest {
  name: string
  description?: string
  content: string
  type?: string
  config?: Record<string, any>
}

export interface UpdateScriptRequest {
  id: number
  name?: string
  description?: string
  content?: string
  config?: Record<string, any>
}

export interface UpdateScriptStatusRequest {
  id: number
  status: number
}

export interface UpdateScriptConfigRequest {
  id: number
  config: Record<string, any>
}

export interface CloneScriptRequest {
  id: number
  new_name: string
}

export interface ScriptListResponse {
  list: Script[]
  total: number
  page: number
  page_size: number
}

// 脚本状态枚举
export enum ScriptStatus {
  ENABLED = 1,
  DISABLED = 2
}

// 脚本类型枚举
export enum ScriptType {
  AUTOX = 'autox',
  HAMIBOT = 'hamibot',
  OTHER = 'other'
}

// 脚本配置项类型
export interface ScriptConfigItem {
  key: string
  label: string
  type: 'string' | 'number' | 'boolean' | 'select' | 'textarea'
  value: any
  options?: Array<{ label: string; value: any }>
  required?: boolean
  description?: string
}
