// 设备相关类型定义

import type { User } from './user'

export interface Device {
  id: number
  device_id: string
  name: string
  type: string // android, ios
  status: number // 0-离线 1-在线 2-忙碌
  last_active?: string
  user_id: number
  user?: User
  ip: string
  user_agent: string
  version: string
  created_at: string
  updated_at: string
}

export interface DeviceGroup {
  id: number
  name: string
  description?: string
  user_id: number
  user?: User
  device_ids: string // JSON格式的设备ID列表
  created_at: string
  updated_at: string
}

export interface RegisterDeviceRequest {
  device_id: string
  name: string
  type?: string
  version?: string
}

export interface UpdateDeviceStatusRequest {
  device_id: string
  status: number
}

export interface CreateDeviceGroupRequest {
  name: string
  description?: string
  device_ids: number[]
}

export interface UpdateDeviceGroupRequest {
  group_id: number
  name: string
  description?: string
  device_ids: number[]
}

export interface DeviceListResponse {
  list: Device[]
  total: number
  page: number
  page_size: number
}

// 设备状态枚举
export enum DeviceStatus {
  OFFLINE = 0,
  ONLINE = 1,
  BUSY = 2
}

// 设备类型枚举
export enum DeviceType {
  ANDROID = 'android',
  IOS = 'ios'
}
