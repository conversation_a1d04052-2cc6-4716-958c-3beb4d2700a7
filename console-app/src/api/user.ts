import { request } from '@/utils/request'
import type { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  User, 
  RefreshTokenRequest,
  ChangePasswordRequest,
  UserListResponse 
} from '@/types/user'

export const userApi = {
  // 用户注册
  register(data: RegisterRequest) {
    return request<{ user: User }>({
      url: '/api/v1/auth/register',
      method: 'POST',
      data
    })
  },

  // 用户登录
  login(data: LoginRequest) {
    return request<LoginResponse>({
      url: '/api/v1/auth/login',
      method: 'POST',
      data
    })
  },

  // 刷新令牌
  refreshToken(data: RefreshTokenRequest) {
    return request<{ token: string }>({
      url: '/api/v1/auth/refresh',
      method: 'POST',
      data
    })
  },

  // 用户登出
  logout(data: { refresh_token: string }) {
    return request<{ message: string }>({
      url: '/api/v1/auth/logout',
      method: 'POST',
      data
    })
  },

  // 获取用户资料
  getProfile() {
    return request<User>({
      url: '/api/v1/user/profile',
      method: 'GET'
    })
  },

  // 更新用户资料
  updateProfile(data: Partial<User>) {
    return request<{ message: string }>({
      url: '/api/v1/user/profile',
      method: 'PUT',
      data
    })
  },

  // 修改密码
  changePassword(data: ChangePasswordRequest) {
    return request<{ message: string }>({
      url: '/api/v1/user/password',
      method: 'PUT',
      data
    })
  },

  // 获取用户仪表盘数据
  getDashboard() {
    return request<{
      devices: {
        list: any[]
        stats: {
          total: number
          online: number
          offline: number
          busy: number
        }
      }
      scripts: {
        list: any[]
        total: number
      }
      tasks: {
        recent: any[]
        stats: {
          total: number
          pending: number
          running: number
          completed: number
          failed: number
        }
      }
    }>({
      url: '/api/v1/user/dashboard',
      method: 'GET'
    })
  },

  // 管理员功能 - 获取用户列表
  getUserList(params: {
    page?: number
    page_size?: number
    keyword?: string
  }) {
    return request<UserListResponse>({
      url: '/admin/v1/users/list',
      method: 'GET',
      params
    })
  },

  // 管理员功能 - 更新用户状态
  updateUserStatus(data: {
    user_id: number
    status: number
  }) {
    return request<{ message: string }>({
      url: '/admin/v1/users/status',
      method: 'PUT',
      data
    })
  },

  // 管理员功能 - 获取用户统计
  getUserStatistics() {
    return request<{
      total: number
      active: number
      today_registered: number
    }>({
      url: '/admin/v1/users/statistics',
      method: 'GET'
    })
  }
}
