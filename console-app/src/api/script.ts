import { request } from '@/utils/request'
import type { 
  Script, 
  ScriptListResponse,
  CreateScriptRequest,
  UpdateScriptRequest,
  UpdateScriptStatusRequest,
  UpdateScriptConfigRequest,
  CloneScriptRequest
} from '@/types/script'

export const scriptApi = {
  // 创建脚本
  createScript(data: CreateScriptRequest) {
    return request<Script>({
      url: '/api/v1/user/scripts',
      method: 'POST',
      data
    })
  },

  // 获取用户脚本列表
  getUserScripts() {
    return request<{ scripts: Script[] }>({
      url: '/api/v1/user/scripts/list',
      method: 'GET'
    })
  },

  // 获取脚本详情
  getScriptById(id: number) {
    return request<Script>({
      url: `/api/v1/user/scripts/${id}`,
      method: 'GET'
    })
  },

  // 更新脚本
  updateScript(data: UpdateScriptRequest) {
    return request<{ message: string }>({
      url: '/api/v1/user/scripts',
      method: 'PUT',
      data
    })
  },

  // 更新脚本状态
  updateScriptStatus(data: UpdateScriptStatusRequest) {
    return request<{ message: string }>({
      url: '/api/v1/user/scripts/status',
      method: 'PUT',
      data
    })
  },

  // 删除脚本
  deleteScript(data: { id: number }) {
    return request<{ message: string }>({
      url: '/api/v1/user/scripts',
      method: 'DELETE',
      data
    })
  },

  // 获取脚本配置
  getScriptConfig(id: number) {
    return request<{ config: Record<string, any> }>({
      url: `/api/v1/user/scripts/${id}/config`,
      method: 'GET'
    })
  },

  // 更新脚本配置
  updateScriptConfig(data: UpdateScriptConfigRequest) {
    return request<{ message: string }>({
      url: '/api/v1/user/scripts/config',
      method: 'PUT',
      data
    })
  },

  // 克隆脚本
  cloneScript(data: CloneScriptRequest) {
    return request<Script>({
      url: '/api/v1/user/scripts/clone',
      method: 'POST',
      data
    })
  },

  // 根据类型获取脚本
  getScriptsByType(type: string) {
    return request<{ scripts: Script[] }>({
      url: `/api/v1/user/scripts/type/${type}`,
      method: 'GET'
    })
  },

  // 管理员功能 - 获取脚本列表
  getScriptList(params: {
    page?: number
    page_size?: number
    owner_id?: number
    type?: string
    keyword?: string
    status?: number
  }) {
    return request<ScriptListResponse>({
      url: '/admin/v1/scripts/list',
      method: 'GET',
      params
    })
  },

  // 管理员功能 - 获取脚本详情
  getScriptByIdAdmin(id: number) {
    return request<Script>({
      url: `/admin/v1/scripts/${id}`,
      method: 'GET'
    })
  },

  // 管理员功能 - 更新脚本状态
  updateScriptStatusAdmin(data: UpdateScriptStatusRequest) {
    return request<{ message: string }>({
      url: '/admin/v1/scripts/status',
      method: 'PUT',
      data
    })
  },

  // 管理员功能 - 删除脚本
  deleteScriptAdmin(data: { id: number }) {
    return request<{ message: string }>({
      url: '/admin/v1/scripts',
      method: 'DELETE',
      data
    })
  }
}
