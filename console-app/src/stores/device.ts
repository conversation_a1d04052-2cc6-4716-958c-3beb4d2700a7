import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { deviceApi } from '@/api/device'
import type { Device, DeviceGroup } from '@/types/device'

export const useDeviceStore = defineStore('device', () => {
  // 状态
  const devices = ref<Device[]>([])
  const deviceGroups = ref<DeviceGroup[]>([])
  const currentDevice = ref<Device | null>(null)
  const loading = ref<boolean>(false)
  const onlineCount = ref<number>(0)

  // 计算属性
  const onlineDevices = computed(() => devices.value.filter(device => device.status === 1))
  const offlineDevices = computed(() => devices.value.filter(device => device.status === 0))
  const busyDevices = computed(() => devices.value.filter(device => device.status === 2))
  
  const deviceStats = computed(() => ({
    total: devices.value.length,
    online: onlineDevices.value.length,
    offline: offlineDevices.value.length,
    busy: busyDevices.value.length
  }))

  // 获取设备列表
  const getDeviceList = async (params?: any): Promise<boolean> => {
    try {
      loading.value = true
      const response = await deviceApi.getDeviceList(params)
      devices.value = response.data.list || []
      return true
    } catch (error) {
      console.error('获取设备列表失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取用户设备
  const getUserDevices = async (): Promise<boolean> => {
    try {
      loading.value = true
      const response = await deviceApi.getUserDevices()
      devices.value = response.data.devices || []
      return true
    } catch (error) {
      console.error('获取用户设备失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册设备
  const registerDevice = async (deviceData: {
    device_id: string
    name: string
    type?: string
    version?: string
  }): Promise<boolean> => {
    try {
      await deviceApi.registerDevice(deviceData)
      // 重新获取设备列表
      await getUserDevices()
      return true
    } catch (error) {
      console.error('注册设备失败:', error)
      return false
    }
  }

  // 更新设备状态
  const updateDeviceStatus = async (deviceId: string, status: number): Promise<boolean> => {
    try {
      await deviceApi.updateDeviceStatus({ device_id: deviceId, status })
      
      // 更新本地状态
      const device = devices.value.find(d => d.device_id === deviceId)
      if (device) {
        device.status = status
        device.last_active = new Date().toISOString()
      }
      
      return true
    } catch (error) {
      console.error('更新设备状态失败:', error)
      return false
    }
  }

  // 删除设备
  const deleteDevice = async (deviceId: string): Promise<boolean> => {
    try {
      await deviceApi.deleteDevice({ device_id: deviceId })
      
      // 从本地列表中移除
      const index = devices.value.findIndex(d => d.device_id === deviceId)
      if (index > -1) {
        devices.value.splice(index, 1)
      }
      
      return true
    } catch (error) {
      console.error('删除设备失败:', error)
      return false
    }
  }

  // 获取设备详情
  const getDeviceInfo = async (deviceId: string): Promise<Device | null> => {
    try {
      const response = await deviceApi.getDeviceInfo(deviceId)
      currentDevice.value = response.data
      return response.data
    } catch (error) {
      console.error('获取设备详情失败:', error)
      return null
    }
  }

  // 获取在线设备数量
  const getOnlineDeviceCount = async (): Promise<boolean> => {
    try {
      const response = await deviceApi.getOnlineDeviceCount()
      onlineCount.value = response.data.count
      return true
    } catch (error) {
      console.error('获取在线设备数量失败:', error)
      return false
    }
  }

  // 获取设备分组
  const getDeviceGroups = async (): Promise<boolean> => {
    try {
      const response = await deviceApi.getDeviceGroups()
      deviceGroups.value = response.data.groups || []
      return true
    } catch (error) {
      console.error('获取设备分组失败:', error)
      return false
    }
  }

  // 创建设备分组
  const createDeviceGroup = async (groupData: {
    name: string
    description?: string
    device_ids: number[]
  }): Promise<boolean> => {
    try {
      await deviceApi.createDeviceGroup(groupData)
      // 重新获取分组列表
      await getDeviceGroups()
      return true
    } catch (error) {
      console.error('创建设备分组失败:', error)
      return false
    }
  }

  // 更新设备分组
  const updateDeviceGroup = async (groupData: {
    group_id: number
    name: string
    description?: string
    device_ids: number[]
  }): Promise<boolean> => {
    try {
      await deviceApi.updateDeviceGroup(groupData)
      // 重新获取分组列表
      await getDeviceGroups()
      return true
    } catch (error) {
      console.error('更新设备分组失败:', error)
      return false
    }
  }

  // 删除设备分组
  const deleteDeviceGroup = async (groupId: number): Promise<boolean> => {
    try {
      await deviceApi.deleteDeviceGroup({ group_id: groupId })
      
      // 从本地列表中移除
      const index = deviceGroups.value.findIndex(g => g.id === groupId)
      if (index > -1) {
        deviceGroups.value.splice(index, 1)
      }
      
      return true
    } catch (error) {
      console.error('删除设备分组失败:', error)
      return false
    }
  }

  // 根据ID查找设备
  const findDeviceById = (deviceId: string): Device | undefined => {
    return devices.value.find(device => device.device_id === deviceId)
  }

  // 根据状态过滤设备
  const filterDevicesByStatus = (status: number): Device[] => {
    return devices.value.filter(device => device.status === status)
  }

  // 重置状态
  const resetState = () => {
    devices.value = []
    deviceGroups.value = []
    currentDevice.value = null
    loading.value = false
    onlineCount.value = 0
  }

  return {
    // 状态
    devices,
    deviceGroups,
    currentDevice,
    loading,
    onlineCount,
    
    // 计算属性
    onlineDevices,
    offlineDevices,
    busyDevices,
    deviceStats,
    
    // 方法
    getDeviceList,
    getUserDevices,
    registerDevice,
    updateDeviceStatus,
    deleteDevice,
    getDeviceInfo,
    getOnlineDeviceCount,
    getDeviceGroups,
    createDeviceGroup,
    updateDeviceGroup,
    deleteDeviceGroup,
    findDeviceById,
    filterDevicesByStatus,
    resetState
  }
})
