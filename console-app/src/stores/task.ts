import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { taskApi } from '@/api/task'
import type { Task, TaskResult } from '@/types/task'

export const useTaskStore = defineStore('task', () => {
  // 状态
  const tasks = ref<Task[]>([])
  const currentTask = ref<Task | null>(null)
  const taskResults = ref<TaskResult[]>([])
  const loading = ref<boolean>(false)
  const total = ref<number>(0)

  // 计算属性
  const pendingTasks = computed(() => tasks.value.filter(task => task.status === 0))
  const runningTasks = computed(() => tasks.value.filter(task => task.status === 1))
  const completedTasks = computed(() => tasks.value.filter(task => task.status === 2))
  const failedTasks = computed(() => tasks.value.filter(task => task.status === 3))
  const cancelledTasks = computed(() => tasks.value.filter(task => task.status === 4))
  
  const taskStats = computed(() => ({
    total: tasks.value.length,
    pending: pendingTasks.value.length,
    running: runningTasks.value.length,
    completed: completedTasks.value.length,
    failed: failedTasks.value.length,
    cancelled: cancelledTasks.value.length
  }))

  // 获取任务列表
  const getTaskList = async (params?: any): Promise<boolean> => {
    try {
      loading.value = true
      const response = await taskApi.getTaskList(params)
      tasks.value = response.data.list || []
      total.value = response.data.total || 0
      return true
    } catch (error) {
      console.error('获取任务列表失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取用户任务
  const getUserTasks = async (limit?: number): Promise<boolean> => {
    try {
      loading.value = true
      const response = await taskApi.getUserTasks(limit)
      tasks.value = response.data.tasks || []
      return true
    } catch (error) {
      console.error('获取用户任务失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 创建任务
  const createTask = async (taskData: {
    name: string
    description?: string
    type?: string
    script_id: number
    target_devices: number[]
    config?: Record<string, any>
  }): Promise<boolean> => {
    try {
      await taskApi.createTask(taskData)
      // 重新获取任务列表
      await getUserTasks()
      return true
    } catch (error) {
      console.error('创建任务失败:', error)
      return false
    }
  }

  // 获取任务详情
  const getTaskById = async (id: number): Promise<Task | null> => {
    try {
      const response = await taskApi.getTaskById(id)
      currentTask.value = response.data
      return response.data
    } catch (error) {
      console.error('获取任务详情失败:', error)
      return null
    }
  }

  // 启动任务
  const startTask = async (id: number): Promise<boolean> => {
    try {
      await taskApi.startTask({ id })
      
      // 更新本地状态
      const task = tasks.value.find(t => t.id === id)
      if (task) {
        task.status = 1 // 执行中
      }
      
      return true
    } catch (error) {
      console.error('启动任务失败:', error)
      return false
    }
  }

  // 取消任务
  const cancelTask = async (id: number): Promise<boolean> => {
    try {
      await taskApi.cancelTask({ id })
      
      // 更新本地状态
      const task = tasks.value.find(t => t.id === id)
      if (task) {
        task.status = 4 // 已取消
      }
      
      return true
    } catch (error) {
      console.error('取消任务失败:', error)
      return false
    }
  }

  // 更新任务进度
  const updateTaskProgress = async (id: number, progress: number): Promise<boolean> => {
    try {
      await taskApi.updateTaskProgress({ id, progress })
      
      // 更新本地状态
      const task = tasks.value.find(t => t.id === id)
      if (task) {
        task.progress = progress
      }
      
      return true
    } catch (error) {
      console.error('更新任务进度失败:', error)
      return false
    }
  }

  // 获取任务结果
  const getTaskResults = async (taskId: number): Promise<boolean> => {
    try {
      const response = await taskApi.getTaskResults(taskId)
      taskResults.value = response.data.results || []
      return true
    } catch (error) {
      console.error('获取任务结果失败:', error)
      return false
    }
  }

  // 更新任务结果
  const updateTaskResult = async (data: {
    task_id: number
    device_id: number
    status: number
    output?: string
    error?: string
  }): Promise<boolean> => {
    try {
      await taskApi.updateTaskResult(data)
      return true
    } catch (error) {
      console.error('更新任务结果失败:', error)
      return false
    }
  }

  // 删除任务
  const deleteTask = async (id: number): Promise<boolean> => {
    try {
      await taskApi.deleteTask({ id })
      
      // 从本地列表中移除
      const index = tasks.value.findIndex(t => t.id === id)
      if (index > -1) {
        tasks.value.splice(index, 1)
      }
      
      return true
    } catch (error) {
      console.error('删除任务失败:', error)
      return false
    }
  }

  // 获取任务统计
  const getTaskStatistics = async (userId?: number): Promise<Record<string, any> | null> => {
    try {
      const response = await taskApi.getTaskStatistics(userId)
      return response.data
    } catch (error) {
      console.error('获取任务统计失败:', error)
      return null
    }
  }

  // 根据ID查找任务
  const findTaskById = (id: number): Task | undefined => {
    return tasks.value.find(task => task.id === id)
  }

  // 根据状态过滤任务
  const filterTasksByStatus = (status: number): Task[] => {
    return tasks.value.filter(task => task.status === status)
  }

  // 搜索任务
  const searchTasks = (keyword: string): Task[] => {
    if (!keyword) return tasks.value
    
    const lowerKeyword = keyword.toLowerCase()
    return tasks.value.filter(task => 
      task.name.toLowerCase().includes(lowerKeyword) ||
      (task.description && task.description.toLowerCase().includes(lowerKeyword))
    )
  }

  // 重置状态
  const resetState = () => {
    tasks.value = []
    currentTask.value = null
    taskResults.value = []
    loading.value = false
    total.value = 0
  }

  return {
    // 状态
    tasks,
    currentTask,
    taskResults,
    loading,
    total,
    
    // 计算属性
    pendingTasks,
    runningTasks,
    completedTasks,
    failedTasks,
    cancelledTasks,
    taskStats,
    
    // 方法
    getTaskList,
    getUserTasks,
    createTask,
    getTaskById,
    startTask,
    cancelTask,
    updateTaskProgress,
    getTaskResults,
    updateTaskResult,
    deleteTask,
    getTaskStatistics,
    findTaskById,
    filterTasksByStatus,
    searchTasks,
    resetState
  }
})
