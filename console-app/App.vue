<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

onLaunch(() => {
  console.log('App Launch')
  // 应用启动时检查登录状态
  userStore.checkLoginStatus()
})

onShow(() => {
  console.log('App Show')
})

onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
/* 全局样式 */
@import '@/styles/global.scss';

/* 重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 通用类 */
.container {
  padding: 20rpx;
}

.card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: #007aff;
}

.text-success {
  color: #4cd964;
}

.text-warning {
  color: #f0ad4e;
}

.text-danger {
  color: #dd524d;
}

.text-muted {
  color: #8f8f94;
}

.text-xs {
  font-size: 24rpx;
}

.text-sm {
  font-size: 28rpx;
}

.text-base {
  font-size: 32rpx;
}

.text-lg {
  font-size: 36rpx;
}

.text-xl {
  font-size: 40rpx;
}

.font-bold {
  font-weight: bold;
}

.mb-10 {
  margin-bottom: 10rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.mb-30 {
  margin-bottom: 30rpx;
}

.mt-10 {
  margin-top: 10rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mt-30 {
  margin-top: 30rpx;
}

.ml-10 {
  margin-left: 10rpx;
}

.mr-10 {
  margin-right: 10rpx;
}

.p-10 {
  padding: 10rpx;
}

.p-20 {
  padding: 20rpx;
}

.p-30 {
  padding: 30rpx;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background: #007aff;
  color: #fff;
}

.btn-success {
  background: #4cd964;
  color: #fff;
}

.btn-warning {
  background: #f0ad4e;
  color: #fff;
}

.btn-danger {
  background: #dd524d;
  color: #fff;
}

.btn-secondary {
  background: #8f8f94;
  color: #fff;
}

.btn-outline {
  background: transparent;
  border: 2rpx solid #007aff;
  color: #007aff;
}

.btn-small {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 30rpx 60rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
}

.btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 表单样式 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
}

.form-input:focus {
  border-color: #007aff;
  outline: none;
}

/* 状态标签 */
.status-tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.status-online {
  background: #4cd964;
}

.status-offline {
  background: #8f8f94;
}

.status-busy {
  background: #f0ad4e;
}

.status-success {
  background: #4cd964;
}

.status-error {
  background: #dd524d;
}

.status-warning {
  background: #f0ad4e;
}

.status-info {
  background: #007aff;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: #8f8f94;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #8f8f94;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
}
</style>
