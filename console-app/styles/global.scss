// 全局变量
:root {
  --primary-color: #007aff;
  --success-color: #4cd964;
  --warning-color: #f0ad4e;
  --danger-color: #dd524d;
  --info-color: #5bc0de;
  --secondary-color: #8f8f94;
  
  --text-color: #333333;
  --text-muted: #8f8f94;
  --text-light: #ffffff;
  
  --bg-color: #f5f5f5;
  --bg-white: #ffffff;
  --bg-gray: #f8f9fa;
  
  --border-color: #e5e5e5;
  --border-radius: 8rpx;
  --border-radius-lg: 16rpx;
  
  --shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  
  --spacing-xs: 10rpx;
  --spacing-sm: 20rpx;
  --spacing-md: 30rpx;
  --spacing-lg: 40rpx;
  --spacing-xl: 60rpx;
  
  --font-size-xs: 24rpx;
  --font-size-sm: 28rpx;
  --font-size-base: 32rpx;
  --font-size-lg: 36rpx;
  --font-size-xl: 40rpx;
  --font-size-xxl: 48rpx;
}

// 主题色
.theme-primary {
  color: var(--primary-color);
}

.theme-success {
  color: var(--success-color);
}

.theme-warning {
  color: var(--warning-color);
}

.theme-danger {
  color: var(--danger-color);
}

.theme-info {
  color: var(--info-color);
}

.theme-secondary {
  color: var(--secondary-color);
}

// 背景色
.bg-primary {
  background-color: var(--primary-color);
}

.bg-success {
  background-color: var(--success-color);
}

.bg-warning {
  background-color: var(--warning-color);
}

.bg-danger {
  background-color: var(--danger-color);
}

.bg-info {
  background-color: var(--info-color);
}

.bg-secondary {
  background-color: var(--secondary-color);
}

.bg-white {
  background-color: var(--bg-white);
}

.bg-gray {
  background-color: var(--bg-gray);
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

// 响应式
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}

// 工具类
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.ellipsis-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.no-scroll {
  overflow: hidden;
}

.scroll-y {
  overflow-y: auto;
}

.scroll-x {
  overflow-x: auto;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.z-1 {
  z-index: 1;
}

.z-10 {
  z-index: 10;
}

.z-100 {
  z-index: 100;
}

.z-1000 {
  z-index: 1000;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.rounded {
  border-radius: var(--border-radius);
}

.rounded-lg {
  border-radius: var(--border-radius-lg);
}

.shadow {
  box-shadow: var(--shadow);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.border {
  border: 2rpx solid var(--border-color);
}

.border-t {
  border-top: 2rpx solid var(--border-color);
}

.border-b {
  border-bottom: 2rpx solid var(--border-color);
}

.border-l {
  border-left: 2rpx solid var(--border-color);
}

.border-r {
  border-right: 2rpx solid var(--border-color);
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-75 {
  opacity: 0.75;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  user-select: none;
}

.pointer-events-none {
  pointer-events: none;
}

// 过渡效果
.transition {
  transition: all 0.3s ease;
}

.transition-fast {
  transition: all 0.15s ease;
}

.transition-slow {
  transition: all 0.5s ease;
}
