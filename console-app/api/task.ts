import { request } from '@/utils/request'
import type { 
  Task, 
  TaskResult,
  TaskListResponse,
  CreateTaskRequest,
  StartTaskRequest,
  CancelTaskRequest,
  UpdateTaskProgressRequest,
  UpdateTaskResultRequest
} from '@/types/task'

export const taskApi = {
  // 创建任务
  createTask(data: CreateTaskRequest) {
    return request<Task>({
      url: '/api/v1/user/tasks',
      method: 'POST',
      data
    })
  },

  // 获取用户任务列表
  getUserTasks(limit?: number) {
    return request<{ tasks: Task[] }>({
      url: '/api/v1/user/tasks/list',
      method: 'GET',
      params: limit ? { limit } : undefined
    })
  },

  // 获取任务详情
  getTaskById(id: number) {
    return request<Task>({
      url: `/api/v1/user/tasks/${id}`,
      method: 'GET'
    })
  },

  // 启动任务
  startTask(data: StartTaskRequest) {
    return request<{ message: string }>({
      url: '/api/v1/user/tasks/start',
      method: 'PUT',
      data
    })
  },

  // 取消任务
  cancelTask(data: CancelTaskRequest) {
    return request<{ message: string }>({
      url: '/api/v1/user/tasks/cancel',
      method: 'PUT',
      data
    })
  },

  // 更新任务进度
  updateTaskProgress(data: UpdateTaskProgressRequest) {
    return request<{ message: string }>({
      url: '/api/v1/user/tasks/progress',
      method: 'PUT',
      data
    })
  },

  // 获取任务结果
  getTaskResults(taskId: number) {
    return request<{ results: TaskResult[] }>({
      url: `/api/v1/user/tasks/${taskId}/results`,
      method: 'GET'
    })
  },

  // 更新任务结果
  updateTaskResult(data: UpdateTaskResultRequest) {
    return request<{ message: string }>({
      url: '/api/v1/user/tasks/result',
      method: 'PUT',
      data
    })
  },

  // 删除任务
  deleteTask(data: { id: number }) {
    return request<{ message: string }>({
      url: '/api/v1/user/tasks',
      method: 'DELETE',
      data
    })
  },

  // 获取任务统计
  getTaskStatistics(userId?: number) {
    return request<{
      total: number
      pending: number
      running: number
      completed: number
      failed: number
      cancelled: number
    }>({
      url: '/api/v1/user/tasks/statistics',
      method: 'GET',
      params: userId ? { user_id: userId } : undefined
    })
  },

  // 管理员功能 - 获取任务列表
  getTaskList(params: {
    page?: number
    page_size?: number
    created_by?: number
    status?: number
    keyword?: string
  }) {
    return request<TaskListResponse>({
      url: '/admin/v1/tasks/list',
      method: 'GET',
      params
    })
  },

  // 管理员功能 - 获取任务详情
  getTaskByIdAdmin(id: number) {
    return request<Task>({
      url: `/admin/v1/tasks/${id}`,
      method: 'GET'
    })
  },

  // 管理员功能 - 启动任务
  startTaskAdmin(data: StartTaskRequest) {
    return request<{ message: string }>({
      url: '/admin/v1/tasks/start',
      method: 'PUT',
      data
    })
  },

  // 管理员功能 - 取消任务
  cancelTaskAdmin(data: CancelTaskRequest) {
    return request<{ message: string }>({
      url: '/admin/v1/tasks/cancel',
      method: 'PUT',
      data
    })
  },

  // 管理员功能 - 获取任务结果
  getTaskResultsAdmin(taskId: number) {
    return request<{ results: TaskResult[] }>({
      url: `/admin/v1/tasks/${taskId}/results`,
      method: 'GET'
    })
  },

  // 管理员功能 - 删除任务
  deleteTaskAdmin(data: { id: number }) {
    return request<{ message: string }>({
      url: '/admin/v1/tasks',
      method: 'DELETE',
      data
    })
  },

  // 管理员功能 - 获取任务统计
  getTaskStatisticsAdmin(userId?: number) {
    return request<{
      total: number
      pending: number
      running: number
      completed: number
      failed: number
      cancelled: number
    }>({
      url: '/admin/v1/tasks/statistics',
      method: 'GET',
      params: userId ? { user_id: userId } : undefined
    })
  },

  // 设备端API - 获取任务列表
  getTasksForDevice() {
    return request<{ tasks: Task[] }>({
      url: '/api/v1/device/tasks',
      method: 'GET'
    })
  },

  // 设备端API - 更新任务结果
  updateTaskResultForDevice(data: UpdateTaskResultRequest) {
    return request<{ message: string }>({
      url: '/api/v1/device/task/result',
      method: 'PUT',
      data
    })
  }
}
