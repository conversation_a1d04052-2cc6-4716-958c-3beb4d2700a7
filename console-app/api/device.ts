import { request } from '@/utils/request'
import type { 
  Device, 
  DeviceGroup, 
  DeviceListResponse,
  RegisterDeviceRequest,
  UpdateDeviceStatusRequest,
  CreateDeviceGroupRequest,
  UpdateDeviceGroupRequest
} from '@/types/device'

export const deviceApi = {
  // 注册设备
  registerDevice(data: RegisterDeviceRequest) {
    return request<Device>({
      url: '/api/v1/user/devices/register',
      method: 'POST',
      data
    })
  },

  // 获取用户设备列表
  getUserDevices() {
    return request<{ devices: Device[] }>({
      url: '/api/v1/user/devices/list',
      method: 'GET'
    })
  },

  // 更新设备状态
  updateDeviceStatus(data: UpdateDeviceStatusRequest) {
    return request<{ message: string }>({
      url: '/api/v1/user/devices/status',
      method: 'PUT',
      data
    })
  },

  // 删除设备
  deleteDevice(data: { device_id: string }) {
    return request<{ message: string }>({
      url: '/api/v1/user/devices',
      method: 'DELETE',
      data
    })
  },

  // 获取设备详情
  getDeviceInfo(deviceId: string) {
    return request<Device>({
      url: `/admin/v1/devices/${deviceId}`,
      method: 'GET'
    })
  },

  // 创建设备分组
  createDeviceGroup(data: CreateDeviceGroupRequest) {
    return request<DeviceGroup>({
      url: '/api/v1/user/devices/groups',
      method: 'POST',
      data
    })
  },

  // 获取设备分组列表
  getDeviceGroups() {
    return request<{ groups: DeviceGroup[] }>({
      url: '/api/v1/user/devices/groups',
      method: 'GET'
    })
  },

  // 更新设备分组
  updateDeviceGroup(data: UpdateDeviceGroupRequest) {
    return request<{ message: string }>({
      url: '/api/v1/user/devices/groups',
      method: 'PUT',
      data
    })
  },

  // 删除设备分组
  deleteDeviceGroup(data: { group_id: number }) {
    return request<{ message: string }>({
      url: '/api/v1/user/devices/groups',
      method: 'DELETE',
      data
    })
  },

  // 管理员功能 - 获取设备列表
  getDeviceList(params: {
    page?: number
    page_size?: number
    user_id?: number
    status?: number
    keyword?: string
  }) {
    return request<DeviceListResponse>({
      url: '/admin/v1/devices/list',
      method: 'GET',
      params
    })
  },

  // 管理员功能 - 获取在线设备数量
  getOnlineDeviceCount() {
    return request<{ count: number }>({
      url: '/admin/v1/devices/online/count',
      method: 'GET'
    })
  },

  // 设备端API - 获取设备信息
  getDeviceInfoForDevice() {
    return request<Device>({
      url: '/api/v1/device/info',
      method: 'GET'
    })
  },

  // 设备端API - 更新设备状态
  updateDeviceStatusForDevice(data: { status: number }) {
    return request<{ message: string }>({
      url: '/api/v1/device/status',
      method: 'PUT',
      data
    })
  }
}
