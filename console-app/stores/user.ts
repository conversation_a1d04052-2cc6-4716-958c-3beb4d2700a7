import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userApi } from '@/api/user'
import type { User, LoginResponse } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const isLoggedIn = ref<boolean>(false)
  const loading = ref<boolean>(false)

  // 计算属性
  const userInfo = computed(() => user.value)
  const isAuthenticated = computed(() => isLoggedIn.value && !!token.value)

  // 初始化检查登录状态
  const checkLoginStatus = () => {
    const savedToken = uni.getStorageSync('token')
    const savedRefreshToken = uni.getStorageSync('refreshToken')
    const savedUser = uni.getStorageSync('user')

    if (savedToken && savedUser) {
      token.value = savedToken
      refreshToken.value = savedRefreshToken
      user.value = savedUser
      isLoggedIn.value = true
    }
  }

  // 登录
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      loading.value = true
      const response = await userApi.login({ username, password })
      const data: LoginResponse = response.data

      // 保存用户信息和令牌
      user.value = data.user
      token.value = data.token
      refreshToken.value = data.refresh_token
      isLoggedIn.value = true

      // 持久化存储
      uni.setStorageSync('token', data.token)
      uni.setStorageSync('refreshToken', data.refresh_token)
      uni.setStorageSync('user', data.user)

      return true
    } catch (error) {
      console.error('登录失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (username: string, password: string, email?: string): Promise<boolean> => {
    try {
      loading.value = true
      await userApi.register({ username, password, email })
      return true
    } catch (error) {
      console.error('注册失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用登出接口
      if (refreshToken.value) {
        await userApi.logout({ refresh_token: refreshToken.value })
      }
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      // 清除本地状态
      user.value = null
      token.value = ''
      refreshToken.value = ''
      isLoggedIn.value = false

      // 清除存储
      uni.removeStorageSync('token')
      uni.removeStorageSync('refreshToken')
      uni.removeStorageSync('user')

      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/login/index'
      })
    }
  }

  // 刷新令牌
  const refreshAccessToken = async (): Promise<boolean> => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新令牌')
      }

      const response = await userApi.refreshToken({ refresh_token: refreshToken.value })
      const newToken = response.data.token

      token.value = newToken
      uni.setStorageSync('token', newToken)

      return true
    } catch (error) {
      console.error('刷新令牌失败:', error)
      // 刷新失败，需要重新登录
      logout()
      return false
    }
  }

  // 获取用户资料
  const getUserProfile = async (): Promise<boolean> => {
    try {
      const response = await userApi.getProfile()
      user.value = response.data
      uni.setStorageSync('user', response.data)
      return true
    } catch (error) {
      console.error('获取用户资料失败:', error)
      return false
    }
  }

  // 更新用户资料
  const updateProfile = async (profileData: Partial<User>): Promise<boolean> => {
    try {
      await userApi.updateProfile(profileData)
      // 重新获取用户资料
      await getUserProfile()
      return true
    } catch (error) {
      console.error('更新用户资料失败:', error)
      return false
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string): Promise<boolean> => {
    try {
      await userApi.changePassword({ old_password: oldPassword, new_password: newPassword })
      return true
    } catch (error) {
      console.error('修改密码失败:', error)
      return false
    }
  }

  // 重置状态
  const resetState = () => {
    user.value = null
    token.value = ''
    refreshToken.value = ''
    isLoggedIn.value = false
    loading.value = false
  }

  return {
    // 状态
    user,
    token,
    refreshToken,
    isLoggedIn,
    loading,
    
    // 计算属性
    userInfo,
    isAuthenticated,
    
    // 方法
    checkLoginStatus,
    login,
    register,
    logout,
    refreshAccessToken,
    getUserProfile,
    updateProfile,
    changePassword,
    resetState
  }
})
