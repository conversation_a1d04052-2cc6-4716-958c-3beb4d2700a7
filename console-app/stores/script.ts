import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { script<PERSON>pi } from '@/api/script'
import type { <PERSON>ript } from '@/types/script'

export const useScriptStore = defineStore('script', () => {
  // 状态
  const scripts = ref<Script[]>([])
  const currentScript = ref<Script | null>(null)
  const loading = ref<boolean>(false)
  const total = ref<number>(0)

  // 计算属性
  const enabledScripts = computed(() => scripts.value.filter(script => script.status === 1))
  const disabledScripts = computed(() => scripts.value.filter(script => script.status === 2))
  
  const scriptStats = computed(() => ({
    total: scripts.value.length,
    enabled: enabledScripts.value.length,
    disabled: disabledScripts.value.length
  }))

  // 获取脚本列表
  const getScriptList = async (params?: any): Promise<boolean> => {
    try {
      loading.value = true
      const response = await scriptApi.getScriptList(params)
      scripts.value = response.data.list || []
      total.value = response.data.total || 0
      return true
    } catch (error) {
      console.error('获取脚本列表失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取用户脚本
  const getUserScripts = async (): Promise<boolean> => {
    try {
      loading.value = true
      const response = await scriptApi.getUserScripts()
      scripts.value = response.data.scripts || []
      return true
    } catch (error) {
      console.error('获取用户脚本失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 创建脚本
  const createScript = async (scriptData: {
    name: string
    description?: string
    content: string
    type?: string
    config?: Record<string, any>
  }): Promise<boolean> => {
    try {
      await scriptApi.createScript(scriptData)
      // 重新获取脚本列表
      await getUserScripts()
      return true
    } catch (error) {
      console.error('创建脚本失败:', error)
      return false
    }
  }

  // 获取脚本详情
  const getScriptById = async (id: number): Promise<Script | null> => {
    try {
      const response = await scriptApi.getScriptById(id)
      currentScript.value = response.data
      return response.data
    } catch (error) {
      console.error('获取脚本详情失败:', error)
      return null
    }
  }

  // 更新脚本
  const updateScript = async (scriptData: {
    id: number
    name?: string
    description?: string
    content?: string
    config?: Record<string, any>
  }): Promise<boolean> => {
    try {
      await scriptApi.updateScript(scriptData)
      
      // 更新本地状态
      const index = scripts.value.findIndex(s => s.id === scriptData.id)
      if (index > -1) {
        const script = scripts.value[index]
        if (scriptData.name) script.name = scriptData.name
        if (scriptData.description) script.description = scriptData.description
        if (scriptData.content) script.content = scriptData.content
        script.updated_at = new Date().toISOString()
      }
      
      return true
    } catch (error) {
      console.error('更新脚本失败:', error)
      return false
    }
  }

  // 更新脚本状态
  const updateScriptStatus = async (id: number, status: number): Promise<boolean> => {
    try {
      await scriptApi.updateScriptStatus({ id, status })
      
      // 更新本地状态
      const script = scripts.value.find(s => s.id === id)
      if (script) {
        script.status = status
      }
      
      return true
    } catch (error) {
      console.error('更新脚本状态失败:', error)
      return false
    }
  }

  // 删除脚本
  const deleteScript = async (id: number): Promise<boolean> => {
    try {
      await scriptApi.deleteScript({ id })
      
      // 从本地列表中移除
      const index = scripts.value.findIndex(s => s.id === id)
      if (index > -1) {
        scripts.value.splice(index, 1)
      }
      
      return true
    } catch (error) {
      console.error('删除脚本失败:', error)
      return false
    }
  }

  // 获取脚本配置
  const getScriptConfig = async (id: number): Promise<Record<string, any> | null> => {
    try {
      const response = await scriptApi.getScriptConfig(id)
      return response.data.config || {}
    } catch (error) {
      console.error('获取脚本配置失败:', error)
      return null
    }
  }

  // 更新脚本配置
  const updateScriptConfig = async (id: number, config: Record<string, any>): Promise<boolean> => {
    try {
      await scriptApi.updateScriptConfig({ id, config })
      return true
    } catch (error) {
      console.error('更新脚本配置失败:', error)
      return false
    }
  }

  // 克隆脚本
  const cloneScript = async (id: number, newName: string): Promise<boolean> => {
    try {
      await scriptApi.cloneScript({ id, new_name: newName })
      // 重新获取脚本列表
      await getUserScripts()
      return true
    } catch (error) {
      console.error('克隆脚本失败:', error)
      return false
    }
  }

  // 根据类型获取脚本
  const getScriptsByType = async (type: string): Promise<boolean> => {
    try {
      const response = await scriptApi.getScriptsByType(type)
      scripts.value = response.data.scripts || []
      return true
    } catch (error) {
      console.error('获取脚本列表失败:', error)
      return false
    }
  }

  // 根据ID查找脚本
  const findScriptById = (id: number): Script | undefined => {
    return scripts.value.find(script => script.id === id)
  }

  // 根据状态过滤脚本
  const filterScriptsByStatus = (status: number): Script[] => {
    return scripts.value.filter(script => script.status === status)
  }

  // 搜索脚本
  const searchScripts = (keyword: string): Script[] => {
    if (!keyword) return scripts.value
    
    const lowerKeyword = keyword.toLowerCase()
    return scripts.value.filter(script => 
      script.name.toLowerCase().includes(lowerKeyword) ||
      (script.description && script.description.toLowerCase().includes(lowerKeyword))
    )
  }

  // 重置状态
  const resetState = () => {
    scripts.value = []
    currentScript.value = null
    loading.value = false
    total.value = 0
  }

  return {
    // 状态
    scripts,
    currentScript,
    loading,
    total,
    
    // 计算属性
    enabledScripts,
    disabledScripts,
    scriptStats,
    
    // 方法
    getScriptList,
    getUserScripts,
    createScript,
    getScriptById,
    updateScript,
    updateScriptStatus,
    deleteScript,
    getScriptConfig,
    updateScriptConfig,
    cloneScript,
    getScriptsByType,
    findScriptById,
    filterScriptsByStatus,
    searchScripts,
    resetState
  }
})
