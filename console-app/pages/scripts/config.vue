<template>
  <view class="script-config">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <uni-icons type="back" size="20" color="#333" />
        </view>
        <text class="navbar-title">脚本配置</text>
        <view class="navbar-action" @click="saveConfig">
          <text class="save-text" :class="{ disabled: !hasChanges }">保存</text>
        </view>
      </view>
    </view>

    <!-- 脚本信息 -->
    <view class="script-info">
      <view class="info-card">
        <view class="script-header">
          <text class="script-name">{{ scriptInfo?.name }}</text>
          <view 
            class="script-status" 
            :class="scriptInfo?.status === 1 ? 'active' : 'inactive'"
          >
            {{ scriptInfo?.status === 1 ? '启用' : '禁用' }}
          </view>
        </view>
        <text v-if="scriptInfo?.description" class="script-desc">
          {{ scriptInfo.description }}
        </text>
      </view>
    </view>

    <!-- 配置表单 -->
    <view class="config-form">
      <!-- 基础配置 -->
      <view class="config-section">
        <view class="section-header">
          <text class="section-title">基础配置</text>
        </view>
        <view class="config-items">
          <view class="config-item">
            <text class="config-label">执行延迟 (毫秒)</text>
            <input
              v-model.number="configData.delay"
              class="config-input"
              type="number"
              placeholder="1000"
            />
          </view>
          
          <view class="config-item">
            <text class="config-label">重试次数</text>
            <input
              v-model.number="configData.retryCount"
              class="config-input"
              type="number"
              placeholder="3"
            />
          </view>
          
          <view class="config-item">
            <text class="config-label">超时时间 (秒)</text>
            <input
              v-model.number="configData.timeout"
              class="config-input"
              type="number"
              placeholder="30"
            />
          </view>
          
          <view class="config-item">
            <text class="config-label">调试模式</text>
            <switch 
              v-model="configData.debug"
              :checked="configData.debug"
              @change="onDebugChange"
            />
          </view>
        </view>
      </view>

      <!-- 高级配置 -->
      <view class="config-section">
        <view class="section-header">
          <text class="section-title">高级配置</text>
        </view>
        <view class="config-items">
          <view class="config-item">
            <text class="config-label">屏幕方向</text>
            <picker
              :value="orientationIndex"
              :range="orientationOptions"
              range-key="label"
              @change="onOrientationChange"
            >
              <view class="picker-input">
                <text>{{ orientationOptions[orientationIndex].label }}</text>
                <uni-icons type="down" size="14" color="#8f8f94" />
              </view>
            </picker>
          </view>
          
          <view class="config-item">
            <text class="config-label">分辨率适配</text>
            <switch 
              v-model="configData.autoScale"
              :checked="configData.autoScale"
              @change="onAutoScaleChange"
            />
          </view>
          
          <view class="config-item">
            <text class="config-label">保持屏幕常亮</text>
            <switch 
              v-model="configData.keepScreenOn"
              :checked="configData.keepScreenOn"
              @change="onKeepScreenOnChange"
            />
          </view>
          
          <view class="config-item">
            <text class="config-label">音量键停止</text>
            <switch 
              v-model="configData.volumeKeyStop"
              :checked="configData.volumeKeyStop"
              @change="onVolumeKeyStopChange"
            />
          </view>
        </view>
      </view>

      <!-- 自定义参数 -->
      <view class="config-section">
        <view class="section-header">
          <text class="section-title">自定义参数</text>
          <view class="section-action" @click="addCustomParam">
            <uni-icons type="plus" size="16" color="#007aff" />
            <text>添加</text>
          </view>
        </view>
        <view class="config-items">
          <view 
            v-for="(param, index) in customParams" 
            :key="index"
            class="custom-param"
          >
            <view class="param-header">
              <input
                v-model="param.key"
                class="param-key"
                type="text"
                placeholder="参数名"
              />
              <view class="param-actions">
                <picker
                  :value="param.typeIndex"
                  :range="paramTypes"
                  range-key="label"
                  @change="(e) => onParamTypeChange(e, index)"
                >
                  <view class="param-type">
                    <text>{{ paramTypes[param.typeIndex].label }}</text>
                    <uni-icons type="down" size="12" color="#8f8f94" />
                  </view>
                </picker>
                <view class="param-delete" @click="removeCustomParam(index)">
                  <uni-icons type="trash" size="16" color="#dd524d" />
                </view>
              </view>
            </view>
            
            <view class="param-value">
              <input
                v-if="param.type !== 'boolean'"
                v-model="param.value"
                class="param-input"
                :type="param.type === 'number' ? 'number' : 'text'"
                placeholder="参数值"
              />
              <switch
                v-else
                v-model="param.value"
                :checked="param.value"
                @change="(e) => onParamValueChange(e, index)"
              />
            </view>
            
            <input
              v-model="param.description"
              class="param-desc"
              type="text"
              placeholder="参数描述（可选）"
            />
          </view>
          
          <view v-if="customParams.length === 0" class="empty-params">
            <uni-icons type="info" size="40" color="#ccc" />
            <text class="empty-text">暂无自定义参数</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useScriptStore } from '@/stores/script'
import { showError, showSuccess, showConfirm } from '@/utils/index'

// 状态栏高度
const statusBarHeight = ref(0)

// 路由参数
const scriptId = ref<number | null>(null)

// 状态管理
const scriptStore = useScriptStore()

// 脚本信息
const scriptInfo = ref<any>(null)

// 配置数据
const configData = ref({
  delay: 1000,
  retryCount: 3,
  timeout: 30,
  debug: false,
  orientation: 'auto',
  autoScale: true,
  keepScreenOn: false,
  volumeKeyStop: true
})

// 原始配置（用于检测变化）
const originalConfig = ref<string>('')

// 屏幕方向选项
const orientationIndex = ref(0)
const orientationOptions = [
  { label: '自动', value: 'auto' },
  { label: '竖屏', value: 'portrait' },
  { label: '横屏', value: 'landscape' }
]

// 自定义参数
const customParams = ref<Array<{
  key: string
  value: any
  type: string
  typeIndex: number
  description: string
}>>([])

// 参数类型
const paramTypes = [
  { label: '文本', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔', value: 'boolean' }
]

// 计算属性
const hasChanges = computed(() => {
  const currentConfig = JSON.stringify({
    ...configData.value,
    customParams: customParams.value
  })
  return currentConfig !== originalConfig.value
})

// 获取系统信息
onMounted(() => {
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight || 0
    }
  })

  // 获取路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options as any
  
  if (options.id) {
    scriptId.value = parseInt(options.id)
    loadScriptInfo()
    loadConfig()
  }
})

// 加载脚本信息
const loadScriptInfo = async () => {
  if (!scriptId.value) return

  try {
    const script = await scriptStore.getScriptById(scriptId.value)
    if (script) {
      scriptInfo.value = script
    }
  } catch (error) {
    showError('加载脚本信息失败')
  }
}

// 加载配置
const loadConfig = async () => {
  if (!scriptId.value) return

  try {
    const config = await scriptStore.getScriptConfig(scriptId.value)
    if (config) {
      // 合并配置
      Object.assign(configData.value, config)
      
      // 设置屏幕方向索引
      const orientationIdx = orientationOptions.findIndex(opt => opt.value === config.orientation)
      if (orientationIdx > -1) {
        orientationIndex.value = orientationIdx
      }
      
      // 设置自定义参数
      if (config.customParams) {
        customParams.value = config.customParams.map((param: any) => ({
          ...param,
          typeIndex: paramTypes.findIndex(type => type.value === param.type) || 0
        }))
      }
      
      // 保存原始配置
      originalConfig.value = JSON.stringify({
        ...configData.value,
        customParams: customParams.value
      })
    }
  } catch (error) {
    showError('加载配置失败')
  }
}

// 保存配置
const saveConfig = async () => {
  if (!scriptId.value || !hasChanges.value) return

  try {
    const config = {
      ...configData.value,
      customParams: customParams.value.map(param => ({
        key: param.key,
        value: param.value,
        type: param.type,
        description: param.description
      }))
    }

    const success = await scriptStore.updateScriptConfig(scriptId.value, config)
    
    if (success) {
      showSuccess('配置保存成功')
      originalConfig.value = JSON.stringify({
        ...configData.value,
        customParams: customParams.value
      })
    }
  } catch (error) {
    showError('保存配置失败')
  }
}

// 事件处理
const onDebugChange = (e: any) => {
  configData.value.debug = e.detail.value
}

const onAutoScaleChange = (e: any) => {
  configData.value.autoScale = e.detail.value
}

const onKeepScreenOnChange = (e: any) => {
  configData.value.keepScreenOn = e.detail.value
}

const onVolumeKeyStopChange = (e: any) => {
  configData.value.volumeKeyStop = e.detail.value
}

const onOrientationChange = (e: any) => {
  orientationIndex.value = e.detail.value
  configData.value.orientation = orientationOptions[orientationIndex.value].value
}

// 自定义参数操作
const addCustomParam = () => {
  customParams.value.push({
    key: '',
    value: '',
    type: 'string',
    typeIndex: 0,
    description: ''
  })
}

const removeCustomParam = (index: number) => {
  customParams.value.splice(index, 1)
}

const onParamTypeChange = (e: any, index: number) => {
  const typeIndex = e.detail.value
  const param = customParams.value[index]
  param.typeIndex = typeIndex
  param.type = paramTypes[typeIndex].value
  
  // 重置值
  if (param.type === 'boolean') {
    param.value = false
  } else if (param.type === 'number') {
    param.value = 0
  } else {
    param.value = ''
  }
}

const onParamValueChange = (e: any, index: number) => {
  customParams.value[index].value = e.detail.value
}

// 返回
const goBack = async () => {
  if (hasChanges.value) {
    const confirmed = await showConfirm('有未保存的更改，确定要离开吗？')
    if (!confirmed) return
  }
  
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.script-config {
  background: #f5f5f5;
  min-height: 100vh;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border-bottom: 2rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-back,
.navbar-action {
  width: 120rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.save-text {
  font-size: 32rpx;
  color: #007aff;
  
  &.disabled {
    color: #ccc;
  }
}

.script-info {
  margin-top: 88rpx;
  padding: 20rpx;
}

.info-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.script-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.script-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.script-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
  
  &.active {
    background: #4cd964;
  }
  
  &.inactive {
    background: #dd524d;
  }
}

.script-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.config-form {
  padding: 0 20rpx 40rpx;
}

.config-section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.section-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #007aff;
}

.config-items {
  padding: 30rpx;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.config-label {
  font-size: 28rpx;
  color: #333;
}

.config-input {
  width: 200rpx;
  height: 60rpx;
  padding: 0 15rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 26rpx;
  text-align: right;
}

.picker-input {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 26rpx;
  min-width: 150rpx;
}

.custom-param {
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.param-key {
  flex: 1;
  height: 60rpx;
  padding: 0 15rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.param-actions {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-left: 15rpx;
}

.param-type {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 15rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 24rpx;
  min-width: 100rpx;
}

.param-delete {
  padding: 10rpx;
}

.param-value {
  margin-bottom: 15rpx;
}

.param-input {
  width: 100%;
  height: 60rpx;
  padding: 0 15rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.param-desc {
  width: 100%;
  height: 60rpx;
  padding: 0 15rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
}

.empty-params {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  display: block;
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #8f8f94;
}
</style>
