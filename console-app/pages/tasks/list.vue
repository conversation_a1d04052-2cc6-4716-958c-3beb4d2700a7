<template>
  <view class="tasks-page">
    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stat-card pending">
          <view class="stat-icon">
            <uni-icons type="clock" size="18" color="#8f8f94" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ taskStats.pending }}</text>
            <text class="stat-label">待执行</text>
          </view>
        </view>
        
        <view class="stat-card running">
          <view class="stat-icon">
            <uni-icons type="gear" size="18" color="#007aff" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ taskStats.running }}</text>
            <text class="stat-label">执行中</text>
          </view>
        </view>
        
        <view class="stat-card completed">
          <view class="stat-icon">
            <uni-icons type="checkmarkempty" size="18" color="#4cd964" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ taskStats.completed }}</text>
            <text class="stat-label">已完成</text>
          </view>
        </view>
        
        <view class="stat-card failed">
          <view class="stat-icon">
            <uni-icons type="clear" size="18" color="#dd524d" />
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ taskStats.failed }}</text>
            <text class="stat-label">失败</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 搜索和筛选 -->
    <view class="search-section">
      <view class="search-bar">
        <uni-icons type="search" size="18" color="#8f8f94" />
        <input
          v-model="searchKeyword"
          class="search-input"
          type="text"
          placeholder="搜索任务名称或描述"
          @input="onSearchInput"
        />
        <view v-if="searchKeyword" class="search-clear" @click="clearSearch">
          <uni-icons type="clear" size="16" color="#8f8f94" />
        </view>
      </view>
      <view class="filter-bar">
        <picker
          :value="statusFilterIndex"
          :range="statusOptions"
          range-key="label"
          @change="onStatusFilterChange"
        >
          <view class="filter-item">
            <text>{{ statusOptions[statusFilterIndex].label }}</text>
            <uni-icons type="down" size="14" color="#8f8f94" />
          </view>
        </picker>
      </view>
    </view>

    <!-- 任务列表 -->
    <view class="task-list">
      <view 
        v-for="task in tasks" 
        :key="task.id"
        class="task-item"
        @click="goToTaskDetail(task)"
      >
        <view class="task-header">
          <view class="task-info">
            <text class="task-name">{{ task.name }}</text>
            <text v-if="task.description" class="task-desc">{{ task.description }}</text>
          </view>
          <view class="task-status">
            <view 
              class="status-tag" 
              :style="{ backgroundColor: getTaskStatusColor(task.status) }"
            >
              {{ getTaskStatusText(task.status) }}
            </view>
          </view>
        </view>
        
        <view class="task-meta">
          <view class="meta-item">
            <uni-icons type="document" size="14" color="#8f8f94" />
            <text>{{ task.script?.name || '未知脚本' }}</text>
          </view>
          <view class="meta-item">
            <uni-icons type="monitor" size="14" color="#8f8f94" />
            <text>{{ getTargetDeviceCount(task) }}台设备</text>
          </view>
          <view class="meta-item">
            <uni-icons type="clock" size="14" color="#8f8f94" />
            <text>{{ formatRelativeTime(task.created_at) }}</text>
          </view>
        </view>
        
        <!-- 进度条 -->
        <view v-if="task.status === 1" class="task-progress">
          <view class="progress-bar">
            <view 
              class="progress-fill" 
              :style="{ width: task.progress + '%' }"
            ></view>
          </view>
          <text class="progress-text">{{ task.progress }}%</text>
        </view>
        
        <view class="task-actions" @click.stop>
          <view 
            v-if="task.status === 0"
            class="action-btn start"
            @click="startTask(task)"
          >
            <uni-icons type="play" size="16" color="#4cd964" />
            <text>启动</text>
          </view>
          <view 
            v-if="task.status === 1"
            class="action-btn cancel"
            @click="cancelTask(task)"
          >
            <uni-icons type="stop" size="16" color="#dd524d" />
            <text>取消</text>
          </view>
          <view class="action-btn" @click="showTaskActions(task)">
            <uni-icons type="more-filled" size="16" color="#8f8f94" />
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more" @click="loadMore">
        <uni-icons v-if="loadingMore" type="spinner-cycle" size="20" color="#8f8f94" class="loading-icon" />
        <text>{{ loadingMore ? '加载中...' : '加载更多' }}</text>
      </view>

      <!-- 空状态 -->
      <view v-if="tasks.length === 0 && !loading" class="empty-state">
        <uni-icons type="list" size="60" color="#ccc" />
        <text class="empty-text">暂无任务</text>
        <button class="empty-btn" @click="goToCreateTask">创建第一个任务</button>
      </view>
    </view>

    <!-- 浮动按钮 -->
    <view class="fab" @click="goToCreateTask">
      <uni-icons type="plus" size="24" color="#fff" />
    </view>

    <!-- 任务操作菜单 -->
    <uni-popup ref="actionPopup" type="bottom">
      <view class="action-sheet">
        <view class="action-header">
          <text class="action-title">{{ selectedTask?.name }}</text>
        </view>
        <view class="action-list">
          <view class="action-item" @click="viewTaskDetail">
            <uni-icons type="eye" size="20" color="#007aff" />
            <text>查看详情</text>
          </view>
          <view class="action-item" @click="viewTaskResults">
            <uni-icons type="list" size="20" color="#4cd964" />
            <text>查看结果</text>
          </view>
          <view 
            v-if="selectedTask?.status === 0"
            class="action-item"
            @click="startSelectedTask"
          >
            <uni-icons type="play" size="20" color="#4cd964" />
            <text>启动任务</text>
          </view>
          <view 
            v-if="selectedTask?.status === 1"
            class="action-item"
            @click="cancelSelectedTask"
          >
            <uni-icons type="stop" size="20" color="#dd524d" />
            <text>取消任务</text>
          </view>
          <view class="action-item danger" @click="deleteTask">
            <uni-icons type="trash" size="20" color="#dd524d" />
            <text>删除任务</text>
          </view>
        </view>
        <view class="action-cancel" @click="hideTaskActions">
          <text>取消</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onPullDownRefresh, onReachBottom } from 'vue'
import { useTaskStore } from '@/stores/task'
import { 
  formatRelativeTime, 
  getTaskStatusText, 
  getTaskStatusColor,
  debounce, 
  showError, 
  showSuccess, 
  showConfirm 
} from '@/utils/index'

// 状态管理
const taskStore = useTaskStore()

// 响应式数据
const tasks = ref<any[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索和筛选
const searchKeyword = ref('')
const statusFilterIndex = ref(0)

const statusOptions = [
  { label: '全部状态', value: '' },
  { label: '待执行', value: 0 },
  { label: '执行中', value: 1 },
  { label: '已完成', value: 2 },
  { label: '失败', value: 3 },
  { label: '已取消', value: 4 }
]

// 操作相关
const selectedTask = ref<any>(null)
const actionPopup = ref()

// 计算属性
const taskStats = computed(() => taskStore.taskStats)

// 获取目标设备数量
const getTargetDeviceCount = (task: any) => {
  try {
    const devices = JSON.parse(task.target_devices || '[]')
    return devices.length
  } catch {
    return 0
  }
}

// 加载任务列表
const loadTasks = async (refresh = false) => {
  if (refresh) {
    currentPage.value = 1
    hasMore.value = true
    tasks.value = []
  }

  if (loading.value || loadingMore.value || !hasMore.value) return

  try {
    if (refresh) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    const success = await taskStore.getUserTasks()
    
    if (success) {
      const newTasks = taskStore.tasks
      if (refresh) {
        tasks.value = newTasks
      } else {
        tasks.value.push(...newTasks)
      }

      hasMore.value = newTasks.length === pageSize.value
      if (hasMore.value) {
        currentPage.value++
      }
    }
  } catch (error) {
    showError('加载任务列表失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 搜索输入处理
const onSearchInput = debounce(() => {
  // 本地搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    tasks.value = taskStore.tasks.filter(task => 
      task.name.toLowerCase().includes(keyword) ||
      (task.description && task.description.toLowerCase().includes(keyword))
    )
  } else {
    tasks.value = taskStore.tasks
  }
}, 300)

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  tasks.value = taskStore.tasks
}

// 状态筛选变化
const onStatusFilterChange = (e: any) => {
  statusFilterIndex.value = e.detail.value
  const status = statusOptions[statusFilterIndex.value].value
  
  if (status === '') {
    tasks.value = taskStore.tasks
  } else {
    tasks.value = taskStore.tasks.filter(task => task.status === status)
  }
}

// 加载更多
const loadMore = () => {
  loadTasks(false)
}

// 任务操作
const startTask = async (task: any) => {
  try {
    const success = await taskStore.startTask(task.id)
    if (success) {
      showSuccess('任务启动成功')
      loadTasks(true)
    }
  } catch (error) {
    showError('启动任务失败')
  }
}

const cancelTask = async (task: any) => {
  try {
    const confirmed = await showConfirm('确定要取消该任务吗？')
    if (!confirmed) return

    const success = await taskStore.cancelTask(task.id)
    if (success) {
      showSuccess('任务取消成功')
      loadTasks(true)
    }
  } catch (error) {
    showError('取消任务失败')
  }
}

// 任务操作菜单
const showTaskActions = (task: any) => {
  selectedTask.value = task
  actionPopup.value?.open()
}

const hideTaskActions = () => {
  actionPopup.value?.close()
  selectedTask.value = null
}

const viewTaskDetail = () => {
  hideTaskActions()
  goToTaskDetail(selectedTask.value)
}

const viewTaskResults = () => {
  hideTaskActions()
  uni.navigateTo({
    url: `/pages/tasks/detail?id=${selectedTask.value.id}&tab=results`
  })
}

const startSelectedTask = () => {
  hideTaskActions()
  startTask(selectedTask.value)
}

const cancelSelectedTask = () => {
  hideTaskActions()
  cancelTask(selectedTask.value)
}

const deleteTask = async () => {
  hideTaskActions()
  
  try {
    const confirmed = await showConfirm('确定要删除该任务吗？此操作不可恢复。')
    if (!confirmed) return

    const success = await taskStore.deleteTask(selectedTask.value.id)
    
    if (success) {
      showSuccess('任务删除成功')
      loadTasks(true)
    }
  } catch (error) {
    showError('删除任务失败')
  }
}

// 页面跳转
const goToTaskDetail = (task: any) => {
  uni.navigateTo({
    url: `/pages/tasks/detail?id=${task.id}`
  })
}

const goToCreateTask = () => {
  uni.navigateTo({
    url: '/pages/tasks/create'
  })
}

// 下拉刷新
onPullDownRefresh(async () => {
  await loadTasks(true)
  uni.stopPullDownRefresh()
})

// 上拉加载
onReachBottom(() => {
  loadMore()
})

// 页面加载
onMounted(() => {
  loadTasks(true)
})
</script>

<style lang="scss" scoped>
.tasks-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.stats-section {
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15rpx;
}

.stat-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stat-icon {
  margin-bottom: 10rpx;
}

.stat-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #8f8f94;
}

.search-section {
  background: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 0 30rpx;
  height: 70rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  margin-left: 20rpx;
}

.search-clear {
  padding: 10rpx;
}

.filter-bar {
  display: flex;
  gap: 20rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  color: #333;
}

.task-list {
  padding: 0 20rpx;
}

.task-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
  }
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.task-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.task-status {
  margin-left: 20rpx;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
}

.task-meta {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #8f8f94;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #f5f5f5;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #007aff;
  border-radius: 4rpx;
  transition: width 0.3s;
}

.progress-text {
  font-size: 24rpx;
  color: #8f8f94;
  min-width: 60rpx;
}

.task-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #333;
  
  &.start {
    color: #4cd964;
  }
  
  &.cancel {
    color: #dd524d;
  }

  &:active {
    background: #e5e5e5;
  }
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  font-size: 28rpx;
  color: #8f8f94;
}

.loading-icon {
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-text {
  display: block;
  margin: 30rpx 0;
  font-size: 28rpx;
  color: #8f8f94;
}

.empty-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.fab {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  background: #007aff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.3);
  z-index: 100;

  &:active {
    transform: scale(0.95);
  }
}

.action-sheet {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
}

.action-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #f5f5f5;
}

.action-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.action-list {
  padding: 20rpx 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  font-size: 30rpx;
  color: #333;

  &:active {
    background: #f5f5f5;
  }

  &.danger {
    color: #dd524d;
  }

  uni-icons {
    margin-right: 20rpx;
  }
}

.action-cancel {
  padding: 25rpx 30rpx;
  text-align: center;
  font-size: 30rpx;
  color: #8f8f94;
  border-top: 2rpx solid #f5f5f5;

  &:active {
    background: #f5f5f5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
