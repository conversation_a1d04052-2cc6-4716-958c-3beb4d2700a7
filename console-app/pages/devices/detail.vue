<template>
  <view class="device-detail">
    <!-- 设备基本信息 -->
    <view class="device-info">
      <view class="info-header">
        <view class="device-avatar">
          <uni-icons 
            :type="device?.type === 'android' ? 'phone' : 'phone'" 
            size="40" 
            color="#007aff" 
          />
        </view>
        <view class="device-basic">
          <text class="device-name">{{ device?.name }}</text>
          <text class="device-id">{{ device?.device_id }}</text>
        </view>
        <view class="device-status">
          <view 
            class="status-indicator" 
            :class="getStatusClass(device?.status)"
          ></view>
          <text class="status-text">{{ getDeviceStatusText(device?.status) }}</text>
        </view>
      </view>
    </view>

    <!-- 设备详细信息 -->
    <view class="detail-sections">
      <!-- 基本信息 -->
      <view class="detail-section">
        <view class="section-header">
          <text class="section-title">基本信息</text>
        </view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">设备类型</text>
            <text class="info-value">{{ device?.type }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">IP地址</text>
            <text class="info-value">{{ device?.ip || '未知' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">版本信息</text>
            <text class="info-value">{{ device?.version || '未知' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">用户代理</text>
            <text class="info-value ellipsis">{{ device?.user_agent || '未知' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">注册时间</text>
            <text class="info-value">{{ formatDate(device?.created_at) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">最后活跃</text>
            <text class="info-value">{{ formatRelativeTime(device?.last_active) }}</text>
          </view>
        </view>
      </view>

      <!-- 快速操作 -->
      <view class="detail-section">
        <view class="section-header">
          <text class="section-title">快速操作</text>
        </view>
        <view class="action-grid">
          <view 
            class="action-item"
            :class="{ disabled: device?.status === 0 }"
            @click="createTask"
          >
            <view class="action-icon">
              <uni-icons type="plus" size="20" color="#007aff" />
            </view>
            <text class="action-text">创建任务</text>
          </view>
          <view class="action-item" @click="viewTasks">
            <view class="action-icon">
              <uni-icons type="list" size="20" color="#4cd964" />
            </view>
            <text class="action-text">查看任务</text>
          </view>
          <view class="action-item" @click="viewLogs">
            <view class="action-icon">
              <uni-icons type="document" size="20" color="#f0ad4e" />
            </view>
            <text class="action-text">查看日志</text>
          </view>
          <view class="action-item" @click="remoteControl">
            <view class="action-icon">
              <uni-icons type="gear" size="20" color="#dd524d" />
            </view>
            <text class="action-text">远程控制</text>
          </view>
        </view>
      </view>

      <!-- 最近任务 -->
      <view class="detail-section">
        <view class="section-header">
          <text class="section-title">最近任务</text>
          <text class="section-more" @click="viewTasks">查看全部</text>
        </view>
        <view class="task-list">
          <view 
            v-for="task in recentTasks" 
            :key="task.id"
            class="task-item"
            @click="goToTaskDetail(task)"
          >
            <view class="task-info">
              <text class="task-name">{{ task.name }}</text>
              <text class="task-time">{{ formatRelativeTime(task.created_at) }}</text>
            </view>
            <view 
              class="task-status" 
              :style="{ backgroundColor: getTaskStatusColor(task.status) }"
            >
              {{ getTaskStatusText(task.status) }}
            </view>
          </view>
          <view v-if="recentTasks.length === 0" class="empty-tasks">
            <uni-icons type="info" size="30" color="#ccc" />
            <text class="empty-text">暂无任务记录</text>
          </view>
        </view>
      </view>

      <!-- 设备统计 -->
      <view class="detail-section">
        <view class="section-header">
          <text class="section-title">设备统计</text>
        </view>
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">{{ deviceStats.totalTasks }}</text>
            <text class="stat-label">总任务数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ deviceStats.successTasks }}</text>
            <text class="stat-label">成功任务</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ deviceStats.failedTasks }}</text>
            <text class="stat-label">失败任务</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ deviceStats.onlineTime }}</text>
            <text class="stat-label">在线时长</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <button class="action-btn secondary" @click="editDevice">
        <uni-icons type="compose" size="18" color="#8f8f94" />
        <text>编辑</text>
      </button>
      <button 
        class="action-btn primary"
        :class="{ disabled: device?.status === 0 }"
        @click="createTask"
      >
        <uni-icons type="plus" size="18" color="#fff" />
        <text>创建任务</text>
      </button>
      <button class="action-btn danger" @click="deleteDevice">
        <uni-icons type="trash" size="18" color="#dd524d" />
        <text>删除</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { 
  formatDate, 
  formatRelativeTime, 
  getDeviceStatusText, 
  getTaskStatusText, 
  getTaskStatusColor,
  showError, 
  showSuccess, 
  showConfirm 
} from '@/utils/index'

// 状态管理
const deviceStore = useDeviceStore()

// 响应式数据
const device = ref<any>(null)
const recentTasks = ref<any[]>([])
const loading = ref(false)

// 设备ID
const deviceId = ref<string>('')

// 计算属性
const deviceStats = computed(() => ({
  totalTasks: recentTasks.value.length,
  successTasks: recentTasks.value.filter(task => task.status === 2).length,
  failedTasks: recentTasks.value.filter(task => task.status === 3).length,
  onlineTime: '24小时' // 这里应该从API获取真实数据
}))

// 获取状态样式类
const getStatusClass = (status: number) => {
  const classMap: Record<number, string> = {
    0: 'offline',
    1: 'online',
    2: 'busy'
  }
  return classMap[status] || 'offline'
}

// 页面加载
onMounted(() => {
  // 获取路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options as any
  
  if (options.id) {
    deviceId.value = options.id
    loadDeviceDetail()
    loadRecentTasks()
  }
})

// 加载设备详情
const loadDeviceDetail = async () => {
  if (!deviceId.value) return

  try {
    loading.value = true
    const deviceInfo = await deviceStore.getDeviceInfo(deviceId.value)
    if (deviceInfo) {
      device.value = deviceInfo
    }
  } catch (error) {
    showError('加载设备详情失败')
  } finally {
    loading.value = false
  }
}

// 加载最近任务（模拟数据）
const loadRecentTasks = () => {
  // 这里应该调用真实的API获取设备相关的任务
  recentTasks.value = [
    {
      id: 1,
      name: '自动签到任务',
      status: 2,
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 2,
      name: '数据采集任务',
      status: 1,
      created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 3,
      name: '定时脚本',
      status: 3,
      created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
    }
  ]
}

// 操作方法
const createTask = () => {
  if (device.value?.status === 0) {
    showError('设备离线，无法创建任务')
    return
  }
  
  uni.navigateTo({
    url: `/pages/tasks/create?deviceId=${deviceId.value}`
  })
}

const viewTasks = () => {
  uni.navigateTo({
    url: `/pages/tasks/list?deviceId=${deviceId.value}`
  })
}

const viewLogs = () => {
  uni.navigateTo({
    url: `/pages/monitor/index?deviceId=${deviceId.value}`
  })
}

const remoteControl = () => {
  if (device.value?.status === 0) {
    showError('设备离线，无法进行远程控制')
    return
  }
  
  uni.showToast({
    title: '远程控制功能开发中',
    icon: 'none'
  })
}

const editDevice = () => {
  uni.showModal({
    title: '修改设备名称',
    editable: true,
    placeholderText: device.value?.name,
    success: async (res) => {
      if (res.confirm && res.content) {
        // 这里应该调用更新设备名称的API
        showSuccess('设备名称修改成功')
        device.value.name = res.content
      }
    }
  })
}

const deleteDevice = async () => {
  try {
    const confirmed = await showConfirm('确定要删除该设备吗？此操作不可恢复。')
    if (!confirmed) return

    const success = await deviceStore.deleteDevice(deviceId.value)
    
    if (success) {
      showSuccess('设备删除成功')
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    }
  } catch (error) {
    showError('删除设备失败')
  }
}

const goToTaskDetail = (task: any) => {
  uni.navigateTo({
    url: `/pages/tasks/detail?id=${task.id}`
  })
}
</script>

<style lang="scss" scoped>
.device-detail {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.device-info {
  background: #fff;
  margin-bottom: 20rpx;
}

.info-header {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
}

.device-avatar {
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.device-basic {
  flex: 1;
}

.device-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.device-id {
  font-size: 26rpx;
  color: #8f8f94;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;

  &.online {
    background: #4cd964;
  }

  &.offline {
    background: #8f8f94;
  }

  &.busy {
    background: #f0ad4e;
  }
}

.status-text {
  font-size: 26rpx;
  color: #8f8f94;
}

.detail-sections {
  padding: 0 20rpx;
}

.detail-section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #007aff;
}

.info-list {
  padding: 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 28rpx;
  color: #333;
}

.info-value {
  font-size: 28rpx;
  color: #8f8f94;
  max-width: 400rpx;
  text-align: right;

  &.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 30rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:active {
    transform: scale(0.95);
    background: #e9ecef;
  }

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 24rpx;
  color: #333;
}

.task-list {
  padding: 30rpx;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8f9fa;
  }
}

.task-info {
  flex: 1;
}

.task-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.task-time {
  font-size: 24rpx;
  color: #8f8f94;
}

.task-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
}

.empty-tasks {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  display: block;
  margin-top: 15rpx;
  font-size: 26rpx;
  color: #8f8f94;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #8f8f94;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 2rpx solid #e5e5e5;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  transition: all 0.3s;

  &.primary {
    background: #007aff;
    color: #fff;

    &:active {
      background: #0056cc;
    }

    &.disabled {
      background: #ccc;
      color: #999;
    }
  }

  &.secondary {
    background: #f5f5f5;
    color: #8f8f94;

    &:active {
      background: #e5e5e5;
    }
  }

  &.danger {
    background: #fff;
    color: #dd524d;
    border: 2rpx solid #dd524d;

    &:active {
      background: #dd524d;
      color: #fff;
    }
  }
}
</style>
