package config

import (
	"time"

	"github.com/spf13/viper"
)

type Config struct {
	Server struct {
		Port string `mapstructure:"port"`
		Mode string `mapstructure:"mode"`
		// 当前环境，用于判断webhook处理逻辑
		Env string `mapstructure:"env"`
	} `mapstructure:"server"`

	JWT struct {
		SecretKey   string        `mapstructure:"secret_key"`
		ExpiresTime time.Duration `mapstructure:"expires_time"`
	} `mapstructure:"jwt"`

	Database struct {
		Driver       string `mapstructure:"driver"`
		Host         string `mapstructure:"host"`
		Port         int    `mapstructure:"port"`
		Username     string `mapstructure:"username"`
		Password     string `mapstructure:"password"`
		DBName       string `mapstructure:"dbname"`
		MaxIdleConns int    `mapstructure:"max_idle_conns"`
		MaxOpenConns int    `mapstructure:"max_open_conns"`
	} `mapstructure:"database"`

	GitHub struct {
		Token string `mapstructure:"token"`
		// Webhook密钥，用于验证请求签名
		WebhookSecret string `mapstructure:"webhook_secret"`
		// 环境对应的分支映射
		BranchMapping map[string]string `mapstructure:"branch_mapping"`
	} `mapstructure:"github"`
}

// Load 加载配置
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	// 设置默认值
	viper.SetDefault("server.port", "9090")
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("server.env", "dev") // 默认环境为dev
	viper.SetDefault("jwt.expires_time", time.Hour*24)
	viper.SetDefault("database.max_idle_conns", 10)
	viper.SetDefault("database.max_open_conns", 100)
	viper.SetDefault("github.webhook_secret", "") // 默认webhook密钥为空字符串

	config := &Config{}
	if err := viper.Unmarshal(config); err != nil {
		return nil, err
	}

	return config, nil
}
