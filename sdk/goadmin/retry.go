package goadmin

import (
	"context"
	"fmt"
	"math"
	"time"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries      int           // 最大重试次数
	InitialInterval time.Duration // 初始重试间隔
	MaxInterval     time.Duration // 最大重试间隔
	Multiplier      float64       // 重试间隔倍数
	EnableJitter    bool          // 是否启用抖动
}

// DefaultRetryConfig 默认重试配置
func DefaultRetryConfig() RetryConfig {
	return RetryConfig{
		MaxRetries:      3,
		InitialInterval: 1 * time.Second,
		MaxInterval:     30 * time.Second,
		Multiplier:      2.0,
		EnableJitter:    true,
	}
}

// RetryableClient 支持重试的客户端
type RetryableClient struct {
	*Client
	retryConfig RetryConfig
}

// NewRetryableClient 创建支持重试的客户端
func NewRetryableClient(config Config, retryConfig ...RetryConfig) *RetryableClient {
	client := NewClient(config)
	
	var retry RetryConfig
	if len(retryConfig) > 0 {
		retry = retryConfig[0]
	} else {
		retry = DefaultRetryConfig()
	}

	return &RetryableClient{
		Client:      client,
		retryConfig: retry,
	}
}

// RechargeWithRetry 带重试的充值操作
func (c *RetryableClient) RechargeWithRetry(ctx context.Context, targetCardKey, newCardKey string) (*RechargeResponse, error) {
	var lastErr error
	
	for attempt := 0; attempt <= c.retryConfig.MaxRetries; attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		// 执行充值操作
		resp, err := c.Client.Recharge(targetCardKey, newCardKey)
		if err == nil {
			return resp, nil
		}

		lastErr = err

		// 如果是最后一次尝试，直接返回错误
		if attempt == c.retryConfig.MaxRetries {
			break
		}

		// 检查是否为可重试错误
		if !IsRetryableError(err) {
			break
		}

		// 计算重试间隔
		interval := c.calculateRetryInterval(attempt)
		
		// 等待重试间隔
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(interval):
		}
	}

	return nil, fmt.Errorf("重试失败，最后错误: %v", lastErr)
}

// calculateRetryInterval 计算重试间隔
func (c *RetryableClient) calculateRetryInterval(attempt int) time.Duration {
	interval := time.Duration(float64(c.retryConfig.InitialInterval) * math.Pow(c.retryConfig.Multiplier, float64(attempt)))
	
	// 限制最大间隔
	if interval > c.retryConfig.MaxInterval {
		interval = c.retryConfig.MaxInterval
	}

	// 添加抖动
	if c.retryConfig.EnableJitter {
		jitter := time.Duration(float64(interval) * 0.1 * (2*time.Now().UnixNano()%2 - 1))
		interval += jitter
	}

	return interval
}

// BatchRecharge 批量充值
type BatchRechargeRequest struct {
	Items []RechargeRequest `json:"items"`
}

// BatchRechargeResponse 批量充值响应
type BatchRechargeResponse struct {
	SuccessCount int                    `json:"success_count"`
	FailedCount  int                    `json:"failed_count"`
	Results      []BatchRechargeResult  `json:"results"`
}

// BatchRechargeResult 批量充值结果
type BatchRechargeResult struct {
	Index         int    `json:"index"`
	Success       bool   `json:"success"`
	Message       string `json:"message,omitempty"`
	TargetCardKey string `json:"target_card_key"`
	NewCardKey    string `json:"new_card_key"`
}

// BatchRechargeWithRetry 批量充值（带重试）
func (c *RetryableClient) BatchRechargeWithRetry(ctx context.Context, requests []RechargeRequest) (*BatchRechargeResponse, error) {
	results := make([]BatchRechargeResult, len(requests))
	successCount := 0
	failedCount := 0

	// 并发执行充值操作
	type result struct {
		index int
		resp  *RechargeResponse
		err   error
	}

	resultChan := make(chan result, len(requests))

	// 启动goroutine执行充值
	for i, req := range requests {
		go func(index int, request RechargeRequest) {
			resp, err := c.RechargeWithRetry(ctx, request.TargetCardKey, request.NewCardKey)
			resultChan <- result{
				index: index,
				resp:  resp,
				err:   err,
			}
		}(i, req)
	}

	// 收集结果
	for i := 0; i < len(requests); i++ {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case res := <-resultChan:
			if res.err != nil {
				results[res.index] = BatchRechargeResult{
					Index:         res.index,
					Success:       false,
					Message:       res.err.Error(),
					TargetCardKey: requests[res.index].TargetCardKey,
					NewCardKey:    requests[res.index].NewCardKey,
				}
				failedCount++
			} else {
				results[res.index] = BatchRechargeResult{
					Index:         res.index,
					Success:       true,
					Message:       res.resp.Message,
					TargetCardKey: requests[res.index].TargetCardKey,
					NewCardKey:    requests[res.index].NewCardKey,
				}
				successCount++
			}
		}
	}

	return &BatchRechargeResponse{
		SuccessCount: successCount,
		FailedCount:  failedCount,
		Results:      results,
	}, nil
}
