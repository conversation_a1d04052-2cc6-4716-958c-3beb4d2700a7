package goadmin

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"
)

// Client 第三方API客户端
type Client struct {
	baseURL   string
	secretID  string
	secretKey string
	timeout   time.Duration
	client    *http.Client
}

// Config 客户端配置
type Config struct {
	BaseURL   string        // API基础URL，如: https://api.example.com
	SecretID  string        // SecretId
	SecretKey string        // SecretKey
	Timeout   time.Duration // 请求超时时间，默认30秒
}

// NewClient 创建新的API客户端
func NewClient(config Config) *Client {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	return &Client{
		baseURL:   strings.TrimRight(config.BaseURL, "/"),
		secretID:  config.SecretID,
		secretKey: config.SecretKey,
		timeout:   config.Timeout,
		client: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// APIResponse 通用API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// APIError API错误
type APIError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func (e *APIError) Error() string {
	return fmt.Sprintf("API Error %d: %s", e.Code, e.Message)
}

// RechargeRequest 充值请求
type RechargeRequest struct {
	TargetCardKey string `json:"target_card_key"` // 目标卡密
	NewCardKey    string `json:"new_card_key"`    // 新卡密
}

// RechargeResponse 充值响应
type RechargeResponse struct {
	Message string `json:"message"`
}

// generateSignature 生成请求签名
func (c *Client) generateSignature(method, uri string, params map[string]string, timestamp int64) string {
	// 构造待签名字符串
	stringToSign := c.buildStringToSign(method, uri, params, timestamp)

	// 使用HMAC-SHA256生成签名
	h := hmac.New(sha256.New, []byte(c.secretKey))
	h.Write([]byte(stringToSign))
	signature := hex.EncodeToString(h.Sum(nil))

	return signature
}

// buildStringToSign 构造待签名字符串
func (c *Client) buildStringToSign(method, uri string, params map[string]string, timestamp int64) string {
	// 1. HTTP方法（大写）
	method = strings.ToUpper(method)

	// 2. URI路径
	if uri == "" {
		uri = "/"
	}

	// 3. 添加时间戳到参数中
	if params == nil {
		params = make(map[string]string)
	}
	params["timestamp"] = fmt.Sprintf("%d", timestamp)

	// 4. 对参数进行排序和编码
	canonicalQueryString := c.buildCanonicalQueryString(params)

	// 5. 构造最终的待签名字符串
	stringToSign := fmt.Sprintf("%s\n%s\n%s", method, uri, canonicalQueryString)

	return stringToSign
}

// buildCanonicalQueryString 构造规范化查询字符串
func (c *Client) buildCanonicalQueryString(params map[string]string) string {
	if len(params) == 0 {
		return ""
	}

	// 1. 对参数名进行排序
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 2. 构造查询字符串
	var parts []string
	for _, key := range keys {
		value := params[key]
		// URL编码
		encodedKey := url.QueryEscape(key)
		encodedValue := url.QueryEscape(value)
		parts = append(parts, fmt.Sprintf("%s=%s", encodedKey, encodedValue))
	}

	return strings.Join(parts, "&")
}

// doRequest 执行HTTP请求
func (c *Client) doRequest(method, path string, body interface{}) (*APIResponse, error) {
	// 构造完整URL
	fullURL := c.baseURL + path

	// 序列化请求体
	var bodyBytes []byte
	var err error
	params := make(map[string]string)

	if body != nil {
		bodyBytes, err = json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("序列化请求体失败: %v", err)
		}

		// 解析请求参数用于签名
		var bodyParams map[string]interface{}
		if err := json.Unmarshal(bodyBytes, &bodyParams); err == nil {
			for k, v := range bodyParams {
				if str, ok := v.(string); ok {
					params[k] = str
				} else {
					if jsonBytes, err := json.Marshal(v); err == nil {
						params[k] = string(jsonBytes)
					}
				}
			}
		}
	}

	// 生成时间戳和签名
	timestamp := time.Now().Unix()
	signature := c.generateSignature(method, path, params, timestamp)

	// 创建HTTP请求
	var req *http.Request
	if bodyBytes != nil {
		req, err = http.NewRequest(method, fullURL, bytes.NewBuffer(bodyBytes))
	} else {
		req, err = http.NewRequest(method, fullURL, nil)
	}
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Secret-Id", c.secretID)
	req.Header.Set("X-Timestamp", fmt.Sprintf("%d", timestamp))
	req.Header.Set("X-Signature", signature)

	// 执行请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求执行失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查API错误
	if apiResp.Code != 200 {
		return &apiResp, &APIError{
			Code:    apiResp.Code,
			Message: apiResp.Message,
		}
	}

	return &apiResp, nil
}

// Recharge 执行充值操作
func (c *Client) Recharge(targetCardKey, newCardKey string) (*RechargeResponse, error) {
	req := RechargeRequest{
		TargetCardKey: targetCardKey,
		NewCardKey:    newCardKey,
	}

	resp, err := c.doRequest("POST", "/api/v1/third/recharge", req)
	if err != nil {
		return nil, err
	}

	// 解析响应数据
	var rechargeResp RechargeResponse
	if resp.Data != nil {
		dataBytes, err := json.Marshal(resp.Data)
		if err != nil {
			return nil, fmt.Errorf("解析响应数据失败: %v", err)
		}
		if err := json.Unmarshal(dataBytes, &rechargeResp); err != nil {
			return nil, fmt.Errorf("解析充值响应失败: %v", err)
		}
	}

	return &rechargeResp, nil
}
