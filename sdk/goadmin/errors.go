package goadmin

import "fmt"

// 错误码常量
const (
	// 通用错误码
	ErrCodeSuccess          = 200
	ErrCodeInvalidParams    = 400
	ErrCodeUnauthorized     = 401
	ErrCodeForbidden        = 403
	ErrCodeNotFound         = 404
	ErrCodeInternalError    = 500
	ErrCodeServiceUnavailable = 503

	// 业务错误码
	ErrCodeCardNotFound     = 1001
	ErrCodeCardDisabled     = 1002
	ErrCodeCardExpired      = 1003
	ErrCodeInsufficientBalance = 1004
	ErrCodeSignatureInvalid = 1005
	ErrCodeTimestampExpired = 1006
	ErrCodePermissionDenied = 1007
)

// 错误消息映射
var errorMessages = map[int]string{
	ErrCodeSuccess:            "成功",
	ErrCodeInvalidParams:      "无效的请求参数",
	ErrCodeUnauthorized:       "未授权",
	ErrCodeForbidden:          "禁止访问",
	ErrCodeNotFound:           "资源不存在",
	ErrCodeInternalError:      "内部服务器错误",
	ErrCodeServiceUnavailable: "服务不可用",
	ErrCodeCardNotFound:       "卡密不存在",
	ErrCodeCardDisabled:       "卡密已被禁用",
	ErrCodeCardExpired:        "卡密已过期",
	ErrCodeInsufficientBalance: "余额不足",
	ErrCodeSignatureInvalid:   "签名验证失败",
	ErrCodeTimestampExpired:   "时间戳已过期",
	ErrCodePermissionDenied:   "权限不足",
}

// GetErrorMessage 获取错误消息
func GetErrorMessage(code int) string {
	if msg, exists := errorMessages[code]; exists {
		return msg
	}
	return fmt.Sprintf("未知错误 (代码: %d)", code)
}

// IsRetryableError 判断错误是否可重试
func IsRetryableError(err error) bool {
	if apiErr, ok := err.(*APIError); ok {
		switch apiErr.Code {
		case ErrCodeInternalError, ErrCodeServiceUnavailable:
			return true
		case ErrCodeTimestampExpired:
			return true // 时间戳过期可以重试
		}
	}
	return false
}

// IsAuthError 判断是否为认证错误
func IsAuthError(err error) bool {
	if apiErr, ok := err.(*APIError); ok {
		switch apiErr.Code {
		case ErrCodeUnauthorized, ErrCodeSignatureInvalid, ErrCodePermissionDenied:
			return true
		}
	}
	return false
}

// IsBusinessError 判断是否为业务错误
func IsBusinessError(err error) bool {
	if apiErr, ok := err.(*APIError); ok {
		return apiErr.Code >= 1000
	}
	return false
}
