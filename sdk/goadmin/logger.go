package goadmin

import (
	"fmt"
	"log"
	"os"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	LogLevelDebug LogLevel = iota
	LogLevelInfo
	LogLevelWarn
	LogLevelError
)

// Logger 日志接口
type Logger interface {
	Debug(format string, args ...interface{})
	Info(format string, args ...interface{})
	Warn(format string, args ...interface{})
	Error(format string, args ...interface{})
}

// DefaultLogger 默认日志实现
type DefaultLogger struct {
	level  LogLevel
	logger *log.Logger
}

// NewDefaultLogger 创建默认日志器
func NewDefaultLogger(level LogLevel) *DefaultLogger {
	return &DefaultLogger{
		level:  level,
		logger: log.New(os.Stdout, "[GoAdmin-SDK] ", log.LstdFlags),
	}
}

func (l *DefaultLogger) Debug(format string, args ...interface{}) {
	if l.level <= LogLevelDebug {
		l.logger.Printf("[DEBUG] "+format, args...)
	}
}

func (l *DefaultLogger) Info(format string, args ...interface{}) {
	if l.level <= LogLevelInfo {
		l.logger.Printf("[INFO] "+format, args...)
	}
}

func (l *DefaultLogger) Warn(format string, args ...interface{}) {
	if l.level <= LogLevelWarn {
		l.logger.Printf("[WARN] "+format, args...)
	}
}

func (l *DefaultLogger) Error(format string, args ...interface{}) {
	if l.level <= LogLevelError {
		l.logger.Printf("[ERROR] "+format, args...)
	}
}

// NoopLogger 空日志实现
type NoopLogger struct{}

func (l *NoopLogger) Debug(format string, args ...interface{}) {}
func (l *NoopLogger) Info(format string, args ...interface{})  {}
func (l *NoopLogger) Warn(format string, args ...interface{})  {}
func (l *NoopLogger) Error(format string, args ...interface{}) {}

// ClientWithLogger 带日志的客户端
type ClientWithLogger struct {
	*Client
	logger Logger
}

// NewClientWithLogger 创建带日志的客户端
func NewClientWithLogger(config Config, logger Logger) *ClientWithLogger {
	if logger == nil {
		logger = &NoopLogger{}
	}

	client := NewClient(config)
	return &ClientWithLogger{
		Client: client,
		logger: logger,
	}
}

// doRequestWithLog 执行带日志的请求
func (c *ClientWithLogger) doRequestWithLog(method, path string, body interface{}) (*APIResponse, error) {
	start := time.Now()
	
	c.logger.Debug("开始请求: %s %s", method, path)
	if body != nil {
		c.logger.Debug("请求体: %+v", body)
	}

	resp, err := c.Client.doRequest(method, path, body)
	
	duration := time.Since(start)
	
	if err != nil {
		c.logger.Error("请求失败: %s %s, 耗时: %v, 错误: %v", method, path, duration, err)
		return resp, err
	}

	c.logger.Info("请求成功: %s %s, 耗时: %v, 响应码: %d", method, path, duration, resp.Code)
	c.logger.Debug("响应数据: %+v", resp.Data)

	return resp, nil
}

// RechargeWithLog 带日志的充值操作
func (c *ClientWithLogger) RechargeWithLog(targetCardKey, newCardKey string) (*RechargeResponse, error) {
	c.logger.Info("开始充值操作: 目标卡密=%s, 新卡密=%s", maskCardKey(targetCardKey), maskCardKey(newCardKey))
	
	req := RechargeRequest{
		TargetCardKey: targetCardKey,
		NewCardKey:    newCardKey,
	}

	resp, err := c.doRequestWithLog("POST", "/api/v1/third/recharge", req)
	if err != nil {
		c.logger.Error("充值失败: %v", err)
		return nil, err
	}

	// 解析响应数据
	var rechargeResp RechargeResponse
	if resp.Data != nil {
		dataBytes, err := json.Marshal(resp.Data)
		if err != nil {
			c.logger.Error("解析响应数据失败: %v", err)
			return nil, fmt.Errorf("解析响应数据失败: %v", err)
		}
		if err := json.Unmarshal(dataBytes, &rechargeResp); err != nil {
			c.logger.Error("解析充值响应失败: %v", err)
			return nil, fmt.Errorf("解析充值响应失败: %v", err)
		}
	}

	c.logger.Info("充值成功: %s", rechargeResp.Message)
	return &rechargeResp, nil
}

// maskCardKey 遮蔽卡密敏感信息
func maskCardKey(cardKey string) string {
	if len(cardKey) <= 8 {
		return "****"
	}
	return cardKey[:4] + "****" + cardKey[len(cardKey)-4:]
}

// DebugClient 调试客户端
type DebugClient struct {
	*ClientWithLogger
	enableRequestDump  bool
	enableResponseDump bool
}

// NewDebugClient 创建调试客户端
func NewDebugClient(config Config) *DebugClient {
	logger := NewDefaultLogger(LogLevelDebug)
	clientWithLogger := NewClientWithLogger(config, logger)
	
	return &DebugClient{
		ClientWithLogger:   clientWithLogger,
		enableRequestDump:  true,
		enableResponseDump: true,
	}
}

// EnableRequestDump 启用请求转储
func (c *DebugClient) EnableRequestDump(enable bool) {
	c.enableRequestDump = enable
}

// EnableResponseDump 启用响应转储
func (c *DebugClient) EnableResponseDump(enable bool) {
	c.enableResponseDump = enable
}

// DumpSignature 转储签名信息
func (c *DebugClient) DumpSignature(method, path string, params map[string]string, timestamp int64) {
	if !c.enableRequestDump {
		return
	}

	signature := c.generateSignature(method, path, params, timestamp)
	stringToSign := c.buildStringToSign(method, path, params, timestamp)

	c.logger.Debug("=== 签名调试信息 ===")
	c.logger.Debug("SecretID: %s", c.secretID)
	c.logger.Debug("SecretKey: %s", maskSecretKey(c.secretKey))
	c.logger.Debug("时间戳: %d", timestamp)
	c.logger.Debug("待签名字符串:\n%s", stringToSign)
	c.logger.Debug("生成的签名: %s", signature)
	c.logger.Debug("==================")
}

// maskSecretKey 遮蔽密钥敏感信息
func maskSecretKey(secretKey string) string {
	if len(secretKey) <= 16 {
		return "****"
	}
	return secretKey[:8] + "****" + secretKey[len(secretKey)-8:]
}
