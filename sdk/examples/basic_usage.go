package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/your-org/goadmin-sdk/goadmin"
)

func main() {
	// 基本使用示例
	basicUsageExample()
	
	// 重试机制示例
	retryExample()
	
	// 批量操作示例
	batchExample()
	
	// 调试模式示例
	debugExample()
}

// basicUsageExample 基本使用示例
func basicUsageExample() {
	fmt.Println("=== 基本使用示例 ===")
	
	// 创建客户端
	client := goadmin.NewClient(goadmin.Config{
		BaseURL:   "https://api.example.com",
		SecretID:  "AKID1234567890abcdef1234567890ab",
		SecretKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
		Timeout:   30 * time.Second,
	})

	// 执行充值操作
	resp, err := client.Recharge("TARGET_CARD_KEY", "SOURCE_CARD_KEY")
	if err != nil {
		// 检查错误类型
		if goadmin.IsAuthError(err) {
			log.Printf("认证错误: %v", err)
		} else if goadmin.IsBusinessError(err) {
			log.Printf("业务错误: %v", err)
		} else {
			log.Printf("其他错误: %v", err)
		}
		return
	}

	fmt.Printf("充值成功: %s\n", resp.Message)
}

// retryExample 重试机制示例
func retryExample() {
	fmt.Println("\n=== 重试机制示例 ===")
	
	// 创建支持重试的客户端
	client := goadmin.NewRetryableClient(goadmin.Config{
		BaseURL:   "https://api.example.com",
		SecretID:  "AKID1234567890abcdef1234567890ab",
		SecretKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
	}, goadmin.RetryConfig{
		MaxRetries:      3,
		InitialInterval: 1 * time.Second,
		MaxInterval:     10 * time.Second,
		Multiplier:      2.0,
		EnableJitter:    true,
	})

	// 创建上下文，设置总超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	// 执行带重试的充值操作
	resp, err := client.RechargeWithRetry(ctx, "TARGET_CARD_KEY", "SOURCE_CARD_KEY")
	if err != nil {
		log.Printf("重试充值失败: %v", err)
		return
	}

	fmt.Printf("重试充值成功: %s\n", resp.Message)
}

// batchExample 批量操作示例
func batchExample() {
	fmt.Println("\n=== 批量操作示例 ===")
	
	client := goadmin.NewRetryableClient(goadmin.Config{
		BaseURL:   "https://api.example.com",
		SecretID:  "AKID1234567890abcdef1234567890ab",
		SecretKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
	})

	// 准备批量充值请求
	requests := []goadmin.RechargeRequest{
		{TargetCardKey: "TARGET1", NewCardKey: "SOURCE1"},
		{TargetCardKey: "TARGET2", NewCardKey: "SOURCE2"},
		{TargetCardKey: "TARGET3", NewCardKey: "SOURCE3"},
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 执行批量充值
	resp, err := client.BatchRechargeWithRetry(ctx, requests)
	if err != nil {
		log.Printf("批量充值失败: %v", err)
		return
	}

	fmt.Printf("批量充值完成: 成功 %d 个, 失败 %d 个\n", resp.SuccessCount, resp.FailedCount)
	
	// 打印详细结果
	for _, result := range resp.Results {
		status := "成功"
		if !result.Success {
			status = "失败"
		}
		fmt.Printf("  [%d] %s -> %s: %s (%s)\n", 
			result.Index, 
			maskCardKey(result.NewCardKey), 
			maskCardKey(result.TargetCardKey), 
			status, 
			result.Message)
	}
}

// debugExample 调试模式示例
func debugExample() {
	fmt.Println("\n=== 调试模式示例 ===")
	
	// 创建调试客户端
	client := goadmin.NewDebugClient(goadmin.Config{
		BaseURL:   "https://api.example.com",
		SecretID:  "AKID1234567890abcdef1234567890ab",
		SecretKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
	})

	// 启用详细调试信息
	client.EnableRequestDump(true)
	client.EnableResponseDump(true)

	// 执行充值操作（会输出详细的调试信息）
	resp, err := client.RechargeWithLog("TARGET_CARD_KEY", "SOURCE_CARD_KEY")
	if err != nil {
		log.Printf("调试充值失败: %v", err)
		return
	}

	fmt.Printf("调试充值成功: %s\n", resp.Message)
}

// maskCardKey 遮蔽卡密敏感信息
func maskCardKey(cardKey string) string {
	if len(cardKey) <= 8 {
		return "****"
	}
	return cardKey[:4] + "****" + cardKey[len(cardKey)-4:]
}

// 错误处理示例
func errorHandlingExample() {
	fmt.Println("\n=== 错误处理示例 ===")
	
	client := goadmin.NewClient(goadmin.Config{
		BaseURL:   "https://api.example.com",
		SecretID:  "INVALID_SECRET_ID",
		SecretKey: "INVALID_SECRET_KEY",
	})

	_, err := client.Recharge("TARGET", "SOURCE")
	if err != nil {
		// 检查具体错误类型
		if apiErr, ok := err.(*goadmin.APIError); ok {
			fmt.Printf("API错误码: %d\n", apiErr.Code)
			fmt.Printf("错误消息: %s\n", apiErr.Message)
			fmt.Printf("标准错误消息: %s\n", goadmin.GetErrorMessage(apiErr.Code))
			
			// 根据错误类型进行不同处理
			switch {
			case goadmin.IsAuthError(err):
				fmt.Println("处理方案: 检查SecretID和SecretKey是否正确")
			case goadmin.IsRetryableError(err):
				fmt.Println("处理方案: 可以重试此操作")
			case goadmin.IsBusinessError(err):
				fmt.Println("处理方案: 检查业务参数是否正确")
			default:
				fmt.Println("处理方案: 联系技术支持")
			}
		}
	}
}
