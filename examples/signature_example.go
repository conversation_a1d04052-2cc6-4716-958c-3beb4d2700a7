package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"time"
)

// 第三方API签名生成示例
// 参考阿里云API签名算法

func main() {
	// 示例参数
	secretID := "AKID1234567890abcdef1234567890ab"
	secretKey := "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
	
	// 请求参数
	method := "POST"
	uri := "/api/v1/third/recharge"
	params := map[string]string{
		"target_card_key": "TARGET_CARD",
		"new_card_key":    "SOURCE_CARD",
	}
	timestamp := time.Now().Unix()
	
	// 生成签名
	signature := generateSignature(method, uri, params, secretKey, timestamp)
	
	fmt.Printf("请求示例:\n")
	fmt.Printf("Method: %s\n", method)
	fmt.Printf("URI: %s\n", uri)
	fmt.Printf("Headers:\n")
	fmt.Printf("  X-Secret-Id: %s\n", secretID)
	fmt.Printf("  X-Timestamp: %d\n", timestamp)
	fmt.Printf("  X-Signature: %s\n", signature)
	fmt.Printf("  Content-Type: application/json\n")
	fmt.Printf("\nBody:\n")
	fmt.Printf("{\n")
	fmt.Printf("  \"target_card_key\": \"%s\",\n", params["target_card_key"])
	fmt.Printf("  \"new_card_key\": \"%s\"\n", params["new_card_key"])
	fmt.Printf("}\n")
	
	// curl 示例
	fmt.Printf("\ncurl 示例:\n")
	fmt.Printf("curl -X POST '%s' \\\n", "https://your-domain.com"+uri)
	fmt.Printf("  -H 'X-Secret-Id: %s' \\\n", secretID)
	fmt.Printf("  -H 'X-Timestamp: %d' \\\n", timestamp)
	fmt.Printf("  -H 'X-Signature: %s' \\\n", signature)
	fmt.Printf("  -H 'Content-Type: application/json' \\\n")
	fmt.Printf("  -d '{\n")
	fmt.Printf("    \"target_card_key\": \"%s\",\n", params["target_card_key"])
	fmt.Printf("    \"new_card_key\": \"%s\"\n", params["new_card_key"])
	fmt.Printf("  }'\n")
}

// generateSignature 生成请求签名
func generateSignature(method, uri string, params map[string]string, secretKey string, timestamp int64) string {
	// 1. 构造待签名字符串
	stringToSign := buildStringToSign(method, uri, params, timestamp)
	
	fmt.Printf("待签名字符串:\n%s\n\n", stringToSign)
	
	// 2. 使用HMAC-SHA256生成签名
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(stringToSign))
	signature := hex.EncodeToString(h.Sum(nil))
	
	return signature
}

// buildStringToSign 构造待签名字符串
func buildStringToSign(method, uri string, params map[string]string, timestamp int64) string {
	// 1. HTTP方法（大写）
	method = strings.ToUpper(method)
	
	// 2. URI路径
	if uri == "" {
		uri = "/"
	}
	
	// 3. 添加时间戳到参数中
	if params == nil {
		params = make(map[string]string)
	}
	params["timestamp"] = fmt.Sprintf("%d", timestamp)
	
	// 4. 对参数进行排序和编码
	canonicalQueryString := buildCanonicalQueryString(params)
	
	// 5. 构造最终的待签名字符串
	stringToSign := fmt.Sprintf("%s\n%s\n%s", method, uri, canonicalQueryString)
	
	return stringToSign
}

// buildCanonicalQueryString 构造规范化查询字符串
func buildCanonicalQueryString(params map[string]string) string {
	if len(params) == 0 {
		return ""
	}
	
	// 1. 对参数名进行排序
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	
	// 2. 构造查询字符串
	var parts []string
	for _, key := range keys {
		value := params[key]
		// URL编码
		encodedKey := url.QueryEscape(key)
		encodedValue := url.QueryEscape(value)
		parts = append(parts, fmt.Sprintf("%s=%s", encodedKey, encodedValue))
	}
	
	return strings.Join(parts, "&")
}
