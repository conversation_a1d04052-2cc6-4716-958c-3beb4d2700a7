package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// DeviceAuth 设备认证中间件
func DeviceAuth(jwtService *service.JWTService, deviceService *service.DeviceService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取设备ID
		deviceID := c.GetHeader("Device-ID")
		if deviceID == "" {
			response.Fail(c, response.UNAUTHORIZED, "缺少设备ID")
			c.Abort()
			return
		}

		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			response.Fail(c, response.UNAUTHORIZED, "缺少认证令牌")
			c.Abort()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			response.Fail(c, response.UNAUTHORIZED, "无效的认证令牌格式")
			c.Abort()
			return
		}

		// 提取令牌
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			response.Fail(c, response.UNAUTHORIZED, "认证令牌不能为空")
			c.Abort()
			return
		}

		// 验证令牌
		claims, err := jwtService.ParseToken(token)
		if err != nil {
			response.Fail(c, response.UNAUTHORIZED, "无效的认证令牌")
			c.Abort()
			return
		}

		// 验证设备是否存在且属于该用户
		device, err := deviceService.GetDeviceByID(c.Request.Context(), deviceID)
		if err != nil {
			response.Fail(c, response.UNAUTHORIZED, "设备不存在或无权限")
			c.Abort()
			return
		}

		// 检查设备是否属于当前用户
		var userID uint
		if uid, ok := claims["user_id"].(float64); ok {
			userID = uint(uid)
		}
		if device.UserID != userID {
			response.Fail(c, response.UNAUTHORIZED, "设备不属于当前用户")
			c.Abort()
			return
		}

		// 检查设备状态
		if device.Status == 0 {
			response.Fail(c, response.UNAUTHORIZED, "设备已离线")
			c.Abort()
			return
		}

		// 将用户和设备信息存储到上下文中
		c.Set("user_id", userID)
		if username, ok := claims["username"]; ok {
			c.Set("username", username)
		}
		c.Set("device_id", device.ID)
		c.Set("device", device)

		c.Next()
	}
}
