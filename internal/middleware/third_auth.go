package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

// ThirdAuth 第三方API认证中间件
func ThirdAuth(thirdJWTService *service.ThirdJWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>er("Authorization")
		if authHeader == "" {
			logger.Error(c, "ThirdAuth: Authorization header is missing")
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			logger.Error(c, "ThirdAuth: Invalid Authorization header format: %s", authHeader)
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		claims, err := thirdJWTService.ValidateThirdToken(parts[1])
		if err != nil {
			logger.Error(c, "ThirdAuth: Token validation error: %v", err)
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		// 将token信息保存到上下文
		c.Set("secret_id", claims["secret_id"])
		c.Set("card_key", claims["card_key"])
		c.Set("permissions", claims["permissions"])
		// 标记为第三方请求
		c.Set("is_third_party", true)
		c.Next()
	}
}
