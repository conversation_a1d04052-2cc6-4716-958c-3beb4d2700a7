package middleware

import (
	"encoding/json"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

// SignatureAuth 签名认证中间件
func SignatureAuth(thirdAPIKeyService *service.ThirdAPIKeyService, signatureService *service.SignatureService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 获取请求头中的认证信息
		secretID := c.GetHeader("X-Secret-Id")
		signature := c.GetHeader("X-Signature")
		timestampStr := c.GetHeader("X-Timestamp")

		if secretID == "" || signature == "" || timestampStr == "" {
			logger.Error(c, "SignatureAuth: Missing required headers")
			response.Fail(c, response.UNAUTHORIZED, "缺少必要的认证头")
			c.Abort()
			return
		}

		// 2. 解析时间戳
		timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
		if err != nil {
			logger.Error(c, "SignatureAuth: Invalid timestamp: %s", timestampStr)
			response.Fail(c, response.UNAUTHORIZED, "无效的时间戳")
			c.Abort()
			return
		}

		// 3. 验证API密钥是否存在
		apiKey, err := thirdAPIKeyService.GetAPIKeyBySecretID(c.Request.Context(), secretID)
		if err != nil {
			logger.Error(c, "SignatureAuth: API key validation error: %v", err)
			response.Fail(c, response.UNAUTHORIZED, "无效的API密钥")
			c.Abort()
			return
		}

		// 4. 获取请求参数
		params := make(map[string]string)
		
		// 从请求体中获取参数（如果是POST请求）
		if c.Request.Method == "POST" {
			var bodyParams map[string]interface{}
			if err := c.ShouldBindJSON(&bodyParams); err == nil {
				for k, v := range bodyParams {
					if str, ok := v.(string); ok {
						params[k] = str
					} else {
						// 对于非字符串类型，转换为JSON字符串
						if jsonBytes, err := json.Marshal(v); err == nil {
							params[k] = string(jsonBytes)
						}
					}
				}
			}
			// 重新绑定请求体，因为上面的操作会消耗请求体
			c.Request.Body = strings.NewReader(getRequestBody(c))
		}

		// 从查询参数中获取参数
		for k, v := range c.Request.URL.Query() {
			if len(v) > 0 {
				params[k] = v[0]
			}
		}

		// 5. 验证签名
		method := c.Request.Method
		uri := c.Request.URL.Path
		
		if !signatureService.ValidateSignature(method, uri, params, apiKey.SecretKey, timestamp, signature) {
			logger.Error(c, "SignatureAuth: Signature validation failed")
			response.Fail(c, response.UNAUTHORIZED, "签名验证失败")
			c.Abort()
			return
		}

		// 6. 将API密钥信息保存到上下文
		c.Set("api_key", apiKey)
		c.Set("secret_id", apiKey.SecretID)
		c.Set("card_key", apiKey.CardKey)
		c.Set("permissions", apiKey.Permissions)
		c.Set("is_third_party", true)

		c.Next()
	}
}

// getRequestBody 获取请求体内容（这里需要实现获取原始请求体的逻辑）
func getRequestBody(c *gin.Context) string {
	// 这里应该从上下文中获取原始请求体
	// 在实际实现中，可能需要在更早的中间件中保存请求体
	return ""
}
