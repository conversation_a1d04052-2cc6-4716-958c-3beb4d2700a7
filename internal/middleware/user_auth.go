package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"

	jwtPkg "goadmin/pkg/jwt"
	"goadmin/pkg/response"
)

// UserAuth 用户认证中间件
func UserAuth(jwtService *jwtPkg.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			response.Fail(c, response.UNAUTHORIZED, "缺少认证令牌")
			c.Abort()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			response.Fail(c, response.UNAUTHORIZED, "无效的认证令牌格式")
			c.Abort()
			return
		}

		// 提取令牌
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			response.Fail(c, response.UNAUTHORIZED, "认证令牌不能为空")
			c.Abort()
			return
		}

		// 验证令牌
		claims, err := jwtService.ParseToken(token)
		if err != nil {
			response.Fail(c, response.UNAUTHORIZED, "无效的认证令牌")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)

		c.Next()
	}
}

// OptionalUserAuth 可选用户认证中间件
func OptionalUserAuth(jwtService *jwt.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.Next()
			return
		}

		// 提取令牌
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.Next()
			return
		}

		// 验证令牌
		claims, err := jwtService.ParseToken(token)
		if err != nil {
			c.Next()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)

		c.Next()
	}
}
