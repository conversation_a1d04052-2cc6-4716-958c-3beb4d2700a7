package middleware

import (
	"github.com/gin-gonic/gin"
	"goadmin/internal/service"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
	"strings"
)

// HamibotAuth Hamibot API认证中间件
func HamibotAuth(jwtService *service.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			logger.Error(c, "Authorization header is missing")
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			logger.Error(c, "Invalid Authorization header format: %s", authHeader)
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		claims, err := jwtService.ValidateHamibotToken(c, parts[1])
		if err != nil {
			logger.Error(c, "Token validation error: %v", err)
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		// 将token信息保存到上下文
		if env, ok := claims["env"].(map[string]interface{}); ok {
			if userID, exists := env["USER_ID"]; exists {
				c.Set("user_id", userID)
			}
			if robotID, exists := env["ROBOT_ID"]; exists {
				c.Set("robot_id", robotID)
			}
			if appEnv, exists := env["APP_ENV"]; exists {
				c.Set("app_env", appEnv)
			}
		}

		// 标记为Hamibot请求
		c.Set("is_hamibot", true)
		c.Next()
	}
}
