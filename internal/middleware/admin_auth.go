package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"goadmin/internal/service"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
	"strings"
)

func AdminAuth(jwtService *service.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取token
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			response.Fail(c, response.UNAUTHORIZED, "未登录或非法访问")
			c.Abort()
			return
		}

		// 检查token格式
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			logger.Error(c, "Invalid Authorization header format: %s", authHeader)
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		// 验证token
		token, err := jwtService.ValidateToken(parts[1])
		if err != nil {
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		if !token.Valid {
			logger.Error(c, "Token is expired")
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		// 获取token中的管理员ID
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			logger.Error(c, "Invalid token claims: %v", claims)
			c.Abort()
			return
		}

		adminID, ok := claims["admin_id"].(float64)
		if !ok {
			logger.Error(c, "Invalid admin_id in token claims: %v", claims)
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		// 将管理员ID存储到上下文
		c.Set("admin_id", uint(adminID))
		// 标记为管理员请求
		c.Set("is_admin", true)
		c.Next()
	}
}
