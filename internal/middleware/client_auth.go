package middleware

import (
	"goadmin/pkg/logger"
	"strings"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// ClientAuth 客户端API认证中间件
func ClientAuth(jwtService *service.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>er("Authorization")
		if authHeader == "" {
			logger.Error(c, "ClientAuth: Authorization header is missing")
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			logger.Error(c, "ClientAuth: Invalid Authorization header format: %s", authHeader)
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		claims, err := jwtService.ValidateClientToken(parts[1])
		if err != nil {
			logger.Error(c, "ClientAuth: Token validation error: %v", err)
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		// 将token信息保存到上下文
		c.Set("card_key", claims["card_key"])
		c.Set("device_id", claims["device_id"])
		// 标记为客户端请求
		c.Set("is_admin", false)
		c.Next()
	}
}
