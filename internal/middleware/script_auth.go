package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

// ScriptAuth 脚本API认证中间件
func ScriptAuth(jwtService *service.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>er("Authorization")
		if authHeader == "" {
			logger.Error(c, "ScriptAuth: Authorization header is missing")
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			logger.Error(c, "ScriptAuth: Invalid Authorization header format: %s", authHeader)
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		claims, err := jwtService.ValidateScriptToken(parts[1])
		if err != nil {
			logger.Error(c, "ScriptAuth: Token validation error: %v", err)
			response.Fail(c, response.UNAUTHORIZED, "invalid")
			c.Abort()
			return
		}

		// 将token信息保存到上下文
		if userID, ok := claims["user_id"].(string); ok {
			c.Set("user_id", userID)
		}
		if deviceID, ok := claims["device_id"].(string); ok {
			c.Set("device_id", deviceID)
		}
		// 标记为脚本请求
		c.Set("is_script", true)
		c.Next()
	}
}
