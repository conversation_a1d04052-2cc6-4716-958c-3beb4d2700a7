package middleware

import (
    "bytes"
    "encoding/json"
    "github.com/gin-gonic/gin"
    "goadmin/internal/service"
    "io"
    "net/http"
    "strings"
)

// OperationLog 操作日志中间件
func OperationLog(operationLogService *service.OperationLogService) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 不记录OPTIONS请求
        if c.Request.Method == http.MethodOptions {
            c.Next()
            return
        }

        // 获取请求信息
        path := c.Request.URL.Path
        method := c.Request.Method
        ip := c.ClientIP()
        userAgent := c.Request.UserAgent()

        // 获取请求参数
        var params interface{}
        if c.Request.Method != http.MethodGet {
            body, err := c.GetRawData()
            if err == nil {
                // 将body重新设置回Request，因为GetRawData会消耗掉body
                c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
                // 尝试解析JSON
                if err := json.Unmarshal(body, &params); err != nil {
                    // 如果不是JSON，则将原始数据作为字符串
                    params = string(body)
                }
            }
        } else {
            // 对于GET请求，记录查询参数
            params = c.Request.URL.Query()
        }

        // 创建自定义ResponseWriter以捕获响应
        blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
        c.Writer = blw

        // 处理请求
        c.Next()

        // 获取响应信息
        responseBody := blw.body.String()
        var response interface{}
        var businessCode = 0 // 默认业务码为0

        if strings.HasPrefix(c.Writer.Header().Get("Content-Type"), "application/json") {
            if err := json.Unmarshal([]byte(responseBody), &response); err != nil {
                response = responseBody
            } else {
                // 尝试提取业务响应码
                if respMap, ok := response.(map[string]interface{}); ok {
                    if code, exists := respMap["code"]; exists {
                        if codeFloat, ok := code.(float64); ok {
                            businessCode = int(codeFloat)
                        } else if codeInt, ok := code.(int); ok {
                            businessCode = codeInt
                        }
                    }
                }
            }
        } else {
            response = responseBody
        }

        // 截断响应结果
        var truncatedResponse interface{}
        if responseStr, ok := response.(string); ok {
            if len(responseStr) > 1000 {
                truncatedResponse = responseStr[:1000]
            } else {
                truncatedResponse = responseStr
            }
        } else {
            responseBytes, _ := json.Marshal(response)
            if len(responseBytes) > 1000 {
                truncatedResponse = string(responseBytes[:1000])
            } else {
                truncatedResponse = response
            }
        }

        // 同步记录操作日志，避免异步处理导致的EOF问题
        _ = operationLogService.CreateLogFromRequest(
            path,
            method,
            params,
            truncatedResponse,
            ip,
            userAgent,
            c.Writer.Status(),
            businessCode, // 添加业务响应码
        )
    }
}

// bodyLogWriter 用于捕获响应体
type bodyLogWriter struct {
    gin.ResponseWriter
    body *bytes.Buffer
}

func (w *bodyLogWriter) Write(b []byte) (int, error) {
    w.body.Write(b)
    return w.ResponseWriter.Write(b)
}
