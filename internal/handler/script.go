package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// ScriptHandler 脚本处理器
type ScriptHandler struct {
	scriptService *service.ScriptService
}

// NewScriptHandler 创建脚本处理器
func NewScriptHandler(scriptService *service.ScriptService) *ScriptHandler {
	return &ScriptHandler{
		scriptService: scriptService,
	}
}

// CreateScriptRequest 创建脚本请求
type CreateScriptRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	Content     string                 `json:"content" binding:"required"`
	Type        string                 `json:"type" binding:"omitempty"`
	Config      map[string]interface{} `json:"config"`
}

// CreateScript 创建脚本
func (h *ScriptHandler) CreateScript(c *gin.Context) {
	var req CreateScriptRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	if req.Type == "" {
		req.Type = "autox"
	}

	if req.Config == nil {
		req.Config = make(map[string]interface{})
	}

	ownerID := c.GetUint("user_id")

	script, err := h.scriptService.CreateScript(
		c.Request.Context(),
		req.Name,
		req.Description,
		req.Content,
		req.Type,
		req.Config,
		ownerID,
	)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "创建脚本失败")
		return
	}

	response.Success(c, script)
}

// GetScriptByID 获取脚本详情
func (h *ScriptHandler) GetScriptByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的脚本ID")
		return
	}

	script, err := h.scriptService.GetScriptByID(c.Request.Context(), uint(id))
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取脚本失败")
		return
	}

	response.Success(c, script)
}

// GetScriptList 获取脚本列表
func (h *ScriptHandler) GetScriptList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	keyword := c.Query("keyword")
	scriptType := c.Query("type")

	var ownerID *uint
	if ownerIDStr := c.Query("owner_id"); ownerIDStr != "" {
		if oid, err := strconv.ParseUint(ownerIDStr, 10, 32); err == nil {
			ownerIDUint := uint(oid)
			ownerID = &ownerIDUint
		}
	}

	var status *int8
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.ParseInt(statusStr, 10, 8); err == nil {
			statusInt8 := int8(s)
			status = &statusInt8
		}
	}

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	scripts, total, err := h.scriptService.GetScriptList(c.Request.Context(), page, pageSize, ownerID, scriptType, keyword, status)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取脚本列表失败")
		return
	}

	response.Success(c, gin.H{
		"list":      scripts,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// UpdateScriptRequest 更新脚本请求
type UpdateScriptRequest struct {
	ID          uint                   `json:"id" binding:"required"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Content     string                 `json:"content"`
	Config      map[string]interface{} `json:"config"`
}

// UpdateScript 更新脚本
func (h *ScriptHandler) UpdateScript(c *gin.Context) {
	var req UpdateScriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.scriptService.UpdateScript(c.Request.Context(), req.ID, req.Name, req.Description, req.Content, req.Config)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新脚本失败")
		return
	}

	response.Success(c, gin.H{"message": "脚本更新成功"})
}

// UpdateScriptStatusRequest 更新脚本状态请求
type UpdateScriptStatusRequest struct {
	ID     uint `json:"id" binding:"required"`
	Status int8 `json:"status" binding:"required,oneof=1 2"`
}

// UpdateScriptStatus 更新脚本状态
func (h *ScriptHandler) UpdateScriptStatus(c *gin.Context) {
	var req UpdateScriptStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.scriptService.UpdateScriptStatus(c.Request.Context(), req.ID, req.Status)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新脚本状态失败")
		return
	}

	action := "启用"
	if req.Status == 2 {
		action = "禁用"
	}

	response.Success(c, gin.H{"message": action + "脚本成功"})
}

// DeleteScriptRequest 删除脚本请求
type DeleteScriptRequest struct {
	ID uint `json:"id" binding:"required"`
}

// DeleteScript 删除脚本
func (h *ScriptHandler) DeleteScript(c *gin.Context) {
	var req DeleteScriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.scriptService.DeleteScript(c.Request.Context(), req.ID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "删除脚本失败")
		return
	}

	response.Success(c, gin.H{"message": "脚本删除成功"})
}

// GetUserScripts 获取用户脚本列表
func (h *ScriptHandler) GetUserScripts(c *gin.Context) {
	userID := c.GetUint("user_id")

	scripts, err := h.scriptService.GetUserScripts(c.Request.Context(), userID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取用户脚本失败")
		return
	}

	response.Success(c, gin.H{"scripts": scripts})
}

// GetScriptConfig 获取脚本配置
func (h *ScriptHandler) GetScriptConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的脚本ID")
		return
	}

	config, err := h.scriptService.GetScriptConfig(c.Request.Context(), uint(id))
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取脚本配置失败")
		return
	}

	response.Success(c, gin.H{"config": config})
}

// UpdateScriptConfigRequest 更新脚本配置请求
type UpdateScriptConfigRequest struct {
	ID     uint                   `json:"id" binding:"required"`
	Config map[string]interface{} `json:"config" binding:"required"`
}

// UpdateScriptConfig 更新脚本配置
func (h *ScriptHandler) UpdateScriptConfig(c *gin.Context) {
	var req UpdateScriptConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.scriptService.UpdateScriptConfig(c.Request.Context(), req.ID, req.Config)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新脚本配置失败")
		return
	}

	response.Success(c, gin.H{"message": "脚本配置更新成功"})
}

// GetScriptsByType 根据类型获取脚本列表
func (h *ScriptHandler) GetScriptsByType(c *gin.Context) {
	scriptType := c.Param("type")
	if scriptType == "" {
		response.Fail(c, response.INVALID_PARAMS, "脚本类型不能为空")
		return
	}

	scripts, err := h.scriptService.GetScriptsByType(c.Request.Context(), scriptType)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取脚本列表失败")
		return
	}

	response.Success(c, gin.H{"scripts": scripts})
}

// CloneScriptRequest 克隆脚本请求
type CloneScriptRequest struct {
	ID      uint   `json:"id" binding:"required"`
	NewName string `json:"new_name" binding:"required"`
}

// CloneScript 克隆脚本
func (h *ScriptHandler) CloneScript(c *gin.Context) {
	var req CloneScriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	ownerID := c.GetUint("user_id")

	script, err := h.scriptService.CloneScript(c.Request.Context(), req.ID, req.NewName, ownerID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "克隆脚本失败")
		return
	}

	response.Success(c, script)
}
