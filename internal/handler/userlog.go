package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

type UserLogHandler struct {
	userLogService *service.UserLogService
}

func NewUserLogHandler(userLogService *service.UserLogService) *UserLogHandler {
	return &UserLogHandler{userLogService: userLogService}
}

// GetDailyActiveUsers 获取每日活跃用户数
func (h *UserLogHandler) GetDailyActiveUsers(c *gin.Context) {
	stats, err := h.userLogService.GetDailyActiveUsers()
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}
	response.Success(c, stats)
}

// GetDailyActiveRobots 获取每日活跃机器人数
func (h *UserLogHandler) GetDailyActiveRobots(c *gin.Context) {
	stats, err := h.userLogService.GetDailyActiveRobots()
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}
	response.Success(c, stats)
}
