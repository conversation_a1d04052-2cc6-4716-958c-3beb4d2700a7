package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
	"time"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
	paymentService *service.PaymentService
}

// NewPaymentHandler 创建支付处理器
func NewPaymentHandler(paymentService *service.PaymentService) *PaymentHandler {
	return &PaymentHandler{
		paymentService: paymentService,
	}
}

// ScriptPackageResponse 用于前端套餐列表精简返回
// 只包含前端需要的字段

type ScriptPackageResponse struct {
	ID             uint    `json:"id"`
	Name           string  `json:"name"`
	Description    string  `json:"description"`
	Price          float64 `json:"price"`
	DeviceLimit    int     `json:"device_limit"`
	Duration       int     `json:"duration"`
	MarketScriptID uint    `json:"market_script_id"`
	Status         int8    `json:"status"`
}

// GetScriptPackages 获取脚本套餐列表（无需登录态）
func (h *PaymentHandler) GetScriptPackages(c *gin.Context) {
	var req struct {
		MarketScriptID uint `json:"market_script_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	packages, err := h.paymentService.GetScriptPackages(c.Request.Context(), req.MarketScriptID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	respList := make([]ScriptPackageResponse, 0, len(packages))
	for _, p := range packages {
		respList = append(respList, ScriptPackageResponse{
			ID:             p.ID,
			Name:           p.Name,
			Description:    p.Description,
			Price:          p.Price,
			DeviceLimit:    p.DeviceLimit,
			Duration:       p.Duration,
			MarketScriptID: p.MarketScriptID,
			Status:         p.Status,
		})
	}

	response.Success(c, respList)
}

// PaymentOrderResponse 用于前端支付订单返回
// 只包含前端需要的字段，包括 pay_url

type PaymentOrderResponse struct {
	OrderNo   string    `json:"order_no"`
	Amount    float64   `json:"amount"`
	PayType   string    `json:"pay_type"`
	Status    int8      `json:"status"`
	PayURL    string    `json:"pay_url"`
	ExpiredAt time.Time `json:"expired_at"`
	CreatedAt time.Time `json:"created_at"`
}

// CreatePaymentOrder 创建支付订单
// 用户选择套餐和支付方式后创建支付订单
func (h *PaymentHandler) CreatePaymentOrder(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	var req struct {
		PackageID uint   `json:"package_id" binding:"required"`
		PayType   string `json:"pay_type" binding:"required,oneof=alipay wxpay"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	order, err := h.paymentService.CreatePaymentOrder(
		c.Request.Context(),
		userID.(uint),
		req.PackageID,
		req.PayType,
	)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	resp := PaymentOrderResponse{
		OrderNo:   order.OrderNo,
		Amount:    order.Amount,
		PayType:   order.PayType,
		Status:    order.Status,
		PayURL:    order.PayURL,
		ExpiredAt: order.ExpiredAt,
		CreatedAt: order.CreatedAt,
	}

	response.Success(c, resp)
}

// UpgradePackageResponse 升级套餐响应结构
type UpgradePackageResponse struct {
	ID             uint    `json:"id"`
	Name           string  `json:"name"`
	Description    string  `json:"description"`
	Price          float64 `json:"price"`         // 原套餐价格
	UpgradePrice   float64 `json:"upgrade_price"` // 升级价格
	DeviceLimit    int     `json:"device_limit"`
	Duration       int     `json:"duration"`
	MarketScriptID uint    `json:"market_script_id"`
	Status         int8    `json:"status"`
}

// GetUpgradePackages 获取升级套餐列表
func (h *PaymentHandler) GetUpgradePackages(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	var req struct {
		ScriptID uint `json:"script_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	packages, err := h.paymentService.GetUpgradePackages(c.Request.Context(), userID.(uint), req.ScriptID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	respList := make([]UpgradePackageResponse, 0, len(packages))
	for _, p := range packages {
		// 计算升级价格
		upgradePrice := h.paymentService.CalculateUpgradePrice(c.Request.Context(), userID.(uint), req.ScriptID, p.ID)

		respList = append(respList, UpgradePackageResponse{
			ID:             p.ID,
			Name:           p.Name,
			Description:    p.Description,
			Price:          p.Price,
			UpgradePrice:   upgradePrice,
			DeviceLimit:    p.DeviceLimit,
			Duration:       p.Duration,
			MarketScriptID: p.MarketScriptID,
			Status:         p.Status,
		})
	}

	response.Success(c, respList)
}

// CreateUpgradeOrder 创建升级订单
func (h *PaymentHandler) CreateUpgradeOrder(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	var req struct {
		ScriptID  uint   `json:"script_id" binding:"required"`
		PackageID uint   `json:"package_id" binding:"required"`
		PayType   string `json:"pay_type" binding:"required,oneof=alipay wxpay"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	order, err := h.paymentService.CreateUpgradeOrder(
		c.Request.Context(),
		userID.(uint),
		req.ScriptID,
		req.PackageID,
		req.PayType,
	)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	resp := PaymentOrderResponse{
		OrderNo:   order.OrderNo,
		Amount:    order.Amount,
		PayType:   order.PayType,
		Status:    order.Status,
		PayURL:    order.PayURL,
		ExpiredAt: order.ExpiredAt,
		CreatedAt: order.CreatedAt,
	}

	response.Success(c, resp)
}

// PaymentNotify ZPAY支付回调
// 处理ZPAY支付平台的异步通知
// 根据ZPAY官方文档要求：
// 1. 返回纯字符串"success"
// 2. 5秒内返回响应
// 3. 处理重复通知
func (h *PaymentHandler) PaymentNotify(c *gin.Context) {
	// 记录回调开始
	logger.Info(c.Request.Context(), "收到ZPAY支付回调请求 - 方法: %s, URL: %s",
		c.Request.Method, c.Request.URL.String())

	// 获取所有参数
	params := make(map[string]string)

	// 处理GET参数
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 {
			params[key] = values[0]
		}
	}

	// 处理POST参数
	if err := c.Request.ParseForm(); err == nil {
		for key, values := range c.Request.PostForm {
			if len(values) > 0 {
				params[key] = values[0]
			}
		}
	}

	logger.Info(c.Request.Context(), "ZPAY回调参数解析完成 - 参数数量: %d", len(params))

	// 处理支付回调
	err := h.paymentService.HandlePaymentNotify(c.Request.Context(), params)
	if err != nil {
		// 记录错误日志
		logger.Error(c.Request.Context(), "ZPAY支付回调处理失败: %v", err)

		// 返回失败响应（ZPAY会重试）
		c.String(500, "fail")
		return
	}

	// 记录成功日志
	logger.Info(c.Request.Context(), "ZPAY支付回调处理成功")

	// 返回成功响应（必须是纯字符串"success"）
	c.String(200, "success")
}

// GetOrderDetail 获取订单详情（通用接口，支持所有类型订单）
// 根据订单号获取订单的详细信息
func (h *PaymentHandler) GetOrderDetail(c *gin.Context) {
	var req struct {
		OrderNo string `json:"order_no" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	userID := c.GetUint("user_id")

	// 尝试获取订单详情（支持所有类型）
	order, err := h.paymentService.GetOrderDetail(c.Request.Context(), userID, req.OrderNo)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取订单详情失败")
		return
	}

	response.Success(c, order)
}
