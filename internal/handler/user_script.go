package handler

import (
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"goadmin/internal/model"
	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// UserScriptHandler 用户脚本处理器
type UserScriptHandler struct {
	userScriptService *service.UserScriptService
	bindingService    *service.UserScriptDeviceBindingService
	webSocketService  *service.WebSocketService
	deviceService     *service.DeviceService
	jwtService        *service.JWTService
}

// NewUserScriptHandler 创建用户脚本处理器
func NewUserScriptHandler(userScriptService *service.UserScriptService, bindingService *service.UserScriptDeviceBindingService, webSocketService *service.WebSocketService, deviceService *service.DeviceService, jwtService *service.JWTService) *UserScriptHandler {
	return &UserScriptHandler{
		userScriptService: userScriptService,
		bindingService:    bindingService,
		webSocketService:  webSocketService,
		deviceService:     deviceService,
		jwtService:        jwtService,
	}
}

// GetUserScripts 获取用户脚本列表
func (h *UserScriptHandler) GetUserScripts(c *gin.Context) {
	userID := c.GetUint("user_id")
	scripts, err := h.userScriptService.GetUserScripts(c.Request.Context(), userID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	// 转换为响应格式
	scriptList := make([]gin.H, 0, len(scripts))
	for _, script := range scripts {
		scriptData := gin.H{
			"id":               script.ID,
			"name":             script.Name,
			"version":          script.Version,
			"status":           script.Status,
			"market_script_id": script.MarketScriptID,
			"package_id":       script.PackageID,
			"package_name":     script.PackageName,
			"default_values":   script.DefaultValues,
			"max_devices":      script.MaxDevices,
			"expired_at":       script.ExpiredAt,
			"created_at":       script.CreatedAt,
			"updated_at":       script.UpdatedAt,
		}

		// 查询绑定设备数量
		bindingCount, err := h.bindingService.GetBindingCount(c.Request.Context(), script.ID)
		if err != nil {
			bindingCount = 0
		}
		scriptData["binding_count"] = bindingCount

		// 如果加载了市场脚本信息，添加相关字段
		if script.MarketScript != nil {
			scriptData["author"] = script.MarketScript.Author
			scriptData["rating"] = script.MarketScript.Rating
		}

		scriptList = append(scriptList, scriptData)
	}

	response.Success(c, gin.H{
		"scripts": scriptList,
	})
}

// GetScriptPackage 根据套餐ID获取套餐信息
func (h *UserScriptHandler) GetScriptPackage(c *gin.Context) {
	var req struct {
		PackageID uint `json:"package_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误: "+err.Error())
		return
	}

	packageInfo, err := h.userScriptService.GetScriptPackageByID(c.Request.Context(), req.PackageID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, packageInfo)
}

// RunScript 运行脚本（通过WebSocket发送给设备）
func (h *UserScriptHandler) RunScript(c *gin.Context) {
	var req struct {
		ScriptID  uint     `json:"script_id" binding:"required"`
		DeviceIDs []string `json:"device_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误: "+err.Error())
		return
	}

	userID := c.GetUint("user_id")

	// 验证脚本权限和状态
	script, err := h.userScriptService.GetUserScriptByID(c.Request.Context(), userID, req.ScriptID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	// 检查脚本状态
	if script.Status != model.UserScriptStatusEnabled {
		response.Fail(c, response.ERROR, "脚本已禁用")
		return
	}

	// 检查脚本是否过期
	if script.ExpiredAt != nil && time.Now().After(*script.ExpiredAt) {
		response.Fail(c, response.ERROR, "脚本已过期")
		return
	}

	// 获取通用配置
	runData, err := h.userScriptService.GetScriptRunData(c.Request.Context(), req.ScriptID, userID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	successCount := 0
	failedDevices := []string{}

	for _, deviceID := range req.DeviceIDs {
		// 查询设备信息获取设备名称
		device, err := h.deviceService.GetDeviceByID(c.Request.Context(), deviceID)
		deviceName := ""
		if err == nil {
			deviceName = device.Name
		}

		// 生成脚本专用的JWT token
		scriptJWTToken, err := h.jwtService.GenerateAccessToken(
			strconv.Itoa(int(userID)),
			deviceID,
			"script", // 标记为脚本专用token
		)
		if err != nil {
			log.Printf("生成脚本JWT token失败: %v", err)
			scriptJWTToken = "" // 如果生成失败，使用空字符串
		}

		// 包装配置信息，添加更多必要的参数
		configWrapper := map[string]interface{}{
			"env":         runData.Config, // 原有的配置内容
			"device_name": deviceName,     // 从数据库查询的设备名称
			"device_id":   deviceID,       // 设备ID
			"script_name": runData.ScriptName,
			"script_id":   runData.ScriptID,
			"jwt_token":   scriptJWTToken, // 脚本专用的JWT token
			"user_id":     userID,
		}

		params := map[string]interface{}{
			"script_id":      runData.ScriptID,
			"script_name":    runData.ScriptName,
			"script_content": runData.ScriptContent,
			"version":        runData.Version,
			"config_name":    runData.ConfigName,
			"config":         configWrapper, // 使用包装后的配置
		}
		err = h.webSocketService.SendScriptTask([]string{deviceID}, fmt.Sprintf("%d", runData.ScriptID), "run", params)
		if err != nil {
			log.Printf("发送任务到设备 %s 失败: %v", deviceID, err)
			failedDevices = append(failedDevices, deviceID)
		} else {
			successCount++
		}
	}

	// 处理发送结果
	if len(failedDevices) > 0 {
		if successCount == 0 {
			response.Fail(c, response.ERROR, "所有设备发送失败")
			return
		} else {
			response.Success(c, gin.H{
				"message":        "脚本运行命令已发送（部分设备失败）",
				"total_devices":  len(req.DeviceIDs),
				"success_count":  successCount,
				"failed_devices": failedDevices,
			})
			return
		}
	}

	response.Success(c, gin.H{
		"message":       "脚本运行命令已发送",
		"total_devices": len(req.DeviceIDs),
		"success_count": successCount,
	})
}

// StopScript 停止脚本
func (h *UserScriptHandler) StopScript(c *gin.Context) {
	var req struct {
		ScriptID  uint     `json:"script_id" binding:"required"`
		DeviceIDs []string `json:"device_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误: "+err.Error())
		return
	}

	userID := c.GetUint("user_id")

	// 验证脚本权限
	_, err := h.userScriptService.GetUserScriptByID(c.Request.Context(), userID, req.ScriptID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	// 为每个设备分别发送停止命令
	successCount := 0
	failedDevices := []string{}

	for _, deviceID := range req.DeviceIDs {
		params := map[string]interface{}{
			"script_id": req.ScriptID,
		}

		err = h.webSocketService.SendScriptTask([]string{deviceID}, fmt.Sprintf("%d", req.ScriptID), "stop", params)
		if err != nil {
			log.Printf("发送停止命令到设备 %s 失败: %v", deviceID, err)
			failedDevices = append(failedDevices, deviceID)
		} else {
			successCount++
		}
	}

	// 处理发送结果
	if len(failedDevices) > 0 {
		if successCount == 0 {
			response.Fail(c, response.ERROR, "所有设备发送失败")
			return
		} else {
			response.Success(c, gin.H{
				"message":        "脚本停止命令已发送（部分设备失败）",
				"total_devices":  len(req.DeviceIDs),
				"success_count":  successCount,
				"failed_devices": failedDevices,
			})
			return
		}
	}

	response.Success(c, gin.H{
		"message":       "脚本停止命令已发送",
		"total_devices": len(req.DeviceIDs),
		"success_count": successCount,
	})
}

// GetUserScriptList 获取用户脚本列表（带分页，管理员用）
func (h *UserScriptHandler) GetUserScriptList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	keyword := c.Query("keyword")
	var userID *uint
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if uid, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			userIDUint := uint(uid)
			userID = &userIDUint
		}
	}

	var status *int8
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.ParseInt(statusStr, 10, 8); err == nil {
			statusInt8 := int8(s)
			status = &statusInt8
		}
	}

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	scripts, total, err := h.userScriptService.GetUserScriptList(c.Request.Context(), page, pageSize, userID, keyword, status)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取脚本列表失败")
		return
	}

	response.Success(c, gin.H{
		"list":      scripts,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// UpdateUserScriptStatus 更新用户脚本状态
func (h *UserScriptHandler) UpdateUserScriptStatus(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	// 解析请求参数
	var req struct {
		ScriptID uint `json:"script_id" binding:"required"`
		Status   int8 `json:"status" binding:"required,oneof=1 2"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "请求参数错误")
		return
	}

	err := h.userScriptService.UpdateUserScriptStatus(c.Request.Context(), userID.(uint), req.ScriptID, req.Status)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新脚本状态失败")
		return
	}

	response.Success(c, gin.H{"message": "更新成功"})
}

// DeleteUserScript 删除用户脚本
func (h *UserScriptHandler) DeleteUserScript(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	// 解析请求参数
	var req struct {
		ScriptID uint `json:"script_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "请求参数错误")
		return
	}

	err := h.userScriptService.DeleteUserScript(c.Request.Context(), userID.(uint), req.ScriptID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "删除脚本失败")
		return
	}

	response.Success(c, gin.H{"message": "删除成功"})
}

// DeleteScript 删除脚本（别名方法，与路由保持一致）
func (h *UserScriptHandler) DeleteScript(c *gin.Context) {
	h.DeleteUserScript(c)
}

// CheckScriptUpdate 检查脚本更新
func (h *UserScriptHandler) CheckScriptUpdate(c *gin.Context) {
	var req struct {
		ScriptID uint `json:"script_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误: "+err.Error())
		return
	}

	userID := c.GetUint("user_id")

	// 检查脚本更新
	updateInfo, err := h.userScriptService.CheckScriptUpdate(c.Request.Context(), userID, req.ScriptID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, updateInfo)
}

// UpdateScript 更新脚本
func (h *UserScriptHandler) UpdateScript(c *gin.Context) {
	var req struct {
		ScriptID uint `json:"script_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误: "+err.Error())
		return
	}

	userID := c.GetUint("user_id")

	// 更新脚本
	err := h.userScriptService.UpdateUserScript(c.Request.Context(), userID, req.ScriptID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, gin.H{
		"message": "脚本更新成功",
	})
}
