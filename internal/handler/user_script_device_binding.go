package handler

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"goadmin/internal/model"
	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// UserScriptDeviceBindingHandler 用户脚本设备绑定处理器
type UserScriptDeviceBindingHandler struct {
	bindingService    *service.UserScriptDeviceBindingService
	userScriptService *service.UserScriptService
	deviceService     *service.DeviceService
}

// NewUserScriptDeviceBindingHandler 创建用户脚本设备绑定处理器
func NewUserScriptDeviceBindingHandler(
	bindingService *service.UserScriptDeviceBindingService,
	userScriptService *service.UserScriptService,
	deviceService *service.DeviceService,
) *UserScriptDeviceBindingHandler {
	return &UserScriptDeviceBindingHandler{
		bindingService:    bindingService,
		userScriptService: userScriptService,
		deviceService:     deviceService,
	}
}

// BindDevice 批量绑定设备到用户脚本
func (h *UserScriptDeviceBindingHandler) BindDevice(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	// 解析请求体
	var req struct {
		UserScriptID uint     `json:"user_script_id" binding:"required"`
		DeviceIDs    []uint64 `json:"device_ids" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "请求参数错误")
		return
	}

	// 验证用户脚本是否属于当前用户
	userScript, err := h.userScriptService.GetUserScriptByID(c.Request.Context(), userID.(uint), req.UserScriptID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "验证用户脚本失败")
		return
	}

	// 验证所有设备是否属于当前用户
	devices := make([]*model.Device, 0, len(req.DeviceIDs))
	for _, deviceID := range req.DeviceIDs {
		device, err := h.deviceService.GetUserDeviceByID(c.Request.Context(), userID.(uint), uint(deviceID))
		if err != nil {
			if e, ok := err.(*response.Error); ok {
				response.Fail(c, e.Code, e.Message)
				return
			}
			response.Fail(c, response.ERROR, "验证设备失败")
			return
		}
		devices = append(devices, device)
	}

	// 执行批量绑定
	results, err := h.bindingService.BindDevices(c.Request.Context(), req.UserScriptID, req.DeviceIDs)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "绑定设备失败")
		return
	}

	response.Success(c, gin.H{
		"message":     "批量绑定完成",
		"script_id":   userScript.ID,
		"script_name": userScript.Name,
		"results":     results,
	})
}

// UnbindDevice 批量解绑设备
func (h *UserScriptDeviceBindingHandler) UnbindDevice(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	// 解析请求体
	var req struct {
		UserScriptID uint     `json:"user_script_id" binding:"required"`
		DeviceIDs    []uint64 `json:"device_ids" binding:"required,min=1"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "请求参数错误")
		return
	}

	// 验证用户脚本是否属于当前用户
	userScript, err := h.userScriptService.GetUserScriptByID(c.Request.Context(), userID.(uint), req.UserScriptID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "验证用户脚本失败")
		return
	}

	// 执行批量解绑
	err = h.bindingService.UnbindDevices(c.Request.Context(), req.UserScriptID, req.DeviceIDs)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "解绑设备失败")
		return
	}

	response.Success(c, gin.H{
		"message":     "批量解绑完成",
		"script_id":   userScript.ID,
		"script_name": userScript.Name,
	})
}

// GetScriptBindings 获取用户脚本的设备绑定列表
func (h *UserScriptDeviceBindingHandler) GetScriptBindings(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	// 解析请求参数
	var req struct {
		UserScriptID uint `json:"user_script_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "请求参数错误")
		return
	}

	scriptID := req.UserScriptID

	// 验证用户脚本是否属于当前用户
	userScript, err := h.userScriptService.GetUserScriptByID(c.Request.Context(), userID.(uint), scriptID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "验证用户脚本失败")
		return
	}

	// 获取绑定列表
	bindings, err := h.bindingService.GetBindingsByUserScript(c.Request.Context(), scriptID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取设备绑定列表失败")
		return
	}

	// 获取绑定数量
	bindingCount, err := h.bindingService.GetBindingCount(c.Request.Context(), scriptID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取绑定数量失败")
		return
	}

	// 转换为响应格式
	bindingList := make([]DeviceBindingResponse, 0, len(bindings))
	for _, binding := range bindings {
		bindingResponse := DeviceBindingResponse{
			ID:         binding.ID,
			DeviceID:   binding.DeviceID,
			Status:     binding.Status,
			LastActive: binding.LastActive,
			CreatedAt:  binding.CreatedAt,
			UpdatedAt:  binding.UpdatedAt,
		}

		// 如果加载了设备信息，添加设备字段
		if binding.Device != nil {
			bindingResponse.Device = &DeviceInfo{
				ID:          binding.Device.ID,
				DeviceID:    binding.Device.DeviceID,
				Name:        binding.Device.Name,
				DeviceModel: binding.Device.DeviceModel,
				Status:      binding.Device.Status,
				LastActive:  binding.Device.LastActive,
			}
		}

		bindingList = append(bindingList, bindingResponse)
	}

	// 构建响应数据
	responseData := GetScriptBindingsResponse{
		Script: ScriptInfo{
			ID:         userScript.ID,
			Name:       userScript.Name,
			MaxDevices: userScript.MaxDevices,
		},
		Bindings:     bindingList,
		BindingCount: bindingCount,
		MaxDevices:   userScript.MaxDevices,
	}

	response.Success(c, responseData)
}

// GetDeviceBindings 获取设备的脚本绑定列表
func (h *UserScriptDeviceBindingHandler) GetDeviceBindings(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	// 获取设备ID
	deviceIDStr := c.Param("device_id")
	deviceID, err := strconv.ParseUint(deviceIDStr, 10, 32)
	if err != nil {
		response.Fail(c, response.INVALID_PARAMS, "设备ID格式错误")
		return
	}

	// 验证设备是否属于当前用户
	device, err := h.deviceService.GetUserDeviceByID(c.Request.Context(), userID.(uint), uint(deviceID))
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "验证设备失败")
		return
	}

	// 获取绑定列表
	bindings, err := h.bindingService.GetBindingsByDevice(c.Request.Context(), deviceID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取脚本绑定列表失败")
		return
	}

	// 转换为响应格式
	bindingList := make([]gin.H, 0, len(bindings))
	for _, binding := range bindings {
		bindingData := gin.H{
			"id":             binding.ID,
			"user_script_id": binding.UserScriptID,
			"status":         binding.Status,
			"last_active":    binding.LastActive,
			"created_at":     binding.CreatedAt,
			"updated_at":     binding.UpdatedAt,
		}

		// 如果加载了用户脚本信息，添加脚本字段
		if binding.UserScript != nil {
			bindingData["user_script"] = gin.H{
				"id":          binding.UserScript.ID,
				"name":        binding.UserScript.Name,
				"version":     binding.UserScript.Version,
				"status":      binding.UserScript.Status,
				"max_devices": binding.UserScript.MaxDevices,
			}
		}

		bindingList = append(bindingList, bindingData)
	}

	response.Success(c, gin.H{
		"device": gin.H{
			"id":           device.ID,
			"device_id":    device.DeviceID,
			"name":         device.Name,
			"device_model": device.DeviceModel,
		},
		"bindings": bindingList,
	})
}

// 响应结构体定义
type DeviceBindingResponse struct {
	ID         uint        `json:"id"`
	DeviceID   uint64      `json:"device_id"`
	Status     int8        `json:"status"`
	LastActive *time.Time  `json:"last_active"`
	CreatedAt  time.Time   `json:"created_at"`
	UpdatedAt  time.Time   `json:"updated_at"`
	Device     *DeviceInfo `json:"device"`
}

type DeviceInfo struct {
	ID          uint64     `json:"id"`
	DeviceID    string     `json:"device_id"`
	Name        string     `json:"name"`
	DeviceModel string     `json:"device_model"`
	Status      int        `json:"status"`
	LastActive  *time.Time `json:"last_active"`
}

type ScriptInfo struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`
	MaxDevices int    `json:"max_devices"`
}

type GetScriptBindingsResponse struct {
	Script       ScriptInfo              `json:"script"`
	Bindings     []DeviceBindingResponse `json:"bindings"`
	BindingCount int64                   `json:"binding_count"`
	MaxDevices   int                     `json:"max_devices"`
}
