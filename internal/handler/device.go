package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"goadmin/internal/constant"
	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// DeviceHandler 设备处理器
type DeviceHandler struct {
	deviceService *service.DeviceService
}

// NewDeviceHandler 创建设备处理器
func NewDeviceHandler(deviceService *service.DeviceService) *DeviceHandler {
	return &DeviceHandler{
		deviceService: deviceService,
	}
}

// GetDeviceInfo 获取设备信息
func (h *DeviceHandler) GetDeviceInfo(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		response.Fail(c, response.INVALID_PARAMS, "设备ID不能为空")
		return
	}

	device, err := h.deviceService.GetDeviceByID(c.Request.Context(), deviceID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取设备信息失败")
		return
	}

	response.Success(c, device)
}

// GetUserDevices 获取用户设备列表（POST接口，支持状态筛选）
func (h *DeviceHandler) GetUserDevices(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req struct {
		Status []int `json:"status"` // 设备状态数组，1-在线 2-离线 3-过期
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	devices, err := h.deviceService.GetUserDevices(c.Request.Context(), userID, req.Status)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取设备列表失败")
		return
	}

	response.Success(c, devices)
}

// DeleteDevice 删除设备
func (h *DeviceHandler) DeleteDevice(c *gin.Context) {
	var req struct {
		ID uint `json:"id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	userID := c.GetUint("user_id")

	err := h.deviceService.DeleteDeviceByID(c.Request.Context(), req.ID, userID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "删除设备失败")
		return
	}

	response.Success(c, gin.H{"message": "设备删除成功"})
}

// RefreshDeviceToken 刷新设备 JWT token
func (h *DeviceHandler) RefreshDeviceToken(c *gin.Context) {
	var req struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	if req.RefreshToken == "" {
		response.Fail(c, response.INVALID_PARAMS, "刷新令牌不能为空")
		return
	}

	// 验证数据库中的 refresh token
	refreshTokenRecord, err := h.deviceService.ValidateDeviceRefreshToken(req.RefreshToken)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "刷新令牌验证失败")
		return
	}

	// 检查是否是设备类型的 refresh token
	if refreshTokenRecord.Type != constant.TokenTypeDevice || refreshTokenRecord.DeviceID == nil {
		response.Fail(c, response.ERROR, "无效的设备刷新令牌")
		return
	}

	// 获取设备信息
	device, err := h.deviceService.GetDeviceByDatabaseID(*refreshTokenRecord.DeviceID)
	if err != nil {
		response.Fail(c, response.ERROR, "获取设备信息失败")
		return
	}

	// 生成新的 access token
	newAccessToken, err := h.deviceService.GenerateDeviceAccessToken(device.UserID, device.DeviceID)
	if err != nil {
		response.Fail(c, response.ERROR, "生成访问令牌失败")
		return
	}

	// 创建新的 refresh token（替换旧的）
	newRefreshToken, err := h.deviceService.CreateDeviceRefreshToken(device.UserID, device.DeviceID)
	if err != nil {
		response.Fail(c, response.ERROR, "生成刷新令牌失败")
		return
	}

	// 删除旧的 refresh token
	h.deviceService.DeleteDeviceRefreshToken(req.RefreshToken)

	response.Success(c, gin.H{
		"access_token":  newAccessToken,
		"refresh_token": newRefreshToken,
		"expires_in":    3600, // access token 有效期（秒）
		"message":       "设备token刷新成功",
	})
}

// UpdateDeviceInfo 更新设备信息
func (h *DeviceHandler) UpdateDeviceInfo(c *gin.Context) {
	var req struct {
		DeviceID string `json:"device_id" binding:"required"`
		Name     string `json:"name" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	if req.DeviceID == "" {
		response.Fail(c, response.INVALID_PARAMS, "设备ID不能为空")
		return
	}

	if req.Name == "" {
		response.Fail(c, response.INVALID_PARAMS, "设备名称不能为空")
		return
	}

	userID := c.GetUint("user_id")

	err := h.deviceService.UpdateDeviceInfo(c.Request.Context(), req.DeviceID, userID, req.Name)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新设备信息失败")
		return
	}

	response.Success(c, gin.H{"message": "设备信息更新成功"})
}

// 配对相关方法

// GeneratePairingCode 生成配对码
func (h *DeviceHandler) GeneratePairingCode(c *gin.Context) {
	// 从 JWT 中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未认证")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		response.Fail(c, response.UNAUTHORIZED, "用户ID类型错误")
		return
	}

	// 生成配对码
	device, err := h.deviceService.GeneratePairingCode(c.Request.Context(), userIDUint)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, gin.H{
		"pairing_code": device.PairingCode,
		"expires_at":   device.PairingExpiresAt,
		"pairing_id":   device.ID,
	})
}

// GetPairingStatus 获取配对状态
func (h *DeviceHandler) GetPairingStatus(c *gin.Context) {
	var req struct {
		PairingCode string `json:"pairing_code" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	// 从 JWT 中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未认证")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		response.Fail(c, response.UNAUTHORIZED, "用户ID类型错误")
		return
	}

	// 获取配对状态
	device, err := h.deviceService.GetPairingStatus(c.Request.Context(), req.PairingCode, userIDUint)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, gin.H{
		"pairing": device,
	})
}

// GetDeviceList 获取设备列表（管理员）
func (h *DeviceHandler) GetDeviceList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	statusStr := c.Query("status")
	keyword := c.Query("keyword")

	var status *int
	if statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			status = &s
		}
	}

	devices, total, err := h.deviceService.GetDeviceList(c.Request.Context(), page, pageSize, nil, status, keyword)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取设备列表失败")
		return
	}

	response.Success(c, gin.H{
		"devices": devices,
		"total":   total,
		"page":    page,
		"size":    pageSize,
	})
}

// GetDeviceInfoByID 根据ID获取设备信息（管理员）
func (h *DeviceHandler) GetDeviceInfoByID(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		response.Fail(c, response.INVALID_PARAMS, "设备ID不能为空")
		return
	}

	device, err := h.deviceService.GetDeviceByID(c.Request.Context(), deviceID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取设备信息失败")
		return
	}

	response.Success(c, device)
}

// DeleteDeviceByID 删除设备（管理员）
func (h *DeviceHandler) DeleteDeviceByID(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		response.Fail(c, response.INVALID_PARAMS, "设备ID不能为空")
		return
	}

	err := h.deviceService.DeleteDevice(c.Request.Context(), deviceID, 0) // 管理员删除不需要验证用户ID
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "删除设备失败")
		return
	}

	response.Success(c, gin.H{"message": "设备删除成功"})
}

// GetOnlineDeviceCount 获取在线设备数量（管理员）
func (h *DeviceHandler) GetOnlineDeviceCount(c *gin.Context) {
	count, err := h.deviceService.GetOnlineDeviceCount(c.Request.Context())
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取在线设备数量失败")
		return
	}

	response.Success(c, gin.H{"count": count})
}

// SubmitDeviceInfoRequest 提交设备信息请求（公开接口）
type SubmitDeviceInfoRequest struct {
	PairingCode    string `json:"pairing_code" binding:"required"`
	DeviceID       string `json:"device_id" binding:"required"`        // 设备ID
	Name           string `json:"name" binding:"omitempty"`            // 设备名称（可选，不传则使用device_model+android_version组合）
	DeviceModel    string `json:"device_model" binding:"omitempty"`    // 设备型号
	AndroidVersion string `json:"android_version" binding:"omitempty"` // 安卓版本
	AutoxVersion   string `json:"autox_version" binding:"omitempty"`   // AutoX版本
}

// SubmitDeviceInfo 提交设备信息（公开接口）
func (h *DeviceHandler) SubmitDeviceInfo(c *gin.Context) {
	var req struct {
		PairingCode    string `json:"pairing_code" binding:"required"`
		DeviceID       string `json:"device_id" binding:"required"`
		Name           string `json:"name" binding:"omitempty"`
		DeviceModel    string `json:"device_model" binding:"required"`
		AndroidVersion string `json:"android_version" binding:"omitempty"`
		AutoxVersion   string `json:"autox_version" binding:"omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误: "+err.Error())
		return
	}

	// 如果名称为空，则使用device_model+android_version组合
	deviceName := req.Name
	if deviceName == "" {
		if req.AndroidVersion != "" {
			deviceName = req.DeviceModel + " " + req.AndroidVersion
		} else {
			deviceName = req.DeviceModel
		}
	}

	device, err := h.deviceService.SubmitDeviceInfo(c.Request.Context(), req.PairingCode, req.DeviceID, deviceName, req.DeviceModel, req.AndroidVersion, req.AutoxVersion)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	// 创建数据库中的 refresh token
	refreshToken, err := h.deviceService.CreateDeviceRefreshToken(device.UserID, device.DeviceID)
	if err != nil {
		response.Fail(c, response.ERROR, "生成刷新令牌失败")
		return
	}

	response.Success(c, gin.H{
		"message":          "设备信息提交成功",
		"device_id":        device.DeviceID,
		"access_token":     device.Token,
		"refresh_token":    refreshToken,
		"token_expires_at": device.TokenExpiresAt,
	})
}
