package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// DeviceHandler 设备处理器
type DeviceHandler struct {
	deviceService *service.DeviceService
}

// NewDeviceHandler 创建设备处理器
func NewDeviceHandler(deviceService *service.DeviceService) *DeviceHandler {
	return &DeviceHandler{
		deviceService: deviceService,
	}
}

// RegisterDeviceRequest 注册设备请求
type RegisterDeviceRequest struct {
	DeviceID  string `json:"device_id" binding:"required"`
	Name      string `json:"name" binding:"required"`
	Type      string `json:"type" binding:"omitempty"`
	Version   string `json:"version" binding:"omitempty"`
}

// RegisterDevice 注册设备
func (h *DeviceHandler) RegisterDevice(c *gin.Context) {
	var req RegisterDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	userID := c.GetUint("user_id")
	ip := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	if req.Type == "" {
		req.Type = "android"
	}

	device, err := h.deviceService.RegisterDevice(
		c.Request.Context(),
		req.DeviceID,
		req.Name,
		req.Type,
		ip,
		userAgent,
		req.Version,
		userID,
	)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "注册设备失败")
		return
	}

	response.Success(c, gin.H{
		"device_id":   device.DeviceID,
		"name":        device.Name,
		"type":        device.Type,
		"status":      device.Status,
		"last_active": device.LastActive,
	})
}

// UpdateDeviceStatusRequest 更新设备状态请求
type UpdateDeviceStatusRequest struct {
	DeviceID string `json:"device_id" binding:"required"`
	Status   int8   `json:"status" binding:"required,oneof=0 1 2"`
}

// UpdateDeviceStatus 更新设备状态
func (h *DeviceHandler) UpdateDeviceStatus(c *gin.Context) {
	var req UpdateDeviceStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.deviceService.UpdateDeviceStatus(c.Request.Context(), req.DeviceID, req.Status)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新设备状态失败")
		return
	}

	response.Success(c, gin.H{"message": "设备状态更新成功"})
}

// GetDeviceInfo 获取设备信息
func (h *DeviceHandler) GetDeviceInfo(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		response.Fail(c, response.INVALID_PARAMS, "设备ID不能为空")
		return
	}

	device, err := h.deviceService.GetDeviceByID(c.Request.Context(), deviceID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取设备信息失败")
		return
	}

	response.Success(c, device)
}

// GetDeviceList 获取设备列表
func (h *DeviceHandler) GetDeviceList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	keyword := c.Query("keyword")
	
	var userID *uint
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if uid, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			userIDUint := uint(uid)
			userID = &userIDUint
		}
	}
	
	var status *int8
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.ParseInt(statusStr, 10, 8); err == nil {
			statusInt8 := int8(s)
			status = &statusInt8
		}
	}

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	devices, total, err := h.deviceService.GetDeviceList(c.Request.Context(), page, pageSize, userID, status, keyword)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取设备列表失败")
		return
	}

	response.Success(c, gin.H{
		"list":      devices,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// GetUserDevices 获取用户设备列表
func (h *DeviceHandler) GetUserDevices(c *gin.Context) {
	userID := c.GetUint("user_id")

	devices, err := h.deviceService.GetUserDevices(c.Request.Context(), userID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取用户设备失败")
		return
	}

	response.Success(c, gin.H{"devices": devices})
}

// DeleteDeviceRequest 删除设备请求
type DeleteDeviceRequest struct {
	DeviceID string `json:"device_id" binding:"required"`
}

// DeleteDevice 删除设备
func (h *DeviceHandler) DeleteDevice(c *gin.Context) {
	var req DeleteDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.deviceService.DeleteDevice(c.Request.Context(), req.DeviceID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "删除设备失败")
		return
	}

	response.Success(c, gin.H{"message": "设备删除成功"})
}

// CreateDeviceGroupRequest 创建设备分组请求
type CreateDeviceGroupRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	DeviceIDs   []uint `json:"device_ids"`
}

// CreateDeviceGroup 创建设备分组
func (h *DeviceHandler) CreateDeviceGroup(c *gin.Context) {
	var req CreateDeviceGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	userID := c.GetUint("user_id")

	group, err := h.deviceService.CreateDeviceGroup(c.Request.Context(), req.Name, req.Description, userID, req.DeviceIDs)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "创建设备分组失败")
		return
	}

	response.Success(c, group)
}

// GetDeviceGroups 获取设备分组列表
func (h *DeviceHandler) GetDeviceGroups(c *gin.Context) {
	userID := c.GetUint("user_id")

	groups, err := h.deviceService.GetDeviceGroups(c.Request.Context(), userID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取设备分组失败")
		return
	}

	response.Success(c, gin.H{"groups": groups})
}

// UpdateDeviceGroupRequest 更新设备分组请求
type UpdateDeviceGroupRequest struct {
	GroupID     uint   `json:"group_id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	DeviceIDs   []uint `json:"device_ids"`
}

// UpdateDeviceGroup 更新设备分组
func (h *DeviceHandler) UpdateDeviceGroup(c *gin.Context) {
	var req UpdateDeviceGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.deviceService.UpdateDeviceGroup(c.Request.Context(), req.GroupID, req.Name, req.Description, req.DeviceIDs)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新设备分组失败")
		return
	}

	response.Success(c, gin.H{"message": "设备分组更新成功"})
}

// DeleteDeviceGroupRequest 删除设备分组请求
type DeleteDeviceGroupRequest struct {
	GroupID uint `json:"group_id" binding:"required"`
}

// DeleteDeviceGroup 删除设备分组
func (h *DeviceHandler) DeleteDeviceGroup(c *gin.Context) {
	var req DeleteDeviceGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.deviceService.DeleteDeviceGroup(c.Request.Context(), req.GroupID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "删除设备分组失败")
		return
	}

	response.Success(c, gin.H{"message": "设备分组删除成功"})
}

// GetOnlineDeviceCount 获取在线设备数量
func (h *DeviceHandler) GetOnlineDeviceCount(c *gin.Context) {
	count, err := h.deviceService.GetOnlineDeviceCount(c.Request.Context())
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取在线设备数量失败")
		return
	}

	response.Success(c, gin.H{"count": count})
}
