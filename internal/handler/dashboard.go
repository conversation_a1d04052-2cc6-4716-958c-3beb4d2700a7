package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// DashboardHandler 仪表盘处理器
type DashboardHandler struct {
	userService      *service.UserService
	deviceService    *service.DeviceService
	scriptService    *service.ScriptService
	taskService      *service.TaskService
	systemLogService *service.SystemLogService
}

// NewDashboardHandler 创建仪表盘处理器
func NewDashboardHandler(
	userService *service.UserService,
	deviceService *service.DeviceService,
	scriptService *service.ScriptService,
	taskService *service.TaskService,
	systemLogService *service.SystemLogService,
) *DashboardHandler {
	return &DashboardHandler{
		userService:      userService,
		deviceService:    deviceService,
		scriptService:    scriptService,
		taskService:      taskService,
		systemLogService: systemLogService,
	}
}

// GetOverview 获取概览信息
func (h *DashboardHandler) GetOverview(c *gin.Context) {
	// 获取用户统计
	userStats, err := h.userService.GetUserStatistics(c.Request.Context())
	if err != nil {
		response.Fail(c, response.ERROR, "获取用户统计失败")
		return
	}

	// 获取在线设备数量
	onlineDeviceCount, err := h.deviceService.GetOnlineDeviceCount(c.Request.Context())
	if err != nil {
		response.Fail(c, response.ERROR, "获取在线设备数量失败")
		return
	}

	// 获取任务统计
	taskStats, err := h.taskService.GetTaskStatistics(c.Request.Context(), nil)
	if err != nil {
		response.Fail(c, response.ERROR, "获取任务统计失败")
		return
	}

	// 获取日志统计
	logStats, err := h.systemLogService.GetLogStatistics(c.Request.Context(), nil, nil)
	if err != nil {
		response.Fail(c, response.ERROR, "获取日志统计失败")
		return
	}

	// 获取最近的错误日志
	recentErrorLogs, err := h.systemLogService.GetRecentLogs(c.Request.Context(), 10, "", "error")
	if err != nil {
		response.Fail(c, response.ERROR, "获取最近错误日志失败")
		return
	}

	overview := gin.H{
		"users": gin.H{
			"total":            userStats["total"],
			"active":           userStats["active"],
			"today_registered": userStats["today_registered"],
		},
		"devices": gin.H{
			"online": onlineDeviceCount,
		},
		"tasks": gin.H{
			"total":     taskStats["total"],
			"pending":   taskStats["pending"],
			"running":   taskStats["running"],
			"completed": taskStats["completed"],
			"failed":    taskStats["failed"],
		},
		"logs": gin.H{
			"total":       logStats["total"],
			"by_level":    logStats["by_level"],
			"recent_errors": recentErrorLogs,
		},
	}

	response.Success(c, overview)
}

// GetUserDashboard 获取用户仪表盘
func (h *DashboardHandler) GetUserDashboard(c *gin.Context) {
	userID := c.GetUint("user_id")

	// 获取用户设备
	userDevices, err := h.deviceService.GetUserDevices(c.Request.Context(), userID)
	if err != nil {
		response.Fail(c, response.ERROR, "获取用户设备失败")
		return
	}

	// 获取用户脚本
	userScripts, err := h.scriptService.GetUserScripts(c.Request.Context(), userID)
	if err != nil {
		response.Fail(c, response.ERROR, "获取用户脚本失败")
		return
	}

	// 获取用户最近任务
	userTasks, err := h.taskService.GetUserTasks(c.Request.Context(), userID, 10)
	if err != nil {
		response.Fail(c, response.ERROR, "获取用户任务失败")
		return
	}

	// 获取用户任务统计
	userTaskStats, err := h.taskService.GetTaskStatistics(c.Request.Context(), &userID)
	if err != nil {
		response.Fail(c, response.ERROR, "获取用户任务统计失败")
		return
	}

	// 统计设备状态
	deviceStats := gin.H{
		"total":   len(userDevices),
		"online":  0,
		"offline": 0,
		"busy":    0,
	}

	for _, device := range userDevices {
		switch device.Status {
		case 1:
			deviceStats["online"] = deviceStats["online"].(int) + 1
		case 2:
			deviceStats["busy"] = deviceStats["busy"].(int) + 1
		default:
			deviceStats["offline"] = deviceStats["offline"].(int) + 1
		}
	}

	dashboard := gin.H{
		"devices": gin.H{
			"list":  userDevices,
			"stats": deviceStats,
		},
		"scripts": gin.H{
			"list":  userScripts,
			"total": len(userScripts),
		},
		"tasks": gin.H{
			"recent": userTasks,
			"stats":  userTaskStats,
		},
	}

	response.Success(c, dashboard)
}

// GetSystemStatus 获取系统状态
func (h *DashboardHandler) GetSystemStatus(c *gin.Context) {
	// 这里可以添加系统健康检查逻辑
	// 比如检查数据库连接、内存使用、CPU使用等

	status := gin.H{
		"status":    "healthy",
		"timestamp": gin.H{
			"server_time": gin.H{
				"unix":      gin.H{},
				"formatted": gin.H{},
			},
		},
		"services": gin.H{
			"database": gin.H{
				"status": "connected",
			},
			"cache": gin.H{
				"status": "connected",
			},
		},
	}

	response.Success(c, status)
}
