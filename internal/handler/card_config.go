package handler

import (
	"encoding/json"
	"strconv"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// CardConfigHandler 卡密配置处理器
type CardConfigHandler struct {
	cardConfigService *service.CardConfigService
}

// NewCardConfigHandler 创建卡密配置处理器
func NewCardConfigHandler(cardConfigService *service.CardConfigService) *CardConfigHandler {
	return &CardConfigHandler{
		cardConfigService: cardConfigService,
	}
}

// UploadConfigRequest 上传配置请求
type UploadConfigRequest struct {
	CardKey string      `json:"card_key" binding:"required"`
	Config  interface{} `json:"config" binding:"required"`
}

// UploadConfig 上传配置
func (h *CardConfigHandler) UploadConfig(c *gin.Context) {
	var req UploadConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	cardConfig, err := h.cardConfigService.UploadConfig(c.Request.Context(), req.CardKey, req.Config)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "上传配置失败")
		return
	}

	response.Success(c, gin.H{
		"card_key": cardConfig.CardKey,
		"version":  cardConfig.Version,
		"message":  "配置上传成功",
	})
}

// GetConfigRequest 获取配置请求
type GetConfigRequest struct {
	CardKey string `json:"card_key" binding:"required"`
}

// GetConfig 获取配置
func (h *CardConfigHandler) GetConfig(c *gin.Context) {
	var req GetConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	cardConfig, err := h.cardConfigService.GetConfig(c.Request.Context(), req.CardKey)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取配置失败")
		return
	}

	// 解析配置JSON
	var config interface{}
	if err := json.Unmarshal([]byte(cardConfig.Config), &config); err != nil {
		response.Fail(c, response.ERROR, "配置解析失败")
		return
	}

	response.Success(c, gin.H{
		"card_key":   cardConfig.CardKey,
		"config":     config,
		"version":    cardConfig.Version,
		"updated_at": cardConfig.UpdatedAt,
	})
}

// SyncConfigRequest 同步配置请求
type SyncConfigRequest struct {
	CardKey       string `json:"card_key" binding:"required"`
	ClientVersion int64  `json:"client_version"`
}

// SyncConfig 同步配置
func (h *CardConfigHandler) SyncConfig(c *gin.Context) {
	var req SyncConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	cardConfig, needUpdate, err := h.cardConfigService.SyncConfig(c.Request.Context(), req.CardKey, req.ClientVersion)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "同步配置失败")
		return
	}

	result := gin.H{
		"need_update":    needUpdate,
		"server_version": cardConfig.Version,
	}

	if needUpdate {
		// 解析配置JSON
		var config interface{}
		if err := json.Unmarshal([]byte(cardConfig.Config), &config); err != nil {
			response.Fail(c, response.ERROR, "配置解析失败")
			return
		}

		result["config"] = config
		result["updated_at"] = cardConfig.UpdatedAt
	}

	response.Success(c, result)
}

// DeleteConfigRequest 删除配置请求
type DeleteConfigRequest struct {
	CardKey string `json:"card_key" binding:"required"`
}

// DeleteConfig 删除配置
func (h *CardConfigHandler) DeleteConfig(c *gin.Context) {
	var req DeleteConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.cardConfigService.DeleteConfig(c.Request.Context(), req.CardKey)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "删除配置失败")
		return
	}

	response.Success(c, gin.H{"message": "删除配置成功"})
}

// ListConfigs 获取配置列表（管理员用）
func (h *CardConfigHandler) ListConfigs(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	configs, total, err := h.cardConfigService.ListConfigs(c.Request.Context(), page, pageSize)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取配置列表失败")
		return
	}

	// 处理配置数据，隐藏敏感信息
	var result []gin.H
	for _, config := range configs {
		// 只显示配置的基本信息，不显示完整配置内容
		configPreview := "..."
		if len(config.Config) > 100 {
			configPreview = config.Config[:100] + "..."
		} else {
			configPreview = config.Config
		}

		result = append(result, gin.H{
			"id":             config.ID,
			"card_key":       config.CardKey,
			"config_preview": configPreview,
			"version":        config.Version,
			"created_at":     config.CreatedAt,
			"updated_at":     config.UpdatedAt,
		})
	}

	response.Success(c, gin.H{
		"list":      result,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}
