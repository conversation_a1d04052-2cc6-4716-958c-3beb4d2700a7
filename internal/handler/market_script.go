package handler

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// MarketScriptHandler 脚本市场处理器
type MarketScriptHandler struct {
	marketScriptService *service.MarketScriptService
}

// NewMarketScriptHandler 创建脚本市场处理器
func NewMarketScriptHandler(marketScriptService *service.MarketScriptService) *MarketScriptHandler {
	return &MarketScriptHandler{
		marketScriptService: marketScriptService,
	}
}

// MarketScriptListItemResponse 用于脚本市场列表接口的精简返回
// 只包含列表页需要的字段
// 注意：如需扩展字段请同步 service/handler 层

type MarketScriptListItemResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Summary     string    `json:"summary"`
	Icon        string    `json:"icon"`
	Version     string    `json:"version"`
	Tags        string    `json:"tags"`
	Author      string    `json:"author"`
	AuthorID    uint      `json:"author_id"`
	Downloads   int       `json:"downloads"`
	Rating      float64   `json:"rating"`
	RatingCount int       `json:"rating_count"`
	Status      int8      `json:"status"`
	IsFeatured  bool      `json:"is_featured"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// MarketScriptDetailResponse 用于脚本市场详情接口的精简返回
// 不包含 content、config 字段

type MarketScriptDetailResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Summary     string    `json:"summary"`
	Description string    `json:"description"`
	Icon        string    `json:"icon"`
	Version     string    `json:"version"`
	Tags        string    `json:"tags"`
	Author      string    `json:"author"`
	AuthorID    uint      `json:"author_id"`
	Downloads   int       `json:"downloads"`
	Rating      float64   `json:"rating"`
	RatingCount int       `json:"rating_count"`
	Status      int8      `json:"status"`
	IsFeatured  bool      `json:"is_featured"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// GetMarketScriptList 获取脚本市场列表
func (h *MarketScriptHandler) GetMarketScriptList(c *gin.Context) {
	// 定义请求参数结构体
	var req struct {
		Page       int    `json:"page"`
		PageSize   int    `json:"page_size"`
		Keyword    string `json:"keyword"`
		IsFeatured *bool  `json:"is_featured"`
		Limit      int    `json:"limit"`
	}

	// 绑定JSON参数
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.Limit > 0 {
		req.PageSize = req.Limit
	}

	// 调用服务
	scripts, total, err := h.marketScriptService.GetMarketScriptList(c.Request.Context(), req.Page, req.PageSize, req.Keyword, req.IsFeatured)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	// 转换为 handler 层的响应结构体
	respList := make([]MarketScriptListItemResponse, 0, len(scripts))
	for _, s := range scripts {
		respList = append(respList, MarketScriptListItemResponse{
			ID:          s.ID,
			Name:        s.Name,
			Summary:     s.Summary,
			Icon:        s.Icon,
			Version:     s.Version,
			Tags:        s.Tags,
			Author:      s.Author,
			AuthorID:    s.AuthorID,
			Downloads:   s.Downloads,
			Rating:      s.Rating,
			RatingCount: s.RatingCount,
			Status:      s.Status,
			IsFeatured:  s.IsFeatured,
			CreatedAt:   s.CreatedAt,
			UpdatedAt:   s.UpdatedAt,
		})
	}

	// 返回标准结构体
	response.Success(c, gin.H{
		"list":  respList,
		"total": total,
		"page":  req.Page,
		"size":  req.PageSize,
	})
}

// GetMarketScriptDetail 获取脚本详情
func (h *MarketScriptHandler) GetMarketScriptDetail(c *gin.Context) {
	// 定义请求参数结构体
	var req struct {
		ID uint `json:"id" binding:"required"`
	}

	// 绑定JSON参数
	if err := c.ShouldBindJSON(&req); err != nil {
		// 如果JSON绑定失败，尝试从URL参数获取（向后兼容）
		scriptIDStr := c.Param("id")
		if scriptIDStr == "" {
			response.Fail(c, response.INVALID_PARAMS, "脚本ID不能为空")
			return
		}

		scriptID, err := strconv.ParseUint(scriptIDStr, 10, 32)
		if err != nil {
			response.Fail(c, response.INVALID_PARAMS, "无效的脚本ID")
			return
		}
		req.ID = uint(scriptID)
	}

	script, err := h.marketScriptService.GetMarketScriptDetail(c.Request.Context(), req.ID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	resp := MarketScriptDetailResponse{
		ID:          script.ID,
		Name:        script.Name,
		Summary:     script.Summary,
		Description: script.Description,
		Icon:        script.Icon,
		Version:     script.Version,
		Tags:        script.Tags,
		Author:      script.Author,
		AuthorID:    script.AuthorID,
		Downloads:   script.Downloads,
		Rating:      script.Rating,
		RatingCount: script.RatingCount,
		Status:      script.Status,
		IsFeatured:  script.IsFeatured,
		CreatedAt:   script.CreatedAt,
		UpdatedAt:   script.UpdatedAt,
	}

	response.Success(c, resp)
}

// GetUserInstalledScripts 获取用户已安装的脚本
func (h *MarketScriptHandler) GetUserInstalledScripts(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.ERROR, "用户未登录")
		return
	}

	scripts, err := h.marketScriptService.GetUserInstalledScripts(c.Request.Context(), userID.(uint))
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	response.Success(c, scripts)
}

// RateScript 为脚本评分
func (h *MarketScriptHandler) RateScript(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.ERROR, "用户未登录")
		return
	}

	// 获取请求参数
	var req struct {
		ScriptID uint   `json:"script_id" binding:"required"`
		Rating   int8   `json:"rating" binding:"required,min=1,max=5"`
		Comment  string `json:"comment"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "请求参数错误")
		return
	}

	err := h.marketScriptService.RateScript(c.Request.Context(), req.ScriptID, userID.(uint), req.Rating, req.Comment)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	response.Success(c, gin.H{"message": "评分成功"})
}

// GetFeaturedScripts 获取推荐脚本
func (h *MarketScriptHandler) GetFeaturedScripts(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "6"))

	scripts, err := h.marketScriptService.GetFeaturedScripts(c.Request.Context(), limit)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	response.Success(c, scripts)
}
