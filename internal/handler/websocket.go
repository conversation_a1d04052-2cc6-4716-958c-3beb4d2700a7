package handler

import (
	"net/http"

	"goadmin/internal/service"

	"github.com/gin-gonic/gin"
)

// WebSocketHandler WebSocket处理器
type WebSocketHandler struct {
	wsService *service.WebSocketService
}

// NewWebSocketHandler 创建WebSocket处理器
func NewWebSocketHandler(wsService *service.WebSocketService) *WebSocketHandler {
	return &WebSocketHandler{
		wsService: wsService,
	}
}

// HandleWebSocket 处理WebSocket连接
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	h.wsService.ServeWebSocket(c.Writer, c.Request)
}

// GetOnlineDevices 获取在线设备列表
func (h *WebSocketHandler) GetOnlineDevices(c *gin.Context) {
	devices := h.wsService.GetOnlineDevices()
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "成功",
		"data":    devices,
	})
}

// SendScriptTask 发送脚本任务
func (h *WebSocketHandler) SendScriptTask(c *gin.Context) {
	var req struct {
		DeviceIDs []string               `json:"device_ids" binding:"required"`
		ScriptID  string                 `json:"script_id" binding:"required"`
		Action    string                 `json:"action" binding:"required"`
		Params    map[string]interface{} `json:"params"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    1,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	err := h.wsService.SendScriptTask(req.DeviceIDs, req.ScriptID, req.Action, req.Params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    1,
			"message": "发送任务失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "任务发送成功",
	})
}
