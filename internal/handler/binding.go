package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

type BindingHandler struct {
	bindingService *service.BindingService
	jwtService     *service.JWTService
}

func NewBindingHandler(bindingService *service.BindingService, jwtService *service.JWTService) *BindingHandler {
	return &BindingHandler{bindingService: bindingService, jwtService: jwtService}
}

// ListBindings 获取绑定列表
func (h *BindingHandler) ListBindings(c *gin.Context) {
	var req struct {
		Page     int    `json:"page" binding:"required,min=1"`
		PageSize int    `json:"page_size" binding:"required,min=1,max=100"`
		CardKey  string `json:"card_key"`
		DeviceID string `json:"device_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, err.Error())
		return
	}

	bindings, total, err := h.bindingService.ListBindings(req.Page, req.PageSize, req.<PERSON>, req.Device<PERSON>)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, gin.H{
		"items": bindings,
		"total": total,
	})
}

// BindDevice 绑定设备
func (h *BindingHandler) BindDevice(c *gin.Context) {
	var req struct {
		CardKey  string `json:"card_key" binding:"required"`
		DeviceID string `json:"device_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, err.Error())
		return
	}

	if err := h.bindingService.BindDevice(c, req.CardKey, req.DeviceID); err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, nil)
}

// UnbindDevice 解绑设备（支持批量）
func (h *BindingHandler) UnbindDevice(c *gin.Context) {
	var req struct {
		CardKey   string   `json:"card_key" binding:"required"`
		DeviceIDs []string `json:"device_ids" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, err.Error())
		return
	}

	// 判断是否是管理员操作
	isAdmin, exists := c.Get("is_admin")
	// 默认为非管理员
	isAdminBool := exists && isAdmin.(bool)

	if err := h.bindingService.UnbindDevice(c.Request.Context(), req.CardKey, req.DeviceIDs, isAdminBool); err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, nil)
}

// UnbindAllDevices 解绑所有设备
func (h *BindingHandler) UnbindAllDevices(c *gin.Context) {
	var req struct {
		CardKey string `json:"card_key" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, err.Error())
		return
	}

	// 判断是否是管理员操作
	isAdmin, exists := c.Get("is_admin")
	// 默认为非管理员
	isAdminBool := exists && isAdmin.(bool)

	if err := h.bindingService.UnbindAllDevices(c.Request.Context(), req.CardKey, isAdminBool); err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, nil)
}

// GetStats 获取统计数据
func (h *BindingHandler) GetStats(c *gin.Context) {
	stats, err := h.bindingService.GetStats()
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}
	response.Success(c, stats)
}
