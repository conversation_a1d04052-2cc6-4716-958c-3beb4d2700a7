package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/pkg/response"
)

// EventRequest 事件请求
type EventRequest struct {
	Event interface{} `json:"event" binding:"required"`
}

// EventHandler 事件处理器
type EventHandler struct {
}

// NewEventHandler 创建事件处理器
func NewEventHandler() *EventHandler {
	return &EventHandler{}
}

// HandleEvent 处理事件接口
func (h *EventHandler) HandleEvent(c *gin.Context) {
	var req EventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	// 仅接收参数，不做任何处理，直接返回成功
	response.Success(c, gin.H{"message": "事件接收成功"})
}
