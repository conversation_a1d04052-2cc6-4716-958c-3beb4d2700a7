package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/internal/model"
	"goadmin/internal/service"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

type AdminHandler struct {
	adminService *service.AdminService
	jwtService   *service.JWTService
}

func NewAdminHandler(adminService *service.AdminService, jwtService *service.JWTService) *AdminHandler {
	return &AdminHandler{
		adminService: adminService,
		jwtService:   jwtService,
	}
}

// Login 管理员登录
func (h *AdminHandler) Login(c *gin.Context) {
	var req struct {
		Username string `json:"username" binding:"required"`
		Password string `json:"password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "用户名和密码不能为空")
		return
	}

	admin, err := h.adminService.Login(c, req.Username, req.Password)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "登录失败")
		return
	}

	// 生成JWT令牌
	token, err := h.jwtService.GenerateToken(admin.ID)
	if err != nil {
		response.Fail(c, response.ERROR, "生成令牌失败")
		return
	}

	response.Success(c, gin.H{
		"token": token,
		"admin": admin,
	})
}

// CreateAdmin 创建管理员
func (h *AdminHandler) CreateAdmin(c *gin.Context) {
	var admin model.Admin
	if err := c.ShouldBindJSON(&admin); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的参数")
		return
	}
	logger.Info(c, "CreateAdmin admin%+v", admin)
	if err := h.adminService.CreateAdmin(c, &admin); err != nil {
		response.Fail(c, response.ERROR, "创建管理员失败")
		return
	}

	response.Success(c, admin)
}

// UpdateAdmin 更新管理员信息
func (h *AdminHandler) UpdateAdmin(c *gin.Context) {
	var admin model.Admin
	if err := c.ShouldBindJSON(&admin); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的参数")
		return
	}

	if err := h.adminService.UpdateAdmin(&admin); err != nil {
		response.Fail(c, response.ERROR, "更新管理员信息失败")
		return
	}

	response.Success(c, admin)
}

// GetAdmin 获取管理员信息
func (h *AdminHandler) GetAdmin(c *gin.Context) {
	var req struct {
		ID uint `json:"id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "ID不能为空")
		return
	}

	admin, err := h.adminService.GetAdmin(req.ID)
	if err != nil {
		response.Fail(c, response.ERROR, "获取管理员信息失败")
		return
	}

	response.Success(c, admin)
}

// ListAdmins 获取管理员列表
func (h *AdminHandler) ListAdmins(c *gin.Context) {
	var req struct {
		Page     int `json:"page" binding:"required,min=1"`
		PageSize int `json:"page_size" binding:"required,min=1,max=100"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "分页参数错误")
		return
	}

	admins, total, err := h.adminService.ListAdmins(req.Page, req.PageSize)
	if err != nil {
		response.Fail(c, response.ERROR, "获取管理员列表失败")
		return
	}

	response.Success(c, gin.H{
		"total": total,
		"items": admins,
	})
}

// DeleteAdmin 删除管理员
func (h *AdminHandler) DeleteAdmin(c *gin.Context) {
	var req struct {
		ID uint `json:"id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "ID不能为空")
		return
	}

	if err := h.adminService.DeleteAdmin(req.ID); err != nil {
		response.Fail(c, response.ERROR, "删除管理员失败")
		return
	}

	response.Success(c, nil)
}

// UpdateStatus 更新管理员状态
func (h *AdminHandler) UpdateStatus(c *gin.Context) {
	var req struct {
		ID     uint `json:"id" binding:"required"`
		Status int8 `json:"status" binding:"required,oneof=1 2"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	if err := h.adminService.UpdateStatus(req.ID, req.Status); err != nil {
		response.Fail(c, response.ERROR, "更新状态失败")
		return
	}

	response.Success(c, nil)
}
