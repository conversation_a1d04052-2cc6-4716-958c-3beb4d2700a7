package handler

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// SystemLogHandler 系统日志处理器
type SystemLogHandler struct {
	systemLogService *service.SystemLogService
}

// NewSystemLogHandler 创建系统日志处理器
func NewSystemLogHandler(systemLogService *service.SystemLogService) *SystemLogHandler {
	return &SystemLogHandler{
		systemLogService: systemLogService,
	}
}

// GetLogList 获取日志列表
func (h *SystemLogHandler) GetLogList(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "10"))
	logType := c.Query("type")
	level := c.Query("level")

	var userID, deviceID, taskID *uint
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if uid, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			userIDUint := uint(uid)
			userID = &userIDUint
		}
	}
	if deviceIDStr := c.Query("device_id"); deviceIDStr != "" {
		if did, err := strconv.ParseUint(deviceIDStr, 10, 32); err == nil {
			deviceIDUint := uint(did)
			deviceID = &deviceIDUint
		}
	}
	if taskIDStr := c.Query("task_id"); taskIDStr != "" {
		if tid, err := strconv.ParseUint(taskIDStr, 10, 32); err == nil {
			taskIDUint := uint(tid)
			taskID = &taskIDUint
		}
	}

	var startTime, endTime *time.Time
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if st, err := time.Parse("2006-01-02 15:04:05", startTimeStr); err == nil {
			startTime = &st
		}
	}
	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if et, err := time.Parse("2006-01-02 15:04:05", endTimeStr); err == nil {
			endTime = &et
		}
	}

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	logs, total, err := h.systemLogService.GetLogList(
		c.Request.Context(),
		page,
		pageSize,
		logType,
		level,
		userID,
		deviceID,
		taskID,
		startTime,
		endTime,
	)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取日志列表失败")
		return
	}

	response.Success(c, gin.H{
		"list":      logs,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// GetLogStatistics 获取日志统计信息
func (h *SystemLogHandler) GetLogStatistics(c *gin.Context) {
	var startTime, endTime *time.Time
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if st, err := time.Parse("2006-01-02 15:04:05", startTimeStr); err == nil {
			startTime = &st
		}
	}
	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if et, err := time.Parse("2006-01-02 15:04:05", endTimeStr); err == nil {
			endTime = &et
		}
	}

	stats, err := h.systemLogService.GetLogStatistics(c.Request.Context(), startTime, endTime)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取日志统计失败")
		return
	}

	response.Success(c, stats)
}

// GetRecentLogs 获取最近的日志
func (h *SystemLogHandler) GetRecentLogs(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	logType := c.Query("type")
	level := c.Query("level")

	if limit < 1 || limit > 1000 {
		limit = 50
	}

	logs, err := h.systemLogService.GetRecentLogs(c.Request.Context(), limit, logType, level)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取最近日志失败")
		return
	}

	response.Success(c, gin.H{"logs": logs})
}

// CleanupLogsRequest 清理日志请求
type CleanupLogsRequest struct {
	RetentionDays int `json:"retention_days" binding:"required,min=1,max=365"`
}

// CleanupLogs 清理日志
func (h *SystemLogHandler) CleanupLogs(c *gin.Context) {
	var req CleanupLogsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	deletedCount, err := h.systemLogService.CleanupLogs(c.Request.Context(), req.RetentionDays)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "清理日志失败")
		return
	}

	response.Success(c, gin.H{
		"message":       "日志清理成功",
		"deleted_count": deletedCount,
	})
}

// CreateLogRequest 创建日志请求
type CreateLogRequest struct {
	Type     string      `json:"type" binding:"required"`
	Level    string      `json:"level" binding:"required,oneof=info warn error"`
	Message  string      `json:"message" binding:"required"`
	Data     interface{} `json:"data"`
	UserID   *uint       `json:"user_id"`
	DeviceID *uint       `json:"device_id"`
	TaskID   *uint       `json:"task_id"`
}

// CreateLog 创建日志
func (h *SystemLogHandler) CreateLog(c *gin.Context) {
	var req CreateLogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	ip := c.ClientIP()

	err := h.systemLogService.CreateLog(
		c.Request.Context(),
		req.Type,
		req.Level,
		req.Message,
		req.Data,
		req.UserID,
		req.DeviceID,
		req.TaskID,
		ip,
	)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "创建日志失败")
		return
	}

	response.Success(c, gin.H{"message": "日志创建成功"})
}

// GetLogsByTimeRange 按时间范围获取日志
func (h *SystemLogHandler) GetLogsByTimeRange(c *gin.Context) {
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")
	logType := c.Query("type")
	level := c.Query("level")

	if startTimeStr == "" || endTimeStr == "" {
		response.Fail(c, response.INVALID_PARAMS, "开始时间和结束时间不能为空")
		return
	}

	startTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr)
	if err != nil {
		response.Fail(c, response.INVALID_PARAMS, "开始时间格式错误")
		return
	}

	endTime, err := time.Parse("2006-01-02 15:04:05", endTimeStr)
	if err != nil {
		response.Fail(c, response.INVALID_PARAMS, "结束时间格式错误")
		return
	}

	logs, err := h.systemLogService.GetLogsByTimeRange(c.Request.Context(), startTime, endTime, logType, level)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取时间范围日志失败")
		return
	}

	response.Success(c, gin.H{"logs": logs})
}
