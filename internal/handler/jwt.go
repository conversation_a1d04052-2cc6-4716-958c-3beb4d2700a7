package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// TokenRequest 生成令牌请求
type TokenRequest struct {
	UserID string `json:"user_id" binding:"required"` // 用户ID
}

// TokenResponse 令牌响应
type TokenResponse struct {
	Token string `json:"token"` // JWT令牌
}



type JWTHandler struct {
	jwtService *service.JWTService
}

func NewJWTHandler(jwtService *service.JWTService) *JWTHandler {
	return &JWTHandler{
		jwtService: jwtService,
	}
}

// GenerateToken 生成JWT令牌
func (h *JWTHandler) GenerateToken(c *gin.Context) {
	var req TokenRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "用户ID不能为空")
		return
	}

	token, err := h.jwtService.GenerateUserToken(req.UserID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "生成令牌失败")
		return
	}

	response.Success(c, TokenResponse{Token: token})
}


