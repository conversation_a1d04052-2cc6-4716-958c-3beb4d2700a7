package handler

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"goadmin/internal/constant"
	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService *service.UserService
	jwtService  *service.JWTService
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService *service.UserService, jwtService *service.JWTService) *UserHandler {
	return &UserHandler{
		userService: userService,
		jwtService:  jwtService,
	}
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Password string `json:"password" binding:"required,min=6,max=50"`
	Email    string `json:"email" binding:"required,email"`
}

// Register 注册返回结构体
type RegisterResponse struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	Nickname string `json:"nickname"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// Login 登录返回结构体
type LoginResponse struct {
	User         UserInfo `json:"user"`
	Token        string   `json:"token"`
	RefreshToken string   `json:"refresh_token"`
}

type UserInfo struct {
	ID          uint       `json:"id"`
	Username    string     `json:"username"`
	Email       string     `json:"email"`
	Nickname    string     `json:"nickname"`
	Avatar      string     `json:"avatar"`
	LastLoginAt *time.Time `json:"last_login_at"`
}

// ProfileResponse 获取资料返回结构体
type ProfileResponse struct {
	ID          uint       `json:"id"`
	Username    string     `json:"username"`
	Email       string     `json:"email"`
	Nickname    string     `json:"nickname"`
	Avatar      string     `json:"avatar"`
	Status      int8       `json:"status"`
	LastLoginAt *time.Time `json:"last_login_at"`
	CreatedAt   time.Time  `json:"created_at"`
}

// Register 用户注册
func (h *UserHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	user, err := h.userService.Register(c.Request.Context(), req.Username, req.Password, req.Email)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "注册失败")
		return
	}

	registerData := RegisterResponse{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Nickname: user.Nickname,
	}

	response.Success(c, registerData)
}

// Login 用户登录
func (h *UserHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	user, err := h.userService.Login(c.Request.Context(), req.Username, req.Password)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "登录失败")
		return
	}

	// 为UniApp客户端生成唯一的deviceID（基于时间戳和用户ID）
	deviceID := uint(time.Now().UnixNano() % 1000000000) // 使用时间戳的后9位作为deviceID

	// 生成标准的 access token
	accessToken, err := h.jwtService.GenerateAccessToken(
		strconv.Itoa(int(user.ID)),
		fmt.Sprintf("uniapp_%d", user.ID),
		"uniapp",
	)
	if err != nil {
		response.Fail(c, response.ERROR, "生成访问令牌失败")
		return
	}

	// 创建数据库中的 refresh token，使用唯一的deviceID
	refreshToken, err := h.userService.CreateRefreshToken(
		c.Request.Context(),
		&user.ID,
		&deviceID,
		"user",
		time.Now().AddDate(0, 0, 30), // 30天有效期
	)
	if err != nil {
		response.Fail(c, response.ERROR, "生成刷新令牌失败")
		return
	}

	loginResp := LoginResponse{
		User: UserInfo{
			ID:          user.ID,
			Username:    user.Username,
			Email:       user.Email,
			Nickname:    user.Nickname,
			Avatar:      user.Avatar,
			LastLoginAt: user.LastLoginAt,
		},
		Token:        accessToken,
		RefreshToken: refreshToken,
	}

	response.Success(c, loginResp)
}

// GetProfile 获取用户资料
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID := c.GetUint("user_id")

	user, err := h.userService.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取用户资料失败")
		return
	}

	profile := ProfileResponse{
		ID:          user.ID,
		Username:    user.Username,
		Email:       user.Email,
		Nickname:    user.Nickname,
		Avatar:      user.Avatar,
		Status:      user.Status,
		LastLoginAt: user.LastLoginAt,
		CreatedAt:   user.CreatedAt,
	}

	response.Success(c, profile)
}

// UpdateProfileRequest 更新资料请求
type UpdateProfileRequest struct {
	Nickname string `json:"nickname" binding:"omitempty,max=50"`
	Email    string `json:"email" binding:"omitempty,email"`
	Avatar   string `json:"avatar" binding:"omitempty,max=255"`
}

// UpdateProfile 更新用户资料
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	userID := c.GetUint("user_id")

	err := h.userService.UpdateProfile(c.Request.Context(), userID, req.Nickname, req.Email, req.Avatar)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新用户资料失败")
		return
	}

	response.Success(c, gin.H{"message": "更新成功"})
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6,max=50"`
}

// ChangePassword 修改密码
func (h *UserHandler) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	userID := c.GetUint("user_id")

	err := h.userService.ChangePassword(c.Request.Context(), userID, req.OldPassword, req.NewPassword)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "修改密码失败")
		return
	}

	response.Success(c, gin.H{"message": "密码修改成功"})
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// RefreshToken 刷新令牌
func (h *UserHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	// 验证数据库中的 refresh token
	refreshTokenRecord, err := h.userService.ValidateRefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "刷新令牌验证失败")
		return
	}

	// 检查是否是用户类型的 refresh token
	if refreshTokenRecord.Type != constant.TokenTypeUser || refreshTokenRecord.UserID == nil {
		response.Fail(c, response.ERROR, "无效的用户刷新令牌")
		return
	}

	// 获取用户信息
	user, err := h.userService.GetUserByID(c.Request.Context(), *refreshTokenRecord.UserID)
	if err != nil {
		response.Fail(c, response.ERROR, "获取用户信息失败")
		return
	}

	// 生成新的 access token
	newAccessToken, err := h.jwtService.GenerateAccessToken(
		strconv.Itoa(int(user.ID)),
		fmt.Sprintf("uniapp_%d", user.ID),
		"uniapp",
	)
	if err != nil {
		response.Fail(c, response.ERROR, "生成访问令牌失败")
		return
	}

	// 创建新的 refresh token（替换旧的），使用原有的deviceID
	newRefreshToken, err := h.userService.CreateRefreshToken(
		c.Request.Context(),
		&user.ID,
		refreshTokenRecord.DeviceID, // 使用原有的deviceID，保持多端登录
		constant.TokenTypeUser,
		time.Now().AddDate(0, 0, 30), // 30天有效期
	)
	if err != nil {
		response.Fail(c, response.ERROR, "生成刷新令牌失败")
		return
	}

	// 删除旧的 refresh token
	h.userService.DeleteRefreshToken(c.Request.Context(), req.RefreshToken)

	response.Success(c, gin.H{
		"token":         newAccessToken,
		"refresh_token": newRefreshToken,
		"expires_in":    3600, // access token 有效期（秒）
	})
}

// Logout 用户登出
func (h *UserHandler) Logout(c *gin.Context) {
	// 从请求中获取刷新令牌（如果有的话）
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err == nil && req.RefreshToken != "" {
		// 删除刷新令牌
		h.userService.DeleteRefreshToken(c.Request.Context(), req.RefreshToken)
	}

	response.Success(c, gin.H{"message": "登出成功"})
}

// GetUserList 获取用户列表（管理员功能）
func (h *UserHandler) GetUserList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	keyword := c.Query("keyword")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	users, total, err := h.userService.GetUserList(c.Request.Context(), page, pageSize, keyword)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取用户列表失败")
		return
	}

	response.Success(c, gin.H{
		"list":      users,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	UserID uint `json:"user_id" binding:"required"`
	Status int8 `json:"status" binding:"required,oneof=1 2"`
}

// UpdateUserStatus 更新用户状态（管理员功能）
func (h *UserHandler) UpdateUserStatus(c *gin.Context) {
	var req UpdateUserStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.userService.UpdateUserStatus(c.Request.Context(), req.UserID, req.Status)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新用户状态失败")
		return
	}

	action := "启用"
	if req.Status == constant.UserStatusDisabled {
		action = "禁用"
	}

	response.Success(c, gin.H{"message": action + "用户成功"})
}

// GetUserStatistics 获取用户统计信息（管理员功能）
func (h *UserHandler) GetUserStatistics(c *gin.Context) {
	stats, err := h.userService.GetUserStatistics(c.Request.Context())
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取用户统计失败")
		return
	}

	response.Success(c, stats)
}
