package handler

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"goadmin/internal/model"
	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// ScriptConfigHandler 脚本配置处理器
type ScriptConfigHandler struct {
	scriptConfigService *service.ScriptConfigService
}

// NewScriptConfigHandler 创建脚本配置处理器
func NewScriptConfigHandler(scriptConfigService *service.ScriptConfigService) *ScriptConfigHandler {
	return &ScriptConfigHandler{
		scriptConfigService: scriptConfigService,
	}
}

// GetScriptConfigSchema 获取脚本配置结构
func (h *ScriptConfigHandler) GetScriptConfigSchema(c *gin.Context) {
	// 解析请求参数
	var req struct {
		ScriptID uint `json:"script_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "脚本ID不能为空")
		return
	}

	// 直接返回原始JSON以保持顺序
	schemaRaw, err := h.scriptConfigService.GetScriptConfigSchemaRaw(c.Request.Context(), req.ScriptID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	// 使用SuccessRaw方法保持JSON顺序，同时维持统一的响应格式
	response.SuccessRaw(c, schemaRaw)
}

// GetUserScriptConfigs 获取用户脚本配置列表
func (h *ScriptConfigHandler) GetUserScriptConfigs(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	// 解析请求参数
	var req struct {
		ScriptID uint `json:"script_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "脚本ID不能为空")
		return
	}

	configs, err := h.scriptConfigService.GetUserScriptConfigs(c.Request.Context(), userID.(uint), req.ScriptID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, configs)
}

// GetUserScriptConfig 获取用户脚本配置详情
func (h *ScriptConfigHandler) GetUserScriptConfig(c *gin.Context) {
	// 解析请求参数
	var req struct {
		ConfigID uint `json:"config_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "配置ID不能为空")
		return
	}

	config, err := h.scriptConfigService.GetUserScriptConfig(c.Request.Context(), req.ConfigID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	response.Success(c, config)
}

// CreateUserScriptConfig 创建用户脚本配置
func (h *ScriptConfigHandler) CreateUserScriptConfig(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "用户未登录")
		return
	}

	// 解析请求参数
	var req struct {
		ScriptID     uint              `json:"script_id" binding:"required"`
		ConfigName   string            `json:"config_name"`
		ConfigValues model.ConfigValue `json:"config_values" binding:"required"`
		DeviceIDs    []uint            `json:"device_ids"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, err.Error())
		return
	}

	// 如果没有提供配置名称，生成默认名称
	configName := req.ConfigName
	if configName == "" {
		configName = fmt.Sprintf("默认配置_%d", time.Now().Unix())
	}

	config, err := h.scriptConfigService.CreateUserScriptConfig(
		c.Request.Context(),
		userID.(uint),
		req.ScriptID,
		configName,
		req.ConfigValues,
		req.DeviceIDs,
	)

	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	response.Success(c, config)
}

// UpdateUserScriptConfig 更新用户脚本配置
func (h *ScriptConfigHandler) UpdateUserScriptConfig(c *gin.Context) {
	// 解析请求参数
	var req struct {
		ConfigID     uint              `json:"config_id" binding:"required"`
		ConfigName   string            `json:"config_name"`
		ConfigValues model.ConfigValue `json:"config_values" binding:"required"`
		DeviceIDs    []uint            `json:"device_ids"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, err.Error())
		return
	}

	// 如果没有提供配置名称，获取现有配置的名称
	configName := req.ConfigName
	if configName == "" {
		existingConfig, err := h.scriptConfigService.GetUserScriptConfig(c.Request.Context(), req.ConfigID)
		if err != nil {
			if e, ok := err.(*response.Error); ok {
				response.Fail(c, e.Code, e.Message)
			} else {
				response.Fail(c, response.ERROR, err.Error())
			}
			return
		}
		configName = existingConfig.ConfigName
	}

	config, err := h.scriptConfigService.UpdateUserScriptConfig(
		c.Request.Context(),
		req.ConfigID,
		configName,
		req.ConfigValues,
		req.DeviceIDs,
	)

	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
		} else {
			response.Fail(c, response.ERROR, err.Error())
		}
		return
	}

	response.Success(c, config)
}

// DeleteUserScriptConfig 删除用户脚本配置
func (h *ScriptConfigHandler) DeleteUserScriptConfig(c *gin.Context) {
	// 解析请求参数
	var req struct {
		ConfigID uint `json:"config_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "配置ID不能为空")
		return
	}

	err := h.scriptConfigService.DeleteUserScriptConfig(c.Request.Context(), req.ConfigID)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, gin.H{"message": "配置删除成功"})
}
