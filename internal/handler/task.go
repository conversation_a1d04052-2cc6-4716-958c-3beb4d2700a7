package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// TaskHandler 任务处理器
type TaskHandler struct {
	taskService *service.TaskService
}

// NewTaskHandler 创建任务处理器
func NewTaskHandler(taskService *service.TaskService) *TaskHandler {
	return &TaskHandler{
		taskService: taskService,
	}
}

// CreateTaskRequest 创建任务请求
type CreateTaskRequest struct {
	Name          string                 `json:"name" binding:"required"`
	Description   string                 `json:"description"`
	Type          string                 `json:"type" binding:"omitempty"`
	ScriptID      uint                   `json:"script_id" binding:"required"`
	TargetDevices []uint                 `json:"target_devices" binding:"required"`
	Config        map[string]interface{} `json:"config"`
}

// CreateTask 创建任务
func (h *TaskHandler) CreateTask(c *gin.Context) {
	var req CreateTaskRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	if req.Type == "" {
		req.Type = "script"
	}

	if req.Config == nil {
		req.Config = make(map[string]interface{})
	}

	createdBy := c.GetUint("user_id")

	task, err := h.taskService.CreateTask(
		c.Request.Context(),
		req.Name,
		req.Description,
		req.Type,
		req.ScriptID,
		req.TargetDevices,
		req.Config,
		createdBy,
	)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "创建任务失败")
		return
	}

	response.Success(c, task)
}

// GetTaskByID 获取任务详情
func (h *TaskHandler) GetTaskByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的任务ID")
		return
	}

	task, err := h.taskService.GetTaskByID(c.Request.Context(), uint(id))
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取任务失败")
		return
	}

	response.Success(c, task)
}

// GetTaskList 获取任务列表
func (h *TaskHandler) GetTaskList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	keyword := c.Query("keyword")

	var createdBy *uint
	if createdByStr := c.Query("created_by"); createdByStr != "" {
		if cb, err := strconv.ParseUint(createdByStr, 10, 32); err == nil {
			createdByUint := uint(cb)
			createdBy = &createdByUint
		}
	}

	var status *int8
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.ParseInt(statusStr, 10, 8); err == nil {
			statusInt8 := int8(s)
			status = &statusInt8
		}
	}

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	tasks, total, err := h.taskService.GetTaskList(c.Request.Context(), page, pageSize, createdBy, status, keyword)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取任务列表失败")
		return
	}

	response.Success(c, gin.H{
		"list":      tasks,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// StartTaskRequest 启动任务请求
type StartTaskRequest struct {
	ID uint `json:"id" binding:"required"`
}

// StartTask 启动任务
func (h *TaskHandler) StartTask(c *gin.Context) {
	var req StartTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.taskService.StartTask(c.Request.Context(), req.ID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "启动任务失败")
		return
	}

	response.Success(c, gin.H{"message": "任务启动成功"})
}

// CancelTaskRequest 取消任务请求
type CancelTaskRequest struct {
	ID uint `json:"id" binding:"required"`
}

// CancelTask 取消任务
func (h *TaskHandler) CancelTask(c *gin.Context) {
	var req CancelTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.taskService.CancelTask(c.Request.Context(), req.ID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "取消任务失败")
		return
	}

	response.Success(c, gin.H{"message": "任务取消成功"})
}

// UpdateTaskProgressRequest 更新任务进度请求
type UpdateTaskProgressRequest struct {
	ID       uint `json:"id" binding:"required"`
	Progress int  `json:"progress" binding:"required,min=0,max=100"`
}

// UpdateTaskProgress 更新任务进度
func (h *TaskHandler) UpdateTaskProgress(c *gin.Context) {
	var req UpdateTaskProgressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.taskService.UpdateTaskProgress(c.Request.Context(), req.ID, req.Progress)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新任务进度失败")
		return
	}

	response.Success(c, gin.H{"message": "任务进度更新成功"})
}

// GetTaskResults 获取任务结果列表
func (h *TaskHandler) GetTaskResults(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的任务ID")
		return
	}

	results, err := h.taskService.GetTaskResults(c.Request.Context(), uint(id))
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取任务结果失败")
		return
	}

	response.Success(c, gin.H{"results": results})
}

// UpdateTaskResultRequest 更新任务结果请求
type UpdateTaskResultRequest struct {
	TaskID   uint   `json:"task_id" binding:"required"`
	DeviceID uint   `json:"device_id" binding:"required"`
	Status   int8   `json:"status" binding:"required,oneof=0 1 2 3"`
	Output   string `json:"output"`
	Error    string `json:"error"`
}

// UpdateTaskResult 更新任务结果
func (h *TaskHandler) UpdateTaskResult(c *gin.Context) {
	var req UpdateTaskResultRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.taskService.UpdateTaskResult(c.Request.Context(), req.TaskID, req.DeviceID, req.Status, req.Output, req.Error)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新任务结果失败")
		return
	}

	response.Success(c, gin.H{"message": "任务结果更新成功"})
}

// DeleteTaskRequest 删除任务请求
type DeleteTaskRequest struct {
	ID uint `json:"id" binding:"required"`
}

// DeleteTask 删除任务
func (h *TaskHandler) DeleteTask(c *gin.Context) {
	var req DeleteTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.taskService.DeleteTask(c.Request.Context(), req.ID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "删除任务失败")
		return
	}

	response.Success(c, gin.H{"message": "任务删除成功"})
}

// GetUserTasks 获取用户任务列表
func (h *TaskHandler) GetUserTasks(c *gin.Context) {
	userID := c.GetUint("user_id")
	limitStr := c.DefaultQuery("limit", "10")
	limit, _ := strconv.Atoi(limitStr)

	tasks, err := h.taskService.GetUserTasks(c.Request.Context(), userID, limit)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取用户任务失败")
		return
	}

	response.Success(c, gin.H{"tasks": tasks})
}

// GetTaskStatistics 获取任务统计信息
func (h *TaskHandler) GetTaskStatistics(c *gin.Context) {
	var userID *uint
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if uid, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			userIDUint := uint(uid)
			userID = &userIDUint
		}
	}

	stats, err := h.taskService.GetTaskStatistics(c.Request.Context(), userID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取任务统计失败")
		return
	}

	response.Success(c, stats)
}
