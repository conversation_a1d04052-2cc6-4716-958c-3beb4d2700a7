package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/internal/model"
	"goadmin/internal/service"
	"goadmin/pkg/response"
)

type ConfigHandler struct {
	service *service.ConfigService
}

func NewConfigHandler(service *service.ConfigService) *ConfigHandler {
	return &ConfigHandler{service: service}
}

// ListConfigs 获取配置列表
func (h *ConfigHandler) ListConfigs(c *gin.Context) {
	var req struct {
		Page     int    `json:"page" binding:"required,min=1"`
		PageSize int    `json:"page_size" binding:"required,min=1,max=100"`
		Key      string `json:"key"`
	}

	if err := c.ShouldBind<PERSON>(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, err.Error())
		return
	}

	configs, total, err := h.service.ListConfigs(req.Page, req.PageSize, req.Key)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, gin.H{
		"items": configs,
		"total": total,
	})
}

// SetConfig 设置配置
func (h *ConfigHandler) SetConfig(c *gin.Context) {
	var req struct {
		Key   string                `json:"key" binding:"required"`
		Value string                `json:"value" binding:"required"`
		Type  model.ConfigValueType `json:"type" binding:"required,oneof=STRING JSON YAML"`
		Desc  string                `json:"desc"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, err.Error())
		return
	}

	if err := h.service.SetConfig(req.Key, req.Value, req.Type, req.Desc); err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, nil)
}

// DeleteConfig 删除配置
func (h *ConfigHandler) DeleteConfig(c *gin.Context) {
	var req struct {
		Key string `json:"key" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, err.Error())
		return
	}

	if err := h.service.DeleteConfig(req.Key); err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, nil)
}

// GetConfig 获取配置
func (h *ConfigHandler) GetConfig(c *gin.Context) {
	var req struct {
		Key string `json:"key" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, err.Error())
		return
	}

	config, err := h.service.GetConfig(c.Request.Context(), req.Key)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	response.Success(c, config)
}
