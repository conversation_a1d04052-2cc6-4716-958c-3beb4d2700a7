package handler

import (
	"time"

	"github.com/gin-gonic/gin"

	"goadmin/internal/model"
	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// ThirdRechargeRequest 第三方充值请求
type ThirdRechargeRequest struct {
	TargetCardKey string `json:"target_card_key" binding:"required"` // 目标卡密
	NewCardKey    string `json:"new_card_key" binding:"required"`    // 新卡密
}

type ThirdAPIHandler struct {
	thirdAPIKeyService *service.ThirdAPIKeyService
	cardService        *service.CardKeyService
}

func NewThirdAPIHandler(thirdAPIKeyService *service.ThirdAPIKeyService, cardService *service.CardKeyService) *ThirdAPIHandler {
	return &ThirdAPIHandler{
		thirdAPIKeyService: thirdAPIKeyService,
		cardService:        cardService,
	}
}

// RechargeCardKey 第三方充值接口
func (h *ThirdAPIHandler) RechargeCardKey(c *gin.Context) {
	var req ThirdRechargeRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	// 从中间件获取API密钥信息
	apiKeyInterface, exists := c.Get("api_key")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "无效的认证信息")
		return
	}

	apiKey, ok := apiKeyInterface.(*model.ThirdAPIKey)
	if !ok {
		response.Fail(c, response.UNAUTHORIZED, "无效的认证信息")
		return
	}

	// 检查充值权限
	permissions, err := h.thirdAPIKeyService.GetAPIKeyPermissions(apiKey)
	if err != nil {
		response.Fail(c, response.UNAUTHORIZED, "权限解析失败")
		return
	}

	if !permissions.Recharge {
		response.Fail(c, response.FORBIDDEN, "无充值权限")
		return
	}

	// 执行充值操作
	err = h.cardService.RechargeCardKey(c.Request.Context(), req.TargetCardKey, req.NewCardKey)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "充值失败")
		return
	}

	response.Success(c, gin.H{"message": "充值成功"})
}

// CreateAPIKeyRequest 创建API密钥请求
type CreateAPIKeyRequest struct {
	Name        string                  `json:"name" binding:"required"`
	Permissions model.APIKeyPermissions `json:"permissions" binding:"required"`
	ExpiresAt   *string                 `json:"expires_at,omitempty"`
}

// CreateAPIKey 创建API密钥
func (h *ThirdAPIHandler) CreateAPIKey(c *gin.Context) {
	var req CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	// 处理过期时间
	var expiresAt *time.Time
	if req.ExpiresAt != nil && *req.ExpiresAt != "" {
		// 简单解析时间字符串，支持常见格式
		parsedTime, err := time.Parse("2006-01-02 15:04:05", *req.ExpiresAt)
		if err != nil {
			response.Fail(c, response.INVALID_PARAMS, "无效的过期时间格式")
			return
		}
		expiresAt = &parsedTime
	}

	apiKey, err := h.thirdAPIKeyService.CreateAPIKey(c.Request.Context(), req.Name, req.Permissions, expiresAt)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "创建API密钥失败")
		return
	}

	response.Success(c, apiKey)
}

// ListAPIKeysRequest 列出API密钥请求
type ListAPIKeysRequest struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

// ListAPIKeys 列出API密钥
func (h *ThirdAPIHandler) ListAPIKeys(c *gin.Context) {
	var req ListAPIKeysRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	apiKeys, total, err := h.thirdAPIKeyService.ListAPIKeys(c.Request.Context(), req.Page, req.PageSize)
	if err != nil {
		response.Fail(c, response.ERROR, "获取API密钥列表失败")
		return
	}

	response.Success(c, gin.H{
		"list":      apiKeys,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	})
}

// DeleteAPIKeyRequest 删除API密钥请求
type DeleteAPIKeyRequest struct {
	SecretID string `json:"secret_id" binding:"required"`
}

// DeleteAPIKey 删除API密钥
func (h *ThirdAPIHandler) DeleteAPIKey(c *gin.Context) {
	var req DeleteAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.thirdAPIKeyService.DeleteAPIKey(c.Request.Context(), req.SecretID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "删除API密钥失败")
		return
	}

	response.Success(c, gin.H{"message": "删除成功"})
}

// UpdateAPIKeyStatusRequest 更新API密钥状态请求
type UpdateAPIKeyStatusRequest struct {
	SecretID string `json:"secret_id" binding:"required"`
	Status   int8   `json:"status" binding:"required,oneof=1 2"`
}

// UpdateAPIKeyStatus 更新API密钥状态
func (h *ThirdAPIHandler) UpdateAPIKeyStatus(c *gin.Context) {
	var req UpdateAPIKeyStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	err := h.thirdAPIKeyService.UpdateAPIKeyStatus(c.Request.Context(), req.SecretID, req.Status)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新API密钥状态失败")
		return
	}

	action := "启用"
	if req.Status == 2 {
		action = "禁用"
	}
	response.Success(c, gin.H{"message": action + "成功"})
}

// GetAPIKeyDetailRequest 获取API密钥详情请求
type GetAPIKeyDetailRequest struct {
	SecretID string `json:"secret_id" binding:"required"`
}

// GetAPIKeyDetail 获取API密钥详情
func (h *ThirdAPIHandler) GetAPIKeyDetail(c *gin.Context) {
	var req GetAPIKeyDetailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	apiKey, err := h.thirdAPIKeyService.GetAPIKeyDetail(c.Request.Context(), req.SecretID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取API密钥详情失败")
		return
	}

	response.Success(c, apiKey)
}

// GetCardKeyDetailRequest 获取卡密详情请求
// 用于第三方API获取卡密的使用时间和到期时间
//
type GetCardKeyDetailRequest struct {
    CardKey string `json:"card_key" binding:"required"`
}

// GetCardKeyDetail 获取卡密的使用时间和到期时间
func (h *ThirdAPIHandler) GetCardKeyDetail(c *gin.Context) {
    var req GetCardKeyDetailRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
        return
    }

    card, err := h.cardService.GetCardKey(c.Request.Context(), req.CardKey)
    if err != nil {
        if e, ok := err.(*response.Error); ok {
            response.Fail(c, e.Code, e.Message)
            return
        }
        response.Fail(c, response.ERROR, "查询卡密失败")
        return
    }

    var usedAt, expiredAt string
    if card.UsedAt != nil && !card.UsedAt.IsZero() {
        usedAt = card.UsedAt.Format("2006-01-02 15:04:05")
    } else {
        usedAt = ""
    }
    if card.ExpiredAt != nil && !card.ExpiredAt.IsZero() {
        expiredAt = card.ExpiredAt.Format("2006-01-02 15:04:05")
    } else {
        expiredAt = ""
    }

    response.Success(c, gin.H{
        "card_key":   card.CardKey,
        "used_at":    usedAt,
        "expired_at": expiredAt,
    })
}
