package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// ThirdTokenRequest 第三方token请求
type ThirdTokenRequest struct {
	CardKey string `json:"card_key" binding:"required"` // 卡密
}

// ThirdTokenResponse 第三方token响应
type ThirdTokenResponse struct {
	Token string `json:"token"` // JWT令牌
}

// ThirdRechargeRequest 第三方充值请求
type ThirdRechargeRequest struct {
	TargetCardKey string `json:"target_card_key" binding:"required"` // 目标卡密
	NewCardKey    string `json:"new_card_key" binding:"required"`    // 新卡密
}

type ThirdAPIHandler struct {
	thirdJWTService *service.ThirdJWTService
	cardService     *service.CardKeyService
}

func NewThirdAPIHandler(thirdJWTService *service.ThirdJWTService, cardService *service.CardKeyService) *ThirdAPIHandler {
	return &ThirdAPIHandler{
		thirdJWTService: thirdJWTService,
		cardService:     cardService,
	}
}

// GetToken 通过卡密获取第三方API token
func (h *ThirdAPIHandler) GetToken(c *gin.Context) {
	var req ThirdTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	// 验证卡密是否存在且有效
	card, err := h.cardService.GetCardKey(c.Request.Context(), req.CardKey)
	if err != nil {
		response.Fail(c, response.ERROR, "卡密不存在或无效")
		return
	}

	// 检查卡密状态
	if card.Status != 1 { // 假设1表示正常状态
		response.Fail(c, response.ERROR, "卡密状态异常")
		return
	}

	// 生成第三方token
	token, err := h.thirdJWTService.GenerateThirdToken(req.CardKey)
	if err != nil {
		response.Fail(c, response.ERROR, "生成token失败")
		return
	}

	response.Success(c, ThirdTokenResponse{Token: token})
}

// RechargeCardKey 第三方充值接口
func (h *ThirdAPIHandler) RechargeCardKey(c *gin.Context) {
	var req ThirdRechargeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	// 从上下文获取当前卡密
	currentCardKey, exists := c.Get("card_key")
	if !exists {
		response.Fail(c, response.UNAUTHORIZED, "无效的认证信息")
		return
	}

	cardKeyStr, ok := currentCardKey.(string)
	if !ok {
		response.Fail(c, response.UNAUTHORIZED, "无效的认证信息")
		return
	}

	// 执行充值操作
	err := h.cardService.RechargeCardKey(c.Request.Context(), cardKeyStr, req.TargetCardKey, req.NewCardKey)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "充值失败")
		return
	}

	response.Success(c, gin.H{"message": "充值成功"})
}
