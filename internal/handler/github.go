package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/ctxutil"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

type GitHubHandler struct {
	githubService *service.GitHubService
	jwtService    *service.JWTService
}

func NewGitHubHandler(githubService *service.GitHubService, jwtService *service.JWTService) *GitHubHandler {
	return &GitHubHandler{
		githubService: githubService,
		jwtService:    jwtService,
	}
}

// GetAutoJSContent 获取AutoJS脚本内容
func (h *GitHubHandler) GetAutoJSContent(c *gin.Context) {
	// 使用带有请求ID的上下文
	ctx := ctxutil.GetRequestContext(c)

	// 获取当前环境
	env := h.githubService.GetCurrentEnvironment()

	// 根据环境决定使用的分支
	var branch service.BranchType
	if env == "online" {
		branch = service.BranchOnline
	} else {
		branch = service.BranchMain
	}

	// 使用指定分支获取脚本
	content, err := h.githubService.GetAutoJSContentByBranch(ctx, branch)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取内容失败")
		return
	}

	response.Success(c, gin.H{"content": content})
}

// GetAutoJSLoginContent 获取AutoJS登录脚本内容
func (h *GitHubHandler) GetAutoJSLoginContent(c *gin.Context) {
	// 使用带有请求ID的上下文
	ctx := ctxutil.GetRequestContext(c)

	// 获取当前环境
	env := h.githubService.GetCurrentEnvironment()

	// 根据环境决定使用的分支
	var branch service.BranchType
	if env == "online" {
		branch = service.BranchOnline
	} else {
		branch = service.BranchMain
	}

	// 使用指定分支获取登录脚本
	// 这里使用不同的方法，获取autojs_login.js脚本
	content, err := h.githubService.GetAutoJSLoginContentByBranch(ctx, branch)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取登录脚本失败")
		return
	}

	response.Success(c, gin.H{"content": content})
}

// GetHamibotContent 获取Hamibot脚本内容
func (h *GitHubHandler) GetHamibotContent(c *gin.Context) {
	// 检查APP_ENV环境变量
	isProduction := false
	if appEnv, exists := c.Get("app_env"); exists {
		if env, ok := appEnv.(string); ok {
			if env == "production" {
				isProduction = true
			}
			// 添加日志记录当前环境
			c.Set("branch", "online")
			if !isProduction {
				c.Set("branch", "main")
			}
		}
	}

	// 使用带有请求ID的上下文
	ctx := ctxutil.GetRequestContext(c)
	content, err := h.githubService.GetHamibotContent(ctx, isProduction)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取内容失败")
		return
	}

	response.Success(c, gin.H{"content": content})
}

// GetBotContent 获取Bot脚本内容
func (h *GitHubHandler) GetBotContent(c *gin.Context) {
	// 检查APP_ENV环境变量
	isProduction := false
	if appEnv, exists := c.Get("app_env"); exists {
		if env, ok := appEnv.(string); ok {
			if env == "production" {
				isProduction = true
			}
			// 添加日志记录当前环境
			c.Set("branch", "online")
			if !isProduction {
				c.Set("branch", "main")
			}
		}
	}

	// 使用带有请求ID的上下文
	ctx := ctxutil.GetRequestContext(c)
	content, err := h.githubService.GetBotContent(ctx, isProduction)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取Bot脚本内容失败")
		return
	}

	response.Success(c, gin.H{"content": content})
}

// GetBotUIContent 获取BotUI脚本内容
func (h *GitHubHandler) GetBotUIContent(c *gin.Context) {
	// 检查APP_ENV环境变量
	isProduction := false
	if appEnv, exists := c.Get("app_env"); exists {
		if env, ok := appEnv.(string); ok {
			if env == "production" {
				isProduction = true
			}
			// 添加日志记录当前环境
			c.Set("branch", "online")
			if !isProduction {
				c.Set("branch", "main")
			}
		}
	}

	// 使用带有请求ID的上下文
	ctx := ctxutil.GetRequestContext(c)
	content, err := h.githubService.GetBotUIContent(ctx, isProduction)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取BotUI脚本内容失败")
		return
	}

	response.Success(c, gin.H{"content": content})
}

// HandleGitHubWebhook 处理GitHub webhook请求
func (h *GitHubHandler) HandleGitHubWebhook(c *gin.Context) {
	// 使用带有请求ID的上下文
	ctx := ctxutil.GetRequestContext(c)

	// 从配置文件获取当前环境
	env := h.githubService.GetCurrentEnvironment()
	if env == "" {
		logger.Error(ctx, "无法获取当前环境信息")
		response.Fail(c, response.ERROR, "无法获取环境信息")
		return
	}

	// 获取当前环境期望的分支
	expectedBranch := h.githubService.GetExpectedBranchForEnvironment(env)
	if expectedBranch == "" {
		logger.Info(ctx, "当前环境 %s 不处理任何分支的webhook", env)
		response.Success(c, gin.H{"message": "webhook已接收但不处理"})
		return
	}

	// 处理webhook（内部会处理签名验证和分支检查）
	accepted, err := h.githubService.ProcessWebhook(ctx, c.Request, expectedBranch)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "处理webhook失败")
		return
	}

	if !accepted {
		response.Success(c, gin.H{"message": "webhook已接收但不处理"})
		return
	}

	response.Success(c, gin.H{"message": "webhook处理成功"})
}

// GetScriptList 获取脚本列表
func (h *GitHubHandler) GetScriptList(c *gin.Context) {
	// 使用带有请求ID的上下文
	ctx := ctxutil.GetRequestContext(c)

	scripts, err := h.githubService.GetScriptList(ctx)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取脚本列表失败")
		return
	}

	response.Success(c, scripts)
}

// UpdateScript 手动更新脚本
func (h *GitHubHandler) UpdateScript(c *gin.Context) {
	var req struct {
		ScriptType string `json:"script_type" binding:"required"`
		Branch     string `json:"branch" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的参数")
		return
	}

	// 使用带有请求ID的上下文
	ctx := ctxutil.GetRequestContext(c)

	// 更新脚本
	err := h.githubService.UpdateScript(ctx, service.ScriptType(req.ScriptType), service.BranchType(req.Branch))
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "更新脚本失败")
		return
	}

	response.Success(c, nil)
}
