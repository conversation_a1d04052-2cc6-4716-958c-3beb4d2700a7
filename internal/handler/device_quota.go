package handler

import (
	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// DeviceQuotaHandler 设备配额处理器
type DeviceQuotaHandler struct {
	quotaService   *service.DeviceQuotaService
	paymentService *service.PaymentService
}

// NewDeviceQuotaHandler 创建设备配额处理器
func NewDeviceQuotaHandler(quotaService *service.DeviceQuotaService, paymentService *service.PaymentService) *DeviceQuotaHandler {
	return &DeviceQuotaHandler{
		quotaService:   quotaService,
		paymentService: paymentService,
	}
}

// GetQuotaInfo 获取用户配额信息
func (h *DeviceQuotaHandler) GetQuotaInfo(c *gin.Context) {
	userID := c.GetUint("user_id")

	stats, err := h.quotaService.GetUserQuotaInfo(c.Request.Context(), userID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取配额信息失败")
		return
	}

	response.Success(c, stats)
}

// GetQuotaList 获取用户配额列表
func (h *DeviceQuotaHandler) GetQuotaList(c *gin.Context) {
	userID := c.GetUint("user_id")

	quotas, err := h.quotaService.GetUserAllQuotas(c.Request.Context(), userID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取配额列表失败")
		return
	}

	response.Success(c, quotas)
}

// CreateQuotaOrder 创建配额购买订单
func (h *DeviceQuotaHandler) CreateQuotaOrder(c *gin.Context) {
	var req struct {
		QuotaCount int    `json:"quota_count" binding:"required,min=1,max=100"`
		PayType    string `json:"pay_type" binding:"required,oneof=alipay wxpay"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	userID := c.GetUint("user_id")

	// 创建配额订单
	order, err := h.paymentService.CreateQuotaOrder(c.Request.Context(), userID, req.QuotaCount, req.PayType)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "创建订单失败")
		return
	}

	response.Success(c, order)
}

// GetRenewableQuotas 获取用户可续费的配额列表
func (h *DeviceQuotaHandler) GetRenewableQuotas(c *gin.Context) {
	userID := c.GetUint("user_id")

	quotas, err := h.quotaService.GetRenewableQuotas(c.Request.Context(), userID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取可续费配额列表失败")
		return
	}

	response.Success(c, quotas)
}

// CreateQuotaRenewalOrder 创建配额续费订单
func (h *DeviceQuotaHandler) CreateQuotaRenewalOrder(c *gin.Context) {
	var req struct {
		QuotaID int    `json:"quota_id" binding:"required"`
		PayType string `json:"pay_type" binding:"required,oneof=alipay wxpay"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	userID := c.GetUint("user_id")

	// 创建配额续费订单
	order, err := h.paymentService.CreateQuotaRenewalOrder(c.Request.Context(), userID, req.QuotaID, req.PayType)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "创建续费订单失败")
		return
	}

	response.Success(c, order)
}

// CreateScriptRenewalOrder 创建脚本续费订单
func (h *DeviceQuotaHandler) CreateScriptRenewalOrder(c *gin.Context) {
	var req struct {
		ScriptID int    `json:"script_id" binding:"required"`
		PayType  string `json:"pay_type" binding:"required,oneof=alipay wxpay"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "参数错误")
		return
	}

	userID := c.GetUint("user_id")

	// 创建脚本续费订单
	order, err := h.paymentService.CreateScriptRenewalOrder(c.Request.Context(), userID, uint(req.ScriptID), req.PayType)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "创建续费订单失败")
		return
	}

	response.Success(c, order)
}
