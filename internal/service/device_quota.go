package service

import (
	"context"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/constant"
	"goadmin/internal/model"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

// DeviceQuotaService 设备配额服务
type DeviceQuotaService struct {
	db *gorm.DB
}

// NewDeviceQuotaService 创建设备配额服务
func NewDeviceQuotaService(db *gorm.DB) *DeviceQuotaService {
	return &DeviceQuotaService{
		db: db,
	}
}

// GetUserDeviceCount 获取用户已配对设备数量
func (s *DeviceQuotaService) GetUserDeviceCount(ctx context.Context, userID uint) (int, error) {
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.Device{}).
		Where("user_id = ? AND pairing_status = ?", userID, model.PairingStatusCompleted).
		Count(&count).Error; err != nil {
		return 0, err
	}
	return int(count), nil
}

// CheckUserQuota 检查用户配额是否足够
func (s *DeviceQuotaService) CheckUserQuota(ctx context.Context, userID uint) (bool, error) {
	// 获取用户已配对设备数量
	deviceCount, err := s.GetUserDeviceCount(ctx, userID)
	if err != nil {
		return false, err
	}

	// 获取用户有效的付费配额总数
	var totalPaidQuota int64
	if err := s.db.WithContext(ctx).Model(&model.DeviceQuota{}).
		Where("user_id = ? AND (expires_at IS NULL OR expires_at > ?)", userID, time.Now()).
		Select("COALESCE(SUM(total_quota), 0)").
		Scan(&totalPaidQuota).Error; err != nil {
		return false, err
	}

	// 计算可用配额：免费配额 + 付费配额 - 已配对设备数量
	availableQuota := model.FreeQuotaCount + int(totalPaidQuota) - deviceCount

	// 检查是否有足够配额：可用配额 >= 1
	hasEnoughQuota := availableQuota >= 1

	logger.Info(ctx, "配额检查", map[string]interface{}{
		"user_id":          userID,
		"device_count":     deviceCount,
		"free_quota_count": model.FreeQuotaCount,
		"total_paid_quota": totalPaidQuota,
		"available_quota":  availableQuota,
		"has_enough_quota": hasEnoughQuota,
	})

	return hasEnoughQuota, nil
}

// GetUserQuotaInfo 获取用户配额信息
func (s *DeviceQuotaService) GetUserQuotaInfo(ctx context.Context, userID uint) (map[string]interface{}, error) {
	// 获取用户已配对设备数量
	deviceCount, err := s.GetUserDeviceCount(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 获取用户有效的付费配额总数
	var totalPaidQuota int64
	if err := s.db.WithContext(ctx).Model(&model.DeviceQuota{}).
		Where("user_id = ? AND (expires_at IS NULL OR expires_at > ?)", userID, time.Now()).
		Select("COALESCE(SUM(total_quota), 0)").
		Scan(&totalPaidQuota).Error; err != nil {
		return nil, err
	}

	// 计算免费配额使用情况
	freeQuotaUsed := 0
	if deviceCount > model.FreeQuotaCount {
		freeQuotaUsed = model.FreeQuotaCount
	} else {
		freeQuotaUsed = deviceCount
	}

	// 计算付费配额使用情况
	paidQuotaUsed := 0
	if deviceCount > model.FreeQuotaCount {
		paidQuotaUsed = deviceCount - model.FreeQuotaCount
	}

	// 计算总配额和可用配额
	totalQuota := model.FreeQuotaCount + int(totalPaidQuota)
	availableQuota := totalQuota - deviceCount

	info := map[string]interface{}{
		"total_quota":     totalQuota,
		"used_quota":      deviceCount,
		"available_quota": availableQuota,
		"free_quota":      model.FreeQuotaCount,
		"free_quota_used": freeQuotaUsed,
		"paid_quota":      int(totalPaidQuota),
		"paid_quota_used": paidQuotaUsed,
		"device_count":    deviceCount,
	}

	return info, nil
}

// GetUserAllQuotas 获取用户所有付费配额
func (s *DeviceQuotaService) GetUserAllQuotas(ctx context.Context, userID uint) ([]model.DeviceQuota, error) {
	var quotas []model.DeviceQuota
	if err := s.db.WithContext(ctx).Where("user_id = ?", userID).Order("expires_at ASC").Find(&quotas).Error; err != nil {
		return nil, response.NewError(response.ERROR, "获取配额列表失败")
	}

	return quotas, nil
}

// GetRenewableQuotas 获取用户可续费的配额列表（未过期的配额）
func (s *DeviceQuotaService) GetRenewableQuotas(ctx context.Context, userID uint) ([]model.DeviceQuota, error) {
	var quotas []model.DeviceQuota
	if err := s.db.WithContext(ctx).
		Where("user_id = ? AND expires_at > ?", userID, time.Now()).
		Order("expires_at ASC").
		Find(&quotas).Error; err != nil {
		return nil, response.NewError(response.ERROR, "获取可续费配额列表失败")
	}

	return quotas, nil
}

// AllocateQuotaForDevice 为设备分配配额
func (s *DeviceQuotaService) AllocateQuotaForDevice(ctx context.Context, userID uint, deviceID uint) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取用户已配对设备数量（不包括当前正在配对的设备）
		var deviceCount int64
		if err := tx.Model(&model.Device{}).
			Where("user_id = ? AND pairing_status = ? AND id != ?", userID, model.PairingStatusCompleted, deviceID).
			Count(&deviceCount).Error; err != nil {
			return response.NewError(response.ERROR, "获取设备数量失败")
		}

		// 如果设备数量小于免费配额，不需要分配付费配额
		if int(deviceCount) < model.FreeQuotaCount {
			logger.Info(ctx, "设备使用免费配额: user_id=%d, device_id=%d, device_count=%d", userID, deviceID, deviceCount)
			return nil
		}

		// 获取用户有效的付费配额，按过期时间排序
		var quotas []model.DeviceQuota
		if err := tx.Where("user_id = ? AND available_quota > 0", userID).
			Where("(expires_at IS NULL OR expires_at > ?)", time.Now()).
			Order("expires_at ASC").
			Find(&quotas).Error; err != nil {
			return response.NewError(response.ERROR, "获取可用配额失败")
		}

		// 调试：查看所有配额状态
		var allQuotas []model.DeviceQuota
		if err := tx.Where("user_id = ?", userID).
			Where("(expires_at IS NULL OR expires_at > ?)", time.Now()).
			Find(&allQuotas).Error; err == nil {
			for _, q := range allQuotas {
				logger.Info(ctx, "配额状态: id=%d, total=%d, used=%d, available=%d, expires_at=%v",
					q.ID, q.TotalQuota, q.UsedQuota, q.AvailableQuota, q.ExpiresAt)
			}
		}

		if len(quotas) == 0 {
			logger.Error(ctx, "没有找到可用的付费配额: user_id=%d, device_count=%d", userID, deviceCount)
			return response.NewError(response.ERROR, "没有可用的设备配额")
		}

		// 选择第一个可用配额进行分配
		quota := &quotas[0]

		// 更新配额使用情况
		quota.UsedQuota += 1
		quota.AvailableQuota -= 1

		if err := tx.Save(quota).Error; err != nil {
			return response.NewError(response.ERROR, "更新配额失败")
		}

		// 记录配额使用
		usage := &model.DeviceQuotaUsage{
			UserID:   userID,
			QuotaID:  quota.ID,
			DeviceID: deviceID,
		}

		if err := tx.Create(usage).Error; err != nil {
			return response.NewError(response.ERROR, "记录配额使用失败")
		}

		logger.Info(ctx, "设备配额分配成功: user_id=%d, device_id=%d, quota_id=%d", userID, deviceID, quota.ID)
		return nil
	})
}

// ReleaseQuotaForDevice 释放设备配额
func (s *DeviceQuotaService) ReleaseQuotaForDevice(ctx context.Context, userID uint, deviceID uint) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 查找该设备的配额使用记录
		var usage model.DeviceQuotaUsage
		if err := tx.Where("user_id = ? AND device_id = ?", userID, deviceID).
			Order("created_at DESC").First(&usage).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				// 没有找到分配记录，可能设备没有使用付费配额
				logger.Info(ctx, "设备没有配额使用记录: user_id=%d, device_id=%d", userID, deviceID)
				return nil
			}
			return response.NewError(response.ERROR, "查找配额使用记录失败")
		}

		// 获取配额信息
		var quota model.DeviceQuota
		if err := tx.Where("id = ?", usage.QuotaID).First(&quota).Error; err != nil {
			return response.NewError(response.ERROR, "获取配额信息失败")
		}

		// 更新配额使用情况
		quota.UsedQuota -= 1
		quota.AvailableQuota += 1

		if err := tx.Save(&quota).Error; err != nil {
			return response.NewError(response.ERROR, "更新配额失败")
		}

		// 删除配额使用记录
		if err := tx.Delete(&usage).Error; err != nil {
			return response.NewError(response.ERROR, "删除配额使用记录失败")
		}

		logger.Info(ctx, "设备配额释放成功: user_id=%d, device_id=%d, quota_id=%d", userID, deviceID, quota.ID)
		return nil
	})
}

// CreatePaidQuota 创建付费配额
func (s *DeviceQuotaService) CreatePaidQuota(ctx context.Context, userID uint, quotaCount int, orderNo string) (*model.DeviceQuota, error) {
	pricePerQuota := 1.00 // 固定价格1元/个
	totalAmount := float64(quotaCount) * pricePerQuota

	// 设置过期时间（30天）
	expiresAt := time.Now().AddDate(0, 0, 30)

	quota := &model.DeviceQuota{
		UserID:         userID,
		TotalQuota:     quotaCount,
		UsedQuota:      0,
		AvailableQuota: quotaCount,
		PricePerQuota:  pricePerQuota,
		TotalAmount:    totalAmount,
		ExpiresAt:      &expiresAt,
		OrderNo:        &orderNo,
	}

	if err := s.db.WithContext(ctx).Create(quota).Error; err != nil {
		logger.Error(ctx, "创建付费配额失败: user_id=%d, quota_count=%d, error=%v", userID, quotaCount, err)
		return nil, response.NewError(response.ERROR, "创建付费配额失败")
	}

	logger.Info(ctx, "付费配额创建成功: user_id=%d, quota_count=%d, order_no=%s, expires_at=%v", userID, quotaCount, orderNo, expiresAt)
	return quota, nil
}

// CleanupExpiredQuotas 清理过期配额的使用记录
func (s *DeviceQuotaService) CleanupExpiredQuotas(ctx context.Context) error {
	// 查找所有过期的配额
	var expiredQuotas []model.DeviceQuota
	if err := s.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).
		Find(&expiredQuotas).Error; err != nil {
		logger.Error(ctx, "查询过期配额失败: %v", err)
		return err
	}

	if len(expiredQuotas) == 0 {
		logger.Info(ctx, "没有找到过期的配额")
		return nil
	}

	// 清理过期配额的使用记录
	for _, quota := range expiredQuotas {
		// 查找使用该配额的设备
		var usages []model.DeviceQuotaUsage
		if err := s.db.WithContext(ctx).
			Where("quota_id = ?", quota.ID).
			Find(&usages).Error; err != nil {
			logger.Error(ctx, "查询配额 %d 的使用记录失败: %v", quota.ID, err)
			continue
		}

		// 将使用过期配额的设备标记为过期状态，并删除refresh token
		for _, usage := range usages {
			// 获取设备信息
			var device model.Device
			if err := s.db.WithContext(ctx).Where("id = ?", usage.DeviceID).First(&device).Error; err != nil {
				logger.Error(ctx, "查询设备 %d 失败: %v", usage.DeviceID, err)
				continue
			}

			// 标记设备为过期状态
			if err := s.db.WithContext(ctx).
				Model(&model.Device{}).
				Where("id = ?", usage.DeviceID).
				Update("status", constant.DeviceStatusExpired).Error; err != nil {
				logger.Error(ctx, "标记设备 %d 为过期状态失败: %v", usage.DeviceID, err)
			}

			// 删除设备的refresh token
			if err := s.db.WithContext(ctx).
				Where("device_id = ? AND type = ?", device.DeviceID, constant.TokenTypeDevice).
				Delete(&model.RefreshToken{}).Error; err != nil {
				logger.Error(ctx, "删除设备 %s 的refresh token失败: %v", device.DeviceID, err)
			}

			logger.Info(ctx, "设备 %d (device_id: %s) 因配额过期被标记为过期状态，refresh token已删除", usage.DeviceID, device.DeviceID)
		}

		// 删除该配额的所有使用记录
		if err := s.db.WithContext(ctx).
			Where("quota_id = ?", quota.ID).
			Delete(&model.DeviceQuotaUsage{}).Error; err != nil {
			logger.Error(ctx, "删除过期配额 %d 的使用记录失败: %v", quota.ID, err)
			continue
		}

		// 重置配额的已使用数量
		if err := s.db.WithContext(ctx).
			Model(&quota).
			Updates(map[string]interface{}{
				"used_quota":      0,
				"available_quota": quota.TotalQuota,
			}).Error; err != nil {
			logger.Error(ctx, "重置过期配额 %d 的使用数量失败: %v", quota.ID, err)
			continue
		}

		logger.Info(ctx, "清理过期配额: id=%d, user_id=%d, expires_at=%v, 影响设备数=%d",
			quota.ID, quota.UserID, quota.ExpiresAt, len(usages))
	}

	logger.Info(ctx, "配额过期清理完成，共清理 %d 个过期配额", len(expiredQuotas))
	return nil
}

// GetQuotaStatistics 获取配额统计信息
func (s *DeviceQuotaService) GetQuotaStatistics(ctx context.Context, userID uint) (map[string]interface{}, error) {
	// 直接使用GetUserQuotaInfo方法
	return s.GetUserQuotaInfo(ctx, userID)
}
