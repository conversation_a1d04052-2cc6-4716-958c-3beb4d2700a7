package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/constant"
	"goadmin/internal/model"
	"goadmin/pkg/response"
)

type ThirdAPIKeyService struct {
	db *gorm.DB
}

func NewThirdAPIKeyService(db *gorm.DB) *ThirdAPIKeyService {
	return &ThirdAPIKeyService{
		db: db,
	}
}

// GenerateSecretPair 生成SecretId和SecretKey对
func (s *ThirdAPIKeyService) GenerateSecretPair() (string, string, error) {
	// 生成SecretId (16字节 = 32字符)
	secretIdBytes := make([]byte, 16)
	if _, err := rand.Read(secretIdBytes); err != nil {
		return "", "", err
	}
	secretId := "AKID" + hex.EncodeToString(secretIdBytes)[:28] // 32字符总长度

	// 生成SecretKey (32字节 = 64字符)
	secretKeyBytes := make([]byte, 32)
	if _, err := rand.Read(secretKeyBytes); err != nil {
		return "", "", err
	}
	secretKey := hex.EncodeToString(secretKeyBytes)

	return secretId, secretKey, nil
}

// CreateAPIKey 创建API密钥
func (s *ThirdAPIKeyService) CreateAPIKey(ctx context.Context, cardKey, name string, permissions model.APIKeyPermissions, expiresAt *time.Time) (*model.ThirdAPIKey, error) {
	// 验证卡密是否存在且有效
	var card model.CardKey
	if err := s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&card).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.CARD_NOT_FOUND, "卡密不存在")
		}
		return nil, response.NewError(response.ERROR, "查询卡密失败")
	}

	// 检查卡密状态
	if card.Status == constant.CardKeyStatusDisabled {
		return nil, response.NewError(response.CARD_DISABLED, "卡密已被禁用")
	}

	// 生成密钥对
	secretId, secretKey, err := s.GenerateSecretPair()
	if err != nil {
		return nil, response.NewError(response.ERROR, "生成密钥失败")
	}

	// 序列化权限
	permissionsJSON, err := json.Marshal(permissions)
	if err != nil {
		return nil, response.NewError(response.ERROR, "权限序列化失败")
	}

	// 创建API密钥记录
	apiKey := &model.ThirdAPIKey{
		SecretID:    secretId,
		SecretKey:   secretKey,
		CardKey:     cardKey,
		Name:        name,
		Status:      1, // 启用状态
		Permissions: string(permissionsJSON),
		ExpiresAt:   expiresAt,
	}

	if err := s.db.WithContext(ctx).Create(apiKey).Error; err != nil {
		return nil, response.NewError(response.ERROR, "创建API密钥失败")
	}

	return apiKey, nil
}

// GetAPIKeyBySecretID 根据SecretID获取API密钥
func (s *ThirdAPIKeyService) GetAPIKeyBySecretID(ctx context.Context, secretId string) (*model.ThirdAPIKey, error) {
	var apiKey model.ThirdAPIKey
	if err := s.db.WithContext(ctx).Where("secret_id = ?", secretId).First(&apiKey).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.UNAUTHORIZED, "无效的API密钥")
		}
		return nil, response.NewError(response.ERROR, "查询API密钥失败")
	}

	// 检查密钥状态
	if apiKey.Status != 1 {
		return nil, response.NewError(response.UNAUTHORIZED, "API密钥已被禁用")
	}

	// 检查密钥是否过期
	if apiKey.ExpiresAt != nil && apiKey.ExpiresAt.Before(time.Now()) {
		return nil, response.NewError(response.UNAUTHORIZED, "API密钥已过期")
	}

	// 更新最后使用时间
	now := time.Now()
	s.db.WithContext(ctx).Model(&apiKey).Update("last_used_at", &now)

	return &apiKey, nil
}

// ValidateAPIKey 验证API密钥（保留兼容性）
func (s *ThirdAPIKeyService) ValidateAPIKey(ctx context.Context, secretId, secretKey string) (*model.ThirdAPIKey, error) {
	var apiKey model.ThirdAPIKey
	if err := s.db.WithContext(ctx).Where("secret_id = ? AND secret_key = ?", secretId, secretKey).First(&apiKey).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.UNAUTHORIZED, "无效的API密钥")
		}
		return nil, response.NewError(response.ERROR, "验证API密钥失败")
	}

	// 检查密钥状态
	if apiKey.Status != 1 {
		return nil, response.NewError(response.UNAUTHORIZED, "API密钥已被禁用")
	}

	// 检查密钥是否过期
	if apiKey.ExpiresAt != nil && apiKey.ExpiresAt.Before(time.Now()) {
		return nil, response.NewError(response.UNAUTHORIZED, "API密钥已过期")
	}

	// 更新最后使用时间
	now := time.Now()
	s.db.WithContext(ctx).Model(&apiKey).Update("last_used_at", &now)

	return &apiKey, nil
}

// GetAPIKeyPermissions 获取API密钥权限
func (s *ThirdAPIKeyService) GetAPIKeyPermissions(apiKey *model.ThirdAPIKey) (*model.APIKeyPermissions, error) {
	var permissions model.APIKeyPermissions
	if err := json.Unmarshal([]byte(apiKey.Permissions), &permissions); err != nil {
		return nil, response.NewError(response.ERROR, "解析权限失败")
	}
	return &permissions, nil
}

// ListAPIKeys 列出API密钥
func (s *ThirdAPIKeyService) ListAPIKeys(ctx context.Context, cardKey string, page, pageSize int) ([]*model.ThirdAPIKey, int64, error) {
	var total int64
	query := s.db.WithContext(ctx).Model(&model.ThirdAPIKey{})

	if cardKey != "" {
		query = query.Where("card_key = ?", cardKey)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	var apiKeys []*model.ThirdAPIKey
	if err := query.
		Order("created_at DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&apiKeys).Error; err != nil {
		return nil, 0, err
	}

	// 隐藏SecretKey
	for _, key := range apiKeys {
		key.SecretKey = "***"
	}

	return apiKeys, total, nil
}

// DeleteAPIKey 删除API密钥
func (s *ThirdAPIKeyService) DeleteAPIKey(ctx context.Context, secretId string) error {
	result := s.db.WithContext(ctx).Where("secret_id = ?", secretId).Delete(&model.ThirdAPIKey{})
	if result.Error != nil {
		return response.NewError(response.ERROR, "删除API密钥失败")
	}
	if result.RowsAffected == 0 {
		return response.NewError(response.NOT_FOUND, "API密钥不存在")
	}
	return nil
}

// UpdateAPIKeyStatus 更新API密钥状态
func (s *ThirdAPIKeyService) UpdateAPIKeyStatus(ctx context.Context, secretId string, status int8) error {
	result := s.db.WithContext(ctx).Model(&model.ThirdAPIKey{}).
		Where("secret_id = ?", secretId).
		Update("status", status)
	
	if result.Error != nil {
		return response.NewError(response.ERROR, "更新API密钥状态失败")
	}
	if result.RowsAffected == 0 {
		return response.NewError(response.NOT_FOUND, "API密钥不存在")
	}
	return nil
}
