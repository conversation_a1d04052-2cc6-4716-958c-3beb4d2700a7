package service

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strings"

	"goadmin/config"
)

// ZPAYService ZPAY支付服务
type ZPAYService struct {
	cfg *config.Config
}

// NewZPAYService 创建ZPAY支付服务
func NewZPAYService(cfg *config.Config) *ZPAYService {
	return &ZPAYService{cfg: cfg}
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	AppID      string `json:"appid"`
	OutTradeNo string `json:"out_trade_no"`
	Money      string `json:"money"`
	NotifyURL  string `json:"notify_url"`
	Name       string `json:"name"`
	Type       string `json:"type"` // alipay/wxpay
	Sign       string `json:"sign"`
}

// CreateOrderResponse 创建订单响应
type CreateOrderResponse struct {
	Code    int    `json:"code"`     // 返回状态码（数字）：1为成功
	Msg     string `json:"msg"`      // 返回信息
	TradeNo string `json:"trade_no"` // 易支付订单号
	PayURL  string `json:"payurl"`   // 支付跳转URL
	PayURL2 string `json:"payurl2"`  // 支付跳转URL2
	QRCode  string `json:"qrcode"`   // 二维码链接
	Img     string `json:"img"`      // 二维码图片
}

// NotifyRequest 支付回调请求
type NotifyRequest struct {
	AppID      string `json:"appid"`
	TradeNo    string `json:"trade_no"`
	OutTradeNo string `json:"out_trade_no"`
	Money      string `json:"money"`
	Status     string `json:"status"`
	Sign       string `json:"sign"`
}

// CreateOrder 创建支付订单
func (s *ZPAYService) CreateOrder(outTradeNo, money, name, payType string) (*CreateOrderResponse, error) {
	// 构建请求参数（根据ZPAY API接口支付文档）
	params := map[string]string{
		"pid":          s.cfg.ZPAY.AppID,     // 商户ID
		"type":         payType,              // 支付方式：alipay/wxpay
		"out_trade_no": outTradeNo,           // 商户订单号
		"notify_url":   s.cfg.ZPAY.NotifyUrl, // 异步通知地址
		"name":         name,                 // 商品名称
		"money":        money,                // 商品金额
		"clientip":     "127.0.0.1",          // 用户IP地址（可以从请求中获取）
		"device":       "pc",                 // 设备类型
		"param":        "",                   // 业务扩展参数
	}

	// 生成签名
	sign := s.generateSign(params)
	params["sign"] = sign
	params["sign_type"] = "MD5"

	// 构建POST请求数据
	postData := url.Values{}
	for k, v := range params {
		postData.Set(k, v)
	}

	fmt.Printf("ZPAY请求参数: %+v\n", params)
	fmt.Printf("ZPAY请求URL: %s\n", s.cfg.ZPAY.ApiUrl)

	// 发送POST请求到ZPAY API接口
	resp, err := http.PostForm(s.cfg.ZPAY.ApiUrl, postData)
	if err != nil {
		return nil, fmt.Errorf("请求ZPAY API失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取ZPAY响应失败: %v", err)
	}

	fmt.Printf("ZPAY响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("ZPAY响应内容: %s\n", string(body))

	// 解析响应
	var response CreateOrderResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析ZPAY响应失败: %v", err)
	}

	if response.Code != 1 { // ZPAY返回1表示成功
		return nil, fmt.Errorf("ZPAY创建订单失败: %s", response.Msg)
	}

	return &response, nil
}

// VerifyNotify 验证支付回调
func (s *ZPAYService) VerifyNotify(params map[string]string) (*NotifyRequest, error) {
	// 记录原始参数用于调试
	fmt.Printf("ZPAY回调原始参数: %+v\n", params)

	// 提取签名
	sign := params["sign"]

	// 创建用于签名验证的参数副本（包含空值参数）
	verifyParams := make(map[string]string)
	for k, v := range params {
		if k != "sign" && k != "sign_type" {
			verifyParams[k] = v
		}
	}

	// 验证签名
	expectedSign := s.generateSign(verifyParams)
	if sign != expectedSign {
		fmt.Printf("签名验证失败 - 期望: %s, 实际: %s\n", expectedSign, sign)
		fmt.Printf("验证参数: %+v\n", verifyParams)
		return nil, fmt.Errorf("签名验证失败 - 期望: %s, 实际: %s", expectedSign, sign)
	}

	// 解析回调数据
	notifyData := &NotifyRequest{}
	notifyData.AppID = params["pid"]               // 商户ID
	notifyData.TradeNo = params["trade_no"]        // ZPAY订单号
	notifyData.OutTradeNo = params["out_trade_no"] // 商户订单号
	notifyData.Money = params["money"]             // 支付金额
	notifyData.Status = params["trade_status"]     // 支付状态（ZPAY文档中是trade_status）
	notifyData.Sign = sign

	fmt.Printf("ZPAY回调解析结果: %+v\n", notifyData)
	return notifyData, nil
}

// generateSign 生成签名
func (s *ZPAYService) generateSign(params map[string]string) string {
	// 按键排序
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建签名字符串
	var signStr strings.Builder
	for _, key := range keys {
		// 跳过sign和sign_type，但包含空值参数
		if key != "sign" && key != "sign_type" {
			signStr.WriteString(key)
			signStr.WriteString("=")
			signStr.WriteString(params[key])
			signStr.WriteString("&")
		}
	}
	signStr.WriteString("key=")
	signStr.WriteString(s.cfg.ZPAY.AppSecret)

	// 打印调试信息
	fmt.Printf("签名字符串: %s\n", signStr.String())

	// MD5加密
	hash := md5.Sum([]byte(signStr.String()))
	return strings.ToUpper(fmt.Sprintf("%x", hash))
}

// QueryOrderResponse 查询订单响应
type QueryOrderResponse struct {
	Code       string `json:"code"`         // 返回状态码（字符串）
	Msg        string `json:"msg"`          // 返回信息
	Status     string `json:"status"`       // 支付状态（字符串）：1为支付成功，0为未支付
	Name       string `json:"name"`         // 商品名称
	Money      string `json:"money"`        // 商品金额
	OutTradeNo string `json:"out_trade_no"` // 商户订单号
	TradeNo    string `json:"trade_no"`     // 易支付订单号
	Type       string `json:"type"`         // 支付方式
	Param      string `json:"param"`        // 业务扩展参数
	AddTime    string `json:"addtime"`      // 创建订单时间
	EndTime    string `json:"endtime"`      // 完成交易时间
	PID        string `json:"pid"`          // 商户ID（字符串）
	Buyer      string `json:"buyer"`        // 支付者账号
}

// QueryOrder 查询订单状态
func (s *ZPAYService) QueryOrder(outTradeNo string) (*QueryOrderResponse, error) {
	// 构建请求参数（根据ZPAY查询订单文档）
	params := map[string]string{
		"pid":          s.cfg.ZPAY.AppID,     // 商户ID
		"key":          s.cfg.ZPAY.AppSecret, // 商户密钥
		"out_trade_no": outTradeNo,           // 商户订单号
	}

	// 生成签名
	sign := s.generateSign(params)
	params["sign"] = sign
	params["sign_type"] = "MD5"

	// 构建GET请求URL
	queryParams := url.Values{}
	for k, v := range params {
		queryParams.Set(k, v)
	}

	requestURL := "https://z-pay.cn/api.php?act=order&" + queryParams.Encode()

	fmt.Printf("ZPAY查询请求URL: %s\n", requestURL)

	// 发送GET请求到ZPAY查询接口
	resp, err := http.Get(requestURL)
	if err != nil {
		return nil, fmt.Errorf("请求ZPAY API失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取ZPAY响应失败: %v", err)
	}

	fmt.Printf("ZPAY查询响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("ZPAY查询响应内容: %s\n", string(body))

	// 检查响应是否为空
	if len(body) == 0 {
		return nil, fmt.Errorf("ZPAY返回空响应，可能订单不存在或参数错误")
	}

	// 解析响应
	var response QueryOrderResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析ZPAY响应失败: %v, 响应内容: %s", err, string(body))
	}

	return &response, nil
}
