package service

import (
	"context"
	"crypto/rand"
	"fmt"
	"log"
	"math/big"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/constant"
	"goadmin/internal/model"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

// DeviceService 设备服务
type DeviceService struct {
	db               *gorm.DB
	jwtService       *JWTService
	userService      *UserService
	redisService     *RedisService       // 新增Redis服务
	webSocketService *WebSocketService   // 新增WebSocket服务引用
	quotaService     *DeviceQuotaService // 新增配额服务
}

// NewDeviceService 创建设备服务
func NewDeviceService(db *gorm.DB, jwtService *JWTService, userService *UserService, redisService *RedisService, webSocketService *WebSocketService, quotaService *DeviceQuotaService) *DeviceService {
	return &DeviceService{
		db:               db,
		jwtService:       jwtService,
		userService:      userService,
		redisService:     redisService,
		webSocketService: webSocketService,
		quotaService:     quotaService,
	}
}

// UpdateDeviceStatus 更新设备状态
func (s *DeviceService) UpdateDeviceStatus(ctx context.Context, deviceID string, status int) error {
	var device model.Device

	if err := s.db.WithContext(ctx).Where("device_id = ?", deviceID).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return response.NewError(response.ERROR, "设备不存在")
		}
		return response.NewError(response.ERROR, "查询设备失败")
	}

	now := time.Now()
	device.Status = status
	device.LastActive = &now

	if err := s.db.WithContext(ctx).Save(&device).Error; err != nil {
		return response.NewError(response.ERROR, "更新设备状态失败")
	}

	return nil
}

// UpdateDeviceInfo 更新设备信息（名称）
func (s *DeviceService) UpdateDeviceInfo(ctx context.Context, deviceID string, userID uint, name string) error {
	var device model.Device

	if err := s.db.WithContext(ctx).Where("device_id = ? AND user_id = ?", deviceID, userID).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return response.NewError(response.ERROR, "设备不存在或无权限修改")
		}
		return response.NewError(response.ERROR, "查询设备失败")
	}

	// 更新设备信息
	device.Name = name

	if err := s.db.WithContext(ctx).Save(&device).Error; err != nil {
		return response.NewError(response.ERROR, "更新设备信息失败")
	}

	return nil
}

// GetDeviceByID 根据设备ID获取设备信息
func (s *DeviceService) GetDeviceByID(ctx context.Context, deviceID string) (*model.Device, error) {
	var device model.Device

	if err := s.db.WithContext(ctx).Where("device_id = ?", deviceID).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "设备不存在")
		}
		return nil, response.NewError(response.ERROR, "查询设备失败")
	}

	return &device, nil
}

// GetUserDevices 获取用户的设备列表（支持状态筛选）
func (s *DeviceService) GetUserDevices(ctx context.Context, userID uint, status []int) ([]model.Device, error) {
	var devices []model.Device

	query := s.db.WithContext(ctx).Where("user_id = ? AND pairing_status = ? AND device_id != '' AND device_id NOT LIKE 'temp_%'",
		userID, model.PairingStatusCompleted)

	// 如果提供了状态筛选条件
	if len(status) > 0 {
		query = query.Where("status IN ?", status)
	}

	if err := query.Find(&devices).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询设备列表失败")
	}

	// 如果有Redis服务，优先使用Redis中的实时状态
	if s.redisService != nil {
		// 获取Redis中的在线设备
		onlineDevices, err := s.redisService.GetOnlineDevices(ctx, userID)
		if err != nil {
			log.Printf("获取Redis在线设备失败: %v", err)
		} else {
			// 更新设备状态为Redis中的实时状态
			for i := range devices {
				if _, isOnline := onlineDevices[devices[i].DeviceID]; isOnline {
					devices[i].Status = constant.DeviceStatusOnline
				} else {
					devices[i].Status = constant.DeviceStatusOffline
				}
			}
		}
	}

	// 为每个设备添加配额信息
	for i := range devices {
		quotaInfo, err := s.getDeviceQuotaInfo(ctx, userID, devices[i].ID)
		if err != nil {
			logger.Error(ctx, "获取设备配额信息失败: device_id=%d, error=%v", devices[i].ID, err)
			// 即使获取配额信息失败，也不影响设备列表返回
			continue
		}
		// 将配额信息添加到设备中
		devices[i].QuotaInfo = quotaInfo
	}

	return devices, nil
}

// GetUserDeviceByID 根据数据库ID获取用户设备
func (s *DeviceService) GetUserDeviceByID(ctx context.Context, userID, deviceID uint) (*model.Device, error) {
	var device model.Device

	if err := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", deviceID, userID).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "设备不存在")
		}
		return nil, response.NewError(response.ERROR, "查询设备失败")
	}

	return &device, nil
}

// DeleteDevice 删除设备
func (s *DeviceService) DeleteDevice(ctx context.Context, deviceID string, userID uint) error {
	// 开启事务，确保删除设备和清理绑定关系的原子性
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 删除设备配置关联关系（没有配置时不报错）
		configResult := tx.Where("device_id = ?", deviceID).Delete(&model.DeviceConfigRelation{})
		if configResult.Error != nil {
			return response.NewError(response.ERROR, "清理配置关联关系失败")
		}
		// 2. 删除设备记录
		result := tx.Where("device_id = ? AND user_id = ?", deviceID, userID).Delete(&model.Device{})
		if result.Error != nil {
			return response.NewError(response.ERROR, "删除设备失败")
		}

		if result.RowsAffected == 0 {
			return response.NewError(response.ERROR, "设备不存在")
		}

		logger.Info(ctx, "设备删除成功: device_id=%s, user_id=%d", deviceID, userID)

		// 3. 断开设备的WebSocket连接
		if s.webSocketService != nil {
			s.webSocketService.DisconnectDevice(deviceID)
		}

		return nil
	})
}

// DeleteDeviceByID 根据数据库ID删除设备
func (s *DeviceService) DeleteDeviceByID(ctx context.Context, deviceID uint, userID uint) error {
	// 开启事务，确保删除设备和清理绑定关系的原子性
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 删除设备配置关联关系（没有配置时不报错）
		configResult := tx.Where("device_id = ?", deviceID).Delete(&model.DeviceConfigRelation{})
		if configResult.Error != nil {
			return response.NewError(response.ERROR, "清理配置关联关系失败")
		}
		// 记录删除的配置关联数量（可选，用于调试）
		if configResult.RowsAffected > 0 {
			logger.Info(ctx, "删除了 %d 个设备配置关联关系: device_id=%d", configResult.RowsAffected, deviceID)
		}

		// 2. 删除设备记录
		result := tx.Where("id = ? AND user_id = ?", deviceID, userID).Delete(&model.Device{})
		if result.Error != nil {
			return response.NewError(response.ERROR, "删除设备失败")
		}

		if result.RowsAffected == 0 {
			return response.NewError(response.ERROR, "设备不存在")
		}

		logger.Info(ctx, "设备删除成功: device_id=%d, user_id=%d", deviceID, userID)

		// 3. 释放设备配额
		if s.quotaService != nil {
			if err := s.quotaService.ReleaseQuotaForDevice(ctx, userID, deviceID); err != nil {
				logger.Error(ctx, "释放设备配额失败: user_id=%d, device_id=%d, error=%v", userID, deviceID, err)
				// 配额释放失败不影响设备删除，只记录日志
			}
		}

		// 4. 断开设备的WebSocket连接（需要先获取device_id）
		var device model.Device
		if err := tx.Where("id = ?", deviceID).First(&device).Error; err == nil {
			if s.webSocketService != nil {
				s.webSocketService.DisconnectDevice(device.DeviceID)
			}
		}

		return nil
	})
}

// 配对相关方法

// GeneratePairingCode 生成配对码
func (s *DeviceService) GeneratePairingCode(ctx context.Context, userID uint) (*model.Device, error) {
	// 检查用户配额是否足够
	if s.quotaService != nil {
		hasQuota, err := s.quotaService.CheckUserQuota(ctx, userID)
		if err != nil {
			logger.Error(ctx, "检查用户配额失败: user_id=%d, error=%v", userID, err)
			return nil, response.NewError(response.ERROR, "检查配额失败")
		}

		if !hasQuota {
			logger.Info(ctx, "用户配额不足，无法生成配对码: user_id=%d", userID)
			return nil, response.NewError(response.ERROR, "设备配额不足，请购买更多配额")
		}
	}

	// 检查用户是否有未配对完成且未过期的配对码
	var existingDevice model.Device
	if err := s.db.WithContext(ctx).Where("user_id = ? AND pairing_status = ? AND pairing_expires_at > ?",
		userID, model.PairingStatusPending, time.Now()).First(&existingDevice).Error; err == nil {
		// 找到未过期的配对码，直接返回
		logger.Info(ctx, "返回现有配对码: user_id=%d, pairing_code=%s", userID, *existingDevice.PairingCode)
		return &existingDevice, nil
	}

	// 生成6位大写字母+数字的配对码
	code, err := s.generateAlphanumericCode(4)
	if err != nil {
		return nil, response.NewError(response.ERROR, "生成配对码失败")
	}

	// 设置过期时间（10分钟后）
	expiresAt := time.Now().Add(10 * time.Minute)

	// 创建临时设备记录
	device := &model.Device{
		DeviceID:    "temp_" + code, // 临时设备ID
		Name:        "待配对设备",
		DeviceModel: "",

		Status:           constant.DeviceStatusOffline, // 使用常量
		UserID:           userID,
		PairingCode:      &code,
		PairingStatus:    model.PairingStatusPending,
		PairingExpiresAt: &expiresAt,
	}

	if err := s.db.WithContext(ctx).Create(device).Error; err != nil {
		return nil, response.NewError(response.ERROR, "创建配对记录失败")
	}

	logger.Info(ctx, "生成配对码成功: user_id=%d, pairing_code=%s, expires_at=%v", userID, code, expiresAt)
	return device, nil
}

// SubmitDeviceInfo 提交设备信息并完成配对
func (s *DeviceService) SubmitDeviceInfo(ctx context.Context, pairingCode, deviceID, name, deviceModel, androidVersion, autoxVersion string) (*model.Device, error) {
	var device model.Device

	logger.Info(ctx, "开始提交设备信息: pairing_code=%s", pairingCode)

	if err := s.db.WithContext(ctx).Where("pairing_code = ?", pairingCode).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Info(ctx, "配对码不存在: %s", pairingCode)
			return nil, response.NewError(response.ERROR, "配对码不存在")
		}
		logger.Error(ctx, "查询配对码失败: %v", err)
		return nil, response.NewError(response.ERROR, "查询配对码失败")
	}

	logger.Info(ctx, "找到配对记录: id=%d, status=%d, expires_at=%v", device.ID, device.PairingStatus, device.PairingExpiresAt)

	// 检查是否已过期
	if device.PairingExpiresAt != nil && time.Now().After(*device.PairingExpiresAt) {
		logger.Info(ctx, "配对码已过期: %s", pairingCode)
		return nil, response.NewError(response.ERROR, "配对码已过期")
	}

	// 检查状态
	if device.PairingStatus != model.PairingStatusPending {
		logger.Info(ctx, "配对码已被使用: status=%d", device.PairingStatus)
		return nil, response.NewError(response.ERROR, "配对码已被使用")
	}

	if deviceID == "" {
		return nil, response.NewError(response.ERROR, "设备ID不能为空")
	}

	// 检查设备是否已存在
	var existingDevice model.Device
	if err := s.db.WithContext(ctx).Where("device_id = ?", deviceID).First(&existingDevice).Error; err == nil {
		// 设备已存在，返回失败
		logger.Info(ctx, "设备ID已存在，配对失败: device_id=%s", deviceID)
		return nil, response.NewError(response.ERROR, fmt.Sprintf("设备ID已存在，无法重复配对: %s", deviceID))
	} else if err == gorm.ErrRecordNotFound {
		// 设备不存在，检查用户配额
		if s.quotaService != nil {
			logger.Info(ctx, "开始检查用户配额: user_id=%d", device.UserID)

			// 检查用户配额是否足够
			hasQuota, err := s.quotaService.CheckUserQuota(ctx, device.UserID)
			if err != nil {
				logger.Error(ctx, "检查用户配额失败: user_id=%d, error=%v", device.UserID, err)
				return nil, response.NewError(response.ERROR, "检查配额失败")
			}

			if !hasQuota {
				logger.Info(ctx, "用户配额不足，配对失败: user_id=%d", device.UserID)
				return nil, response.NewError(response.ERROR, "设备配额不足，请购买更多配额")
			}

			logger.Info(ctx, "用户配额检查通过: user_id=%d", device.UserID)
		}

		// 使用事务处理设备创建和配额分配
		var resultDevice model.Device
		err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			// 设备不存在，更新临时记录为真实设备信息并完成配对
			now := time.Now()
			device.DeviceID = deviceID
			device.Name = name
			device.DeviceModel = deviceModel
			device.AndroidVersion = androidVersion
			device.AutoxVersion = autoxVersion
			device.Status = constant.DeviceStatusOffline // 使用常量
			device.LastActive = &now
			device.PairingStatus = model.PairingStatusCompleted // 直接完成配对

			// 生成标准的 access token 和 refresh token
			accessToken, err := s.jwtService.GenerateAccessToken(
				fmt.Sprintf("%d", device.UserID),
				deviceID,
				constant.TokenTypeDevice, // AutoX.js 设备客户端类型
			)
			if err != nil {
				logger.Error(ctx, "生成访问令牌失败: %v", err)
				return response.NewError(response.ERROR, "生成访问令牌失败")
			}

			// 设置 access token 和过期时间（1小时）
			tokenExpiresAt := now.Add(time.Hour)
			device.Token = &accessToken
			device.TokenExpiresAt = &tokenExpiresAt

			if err := tx.Save(&device).Error; err != nil {
				logger.Error(ctx, "更新设备信息失败: %v", err)
				return response.NewError(response.ERROR, "更新设备信息失败")
			}

			// 分配配额给设备（在同一个事务中）
			if s.quotaService != nil {
				// 创建临时配额服务，使用当前事务
				tempQuotaService := &DeviceQuotaService{db: tx}
				if err := tempQuotaService.AllocateQuotaForDevice(ctx, device.UserID, uint(device.ID)); err != nil {
					logger.Error(ctx, "分配设备配额失败: user_id=%d, device_id=%d, error=%v", device.UserID, device.ID, err)
					return response.NewError(response.ERROR, "配额分配失败")
				}
			}

			resultDevice = device
			return nil
		})

		if err != nil {
			return nil, err
		}

		logger.Info(ctx, "设备信息提交成功（新设备，配对完成）: pairing_code=%s, device_id=%s", pairingCode, deviceID)
		return &resultDevice, nil
	} else {
		return nil, response.NewError(response.ERROR, "查询设备失败")
	}
}

// GetPairingStatus 获取配对状态
func (s *DeviceService) GetPairingStatus(ctx context.Context, pairingCode string, userID uint) (*model.Device, error) {
	var device model.Device

	logger.Info(ctx, "查询配对状态: pairing_code=%s, userID=%d", pairingCode, userID)

	// 通过配对码查询配对记录
	if err := s.db.WithContext(ctx).Where("pairing_code = ?", pairingCode).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Info(ctx, "配对记录不存在: pairing_code=%s", pairingCode)
			return nil, response.NewError(response.ERROR, "配对记录不存在")
		}
		logger.Error(ctx, "查询配对记录失败: %v", err)
		return nil, response.NewError(response.ERROR, "查询配对记录失败")
	}

	logger.Info(ctx, "找到配对记录: id=%d, device_id=%s, pairing_status=%d", device.ID, device.DeviceID, device.PairingStatus)

	return &device, nil
}

// CleanupExpiredPairings 清理过期的配对记录
func (s *DeviceService) CleanupExpiredPairings(ctx context.Context) error {
	now := time.Now()
	result := s.db.WithContext(ctx).Where("pairing_expires_at < ? AND pairing_status = ?", now, model.PairingStatusPending).Delete(&model.Device{})

	if result.Error != nil {
		return response.NewError(response.ERROR, "清理过期配对记录失败")
	}

	return nil
}

// generateRandomCode 生成随机数字码
func (s *DeviceService) generateRandomCode(length int) (string, error) {
	const digits = "0123456789"
	code := make([]byte, length)

	for i := 0; i < length; i++ {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(digits))))
		if err != nil {
			return "", err
		}
		code[i] = digits[num.Int64()]
	}

	return string(code), nil
}

// generateAlphanumericCode 生成大写字母+数字的配对码
func (s *DeviceService) generateAlphanumericCode(length int) (string, error) {
	const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	code := make([]byte, length)

	for i := 0; i < length; i++ {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(chars))))
		if err != nil {
			return "", err
		}
		code[i] = chars[num.Int64()]
	}

	return string(code), nil
}

// CreateDeviceRefreshToken 为设备创建数据库中的刷新令牌
func (s *DeviceService) CreateDeviceRefreshToken(userID uint, deviceID string) (string, error) {
	// 获取设备ID对应的数据库ID
	var device model.Device
	if err := s.db.Where("device_id = ? AND user_id = ?", deviceID, userID).First(&device).Error; err != nil {
		return "", response.NewError(response.ERROR, "设备不存在")
	}

	// 使用用户服务创建 refresh token
	deviceIDUint := uint(device.ID)
	return s.userService.CreateRefreshToken(
		context.Background(),
		&userID,
		&deviceIDUint,
		constant.TokenTypeDevice,
		time.Now().AddDate(0, 0, 30), // 30天有效期
	)
}

// ValidateDeviceRefreshToken 验证设备刷新令牌
func (s *DeviceService) ValidateDeviceRefreshToken(refreshToken string) (*model.RefreshToken, error) {
	return s.userService.ValidateRefreshToken(context.Background(), refreshToken)
}

// GetDeviceByDatabaseID 根据数据库ID获取设备
func (s *DeviceService) GetDeviceByDatabaseID(deviceID uint) (*model.Device, error) {
	var device model.Device
	if err := s.db.Where("id = ?", deviceID).First(&device).Error; err != nil {
		return nil, response.NewError(response.ERROR, "设备不存在")
	}
	return &device, nil
}

// GenerateDeviceAccessToken 生成设备访问令牌
func (s *DeviceService) GenerateDeviceAccessToken(userID uint, deviceID string) (string, error) {
	return s.jwtService.GenerateAccessToken(
		fmt.Sprintf("%d", userID),
		deviceID,
		constant.TokenTypeDevice,
	)
}

// DeleteDeviceRefreshToken 删除设备刷新令牌
func (s *DeviceService) DeleteDeviceRefreshToken(refreshToken string) error {
	return s.userService.DeleteRefreshToken(context.Background(), refreshToken)
}

// RefreshDeviceAccessToken 刷新设备访问令牌
func (s *DeviceService) RefreshDeviceAccessToken(refreshToken string) (string, string, error) {
	return s.jwtService.RefreshAccessToken(refreshToken)
}

// ValidateDeviceToken 验证设备token
func (s *DeviceService) ValidateDeviceToken(ctx context.Context, deviceID, token string) (*model.Device, error) {
	// 首先验证 JWT token
	claims, err := s.jwtService.ParseToken(token)
	if err != nil {
		return nil, response.NewError(response.ERROR, "JWT token 验证失败")
	}

	// 从 claims 中获取用户ID
	userIDStr, ok := claims["user_id"].(string)
	if !ok {
		return nil, response.NewError(response.ERROR, "JWT token 中缺少 user_id")
	}

	var userID uint
	_, err = fmt.Sscanf(userIDStr, "%d", &userID)
	if err != nil {
		return nil, response.NewError(response.ERROR, "无效的用户ID格式")
	}

	// 查询设备信息
	var device model.Device
	if err := s.db.WithContext(ctx).Where("device_id = ? AND user_id = ?", deviceID, userID).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "设备不存在或无权限访问")
		}
		return nil, response.NewError(response.ERROR, "查询设备失败")
	}

	// 检查设备 token 是否过期（用于自动续期判断）
	if device.TokenExpiresAt != nil && time.Now().After(*device.TokenExpiresAt) {
		// Token已过期，尝试自动续期
		logger.Info(ctx, "设备JWT token已过期，尝试自动续期: device_id=%s", deviceID)

		// 生成新的包含设备信息的 JWT token
		newToken, err := s.jwtService.GenerateWebSocketToken(
			fmt.Sprintf("%d", device.UserID),
			device.DeviceID,
			constant.TokenTypeDevice, // AutoX.js 设备客户端类型
		)
		if err != nil {
			logger.Error(ctx, "自动续期JWT token失败: device_id=%s, error=%v", deviceID, err)
			return nil, response.NewError(response.ERROR, "设备token已过期且续期失败")
		}

		// 更新token和过期时间
		now := time.Now()
		tokenExpiresAt := now.AddDate(0, 0, 30) // 再延长30天
		device.Token = &newToken
		device.TokenExpiresAt = &tokenExpiresAt

		if err := s.db.WithContext(ctx).Save(&device).Error; err != nil {
			logger.Error(ctx, "保存续期token失败: device_id=%s, error=%v", deviceID, err)
			return nil, response.NewError(response.ERROR, "设备token已过期且续期失败")
		}

		logger.Info(ctx, "设备JWT token自动续期成功: device_id=%s", deviceID)
	}

	return &device, nil
}

// RefreshDeviceToken 刷新设备JWT token
func (s *DeviceService) RefreshDeviceToken(ctx context.Context, deviceID string, userID uint) (*model.Device, error) {
	var device model.Device

	if err := s.db.WithContext(ctx).Where("device_id = ? AND user_id = ?", deviceID, userID).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "设备不存在")
		}
		return nil, response.NewError(response.ERROR, "查询设备失败")
	}

	// 生成新的包含设备信息的 JWT token
	newToken, err := s.jwtService.GenerateWebSocketToken(
		fmt.Sprintf("%d", userID),
		deviceID,
		constant.TokenTypeDevice, // AutoX.js 设备客户端类型
	)
	if err != nil {
		return nil, response.NewError(response.ERROR, "生成新JWT token失败")
	}

	// 更新token和过期时间
	now := time.Now()
	tokenExpiresAt := now.AddDate(0, 0, 30)
	device.Token = &newToken
	device.TokenExpiresAt = &tokenExpiresAt

	if err := s.db.WithContext(ctx).Save(&device).Error; err != nil {
		return nil, response.NewError(response.ERROR, "更新JWT token失败")
	}

	logger.Info(ctx, "设备JWT token手动续期成功: device_id=%s", deviceID)
	return &device, nil
}

// GetDeviceList 获取设备列表（管理员）
func (s *DeviceService) GetDeviceList(ctx context.Context, page, pageSize int, userID *uint, status *int, keyword string) ([]*model.Device, int64, error) {
	var devices []*model.Device
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Device{}).Preload("User")

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	if keyword != "" {
		query = query.Where("device_id LIKE ? OR name LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询设备总数失败")
	}

	// 查询设备列表
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("last_active DESC").Find(&devices).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询设备列表失败")
	}

	return devices, total, nil
}

// GetOnlineDeviceCount 获取在线设备数量
func (s *DeviceService) GetOnlineDeviceCount(ctx context.Context) (int64, error) {
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.Device{}).Where("status = ?", constant.DeviceStatusOnline).Count(&count).Error; err != nil {
		return 0, response.NewError(response.ERROR, "查询在线设备数量失败")
	}
	return count, nil
}

// getDeviceQuotaInfo 获取设备的配额信息
func (s *DeviceService) getDeviceQuotaInfo(ctx context.Context, userID uint, deviceID uint64) (*model.DeviceQuotaInfo, error) {
	// 先检查设备状态
	var device model.Device
	if err := s.db.WithContext(ctx).Where("id = ?", deviceID).First(&device).Error; err != nil {
		return nil, err
	}

	// 如果设备已过期，返回过期状态
	if device.Status == constant.DeviceStatusExpired {
		return &model.DeviceQuotaInfo{
			IsFree:      false,
			ExpiresAt:   nil,
			QuotaType:   "expired",
			Description: "配额已过期",
		}, nil
	}

	// 查找设备使用的付费配额
	var usage model.DeviceQuotaUsage
	if err := s.db.WithContext(ctx).Where("user_id = ? AND device_id = ?", userID, deviceID).
		Order("created_at DESC").First(&usage).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 没有找到配额使用记录，说明使用免费配额
			return &model.DeviceQuotaInfo{
				IsFree:      true,
				ExpiresAt:   nil,
				QuotaType:   "free",
				Description: "免费配额",
			}, nil
		}
		return nil, err
	}

	// 获取配额详情
	var quota model.DeviceQuota
	if err := s.db.WithContext(ctx).Where("id = ?", usage.QuotaID).First(&quota).Error; err != nil {
		return nil, err
	}

	return &model.DeviceQuotaInfo{
		IsFree:      false,
		ExpiresAt:   quota.ExpiresAt,
		QuotaType:   "paid",
		Description: "付费配额",
		QuotaID:     &quota.ID,
	}, nil
}
