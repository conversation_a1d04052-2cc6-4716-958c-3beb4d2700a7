package service

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// DeviceService 设备服务
type DeviceService struct {
	db *gorm.DB
}

// NewDeviceService 创建设备服务
func NewDeviceService(db *gorm.DB) *DeviceService {
	return &DeviceService{
		db: db,
	}
}

// RegisterDevice 注册设备
func (s *DeviceService) RegisterDevice(ctx context.Context, deviceID, name, deviceType, ip, userAgent, version string, userID uint) (*model.Device, error) {
	var device model.Device
	
	// 查找现有设备
	err := s.db.WithContext(ctx).Where("device_id = ?", deviceID).First(&device).Error
	
	if err == gorm.ErrRecordNotFound {
		// 创建新设备
		device = model.Device{
			DeviceID:  deviceID,
			Name:      name,
			Type:      deviceType,
			Status:    1, // 在线
			UserID:    userID,
			IP:        ip,
			UserAgent: userAgent,
			Version:   version,
		}
		now := time.Now()
		device.LastActive = &now
		
		if err := s.db.WithContext(ctx).Create(&device).Error; err != nil {
			return nil, response.NewError(response.ERROR, "注册设备失败")
		}
	} else if err != nil {
		return nil, response.NewError(response.ERROR, "查询设备失败")
	} else {
		// 更新现有设备信息
		now := time.Now()
		updates := map[string]interface{}{
			"name":        name,
			"type":        deviceType,
			"status":      1,
			"user_id":     userID,
			"ip":          ip,
			"user_agent":  userAgent,
			"version":     version,
			"last_active": now,
		}
		
		if err := s.db.WithContext(ctx).Model(&device).Updates(updates).Error; err != nil {
			return nil, response.NewError(response.ERROR, "更新设备信息失败")
		}
		
		// 重新查询更新后的设备信息
		if err := s.db.WithContext(ctx).Where("device_id = ?", deviceID).First(&device).Error; err != nil {
			return nil, response.NewError(response.ERROR, "查询设备信息失败")
		}
	}
	
	return &device, nil
}

// UpdateDeviceStatus 更新设备状态
func (s *DeviceService) UpdateDeviceStatus(ctx context.Context, deviceID string, status int8) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":      status,
		"last_active": now,
	}
	
	result := s.db.WithContext(ctx).Model(&model.Device{}).Where("device_id = ?", deviceID).Updates(updates)
	if result.Error != nil {
		return response.NewError(response.ERROR, "更新设备状态失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "设备不存在")
	}
	
	return nil
}

// GetDeviceByID 根据设备ID获取设备信息
func (s *DeviceService) GetDeviceByID(ctx context.Context, deviceID string) (*model.Device, error) {
	var device model.Device
	if err := s.db.WithContext(ctx).Preload("User").Where("device_id = ?", deviceID).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "设备不存在")
		}
		return nil, response.NewError(response.ERROR, "查询设备失败")
	}
	return &device, nil
}

// GetDeviceList 获取设备列表
func (s *DeviceService) GetDeviceList(ctx context.Context, page, pageSize int, userID *uint, status *int8, keyword string) ([]*model.Device, int64, error) {
	var devices []*model.Device
	var total int64
	
	query := s.db.WithContext(ctx).Model(&model.Device{}).Preload("User")
	
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}
	
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	
	if keyword != "" {
		query = query.Where("device_id LIKE ? OR name LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	
	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询设备总数失败")
	}
	
	// 查询设备列表
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("last_active DESC").Find(&devices).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询设备列表失败")
	}
	
	return devices, total, nil
}

// GetUserDevices 获取用户的设备列表
func (s *DeviceService) GetUserDevices(ctx context.Context, userID uint) ([]*model.Device, error) {
	var devices []*model.Device
	if err := s.db.WithContext(ctx).Where("user_id = ?", userID).Order("last_active DESC").Find(&devices).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询用户设备失败")
	}
	return devices, nil
}

// DeleteDevice 删除设备
func (s *DeviceService) DeleteDevice(ctx context.Context, deviceID string) error {
	result := s.db.WithContext(ctx).Where("device_id = ?", deviceID).Delete(&model.Device{})
	if result.Error != nil {
		return response.NewError(response.ERROR, "删除设备失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "设备不存在")
	}
	
	return nil
}

// CreateDeviceGroup 创建设备分组
func (s *DeviceService) CreateDeviceGroup(ctx context.Context, name, description string, userID uint, deviceIDs []uint) (*model.DeviceGroup, error) {
	// 序列化设备ID列表
	deviceIDsJSON, err := json.Marshal(deviceIDs)
	if err != nil {
		return nil, response.NewError(response.ERROR, "设备ID序列化失败")
	}
	
	group := &model.DeviceGroup{
		Name:        name,
		Description: description,
		UserID:      userID,
		DeviceIDs:   string(deviceIDsJSON),
	}
	
	if err := s.db.WithContext(ctx).Create(group).Error; err != nil {
		return nil, response.NewError(response.ERROR, "创建设备分组失败")
	}
	
	return group, nil
}

// GetDeviceGroups 获取设备分组列表
func (s *DeviceService) GetDeviceGroups(ctx context.Context, userID uint) ([]*model.DeviceGroup, error) {
	var groups []*model.DeviceGroup
	if err := s.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").Find(&groups).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询设备分组失败")
	}
	return groups, nil
}

// UpdateDeviceGroup 更新设备分组
func (s *DeviceService) UpdateDeviceGroup(ctx context.Context, groupID uint, name, description string, deviceIDs []uint) error {
	// 序列化设备ID列表
	deviceIDsJSON, err := json.Marshal(deviceIDs)
	if err != nil {
		return response.NewError(response.ERROR, "设备ID序列化失败")
	}
	
	updates := map[string]interface{}{
		"name":        name,
		"description": description,
		"device_ids":  string(deviceIDsJSON),
	}
	
	result := s.db.WithContext(ctx).Model(&model.DeviceGroup{}).Where("id = ?", groupID).Updates(updates)
	if result.Error != nil {
		return response.NewError(response.ERROR, "更新设备分组失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "设备分组不存在")
	}
	
	return nil
}

// DeleteDeviceGroup 删除设备分组
func (s *DeviceService) DeleteDeviceGroup(ctx context.Context, groupID uint) error {
	result := s.db.WithContext(ctx).Delete(&model.DeviceGroup{}, groupID)
	if result.Error != nil {
		return response.NewError(response.ERROR, "删除设备分组失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "设备分组不存在")
	}
	
	return nil
}

// GetOnlineDeviceCount 获取在线设备数量
func (s *DeviceService) GetOnlineDeviceCount(ctx context.Context) (int64, error) {
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.Device{}).Where("status = ?", 1).Count(&count).Error; err != nil {
		return 0, response.NewError(response.ERROR, "查询在线设备数量失败")
	}
	return count, nil
}

// CleanOfflineDevices 清理离线设备
func (s *DeviceService) CleanOfflineDevices(ctx context.Context, offlineThreshold time.Duration) error {
	cutoffTime := time.Now().Add(-offlineThreshold)
	
	result := s.db.WithContext(ctx).Model(&model.Device{}).
		Where("last_active < ? AND status != ?", cutoffTime, 0).
		Update("status", 0)
	
	if result.Error != nil {
		return response.NewError(response.ERROR, "清理离线设备失败")
	}
	
	return nil
}
