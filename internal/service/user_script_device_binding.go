package service

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// UserScriptDeviceBindingService 用户脚本设备绑定服务
type UserScriptDeviceBindingService struct {
	db *gorm.DB
}

// NewUserScriptDeviceBindingService 创建用户脚本设备绑定服务
func NewUserScriptDeviceBindingService(db *gorm.DB) *UserScriptDeviceBindingService {
	return &UserScriptDeviceBindingService{
		db: db,
	}
}

// BindDevice 绑定设备到用户脚本
func (s *UserScriptDeviceBindingService) BindDevice(ctx context.Context, userScriptID uint, deviceID uint64) error {
	// 检查是否已存在绑定关系
	var existingBinding model.UserScriptDeviceBinding
	err := s.db.WithContext(ctx).
		Where("user_script_id = ? AND device_id = ?", userScriptID, deviceID).
		First(&existingBinding).Error

	if err == nil {
		// 绑定关系已存在
		return response.NewError(response.ERROR, "设备已绑定到该脚本")
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return response.NewError(response.ERROR, "检查绑定关系失败")
	}

	// 检查用户脚本是否存在
	var userScript model.UserScript
	if err := s.db.WithContext(ctx).Where("id = ?", userScriptID).First(&userScript).Error; err != nil {
		return response.NewError(response.ERROR, "用户脚本不存在")
	}

	// 检查设备是否存在
	var device model.Device
	if err := s.db.WithContext(ctx).Where("id = ?", deviceID).First(&device).Error; err != nil {
		return response.NewError(response.ERROR, "设备不存在")
	}

	// 检查绑定数量是否超过限制
	var bindingCount int64
	if err := s.db.WithContext(ctx).
		Model(&model.UserScriptDeviceBinding{}).
		Where("user_script_id = ? AND status = ?", userScriptID, model.UserScriptDeviceBindingStatusActive).
		Count(&bindingCount).Error; err != nil {
		return response.NewError(response.ERROR, "查询绑定数量失败")
	}

	if userScript.MaxDevices > 0 && bindingCount >= int64(userScript.MaxDevices) {
		return response.NewError(response.ERROR, "设备绑定数量已达上限")
	}

	// 使用事务创建绑定关系
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建绑定关系
		binding := &model.UserScriptDeviceBinding{
			UserScriptID: userScriptID,
			DeviceID:     deviceID,
			Status:       model.UserScriptDeviceBindingStatusActive,
		}

		return tx.Create(binding).Error
	})
}

// UnbindDevice 解绑设备
func (s *UserScriptDeviceBindingService) UnbindDevice(ctx context.Context, userScriptID uint, deviceID uint64) error {
	result := s.db.WithContext(ctx).
		Where("user_script_id = ? AND device_id = ?", userScriptID, deviceID).
		Delete(&model.UserScriptDeviceBinding{})

	if result.Error != nil {
		return response.NewError(response.ERROR, "解绑设备失败")
	}

	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "绑定关系不存在")
	}

	return nil
}

// GetBindingsByUserScript 获取用户脚本的设备绑定列表
func (s *UserScriptDeviceBindingService) GetBindingsByUserScript(ctx context.Context, userScriptID uint) ([]*model.UserScriptDeviceBinding, error) {
	var bindings []*model.UserScriptDeviceBinding

	if err := s.db.WithContext(ctx).
		Where("user_script_id = ?", userScriptID).
		Preload("Device").
		Order("created_at DESC").
		Find(&bindings).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询设备绑定列表失败")
	}

	// 手动查询设备信息
	if len(bindings) > 0 {
		deviceIDs := make([]uint64, 0, len(bindings))
		for _, binding := range bindings {
			deviceIDs = append(deviceIDs, binding.DeviceID)
		}

		var devices []*model.Device
		if err := s.db.WithContext(ctx).
			Where("id IN ?", deviceIDs).
			Find(&devices).Error; err != nil {
			return nil, response.NewError(response.ERROR, "查询设备信息失败")
		}

		// 创建设备ID到设备的映射
		deviceMap := make(map[uint64]*model.Device)
		for _, device := range devices {
			deviceMap[device.ID] = device
		}

		// 为每个绑定设置设备信息
		for _, binding := range bindings {
			if device, exists := deviceMap[binding.DeviceID]; exists {
				binding.Device = device
			}
		}
	}

	return bindings, nil
}

// GetBindingsByDevice 获取设备的脚本绑定列表
func (s *UserScriptDeviceBindingService) GetBindingsByDevice(ctx context.Context, deviceID uint64) ([]*model.UserScriptDeviceBinding, error) {
	var bindings []*model.UserScriptDeviceBinding

	if err := s.db.WithContext(ctx).
		Where("device_id = ?", deviceID).
		Preload("UserScript").
		Order("created_at DESC").
		Find(&bindings).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询脚本绑定列表失败")
	}

	return bindings, nil
}

// UpdateLastActive 更新设备最后活跃时间
func (s *UserScriptDeviceBindingService) UpdateLastActive(ctx context.Context, userScriptID uint, deviceID uint64) error {
	now := time.Now()
	result := s.db.WithContext(ctx).
		Model(&model.UserScriptDeviceBinding{}).
		Where("user_script_id = ? AND device_id = ?", userScriptID, deviceID).
		Update("last_active", &now)

	if result.Error != nil {
		return response.NewError(response.ERROR, "更新最后活跃时间失败")
	}

	return nil
}

// CheckDeviceBinding 检查设备是否已绑定到指定用户脚本
func (s *UserScriptDeviceBindingService) CheckDeviceBinding(ctx context.Context, userScriptID uint, deviceID uint64) (bool, error) {
	var count int64
	err := s.db.WithContext(ctx).
		Model(&model.UserScriptDeviceBinding{}).
		Where("user_script_id = ? AND device_id = ? AND status = ?", userScriptID, deviceID, model.UserScriptDeviceBindingStatusActive).
		Count(&count).Error

	if err != nil {
		return false, response.NewError(response.ERROR, "检查设备绑定状态失败")
	}

	return count > 0, nil
}

// GetBindingCount 获取用户脚本的设备绑定数量
func (s *UserScriptDeviceBindingService) GetBindingCount(ctx context.Context, userScriptID uint) (int64, error) {
	var count int64
	err := s.db.WithContext(ctx).
		Model(&model.UserScriptDeviceBinding{}).
		Where("user_script_id = ? AND status = ?", userScriptID, model.UserScriptDeviceBindingStatusActive).
		Count(&count).Error

	if err != nil {
		return 0, response.NewError(response.ERROR, "查询绑定数量失败")
	}

	return count, nil
}

// UpdateBindingStatus 更新绑定状态
func (s *UserScriptDeviceBindingService) UpdateBindingStatus(ctx context.Context, userScriptID uint, deviceID uint64, status int8) error {
	result := s.db.WithContext(ctx).
		Model(&model.UserScriptDeviceBinding{}).
		Where("user_script_id = ? AND device_id = ?", userScriptID, deviceID).
		Update("status", status)

	if result.Error != nil {
		return response.NewError(response.ERROR, "更新绑定状态失败")
	}

	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "绑定关系不存在")
	}

	return nil
}

// BindDevices 批量绑定设备到用户脚本
func (s *UserScriptDeviceBindingService) BindDevices(ctx context.Context, userScriptID uint, deviceIDs []uint64) (map[uint64]string, error) {
	// 检查用户脚本是否存在
	var userScript model.UserScript
	if err := s.db.WithContext(ctx).Where("id = ?", userScriptID).First(&userScript).Error; err != nil {
		return nil, response.NewError(response.ERROR, "用户脚本不存在")
	}

	// 检查当前绑定数量
	var currentBindingCount int64
	if err := s.db.WithContext(ctx).
		Model(&model.UserScriptDeviceBinding{}).
		Where("user_script_id = ? AND status = ?", userScriptID, model.UserScriptDeviceBindingStatusActive).
		Count(&currentBindingCount).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询绑定数量失败")
	}

	// 检查是否超过最大绑定数限制
	if userScript.MaxDevices > 0 {
		// 过滤掉已绑定的设备ID
		var existingDeviceIDs []uint64
		if err := s.db.WithContext(ctx).
			Model(&model.UserScriptDeviceBinding{}).
			Where("user_script_id = ? AND device_id IN ? AND status = ?", userScriptID, deviceIDs, model.UserScriptDeviceBindingStatusActive).
			Pluck("device_id", &existingDeviceIDs).Error; err != nil {
			return nil, response.NewError(response.ERROR, "查询已绑定设备失败")
		}

		// 计算新增绑定数量
		newBindingCount := int64(len(deviceIDs) - len(existingDeviceIDs))
		if currentBindingCount+newBindingCount > int64(userScript.MaxDevices) {
			return nil, response.NewError(response.ERROR, "设备绑定数量将超过上限")
		}
	}

	// 检查设备是否存在
	var deviceCount int64
	if err := s.db.WithContext(ctx).
		Model(&model.Device{}).
		Where("id IN ?", deviceIDs).
		Count(&deviceCount).Error; err != nil {
		return nil, response.NewError(response.ERROR, "验证设备失败")
	}

	if int(deviceCount) != len(deviceIDs) {
		return nil, response.NewError(response.ERROR, "部分设备不存在")
	}

	// 使用事务执行批量绑定
	results := make(map[uint64]string)
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, deviceID := range deviceIDs {
			// 检查是否已存在绑定关系
			var existingBinding model.UserScriptDeviceBinding
			err := tx.Where("user_script_id = ? AND device_id = ?", userScriptID, deviceID).
				First(&existingBinding).Error

			if err == nil {
				// 绑定关系已存在
				results[deviceID] = "已绑定"
				continue
			}

			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}

			// 创建新的绑定关系
			binding := &model.UserScriptDeviceBinding{
				UserScriptID: userScriptID,
				DeviceID:     deviceID,
				Status:       model.UserScriptDeviceBindingStatusActive,
			}

			if err := tx.Create(binding).Error; err != nil {
				return err
			}

			results[deviceID] = "绑定成功"
		}
		return nil
	})

	if err != nil {
		return nil, response.NewError(response.ERROR, "批量绑定失败")
	}

	return results, nil
}

// UnbindDevices 批量解绑设备
func (s *UserScriptDeviceBindingService) UnbindDevices(ctx context.Context, userScriptID uint, deviceIDs []uint64) error {
	// 检查用户脚本是否存在
	var userScript model.UserScript
	if err := s.db.WithContext(ctx).Where("id = ?", userScriptID).First(&userScript).Error; err != nil {
		return response.NewError(response.ERROR, "用户脚本不存在")
	}

	// 使用事务执行批量解绑
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, deviceID := range deviceIDs {
			// 直接删除绑定关系，有则解绑没有则不管
			if err := tx.Where("user_script_id = ? AND device_id = ?", userScriptID, deviceID).
				Delete(&model.UserScriptDeviceBinding{}).Error; err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return response.NewError(response.ERROR, "批量解绑失败")
	}

	return nil
}
