package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"goadmin/config"
	"goadmin/pkg/redis"
)

// RedisService Redis服务管理器
type RedisService struct {
	client *redis.RedisClient
	config *config.Config
}

// NewRedisService 创建Redis服务
func NewRedisService(cfg *config.Config) (*RedisService, error) {
	client := redis.NewRedisClient(
		cfg.Redis.Addr,
		cfg.Redis.Password,
		cfg.Redis.DB,
	)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := client.Ping(ctx); err != nil {
		return nil, fmt.Errorf("Redis连接失败: %v (地址: %s)", err, cfg.Redis.Addr)
	}

	log.Println("Redis连接成功")

	return &RedisService{
		client: client,
		config: cfg,
	}, nil
}

// GetClient 获取Redis客户端
func (s *RedisService) GetClient() *redis.RedisClient {
	return s.client
}

// Close 关闭Redis连接
func (s *RedisService) Close() error {
	return s.client.Close()
}

// 设备状态管理方法

// SetDeviceOnline 设置设备在线
func (s *RedisService) SetDeviceOnline(ctx context.Context, userID uint, deviceID string) error {
	return s.client.SetDeviceOnline(ctx, userID, deviceID)
}

// SetDeviceOffline 设置设备离线
func (s *RedisService) SetDeviceOffline(ctx context.Context, userID uint, deviceID string) error {
	return s.client.SetDeviceOffline(ctx, userID, deviceID)
}

// IsDeviceOnline 检查设备是否在线
func (s *RedisService) IsDeviceOnline(ctx context.Context, userID uint, deviceID string) (bool, error) {
	return s.client.IsDeviceOnline(ctx, userID, deviceID)
}

// GetOnlineDevices 获取用户在线设备
func (s *RedisService) GetOnlineDevices(ctx context.Context, userID uint) (map[string]int64, error) {
	return s.client.GetOnlineDevices(ctx, userID)
}

// UpdateDeviceHeartbeat 更新设备心跳
func (s *RedisService) UpdateDeviceHeartbeat(ctx context.Context, userID uint, deviceID string) error {
	return s.client.UpdateDeviceHeartbeat(ctx, userID, deviceID)
}

// GetDeviceHeartbeat 获取设备心跳时间
func (s *RedisService) GetDeviceHeartbeat(ctx context.Context, userID uint, deviceID string) (int64, error) {
	return s.client.GetDeviceHeartbeat(ctx, userID, deviceID)
}

// SetDeviceConnection 设置设备连接信息
func (s *RedisService) SetDeviceConnection(ctx context.Context, userID uint, deviceID, connectionID string) error {
	return s.client.SetDeviceConnection(ctx, userID, deviceID, connectionID)
}

// RemoveDeviceConnection 移除设备连接信息
func (s *RedisService) RemoveDeviceConnection(ctx context.Context, userID uint, deviceID string) error {
	return s.client.RemoveDeviceConnection(ctx, userID, deviceID)
}

// UpdateUserConnectionCount 更新用户连接统计
func (s *RedisService) UpdateUserConnectionCount(ctx context.Context, userID uint, deviceCount, uniappCount int) error {
	return s.client.UpdateUserConnectionCount(ctx, userID, deviceCount, uniappCount)
}

// GetUserConnectionCount 获取用户连接统计
func (s *RedisService) GetUserConnectionCount(ctx context.Context, userID uint) (map[string]int, error) {
	return s.client.GetUserConnectionCount(ctx, userID)
}

// SetDeviceInfo 缓存设备信息
func (s *RedisService) SetDeviceInfo(ctx context.Context, deviceID string, info map[string]interface{}) error {
	return s.client.SetDeviceInfo(ctx, deviceID, info)
}

// GetDeviceInfo 获取设备信息
func (s *RedisService) GetDeviceInfo(ctx context.Context, deviceID string) (map[string]string, error) {
	return s.client.GetDeviceInfo(ctx, deviceID)
}

// CleanupExpiredConnections 清理过期连接
func (s *RedisService) CleanupExpiredConnections(ctx context.Context, timeoutSeconds int64) error {
	return s.client.CleanupExpiredConnections(ctx, timeoutSeconds)
}

// BatchUpdateDeviceStatus 批量更新设备状态
func (s *RedisService) BatchUpdateDeviceStatus(ctx context.Context, userID uint) (map[string]int, error) {
	return s.client.BatchUpdateDeviceStatus(ctx, userID)
}

// 脚本市场缓存相关方法

// SetMarketScriptList 缓存脚本市场列表
func (s *RedisService) SetMarketScriptList(ctx context.Context, key string, data interface{}, expiration time.Duration) error {
	return s.client.SetMarketScriptList(ctx, key, data, expiration)
}

// GetMarketScriptList 获取缓存的脚本市场列表
func (s *RedisService) GetMarketScriptList(ctx context.Context, key string) (string, error) {
	return s.client.GetMarketScriptList(ctx, key)
}

// SetMarketScriptDetail 缓存脚本详情
func (s *RedisService) SetMarketScriptDetail(ctx context.Context, scriptID uint, data interface{}, expiration time.Duration) error {
	return s.client.SetMarketScriptDetail(ctx, scriptID, data, expiration)
}

// GetMarketScriptDetail 获取缓存的脚本详情
func (s *RedisService) GetMarketScriptDetail(ctx context.Context, scriptID uint) (string, error) {
	return s.client.GetMarketScriptDetail(ctx, scriptID)
}

// DeleteMarketScriptCache 删除脚本相关缓存
func (s *RedisService) DeleteMarketScriptCache(ctx context.Context, scriptID uint) error {
	return s.client.DeleteMarketScriptCache(ctx, scriptID)
}

// DeleteAllMarketScriptCache 删除所有脚本市场缓存
func (s *RedisService) DeleteAllMarketScriptCache(ctx context.Context) error {
	return s.client.DeleteAllMarketScriptCache(ctx)
}
