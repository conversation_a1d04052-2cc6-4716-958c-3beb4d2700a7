package service

import (
	"context"
	"encoding/json"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// CardConfigService 卡密配置服务
type CardConfigService struct {
	db *gorm.DB
}

// NewCardConfigService 创建卡密配置服务
func NewCardConfigService(db *gorm.DB) *CardConfigService {
	return &CardConfigService{
		db: db,
	}
}

// UploadConfig 上传配置
func (s *CardConfigService) UploadConfig(ctx context.Context, cardKey string, config interface{}) (*model.CardConfig, error) {
	// 验证卡密是否存在
	var cardKeyModel model.CardKey
	if err := s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&cardKeyModel).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.CARD_NOT_FOUND, "卡密不存在")
		}
		return nil, response.NewError(response.ERROR, "查询卡密失败")
	}

	// 检查卡密状态
	if cardKeyModel.Status != 1 {
		return nil, response.NewError(response.CARD_DISABLED, "卡密已被禁用")
	}

	// 序列化配置
	configJSON, err := json.Marshal(config)
	if err != nil {
		return nil, response.NewError(response.ERROR, "配置序列化失败")
	}

	// 查找现有配置
	var cardConfig model.CardConfig
	err = s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&cardConfig).Error
	
	if err == gorm.ErrRecordNotFound {
		// 创建新配置
		cardConfig = model.CardConfig{
			CardKey: cardKey,
			Config:  string(configJSON),
			Version: 1,
		}
		if err := s.db.WithContext(ctx).Create(&cardConfig).Error; err != nil {
			return nil, response.NewError(response.ERROR, "创建配置失败")
		}
	} else if err != nil {
		return nil, response.NewError(response.ERROR, "查询配置失败")
	} else {
		// 更新现有配置
		cardConfig.Config = string(configJSON)
		cardConfig.Version++
		if err := s.db.WithContext(ctx).Save(&cardConfig).Error; err != nil {
			return nil, response.NewError(response.ERROR, "更新配置失败")
		}
	}

	return &cardConfig, nil
}

// GetConfig 获取配置
func (s *CardConfigService) GetConfig(ctx context.Context, cardKey string) (*model.CardConfig, error) {
	// 验证卡密是否存在
	var cardKeyModel model.CardKey
	if err := s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&cardKeyModel).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.CARD_NOT_FOUND, "卡密不存在")
		}
		return nil, response.NewError(response.ERROR, "查询卡密失败")
	}

	// 检查卡密状态
	if cardKeyModel.Status != 1 {
		return nil, response.NewError(response.CARD_DISABLED, "卡密已被禁用")
	}

	// 查找配置
	var cardConfig model.CardConfig
	err := s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&cardConfig).Error
	
	if err == gorm.ErrRecordNotFound {
		return nil, response.NewError(response.ERROR, "配置不存在")
	} else if err != nil {
		return nil, response.NewError(response.ERROR, "查询配置失败")
	}

	return &cardConfig, nil
}

// SyncConfig 同步配置（检查版本）
func (s *CardConfigService) SyncConfig(ctx context.Context, cardKey string, clientVersion int64) (*model.CardConfig, bool, error) {
	// 验证卡密是否存在
	var cardKeyModel model.CardKey
	if err := s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&cardKeyModel).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, false, response.NewError(response.CARD_NOT_FOUND, "卡密不存在")
		}
		return nil, false, response.NewError(response.ERROR, "查询卡密失败")
	}

	// 检查卡密状态
	if cardKeyModel.Status != 1 {
		return nil, false, response.NewError(response.CARD_DISABLED, "卡密已被禁用")
	}

	// 查找配置
	var cardConfig model.CardConfig
	err := s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&cardConfig).Error
	
	if err == gorm.ErrRecordNotFound {
		return nil, false, response.NewError(response.ERROR, "配置不存在")
	} else if err != nil {
		return nil, false, response.NewError(response.ERROR, "查询配置失败")
	}

	// 检查版本是否需要更新
	needUpdate := cardConfig.Version > clientVersion
	
	return &cardConfig, needUpdate, nil
}

// DeleteConfig 删除配置
func (s *CardConfigService) DeleteConfig(ctx context.Context, cardKey string) error {
	result := s.db.WithContext(ctx).Where("card_key = ?", cardKey).Delete(&model.CardConfig{})
	if result.Error != nil {
		return response.NewError(response.ERROR, "删除配置失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "配置不存在")
	}
	
	return nil
}

// ListConfigs 获取配置列表（管理员用）
func (s *CardConfigService) ListConfigs(ctx context.Context, page, pageSize int) ([]*model.CardConfig, int64, error) {
	var total int64
	var configs []*model.CardConfig

	// 计算总数
	if err := s.db.WithContext(ctx).Model(&model.CardConfig{}).Count(&total).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询配置总数失败")
	}

	// 查询配置列表
	offset := (page - 1) * pageSize
	if err := s.db.WithContext(ctx).
		Order("updated_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&configs).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询配置列表失败")
	}

	return configs, total, nil
}
