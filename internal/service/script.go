package service

import (
	"context"
	"encoding/json"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// ScriptService 脚本服务
type ScriptService struct {
	db *gorm.DB
}

// NewScriptService 创建脚本服务
func NewScriptService(db *gorm.DB) *ScriptService {
	return &ScriptService{
		db: db,
	}
}

// CreateScript 创建脚本
func (s *ScriptService) CreateScript(ctx context.Context, name, description, content, scriptType string, config map[string]interface{}, ownerID uint) (*model.Script, error) {
	// 序列化配置
	configJSON, err := json.Marshal(config)
	if err != nil {
		return nil, response.NewError(response.ERROR, "配置序列化失败")
	}
	
	script := &model.Script{
		Name:        name,
		Description: description,
		Content:     content,
		Config:      string(configJSON),
		Type:        scriptType,
		Status:      1, // 启用
		OwnerID:     ownerID,
	}
	
	if err := s.db.WithContext(ctx).Create(script).Error; err != nil {
		return nil, response.NewError(response.ERROR, "创建脚本失败")
	}
	
	return script, nil
}

// GetScriptByID 根据ID获取脚本
func (s *ScriptService) GetScriptByID(ctx context.Context, id uint) (*model.Script, error) {
	var script model.Script
	if err := s.db.WithContext(ctx).Preload("Owner").First(&script, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "脚本不存在")
		}
		return nil, response.NewError(response.ERROR, "查询脚本失败")
	}
	return &script, nil
}

// GetScriptList 获取脚本列表
func (s *ScriptService) GetScriptList(ctx context.Context, page, pageSize int, ownerID *uint, scriptType, keyword string, status *int8) ([]*model.Script, int64, error) {
	var scripts []*model.Script
	var total int64
	
	query := s.db.WithContext(ctx).Model(&model.Script{}).Preload("Owner")
	
	if ownerID != nil {
		query = query.Where("owner_id = ?", *ownerID)
	}
	
	if scriptType != "" {
		query = query.Where("type = ?", scriptType)
	}
	
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	
	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	
	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询脚本总数失败")
	}
	
	// 查询脚本列表
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&scripts).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询脚本列表失败")
	}
	
	return scripts, total, nil
}

// UpdateScript 更新脚本
func (s *ScriptService) UpdateScript(ctx context.Context, id uint, name, description, content string, config map[string]interface{}) error {
	updates := make(map[string]interface{})
	
	if name != "" {
		updates["name"] = name
	}
	if description != "" {
		updates["description"] = description
	}
	if content != "" {
		updates["content"] = content
	}
	if config != nil {
		configJSON, err := json.Marshal(config)
		if err != nil {
			return response.NewError(response.ERROR, "配置序列化失败")
		}
		updates["config"] = string(configJSON)
	}
	
	if len(updates) > 0 {
		result := s.db.WithContext(ctx).Model(&model.Script{}).Where("id = ?", id).Updates(updates)
		if result.Error != nil {
			return response.NewError(response.ERROR, "更新脚本失败")
		}
		
		if result.RowsAffected == 0 {
			return response.NewError(response.ERROR, "脚本不存在")
		}
	}
	
	return nil
}

// UpdateScriptStatus 更新脚本状态
func (s *ScriptService) UpdateScriptStatus(ctx context.Context, id uint, status int8) error {
	result := s.db.WithContext(ctx).Model(&model.Script{}).Where("id = ?", id).Update("status", status)
	if result.Error != nil {
		return response.NewError(response.ERROR, "更新脚本状态失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "脚本不存在")
	}
	
	return nil
}

// DeleteScript 删除脚本
func (s *ScriptService) DeleteScript(ctx context.Context, id uint) error {
	// 检查是否有关联的任务
	var taskCount int64
	if err := s.db.WithContext(ctx).Model(&model.Task{}).Where("script_id = ?", id).Count(&taskCount).Error; err != nil {
		return response.NewError(response.ERROR, "检查关联任务失败")
	}
	
	if taskCount > 0 {
		return response.NewError(response.ERROR, "脚本存在关联任务，无法删除")
	}
	
	result := s.db.WithContext(ctx).Delete(&model.Script{}, id)
	if result.Error != nil {
		return response.NewError(response.ERROR, "删除脚本失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "脚本不存在")
	}
	
	return nil
}

// GetUserScripts 获取用户的脚本列表
func (s *ScriptService) GetUserScripts(ctx context.Context, userID uint) ([]*model.Script, error) {
	var scripts []*model.Script
	if err := s.db.WithContext(ctx).Where("owner_id = ? AND status = ?", userID, 1).Order("created_at DESC").Find(&scripts).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询用户脚本失败")
	}
	return scripts, nil
}

// GetScriptConfig 获取脚本配置
func (s *ScriptService) GetScriptConfig(ctx context.Context, id uint) (map[string]interface{}, error) {
	var script model.Script
	if err := s.db.WithContext(ctx).Select("config").First(&script, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "脚本不存在")
		}
		return nil, response.NewError(response.ERROR, "查询脚本失败")
	}
	
	var config map[string]interface{}
	if script.Config != "" {
		if err := json.Unmarshal([]byte(script.Config), &config); err != nil {
			return nil, response.NewError(response.ERROR, "解析脚本配置失败")
		}
	}
	
	return config, nil
}

// UpdateScriptConfig 更新脚本配置
func (s *ScriptService) UpdateScriptConfig(ctx context.Context, id uint, config map[string]interface{}) error {
	configJSON, err := json.Marshal(config)
	if err != nil {
		return response.NewError(response.ERROR, "配置序列化失败")
	}
	
	result := s.db.WithContext(ctx).Model(&model.Script{}).Where("id = ?", id).Update("config", string(configJSON))
	if result.Error != nil {
		return response.NewError(response.ERROR, "更新脚本配置失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "脚本不存在")
	}
	
	return nil
}

// GetScriptsByType 根据类型获取脚本列表
func (s *ScriptService) GetScriptsByType(ctx context.Context, scriptType string) ([]*model.Script, error) {
	var scripts []*model.Script
	if err := s.db.WithContext(ctx).Where("type = ? AND status = ?", scriptType, 1).Order("created_at DESC").Find(&scripts).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询脚本列表失败")
	}
	return scripts, nil
}

// CloneScript 克隆脚本
func (s *ScriptService) CloneScript(ctx context.Context, id uint, newName string, ownerID uint) (*model.Script, error) {
	// 获取原脚本
	var originalScript model.Script
	if err := s.db.WithContext(ctx).First(&originalScript, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "原脚本不存在")
		}
		return nil, response.NewError(response.ERROR, "查询原脚本失败")
	}
	
	// 创建新脚本
	newScript := &model.Script{
		Name:        newName,
		Description: originalScript.Description + " (克隆)",
		Content:     originalScript.Content,
		Config:      originalScript.Config,
		Type:        originalScript.Type,
		Status:      1,
		OwnerID:     ownerID,
	}
	
	if err := s.db.WithContext(ctx).Create(newScript).Error; err != nil {
		return nil, response.NewError(response.ERROR, "克隆脚本失败")
	}
	
	return newScript, nil
}
