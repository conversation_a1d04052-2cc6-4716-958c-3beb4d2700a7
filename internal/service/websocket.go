package service

import (
	"context"
	"encoding/json"
	"fmt"
	"goadmin/config"
	"goadmin/internal/constant"
	"goadmin/internal/model"
	"net"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"goadmin/pkg/logger"

	"github.com/gorilla/websocket"
)

// 安全配置常量
const (
	// 分别设置不同类型客户端的连接数限制
	MaxDeviceConnectionsPerUser = 200 // 每个用户最大设备连接数
	MaxUniAppConnectionsPerUser = 10  // 每个用户最大UniApp连接数（支持多端登录）

	// IP限流（极限防护）- 防止大规模攻击
	IPRateLimitWindow      = 60  // IP限流窗口（秒）
	MaxIPAttemptsPerWindow = 100 // 每个IP每窗口最大尝试次数（宽松）

	// 设备限流（精细控制）- 防止单设备异常
	DeviceRateLimitWindow      = 60 // 设备限流窗口（秒）
	MaxDeviceAttemptsPerWindow = 5  // 每台设备每窗口最大尝试次数（严格）

	HeartbeatTimeout = 45   // 心跳超时时间（秒）
	MaxMessageSize   = 1024 // 最大消息大小（字节）
)

// 客户端类型
const (
	ClientTypeDevice = constant.ClientTypeDevice // AutoX.js 设备客户端
	ClientTypeUniApp = constant.ClientTypeUniApp // UniApp 控制端客户端
)

// 速率限制结构
type RateLimiter struct {
	attempts map[string][]time.Time
	mu       sync.RWMutex
}

// NewRateLimiter 创建速率限制器
func NewRateLimiter() *RateLimiter {
	return &RateLimiter{
		attempts: make(map[string][]time.Time),
	}
}

// IsAllowed 检查是否允许连接（通用方法）
func (rl *RateLimiter) IsAllowed(key string, windowSeconds int, maxAttempts int) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()
	windowStart := now.Add(-time.Duration(windowSeconds) * time.Second)

	// 清理过期的尝试记录
	if attempts, exists := rl.attempts[key]; exists {
		var validAttempts []time.Time
		for _, attempt := range attempts {
			if attempt.After(windowStart) {
				validAttempts = append(validAttempts, attempt)
			}
		}
		rl.attempts[key] = validAttempts
	}

	// 检查尝试次数
	if len(rl.attempts[key]) >= maxAttempts {
		return false
	}

	// 记录本次尝试
	rl.attempts[key] = append(rl.attempts[key], now)
	return true
}

// IsIPAllowed 检查IP是否允许连接（极限防护）
func (rl *RateLimiter) IsIPAllowed(ip string) bool {
	return rl.IsAllowed(ip, IPRateLimitWindow, MaxIPAttemptsPerWindow)
}

// IsDeviceAllowed 检查设备是否允许连接（精细控制）
func (rl *RateLimiter) IsDeviceAllowed(deviceID string) bool {
	return rl.IsAllowed(deviceID, DeviceRateLimitWindow, MaxDeviceAttemptsPerWindow)
}

// GetAttemptsCount 获取当前尝试次数（用于调试）
func (rl *RateLimiter) GetAttemptsCount(key string, windowSeconds int) int {
	rl.mu.RLock()
	defer rl.mu.RUnlock()

	now := time.Now()
	windowStart := now.Add(-time.Duration(windowSeconds) * time.Second)

	if attempts, exists := rl.attempts[key]; exists {
		count := 0
		for _, attempt := range attempts {
			if attempt.After(windowStart) {
				count++
			}
		}
		return count
	}
	return 0
}

// Hub 管理所有WebSocket连接
type Hub struct {
	// 客户端连接映射
	clients    map[string]*Client
	register   chan *Client
	unregister chan *Client
	// 任务分发通道
	taskChan chan *TaskMessage
	// 状态更新通道
	statusChan chan *StatusUpdate
	// 互斥锁保护clients map
	mu sync.RWMutex
	// 速率限制器
	rateLimiter *RateLimiter
	// 连接统计 - 分别统计不同类型的连接
	userDeviceConnections map[uint]int // 用户设备连接数统计
	userUniAppConnections map[uint]int // 用户UniApp连接数统计
	// 服务引用
	deviceService     *DeviceService
	userScriptService *UserScriptService
	redisService      *RedisService
	jwtService        *JWTService // 新增JWT服务引用
}

// safeCloseSend 安全关闭客户端的Send channel
func (c *Client) safeCloseSend() {
	c.closeOnce.Do(func() {
		close(c.Send)
		c.Connected = false
	})
}

// StatusUpdate 设备状态更新
type StatusUpdate struct {
	DeviceID string `json:"device_id"`
	Status   int    `json:"status"` // 0: 离线, 1: 在线
	UserID   uint   `json:"user_id"`
	Time     int64  `json:"time"`
}

// Client WebSocket客户端
type Client struct {
	Hub        *Hub
	Conn       *websocket.Conn
	Send       chan []byte
	DeviceID   string
	Token      string
	UserID     uint   // 添加用户ID
	ClientType string // 客户端类型：device/uniapp
	// 客户端信息
	DeviceInfo *DeviceInfo
	// 连接状态
	Connected bool
	// 最后心跳时间
	LastHeartbeat time.Time
	// 设备服务引用
	deviceService *DeviceService
	// 防重复心跳
	lastHeartbeatSeq string
	// 防止重复关闭channel的锁
	closeOnce sync.Once
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	DeviceID       string    `json:"device_id"`
	DeviceName     string    `json:"device_name"`
	AndroidVersion string    `json:"android_version"`
	ScreenWidth    int       `json:"screen_width"`
	ScreenHeight   int       `json:"screen_height"`
	AppVersion     string    `json:"app_version"`
	LastActive     time.Time `json:"last_active"`
}

// TaskMessage 任务消息
type TaskMessage struct {
	Type      string      `json:"type"`
	Seq       string      `json:"seq"`
	Timestamp int64       `json:"timestamp"`
	Payload   interface{} `json:"payload"`
}

// TaskPayload 任务载荷
type TaskPayload struct {
	TaskID   string                 `json:"task_id"`
	ScriptID string                 `json:"script_id"`
	Action   string                 `json:"action"` // run, stop, config
	Params   map[string]interface{} `json:"params"`
}

// TaskResult 任务结果
type TaskResult struct {
	TaskID    string                 `json:"task_id"`
	Status    string                 `json:"status"` // success, failed, running
	Message   string                 `json:"message"`
	Data      map[string]interface{} `json:"data"`
	Timestamp int64                  `json:"timestamp"`
}

// RegisterRequest 设备注册请求结构体
type RegisterRequest struct {
	DeviceID       string `json:"device_id"`
	DeviceModel    string `json:"device_model"`
	AndroidVersion string `json:"android_version"`
	AutoxVersion   string `json:"autox_version"`
}

// NewHub 创建Hub
func NewHub(deviceService *DeviceService, userScriptService *UserScriptService, redisService *RedisService, jwtService *JWTService) *Hub {
	return &Hub{
		clients:               make(map[string]*Client),
		register:              make(chan *Client),
		unregister:            make(chan *Client),
		taskChan:              make(chan *TaskMessage, 100),
		statusChan:            make(chan *StatusUpdate, 100),
		rateLimiter:           NewRateLimiter(),
		userDeviceConnections: make(map[uint]int),
		userUniAppConnections: make(map[uint]int),
		deviceService:         deviceService,
		userScriptService:     userScriptService,
		redisService:          redisService,
		jwtService:            jwtService,
	}
}

// Run Hub主循环
func (h *Hub) Run() {
	// 启动心跳超时检查协程
	go h.heartbeatChecker()

	for {
		select {
		case client := <-h.register:
			h.mu.Lock()
			// 检查设备连接数限制
			if existingClient, exists := h.clients[client.DeviceID]; exists {
				// 对于设备类型，断开旧连接（同设备只能有一个连接）
				if client.ClientType == ClientTypeDevice {
					logger.Warn(context.TODO(), "设备 %s 已有连接，断开旧连接", client.DeviceID)
					existingClient.Conn.Close()
					// 减少对应类型的连接数
					if existingClient.ClientType == ClientTypeDevice {
						h.userDeviceConnections[existingClient.UserID]--
					} else if existingClient.ClientType == ClientTypeUniApp {
						h.userUniAppConnections[existingClient.UserID]--
					}
				} else if client.ClientType == ClientTypeUniApp {
					// 对于UniApp类型，允许多端登录，不断开旧连接
					logger.Warn(context.TODO(), "UniApp客户端 %s 已有连接，允许多端登录", client.DeviceID)
					// 不减少连接数，因为要支持多端
				}
			}

			// 检查用户连接数限制（分别检查设备连接数和UniApp连接数）
			var currentConnections int
			var maxConnections int
			if client.ClientType == ClientTypeDevice {
				currentConnections = h.userDeviceConnections[client.UserID]
				maxConnections = MaxDeviceConnectionsPerUser
			} else if client.ClientType == ClientTypeUniApp {
				currentConnections = h.userUniAppConnections[client.UserID]
				maxConnections = MaxUniAppConnectionsPerUser
			}

			if currentConnections >= maxConnections {
				logger.Warn(context.TODO(), "用户 %d %s连接数超限 (%d/%d)，拒绝连接", client.UserID, client.ClientType, currentConnections, maxConnections)
				client.Conn.Close()
				h.mu.Unlock()
				continue
			}

			h.clients[client.DeviceID] = client
			// 增加对应类型的连接数
			if client.ClientType == ClientTypeDevice {
				h.userDeviceConnections[client.UserID]++
			} else if client.ClientType == ClientTypeUniApp {
				h.userUniAppConnections[client.UserID]++
			}
			client.Connected = true
			client.LastHeartbeat = time.Now() // 初始化心跳时间
			h.mu.Unlock()

		case client := <-h.unregister:
			h.mu.Lock()
			if _, ok := h.clients[client.DeviceID]; ok {
				delete(h.clients, client.DeviceID)
				// 减少对应类型的连接数
				if client.ClientType == ClientTypeDevice {
					h.userDeviceConnections[client.UserID]--
				} else if client.ClientType == ClientTypeUniApp {
					h.userUniAppConnections[client.UserID]--
				}
				client.safeCloseSend()
			}
			h.mu.Unlock()

			// 只有设备类型的客户端才更新设备状态
			if client.ClientType == ClientTypeDevice && h.deviceService != nil {
				ctx := context.Background()

				// 检查Redis服务是否可用
				if h.redisService != nil {
					// 更新Redis中的设备状态为离线
					if err := h.redisService.SetDeviceOffline(ctx, client.UserID, client.DeviceID); err != nil {
						logger.Warn(context.TODO(), "更新设备 %s Redis离线状态失败: %v", client.DeviceID, err)
					}
				}

				// 更新数据库中的设备状态
				err := h.deviceService.UpdateDeviceStatus(ctx, client.DeviceID, 0)
				if err != nil {
					logger.Warn(context.TODO(), "更新设备 %s 数据库离线状态失败: %v", client.DeviceID, err)
				} else {
					logger.Debug(context.TODO(), "设备 %s (用户 %d) 状态已更新为离线", client.DeviceID, client.UserID)

					// 通知 UniApp 客户端设备状态变化
					h.statusChan <- &StatusUpdate{
						DeviceID: client.DeviceID,
						Status:   0, // 离线
						UserID:   client.UserID,
						Time:     time.Now().UnixMilli(),
					}
				}
			}

		case task := <-h.taskChan:
			h.handleTask(task)
		case statusUpdate := <-h.statusChan:
			h.broadcastStatusUpdate(statusUpdate)
		}
	}
}

// heartbeatChecker 心跳超时检查器
func (h *Hub) heartbeatChecker() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for range ticker.C {
		h.checkHeartbeatTimeouts()
	}
}

// checkHeartbeatTimeouts 检查心跳超时
func (h *Hub) checkHeartbeatTimeouts() {
	h.mu.Lock()
	defer h.mu.Unlock()

	now := time.Now()
	timeoutThreshold := now.Add(-time.Duration(HeartbeatTimeout) * time.Second)

	var timeoutClients []*Client

	// 检查所有客户端的心跳超时
	for deviceID, client := range h.clients {
		if client.LastHeartbeat.Before(timeoutThreshold) {
			logger.Warn(context.TODO(), "设备 %s 心跳超时，最后心跳时间: %s, 超时阈值: %s",
				deviceID,
				client.LastHeartbeat.Format("15:04:05"),
				timeoutThreshold.Format("15:04:05"))
			timeoutClients = append(timeoutClients, client)
		}
	}

	// 断开超时的客户端
	for _, client := range timeoutClients {
		logger.Warn(context.TODO(), "断开心跳超时的客户端 %s (用户 %d, 类型: %s)", client.DeviceID, client.UserID, client.ClientType)
		client.safeCloseSend()
		delete(h.clients, client.DeviceID)

		// 减少对应类型的连接数
		if client.ClientType == ClientTypeDevice {
			h.userDeviceConnections[client.UserID]--
		} else if client.ClientType == ClientTypeUniApp {
			h.userUniAppConnections[client.UserID]--
		}
	}
}

// handleTask 处理任务
func (h *Hub) handleTask(task *TaskMessage) {
	switch task.Type {
	case "SCRIPT_RUN", "SCRIPT_STOP":
		h.distributeScriptTask(task)
	case "CONFIG_UPDATE":
		h.distributeConfigTask(task)
	default:
		logger.Warn(context.TODO(), "未知任务类型: %s", task.Type)
	}
}

// distributeScriptTask 分发脚本任务
func (h *Hub) distributeScriptTask(task *TaskMessage) {
	payload, ok := task.Payload.(map[string]interface{})
	if !ok {
		logger.Warn(context.TODO(), "任务载荷格式错误")
		return
	}

	deviceIDs, ok := payload["device_ids"].([]string)
	if !ok {
		logger.Warn(context.TODO(), "设备ID列表格式错误")
		return
	}

	h.mu.RLock()
	defer h.mu.RUnlock()

	for _, deviceID := range deviceIDs {
		if client, exists := h.clients[deviceID]; exists {
			// 检查客户端是否在线
			if !client.Connected {
				logger.Warn(context.TODO(), "设备 %s 不在线，跳过发送", deviceID)
				continue
			}

			// 检查心跳超时
			if time.Since(client.LastHeartbeat) > HeartbeatTimeout*time.Second {
				logger.Warn(context.TODO(), "设备 %s 心跳超时，跳过发送", deviceID)
				continue
			}

			// 发送任务
			message, err := json.Marshal(task)
			if err != nil {
				logger.Warn(context.TODO(), "序列化任务消息失败: %v", err)
				continue
			}

			select {
			case client.Send <- message:
				logger.Debug(context.TODO(), "任务已发送到设备: %s", deviceID)
			default:
				logger.Warn(context.TODO(), "设备 %s 发送队列已满", deviceID)
			}
		} else {
			logger.Warn(context.TODO(), "设备 %s 不在线", deviceID)
		}
	}
}

// distributeConfigTask 分发配置任务
func (h *Hub) distributeConfigTask(task *TaskMessage) {
	// 实现配置更新任务分发
	logger.Debug(context.TODO(), "分发配置更新任务")
}

// GetOnlineDevices 获取在线设备列表
func (h *Hub) GetOnlineDevices() []*DeviceInfo {
	h.mu.RLock()
	defer h.mu.RUnlock()

	var devices []*DeviceInfo
	for _, client := range h.clients {
		if client.Connected && time.Since(client.LastHeartbeat) <= HeartbeatTimeout*time.Second {
			devices = append(devices, client.DeviceInfo)
		}
	}
	return devices
}

// SendTaskToDevice 发送任务到指定设备
func (h *Hub) SendTaskToDevice(deviceID string, task *TaskMessage) error {
	h.mu.RLock()
	client, exists := h.clients[deviceID]
	h.mu.RUnlock()

	if !exists {
		return fmt.Errorf("设备 %s 不在线", deviceID)
	}

	if !client.Connected {
		return fmt.Errorf("设备 %s 连接已断开", deviceID)
	}

	// 检查心跳超时
	if time.Since(client.LastHeartbeat) > HeartbeatTimeout*time.Second {
		return fmt.Errorf("设备 %s 心跳超时", deviceID)
	}

	// 序列化任务
	message, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("序列化任务失败: %v", err)
	}

	// 检查客户端是否仍然连接
	if !client.Connected {
		return fmt.Errorf("设备 %s 连接已断开", deviceID)
	}

	// 发送任务
	select {
	case client.Send <- message:
		return nil
	default:
		return fmt.Errorf("设备 %s 发送队列已满", deviceID)
	}
}

// BroadcastTask 广播任务
func (h *Hub) BroadcastTask(task *TaskMessage) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	message, err := json.Marshal(task)
	if err != nil {
		logger.Error(context.TODO(), "序列化广播消息失败: %v", err)
		return
	}

	for deviceID, client := range h.clients {
		if client.Connected && time.Since(client.LastHeartbeat) <= HeartbeatTimeout*time.Second {
			select {
			case client.Send <- message:
				logger.Debug(context.TODO(), "广播消息已发送到设备: %s", deviceID)
			default:
				logger.Warn(context.TODO(), "设备 %s 发送队列已满，跳过广播", deviceID)
			}
		} else {
			logger.Warn(context.TODO(), "设备 %s 连接已断开或心跳超时，跳过广播", deviceID)
		}
	}
}

// broadcastStatusUpdate 广播设备状态更新到所有 UniApp 客户端
func (h *Hub) broadcastStatusUpdate(statusUpdate *StatusUpdate) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	logger.Debug(context.TODO(), "开始广播状态更新: 设备 %s, 状态 %d, 用户 %d", statusUpdate.DeviceID, statusUpdate.Status, statusUpdate.UserID)

	// 统计不同类型的客户端数量
	deviceCount, uniappCount := h.getConnectionStats()
	logger.Debug(context.TODO(), "当前连接统计: 设备端 %d 个, UniApp端 %d 个, 总计 %d 个", deviceCount, uniappCount, len(h.clients))

	// 构建状态更新消息
	statusMessage := &TaskMessage{
		Type:      "DEVICE_STATUS_UPDATE",
		Seq:       generateUUID(),
		Timestamp: time.Now().UnixMilli(),
		Payload:   statusUpdate,
	}

	statusBytes, err := json.Marshal(statusMessage)
	if err != nil {
		logger.Error(context.TODO(), "序列化状态更新消息失败: %v", err)
		return
	}

	// 只发送给 UniApp 客户端
	var sentCount int
	for clientID, client := range h.clients {
		logger.Debug(context.TODO(), "检查客户端 %s: 类型=%s, 用户ID=%d", clientID, client.ClientType, client.UserID)
		if client.ClientType == ClientTypeUniApp && client.UserID == statusUpdate.UserID {
			// 检查客户端是否仍然连接
			if !client.Connected {
				logger.Warn(context.TODO(), "UniApp 客户端 %s 已断开连接，跳过状态更新", clientID)
				continue
			}

			select {
			case client.Send <- statusBytes:
				logger.Debug(context.TODO(), "发送设备状态更新到 UniApp 客户端 %s: 设备 %s 状态 %d", clientID, statusUpdate.DeviceID, statusUpdate.Status)
				sentCount++
			default:
				logger.Warn(context.TODO(), "UniApp 客户端 %s 发送通道已满，跳过状态更新", clientID)
			}
		}
	}

	logger.Debug(context.TODO(), "状态更新广播完成: 发送给 %d 个 UniApp 客户端", sentCount)
}

// readPump 处理WebSocket读取
func (c *Client) readPump() {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
	}()

	// 设置读取限制
	c.Conn.SetReadLimit(MaxMessageSize)
	// 设置合理的读取超时，给客户端足够时间发送心跳
	// 如果客户端心跳间隔是30秒，我们设置60秒超时
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// 设置连接保活
	if tcpConn, ok := c.Conn.UnderlyingConn().(*net.TCPConn); ok {
		tcpConn.SetKeepAlive(true)
		tcpConn.SetKeepAlivePeriod(30 * time.Second)
		tcpConn.SetLinger(0)
	}

	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			logger.Warn(context.TODO(), "客户端 %s 读取消息失败: %v", c.DeviceID, err)
			break
		}

		// 更新心跳时间
		c.LastHeartbeat = time.Now()

		// 处理消息
		c.handleMessage(message)
	}
}

// writePump 处理WebSocket写入
func (c *Client) writePump() {
	ticker := time.NewTicker(HeartbeatTimeout * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				logger.Warn(context.TODO(), "客户端 %s 发送通道已关闭，准备断开连接", c.DeviceID)
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				logger.Warn(context.TODO(), "客户端 %s 写入消息失败: %v", c.DeviceID, err)
				return
			}
			w.Write(message)

			if err := w.Close(); err != nil {
				logger.Warn(context.TODO(), "客户端 %s 关闭写入器失败: %v", c.DeviceID, err)
				return
			}
		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				logger.Warn(context.TODO(), "客户端 %s 发送Ping失败: %v", c.DeviceID, err)
				return
			}
		}
	}
}

// handleMessage 处理客户端消息
func (c *Client) handleMessage(message []byte) {
	var msg TaskMessage
	if err := json.Unmarshal(message, &msg); err != nil {
		logger.Warn(context.TODO(), "解析客户端消息失败: %v", err)
		return
	}

	// 更新心跳时间
	c.LastHeartbeat = time.Now()

	switch msg.Type {
	case "REGISTER_REQ":
		c.handleRegisterRequest(msg)
	case "HEARTBEAT":
		c.handleHeartbeat(msg)
	case "TASK_RESULT":
		c.handleTaskResult(msg)
	case "DEVICE_INFO":
		c.handleDeviceInfo(msg)
	case "SCRIPT_STOP":
		c.handleScriptStop(msg)
	case "SCRIPT_RUN":
		c.handleScriptRun(msg)
	default:
		logger.Warn(context.TODO(), "未知消息类型: %s", msg.Type)
	}
}

// handleRegisterRequest 处理注册请求
func (c *Client) handleRegisterRequest(msg TaskMessage) {
	// 解析注册请求结构体
	var registerReq RegisterRequest
	payloadBytes, err := json.Marshal(msg.Payload)
	if err != nil {
		logger.Warn(context.TODO(), "序列化注册请求payload失败: %v", err)
		return
	}

	if err := json.Unmarshal(payloadBytes, &registerReq); err != nil {
		logger.Warn(context.TODO(), "解析注册请求失败: %v", err)
		return
	}

	// 先查询设备信息，获取设备名称
	var deviceInfo model.Device
	if err := c.deviceService.db.WithContext(context.Background()).
		Where("device_id = ?", c.DeviceID).
		First(&deviceInfo).Error; err != nil {
		logger.Warn(context.TODO(), "查询设备 %s 信息失败: %v", c.DeviceID, err)
		return
	}

	// 直接更新设备信息
	now := time.Now()
	device := &model.Device{
		DeviceModel:    registerReq.DeviceModel,
		AndroidVersion: registerReq.AndroidVersion,
		AutoxVersion:   registerReq.AutoxVersion,
		Status:         constant.DeviceStatusOnline, // 设置为在线状态
		LastActive:     &now,
	}

	// 直接更新设备信息
	if err := c.deviceService.db.WithContext(context.Background()).
		Model(&model.Device{}).
		Where("device_id = ?", c.DeviceID).
		Updates(device).Error; err != nil {
		logger.Warn(context.TODO(), "更新设备 %s 信息失败: %v", c.DeviceID, err)
	} else {
		logger.Debug(context.TODO(), "设备 %s 注册信息已更新: 型号=%s, 安卓=%s, AutoX=%s",
			c.DeviceID, registerReq.DeviceModel, registerReq.AndroidVersion, registerReq.AutoxVersion)
	}

	// 设备注册成功时，广播状态更新给UniApp客户端
	if c.ClientType == ClientTypeDevice {
		// 广播设备状态更新
		c.Hub.statusChan <- &StatusUpdate{
			DeviceID: c.DeviceID,
			Status:   constant.DeviceStatusOnline, // 在线
			UserID:   c.UserID,
			Time:     time.Now().UnixMilli(),
		}
		logger.Debug(context.TODO(), "设备 %s 注册成功，已广播状态更新", c.DeviceID)
	}

	// 发送注册响应，包含设备名称
	response := TaskMessage{
		Type:      "REGISTER_RESP",
		Seq:       msg.Seq,
		Timestamp: time.Now().UnixMilli(),
		Payload: map[string]interface{}{
			"success":     true,
			"message":     "设备注册成功",
			"device_name": deviceInfo.Name, // 返回设备名称
		},
	}

	responseBytes, err := json.Marshal(response)
	if err != nil {
		logger.Warn(context.TODO(), "序列化注册响应失败: %v", err)
		return
	}

	// 检查客户端是否仍然连接
	if !c.Connected {
		logger.Warn(context.TODO(), "客户端 %s 已断开连接，跳过注册响应", c.DeviceID)
		return
	}

	select {
	case c.Send <- responseBytes:
		logger.Debug(context.TODO(), "设备 %s 注册成功，返回设备名称: %s", c.DeviceID, deviceInfo.Name)
	default:
		logger.Warn(context.TODO(), "设备 %s 发送队列已满，无法发送注册响应", c.DeviceID)
	}
}

// handleHeartbeat 处理心跳
func (c *Client) handleHeartbeat(msg TaskMessage) {
	// 检查是否是重复的心跳包
	if c.lastHeartbeatSeq == msg.Seq {
		return
	}

	// 记录心跳seq，防止重复处理
	c.lastHeartbeatSeq = msg.Seq

	// 更新最后心跳时间
	c.LastHeartbeat = time.Now()

	// 只有设备类型的客户端才更新设备状态
	if c.ClientType == ClientTypeDevice {
		// 检查Redis服务是否可用
		if c.Hub.redisService != nil {
			// 使用Redis更新设备心跳和状态
			ctx := context.Background()

			// 更新Redis中的心跳时间
			if err := c.Hub.redisService.UpdateDeviceHeartbeat(ctx, c.UserID, c.DeviceID); err != nil {
				logger.Warn(context.TODO(), "更新设备 %s 心跳失败: %v", c.DeviceID, err)
			}

			// 设置设备为在线状态
			if err := c.Hub.redisService.SetDeviceOnline(ctx, c.UserID, c.DeviceID); err != nil {
				logger.Warn(context.TODO(), "设置设备 %s 在线状态失败: %v", c.DeviceID, err)
			} else {
				// 设备心跳时，广播状态更新给UniApp客户端
				c.Hub.statusChan <- &StatusUpdate{
					DeviceID: c.DeviceID,
					Status:   constant.DeviceStatusOnline, // 在线
					UserID:   c.UserID,
					Time:     time.Now().UnixMilli(),
				}
			}
		} else {
			// Redis不可用时，直接更新数据库
			ctx := context.Background()
			if err := c.deviceService.UpdateDeviceStatus(ctx, c.DeviceID, 1); err != nil {
				logger.Warn(context.TODO(), "Redis不可用，更新设备 %s 数据库状态失败: %v", c.DeviceID, err)
			} else {
				// 设备心跳时，广播状态更新给UniApp客户端
				c.Hub.statusChan <- &StatusUpdate{
					DeviceID: c.DeviceID,
					Status:   constant.DeviceStatusOnline, // 在线
					UserID:   c.UserID,
					Time:     time.Now().UnixMilli(),
				}
			}
		}
	}

	// 发送心跳响应
	response := TaskMessage{
		Type:      "HEARTBEAT_RESP",
		Seq:       msg.Seq,
		Timestamp: time.Now().UnixMilli(),
		Payload: map[string]interface{}{
			"success":     true,
			"server_time": time.Now().UnixMilli(),
		},
	}

	responseBytes, err := json.Marshal(response)
	if err != nil {
		logger.Warn(context.TODO(), "序列化心跳响应失败: %v", err)
		return
	}

	// 检查客户端是否仍然连接
	if !c.Connected {
		logger.Warn(context.TODO(), "客户端 %s 已断开连接，跳过心跳响应", c.DeviceID)
		return
	}

	select {
	case c.Send <- responseBytes:
	default:
		logger.Warn(context.TODO(), "客户端 %s 发送队列已满，无法发送心跳响应", c.DeviceID)
	}
}

// handleTaskResult 处理任务结果
func (c *Client) handleTaskResult(msg TaskMessage) {
	// 记录任务结果
	logger.Debug(context.TODO(), "收到设备 %s 的任务结果: %+v", c.DeviceID, msg.Payload)
}

// handleDeviceInfo 处理设备信息
func (c *Client) handleDeviceInfo(msg TaskMessage) {
	// 更新设备信息
	if payload, ok := msg.Payload.(map[string]interface{}); ok {
		if deviceName, ok := payload["device_name"].(string); ok {
			c.DeviceInfo.DeviceName = deviceName
		}
		if androidVersion, ok := payload["android_version"].(string); ok {
			c.DeviceInfo.AndroidVersion = androidVersion
		}
		if appVersion, ok := payload["app_version"].(string); ok {
			c.DeviceInfo.AppVersion = appVersion
		}
		c.DeviceInfo.LastActive = time.Now()
	}
}

// handleScriptStop 处理停止脚本消息
func (c *Client) handleScriptStop(msg TaskMessage) {
	logger.Debug(context.TODO(), "收到停止脚本消息: %s", c.DeviceID)

	// 解析payload
	payload, ok := msg.Payload.(map[string]interface{})
	if !ok {
		logger.Warn(context.TODO(), "停止脚本消息payload格式错误")
		return
	}

	// script_id 是可选的，用于停止特定脚本；如果没有则停止所有脚本
	scriptID, hasScriptID := payload["script_id"].(string)

	deviceIDs, ok := payload["device_ids"].([]interface{})
	if !ok {
		logger.Warn(context.TODO(), "停止脚本消息缺少device_ids")
		return
	}

	action, _ := payload["action"].(string)

	// 如果是UniApp客户端发送的停止消息，转发给对应的设备
	if c.ClientType == ClientTypeUniApp {
		// 将interface{}切片转换为string切片
		var deviceIDStrings []string
		for _, deviceID := range deviceIDs {
			if deviceIDStr, ok := deviceID.(string); ok {
				deviceIDStrings = append(deviceIDStrings, deviceIDStr)
			}
		}

		// 为每个设备发送停止脚本任务
		for _, deviceID := range deviceIDStrings {
			// 创建停止任务消息
			stopTaskPayload := map[string]interface{}{
				"action": "stop",
				"params": map[string]interface{}{
					"action": action,
				},
			}

			// 如果有script_id，则添加到payload中
			if hasScriptID {
				stopTaskPayload["script_id"] = scriptID
			}

			stopTask := &TaskMessage{
				Type:      "SCRIPT_STOP",
				Seq:       msg.Seq,
				Timestamp: msg.Timestamp,
				Payload:   stopTaskPayload,
			}

			// 发送给指定设备
			if err := c.Hub.SendTaskToDevice(deviceID, stopTask); err != nil {
				logger.Warn(context.TODO(), "发送停止命令到设备 %s 失败: %v", deviceID, err)
			} else {
				logger.Debug(context.TODO(), "停止命令已发送到设备: %s", deviceID)
			}
		}
	}
}

// handleScriptRun 处理运行脚本消息
func (c *Client) handleScriptRun(msg TaskMessage) {
	logger.Debug(context.TODO(), "收到运行脚本消息: %s", c.DeviceID)

	// 解析payload
	payload, ok := msg.Payload.(map[string]interface{})
	if !ok {
		logger.Warn(context.TODO(), "运行脚本消息payload格式错误")
		return
	}

	scriptID, ok := payload["script_id"].(string)
	if !ok {
		logger.Warn(context.TODO(), "运行脚本消息缺少script_id")
		return
	}

	deviceIDs, ok := payload["device_ids"].([]interface{})
	if !ok {
		logger.Warn(context.TODO(), "运行脚本消息缺少device_ids")
		return
	}

	// 如果是UniApp客户端发送的运行消息，需要获取脚本数据并转发给设备
	if c.ClientType == ClientTypeUniApp {
		logger.Debug(context.TODO(), "UniApp客户端 %s 请求运行脚本 %s 到设备 %v", c.DeviceID, scriptID, deviceIDs)

		// 将interface{}切片转换为string切片
		var deviceIDStrings []string
		for _, deviceID := range deviceIDs {
			if deviceIDStr, ok := deviceID.(string); ok {
				deviceIDStrings = append(deviceIDStrings, deviceIDStr)
			}
		}

		// 将scriptID转换为uint
		scriptIDUint, err := strconv.ParseUint(scriptID, 10, 32)
		if err != nil {
			logger.Warn(context.TODO(), "脚本ID格式错误: %s", scriptID)
			return
		}

		// 获取脚本运行数据（与HTTP版本保持一致）
		runData, err := c.Hub.userScriptService.GetScriptRunData(context.Background(), uint(scriptIDUint), c.UserID)
		if err != nil {
			logger.Warn(context.TODO(), "获取脚本运行数据失败: %v", err)
			return
		}

		// 为每个设备发送运行脚本任务
		for _, deviceID := range deviceIDStrings {
			// 查询设备信息获取设备名称
			device, err := c.Hub.deviceService.GetDeviceByID(context.Background(), deviceID)
			deviceName := ""
			if err == nil {
				deviceName = device.Name
			}

			// 生成脚本专用的JWT token
			scriptJWTToken, err := c.Hub.jwtService.GenerateAccessToken(
				strconv.Itoa(int(c.UserID)),
				deviceID,
				"script", // 标记为脚本专用token
			)
			if err != nil {
				logger.Warn(context.TODO(), "生成脚本JWT token失败: %v", err)
				scriptJWTToken = "" // 如果生成失败，使用空字符串
			}

			// 包装配置信息，添加更多必要的参数
			configWrapper := map[string]interface{}{
				"env":         runData.Config, // 原有的配置内容
				"device_name": deviceName,     // 从数据库查询的设备名称
				"device_id":   deviceID,       // 设备ID
				"script_name": runData.ScriptName,
				"script_id":   runData.ScriptID,
				"jwt_token":   scriptJWTToken, // 脚本专用的JWT token
				"user_id":     c.UserID,
			}

			// 构建完整的脚本参数（与HTTP版本保持一致）
			params := map[string]interface{}{
				"script_id":      runData.ScriptID,
				"script_name":    runData.ScriptName,
				"script_content": runData.ScriptContent,
				"version":        runData.Version,
				"config_name":    runData.ConfigName,
				"config":         configWrapper, // 使用包装后的配置
			}

			// 创建运行脚本任务消息
			runTask := &TaskMessage{
				Type:      "SCRIPT_RUN",
				Seq:       msg.Seq,
				Timestamp: msg.Timestamp,
				Payload: map[string]interface{}{
					"script_id": fmt.Sprintf("%d", runData.ScriptID),
					"action":    "run",
					"params":    params,
				},
			}

			// 发送给指定设备
			if err := c.Hub.SendTaskToDevice(deviceID, runTask); err != nil {
				logger.Warn(context.TODO(), "发送运行命令到设备 %s 失败: %v", deviceID, err)
			} else {
				logger.Debug(context.TODO(), "运行命令已发送到设备: %s", deviceID)
			}
		}
	}
}

// WebSocketService WebSocket服务
type WebSocketService struct {
	hub           *Hub
	deviceService *DeviceService
	jwtService    *JWTService
	redisService  *RedisService
	config        *config.Config
}

// NewWebSocketService 创建WebSocket服务
func NewWebSocketService(deviceService *DeviceService, userScriptService *UserScriptService, jwtService *JWTService, redisService *RedisService, config *config.Config) *WebSocketService {
	hub := NewHub(deviceService, userScriptService, redisService, jwtService)
	go hub.Run()
	return &WebSocketService{
		hub:           hub,
		deviceService: deviceService,
		jwtService:    jwtService,
		redisService:  redisService,
		config:        config,
	}
}

// ServeWebSocket 处理WebSocket连接
func (s *WebSocketService) ServeWebSocket(w http.ResponseWriter, r *http.Request) {
	// 1. IP速率限制检查（极限防护）
	clientIP := getClientIP(r)
	if !s.hub.rateLimiter.IsIPAllowed(clientIP) {
		ipAttempts := s.hub.rateLimiter.GetAttemptsCount(clientIP, IPRateLimitWindow)
		logger.Warn(context.TODO(), "客户端 %s 连接频率过高 (IP限流): %d/%d", clientIP, ipAttempts, MaxIPAttemptsPerWindow)
		s.sendWSError(w, constant.WS_ERROR_RETRY, fmt.Sprintf("IP限流: %d/%d", ipAttempts, MaxIPAttemptsPerWindow))
		return
	}

	// 2. Origin检查（生产环境）
	if !isValidOrigin(r, s.config.WebSocket.AllowedOrigins) {
		logger.Warn(context.TODO(), "无效的Origin: %s", r.Header.Get("Origin"))
		s.sendWSError(w, constant.WS_ERROR_NO_RETRY, fmt.Sprintf("无效的Origin: %s", r.Header.Get("Origin")))
		return
	}

	// 3. 获取连接参数
	token := r.URL.Query().Get("token")

	if token == "" {
		logger.Warn(context.TODO(), "缺少认证参数: token=%s", maskToken(token))
		s.sendWSError(w, constant.WS_ERROR_NO_RETRY, "缺少token参数")
		return
	}

	// 4. 统一 JWT 认证，从 token 中提取所有信息
	claims, err := s.jwtService.ParseToken(token)
	if err != nil {
		logger.Warn(context.TODO(), "JWT token 验证失败: token=%s, error=%v", maskToken(token), err)
		// 判断是否是token过期
		if strings.Contains(err.Error(), "expired") {
			s.sendWSError(w, constant.WS_ERROR_RETRY, "token已过期")
		} else {
			s.sendWSError(w, constant.WS_ERROR_NO_RETRY, "token无效")
		}
		return
	}

	// 从 claims 中获取用户ID
	userIDStr, ok := claims["user_id"].(string)
	if !ok {
		logger.Warn(context.TODO(), "JWT token 中缺少 user_id: token=%s", maskToken(token))
		s.sendWSError(w, constant.WS_ERROR_NO_RETRY, "token中缺少user_id")
		return
	}

	var userID uint
	_, err = fmt.Sscanf(userIDStr, "%d", &userID)
	if err != nil {
		logger.Warn(context.TODO(), "无效的用户ID格式: %v", err)
		s.sendWSError(w, constant.WS_ERROR_NO_RETRY, "无效的用户ID格式")
		return
	}

	// 验证用户是否存在
	var user model.User
	if err := s.deviceService.db.Where("id = ?", userID).First(&user).Error; err != nil {
		logger.Warn(context.TODO(), "用户不存在: user_id=%d, error=%v", userID, err)
		s.sendWSError(w, constant.WS_ERROR_NO_RETRY, fmt.Sprintf("用户不存在: %d", userID))
		return
	}

	// 从 claims 中获取设备ID和客户端类型
	deviceID, ok := claims["device_id"].(string)
	if !ok {
		logger.Warn(context.TODO(), "JWT token 中缺少 device_id: token=%s", maskToken(token))
		s.sendWSError(w, constant.WS_ERROR_NO_RETRY, "token中缺少device_id")
		return
	}

	clientTypeStr, ok := claims["client_type"].(string)
	if !ok {
		logger.Warn(context.TODO(), "JWT token 中缺少 client_type: token=%s", maskToken(token))
		s.sendWSError(w, constant.WS_ERROR_NO_RETRY, "token中缺少client_type")
		return
	}

	// 根据客户端类型进行额外验证
	if clientTypeStr == ClientTypeDevice {
		// AutoX.js 设备客户端：验证设备是否存在且属于该用户
		var device model.Device
		if err := s.deviceService.db.Where("device_id = ? AND user_id = ?", deviceID, userID).First(&device).Error; err != nil {
			logger.Warn(context.TODO(), "设备不存在或无权限访问: device_id=%s, user_id=%d, error=%v", deviceID, userID, err)
			s.sendWSError(w, constant.WS_ERROR_NO_RETRY, fmt.Sprintf("设备不存在或无权限访问: %s", deviceID))
			return
		}

		// 5. 设备速率限制检查（精细控制）
		if !s.hub.rateLimiter.IsDeviceAllowed(deviceID) {
			deviceAttempts := s.hub.rateLimiter.GetAttemptsCount(deviceID, DeviceRateLimitWindow)
			logger.Warn(context.TODO(), "设备 %s 连接频率过高 (设备限流): %d/%d", deviceID, deviceAttempts, MaxDeviceAttemptsPerWindow)
			s.sendWSError(w, constant.WS_ERROR_RETRY, fmt.Sprintf("设备限流: %d/%d", deviceAttempts, MaxDeviceAttemptsPerWindow))
			return
		}
	} else if clientTypeStr == ClientTypeUniApp {
		// UniApp 客户端：验证设备ID格式
		// 支持多种格式：uniapp_123, uniapp_h5_xxx, uniapp_app_xxx, uniapp_mp_xxx
		if !strings.HasPrefix(deviceID, "uniapp_") {
			logger.Warn(context.TODO(), "UniApp 设备ID格式错误: %s", deviceID)
			s.sendWSError(w, constant.WS_ERROR_NO_RETRY, fmt.Sprintf("UniApp设备ID格式错误: %s", deviceID))
			return
		}
	} else {
		logger.Warn(context.TODO(), "无效的客户端类型: %s", clientTypeStr)
		s.sendWSError(w, constant.WS_ERROR_NO_RETRY, fmt.Sprintf("无效的客户端类型: %s", clientTypeStr))
		return
	}

	// 6. 升级HTTP连接为WebSocket
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return isValidOrigin(r, s.config.WebSocket.AllowedOrigins)
		},
		EnableCompression: true,
	}

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		logger.Warn(context.TODO(), "WebSocket升级失败: %v", err)
		return
	}

	// 7. 创建客户端
	client := &Client{
		Hub:  s.hub,
		Conn: conn,
		Send: make(chan []byte, 256),
		// 设置客户端信息
		DeviceID:   deviceID,
		Token:      token,
		UserID:     userID,
		ClientType: clientTypeStr,
		DeviceInfo: &DeviceInfo{
			DeviceID: deviceID,
		},
		Connected:        false,
		LastHeartbeat:    time.Now(),
		deviceService:    s.deviceService,
		lastHeartbeatSeq: "",
	}

	// 8. 注册客户端
	s.hub.register <- client

	// 9. 启动读写协程
	go client.writePump()
	go client.readPump()

	logger.Debug(context.TODO(), "WebSocket客户端已连接: %s (类型: %s, 用户: %d)", deviceID, clientTypeStr, userID)
}

// GetOnlineDevices 获取在线设备列表
func (s *WebSocketService) GetOnlineDevices() []*DeviceInfo {
	return s.hub.GetOnlineDevices()
}

// SendScriptTask 发送脚本任务到指定设备
func (s *WebSocketService) SendScriptTask(deviceIDs []string, scriptID string, action string, params map[string]interface{}) error {
	// 现在调用方已经为每个设备分别调用，这里只需要发送给指定的设备
	if len(deviceIDs) != 1 {
		return fmt.Errorf("SendScriptTask 现在只支持单个设备，请为每个设备分别调用")
	}

	deviceID := deviceIDs[0]

	// 生成任务消息
	task := &TaskMessage{
		Type:      "SCRIPT_" + strings.ToUpper(action),
		Seq:       generateUUID(),
		Timestamp: time.Now().UnixMilli(),
		Payload: map[string]interface{}{
			"script_id": scriptID,
			"action":    action,
			"params":    params,
		},
	}

	// 发送给指定设备
	if err := s.hub.SendTaskToDevice(deviceID, task); err != nil {
		logger.Warn(context.TODO(), "发送任务到设备 %s 失败: %v", deviceID, err)
		return fmt.Errorf("发送任务到设备 %s 失败: %v", deviceID, err)
	}

	logger.Debug(context.TODO(), "任务已发送到设备: %s", deviceID)
	return nil
}

// DisconnectDevice 断开指定设备的WebSocket连接
func (s *WebSocketService) DisconnectDevice(deviceID string) {
	s.hub.mu.Lock()
	defer s.hub.mu.Unlock()

	if client, exists := s.hub.clients[deviceID]; exists {
		logger.Debug(context.TODO(), "断开设备 %s 的WebSocket连接", deviceID)
		client.safeCloseSend()
		client.Conn.Close()
		delete(s.hub.clients, deviceID)

		// 更新连接统计
		if client.ClientType == ClientTypeDevice {
			if s.hub.userDeviceConnections[client.UserID] > 0 {
				s.hub.userDeviceConnections[client.UserID]--
			}
		}
	}
}

// generateUUID 生成UUID（简化版本）
func generateUUID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// getClientIP 获取客户端IP
func getClientIP(r *http.Request) string {
	// 优先从X-Forwarded-For获取
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		return strings.Split(xff, ",")[0]
	}
	// 从X-Real-IP获取
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}
	// 从RemoteAddr获取
	return strings.Split(r.RemoteAddr, ":")[0]
}

// isValidOrigin 检查Origin是否有效
func isValidOrigin(r *http.Request, allowedOrigins []string) bool {
	origin := r.Header.Get("Origin")
	if origin == "" {
		return true // 允许没有Origin的请求（如移动应用）
	}

	for _, allowed := range allowedOrigins {
		if origin == allowed {
			return true
		}
	}

	return false
}

// maskToken 脱敏token（用于日志）
func maskToken(token string) string {
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "***" + token[len(token)-4:]
}

// sendWSError 发送WebSocket错误响应
func (s *WebSocketService) sendWSError(w http.ResponseWriter, errorCode int, details string) {
	// 设置响应头
	w.Header().Set("Content-Type", "application/json")

	// 根据错误码设置HTTP状态码
	var statusCode int
	switch errorCode {
	case constant.WS_ERROR_RETRY: // 可重试错误
		statusCode = http.StatusInternalServerError
	case constant.WS_ERROR_NO_RETRY: // 不可重试错误
		statusCode = http.StatusUnauthorized
	default: // 默认错误
		statusCode = http.StatusInternalServerError
	}

	w.WriteHeader(statusCode)

	// 构造错误响应
	errorResponse := constant.GetWSErrorResponse(errorCode, details)

	// 序列化并发送错误响应
	responseJSON, err := json.Marshal(errorResponse)
	if err != nil {
		logger.Warn(context.TODO(), "序列化错误响应失败: %v", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	w.Write(responseJSON)
}

// getConnectionStats 获取连接统计信息
func (h *Hub) getConnectionStats() (deviceCount, uniappCount int) {
	for _, client := range h.clients {
		if client.ClientType == ClientTypeDevice {
			deviceCount++
		} else if client.ClientType == ClientTypeUniApp {
			uniappCount++
		}
	}
	return deviceCount, uniappCount
}
