package service

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// TaskService 任务服务
type TaskService struct {
	db *gorm.DB
}

// NewTaskService 创建任务服务
func NewTaskService(db *gorm.DB) *TaskService {
	return &TaskService{
		db: db,
	}
}

// CreateTask 创建任务
func (s *TaskService) CreateTask(ctx context.Context, name, description, taskType string, scriptID uint, targetDevices []uint, config map[string]interface{}, createdBy uint) (*model.Task, error) {
	// 序列化目标设备列表
	targetDevicesJSON, err := json.Marshal(targetDevices)
	if err != nil {
		return nil, response.NewError(response.ERROR, "目标设备序列化失败")
	}
	
	// 序列化配置
	configJSON, err := json.Marshal(config)
	if err != nil {
		return nil, response.NewError(response.ERROR, "配置序列化失败")
	}
	
	task := &model.Task{
		Name:          name,
		Description:   description,
		Type:          taskType,
		Status:        0, // 待执行
		TargetDevices: string(targetDevicesJSON),
		ScriptID:      scriptID,
		Config:        string(configJSON),
		Progress:      0,
		CreatedBy:     createdBy,
	}
	
	if err := s.db.WithContext(ctx).Create(task).Error; err != nil {
		return nil, response.NewError(response.ERROR, "创建任务失败")
	}
	
	// 为每个目标设备创建任务结果记录
	for _, deviceID := range targetDevices {
		taskResult := &model.TaskResult{
			TaskID:   task.ID,
			DeviceID: deviceID,
			Status:   0, // 待执行
		}
		s.db.WithContext(ctx).Create(taskResult)
	}
	
	return task, nil
}

// GetTaskByID 根据ID获取任务
func (s *TaskService) GetTaskByID(ctx context.Context, id uint) (*model.Task, error) {
	var task model.Task
	if err := s.db.WithContext(ctx).Preload("Script").Preload("Creator").First(&task, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "任务不存在")
		}
		return nil, response.NewError(response.ERROR, "查询任务失败")
	}
	return &task, nil
}

// GetTaskList 获取任务列表
func (s *TaskService) GetTaskList(ctx context.Context, page, pageSize int, createdBy *uint, status *int8, keyword string) ([]*model.Task, int64, error) {
	var tasks []*model.Task
	var total int64
	
	query := s.db.WithContext(ctx).Model(&model.Task{}).Preload("Script").Preload("Creator")
	
	if createdBy != nil {
		query = query.Where("created_by = ?", *createdBy)
	}
	
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	
	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	
	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询任务总数失败")
	}
	
	// 查询任务列表
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&tasks).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询任务列表失败")
	}
	
	return tasks, total, nil
}

// StartTask 启动任务
func (s *TaskService) StartTask(ctx context.Context, id uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":     1, // 执行中
		"started_at": now,
	}
	
	result := s.db.WithContext(ctx).Model(&model.Task{}).Where("id = ? AND status = ?", id, 0).Updates(updates)
	if result.Error != nil {
		return response.NewError(response.ERROR, "启动任务失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "任务不存在或状态不正确")
	}
	
	// 更新任务结果状态
	s.db.WithContext(ctx).Model(&model.TaskResult{}).Where("task_id = ?", id).Update("status", 1)
	
	return nil
}

// CompleteTask 完成任务
func (s *TaskService) CompleteTask(ctx context.Context, id uint, status int8) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":       status,
		"completed_at": now,
		"progress":     100,
	}
	
	result := s.db.WithContext(ctx).Model(&model.Task{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return response.NewError(response.ERROR, "完成任务失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "任务不存在")
	}
	
	return nil
}

// CancelTask 取消任务
func (s *TaskService) CancelTask(ctx context.Context, id uint) error {
	result := s.db.WithContext(ctx).Model(&model.Task{}).Where("id = ? AND status IN (?)", id, []int{0, 1}).Update("status", 4)
	if result.Error != nil {
		return response.NewError(response.ERROR, "取消任务失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "任务不存在或无法取消")
	}
	
	// 取消相关的任务结果
	s.db.WithContext(ctx).Model(&model.TaskResult{}).Where("task_id = ? AND status IN (?)", id, []int{0, 1}).Update("status", 3)
	
	return nil
}

// UpdateTaskProgress 更新任务进度
func (s *TaskService) UpdateTaskProgress(ctx context.Context, id uint, progress int) error {
	result := s.db.WithContext(ctx).Model(&model.Task{}).Where("id = ?", id).Update("progress", progress)
	if result.Error != nil {
		return response.NewError(response.ERROR, "更新任务进度失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "任务不存在")
	}
	
	return nil
}

// GetTaskResults 获取任务结果列表
func (s *TaskService) GetTaskResults(ctx context.Context, taskID uint) ([]*model.TaskResult, error) {
	var results []*model.TaskResult
	if err := s.db.WithContext(ctx).Preload("Device").Where("task_id = ?", taskID).Order("created_at ASC").Find(&results).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询任务结果失败")
	}
	return results, nil
}

// UpdateTaskResult 更新任务结果
func (s *TaskService) UpdateTaskResult(ctx context.Context, taskID, deviceID uint, status int8, output, errorMsg string) error {
	updates := map[string]interface{}{
		"status": status,
		"output": output,
		"error":  errorMsg,
	}
	
	now := time.Now()
	if status == 1 { // 执行中
		updates["started_at"] = now
	} else if status == 2 || status == 3 { // 成功或失败
		updates["ended_at"] = now
	}
	
	result := s.db.WithContext(ctx).Model(&model.TaskResult{}).
		Where("task_id = ? AND device_id = ?", taskID, deviceID).
		Updates(updates)
	
	if result.Error != nil {
		return response.NewError(response.ERROR, "更新任务结果失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "任务结果不存在")
	}
	
	// 检查任务是否完成
	s.checkTaskCompletion(ctx, taskID)
	
	return nil
}

// checkTaskCompletion 检查任务是否完成
func (s *TaskService) checkTaskCompletion(ctx context.Context, taskID uint) {
	var totalCount, completedCount int64
	
	// 统计总数和完成数
	s.db.WithContext(ctx).Model(&model.TaskResult{}).Where("task_id = ?", taskID).Count(&totalCount)
	s.db.WithContext(ctx).Model(&model.TaskResult{}).Where("task_id = ? AND status IN (?)", taskID, []int{2, 3}).Count(&completedCount)
	
	if totalCount > 0 && completedCount == totalCount {
		// 所有子任务都完成了，更新主任务状态
		var successCount int64
		s.db.WithContext(ctx).Model(&model.TaskResult{}).Where("task_id = ? AND status = ?", taskID, 2).Count(&successCount)
		
		var taskStatus int8 = 2 // 成功
		if successCount == 0 {
			taskStatus = 3 // 失败
		} else if successCount < totalCount {
			taskStatus = 2 // 部分成功，仍标记为成功
		}
		
		s.CompleteTask(ctx, taskID, taskStatus)
	}
}

// DeleteTask 删除任务
func (s *TaskService) DeleteTask(ctx context.Context, id uint) error {
	// 删除任务结果
	s.db.WithContext(ctx).Where("task_id = ?", id).Delete(&model.TaskResult{})
	
	// 删除任务
	result := s.db.WithContext(ctx).Delete(&model.Task{}, id)
	if result.Error != nil {
		return response.NewError(response.ERROR, "删除任务失败")
	}
	
	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "任务不存在")
	}
	
	return nil
}

// GetUserTasks 获取用户的任务列表
func (s *TaskService) GetUserTasks(ctx context.Context, userID uint, limit int) ([]*model.Task, error) {
	var tasks []*model.Task
	query := s.db.WithContext(ctx).Preload("Script").Where("created_by = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if err := query.Find(&tasks).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询用户任务失败")
	}
	
	return tasks, nil
}

// GetTaskStatistics 获取任务统计信息
func (s *TaskService) GetTaskStatistics(ctx context.Context, userID *uint) (map[string]int64, error) {
	stats := make(map[string]int64)
	
	query := s.db.WithContext(ctx).Model(&model.Task{})
	if userID != nil {
		query = query.Where("created_by = ?", *userID)
	}
	
	// 统计各状态的任务数量
	statuses := []int8{0, 1, 2, 3, 4} // 待执行、执行中、已完成、失败、已取消
	statusNames := []string{"pending", "running", "completed", "failed", "cancelled"}
	
	for i, status := range statuses {
		var count int64
		if err := query.Where("status = ?", status).Count(&count).Error; err != nil {
			return nil, response.NewError(response.ERROR, "查询任务统计失败")
		}
		stats[statusNames[i]] = count
	}
	
	// 总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询任务总数失败")
	}
	stats["total"] = total
	
	return stats, nil
}
