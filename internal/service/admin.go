package service

import (
	"context"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

type AdminService struct {
	db *gorm.DB
}

func NewAdminService(db *gorm.DB) *AdminService {
	return &AdminService{db: db}
}

// Login 管理员登录
func (s *AdminService) Login(c context.Context, username, password string) (*model.Admin, error) {
	var admin model.Admin
	if err := s.db.Where("username = ?", username).First(&admin).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Info(c, "Login failed: user not found - username: %s", username)
			return nil, response.NewError(response.UNAUTHORIZED, "用户名或密码错误")
		}
		return nil, err
	}

	if admin.Status != 1 {
		logger.Info(c, "Login failed: account disabled - username: %s", username)
		return nil, response.NewError(response.UNAUTHORIZED, "账号已被禁用")
	}

	logger.Info(c, "Login attempt - username: %s, input_password: %s", username, password)
	if err := bcrypt.CompareHashAndPassword([]byte(admin.Password), []byte(password)); err != nil {
		logger.Info(c, "Login failed: password mismatch - username: %s, error: %v", username, err)
		return nil, response.NewError(response.UNAUTHORIZED, "用户名或密码错误")
	}

	now := time.Now()
	admin.LastLogin = &now
	if err := s.db.Model(&model.Admin{}).Where("id = ?", admin.ID).Update("last_login", now).Error; err != nil {
		logger.Error(c, "Failed to update last login time - error: %v", err)
	}

	return &admin, nil
}

// CreateAdmin 创建管理员
func (s *AdminService) CreateAdmin(c context.Context, admin *model.Admin) error {
	// 设置默认状态
	if admin.Status == 0 {
		admin.Status = 1
	}
	logger.Info(c, "Creating admin - username: %s, password: %s", admin.Username, admin.Password)
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(admin.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	admin.Password = string(hashedPassword)
	logger.Info(c, "Creating admin - username: %s", admin.Username)
	return s.db.Create(admin).Error
}

// UpdateAdmin 更新管理员信息
func (s *AdminService) UpdateAdmin(admin *model.Admin) error {
	// 获取现有管理员信息
	var existingAdmin model.Admin
	if err := s.db.First(&existingAdmin, admin.ID).Error; err != nil {
		return err
	}

	// 如果提供了新密码，则更新密码
	if admin.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(admin.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		existingAdmin.Password = string(hashedPassword)
	}

	// 更新数据库
	return s.db.Model(&existingAdmin).Updates(map[string]interface{}{
		"password": existingAdmin.Password,
	}).Error
}

// GetAdmin 获取管理员信息
func (s *AdminService) GetAdmin(id uint) (*model.Admin, error) {
	var admin model.Admin
	if err := s.db.First(&admin, id).Error; err != nil {
		return nil, err
	}
	return &admin, nil
}

// ListAdmins 获取管理员列表
func (s *AdminService) ListAdmins(page, pageSize int) ([]*model.Admin, int64, error) {
	var admins []*model.Admin
	var total int64

	s.db.Model(&model.Admin{}).Count(&total)
	if err := s.db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&admins).Error; err != nil {
		return nil, 0, err
	}

	return admins, total, nil
}

// DeleteAdmin 删除管理员
func (s *AdminService) DeleteAdmin(id uint) error {
	return s.db.Delete(&model.Admin{}, id).Error
}

// UpdateStatus 更新管理员状态
func (s *AdminService) UpdateStatus(id uint, status int8) error {
	return s.db.Model(&model.Admin{}).Where("id = ?", id).Update("status", status).Error
}
