package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

// MarketScriptService 脚本市场服务
type MarketScriptService struct {
	db           *gorm.DB
	redisService *RedisService
}

// NewMarketScriptService 创建脚本市场服务
func NewMarketScriptService(db *gorm.DB, redisService *RedisService) *MarketScriptService {
	return &MarketScriptService{
		db:           db,
		redisService: redisService,
	}
}

// GetMarketScriptList 获取脚本市场列表
func (s *MarketScriptService) GetMarketScriptList(ctx context.Context, page, pageSize int, keyword string, isFeatured *bool) ([]*model.MarketScript, int64, error) {
	// 生成缓存key
	cacheKey := fmt.Sprintf("market_script:list:page_%d_size_%d_keyword_%s_featured_%v",
		page, pageSize, keyword, isFeatured)

	// 尝试从Redis获取缓存
	if s.redisService != nil {
		cachedData, err := s.redisService.GetMarketScriptList(ctx, cacheKey)
		if err == nil {
			// 缓存命中，解析数据
			var result struct {
				Scripts []*model.MarketScript `json:"scripts"`
				Total   int64                 `json:"total"`
			}
			if err := json.Unmarshal([]byte(cachedData), &result); err == nil {
				logger.Info(ctx, "脚本市场列表缓存命中: %s", cacheKey)
				return result.Scripts, result.Total, nil
			}
		}
	}

	// 缓存未命中，从数据库查询
	var scripts []*model.MarketScript
	var total int64

	query := s.db.WithContext(ctx).Model(&model.MarketScript{}).Where("status = ?", 1)

	// 关键词搜索
	if keyword != "" {
		query = query.Where("name LIKE ? OR summary LIKE ? OR tags LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 推荐脚本筛选
	if isFeatured != nil {
		query = query.Where("is_featured = ?", *isFeatured)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询脚本总数失败")
	}

	// 查询脚本列表（只加载必要字段）
	offset := (page - 1) * pageSize
	fields := []string{"id", "name", "summary", "icon", "version", "tags", "author", "author_id", "downloads", "rating", "rating_count", "status", "is_featured", "created_at", "updated_at"}
	if err := query.Select(fields).Offset(offset).Limit(pageSize).Order("is_featured DESC, downloads DESC, rating DESC").Find(&scripts).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询脚本列表失败")
	}

	// 缓存到Redis（缓存30分钟）
	if s.redisService != nil {
		cacheData := struct {
			Scripts []*model.MarketScript `json:"scripts"`
			Total   int64                 `json:"total"`
		}{
			Scripts: scripts,
			Total:   total,
		}

		if jsonData, err := json.Marshal(cacheData); err == nil {
			s.redisService.SetMarketScriptList(ctx, cacheKey, jsonData, 30*time.Minute)
			logger.Info(ctx, "脚本市场列表已缓存: %s", cacheKey)
		}
	}

	return scripts, total, nil
}

// GetMarketScriptDetail 获取脚本详情
func (s *MarketScriptService) GetMarketScriptDetail(ctx context.Context, scriptID uint) (*model.MarketScript, error) {
	// 尝试从Redis获取缓存
	if s.redisService != nil {
		cachedData, err := s.redisService.GetMarketScriptDetail(ctx, scriptID)
		if err == nil {
			// 缓存命中，解析数据
			var script model.MarketScript
			if err := json.Unmarshal([]byte(cachedData), &script); err == nil {
				logger.Info(ctx, "脚本详情缓存命中: script_id=%d", scriptID)
				return &script, nil
			}
		}
	}

	// 缓存未命中，从数据库查询
	var script model.MarketScript

	if err := s.db.WithContext(ctx).Where("id = ? AND status = ?", scriptID, 1).First(&script).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "脚本不存在或已下架")
		}
		return nil, response.NewError(response.ERROR, "查询脚本详情失败")
	}

	// 缓存到Redis（缓存1小时）
	if s.redisService != nil {
		if jsonData, err := json.Marshal(script); err == nil {
			s.redisService.SetMarketScriptDetail(ctx, scriptID, jsonData, time.Hour)
			logger.Info(ctx, "脚本详情已缓存: script_id=%d", scriptID)
		}
	}

	return &script, nil
}

// InstallScript 安装脚本到用户账户（新版本，使用 UserScript 模型）
func (s *MarketScriptService) InstallScript(ctx context.Context, scriptID uint, userID uint, paidAt, expiredAt, completedAt time.Time, deviceLimit int, packageID uint, packageName string) error {
	// 检查脚本是否存在且上架
	var marketScript model.MarketScript
	if err := s.db.WithContext(ctx).Where("id = ? AND status = ?", scriptID, 1).First(&marketScript).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return response.NewError(response.ERROR, "脚本不存在或已下架")
		}
		return response.NewError(response.ERROR, "查询脚本失败")
	}

	// 检查是否已经安装过
	var existingScript model.UserScript
	if err := s.db.WithContext(ctx).Where("market_script_id = ? AND user_id = ? AND status = ?",
		scriptID, userID, model.UserScriptStatusEnabled).First(&existingScript).Error; err == nil {
		return response.NewError(response.ERROR, "脚本已安装")
	}

	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 设置最大设备数量，如果传入的值无效则使用默认值1
	maxDevices := deviceLimit
	if maxDevices <= 0 {
		maxDevices = 1
	}

	// 创建用户脚本记录
	userScript := &model.UserScript{
		UserID:         userID,
		MarketScriptID: scriptID,
		PackageID:      packageID,
		PackageName:    packageName,
		Name:           marketScript.Name,
		Content:        marketScript.Content,
		Config:         marketScript.Config,
		DefaultValues:  marketScript.DefaultValues,
		Version:        marketScript.Version,
		Status:         model.UserScriptStatusEnabled,
		MaxDevices:     maxDevices, // 从套餐信息设置最大设备数量
	}

	if !expiredAt.IsZero() {
		userScript.ExpiredAt = &expiredAt
	}

	if err := tx.Create(userScript).Error; err != nil {
		tx.Rollback()
		return response.NewError(response.ERROR, "创建用户脚本失败")
	}

	// 更新下载次数
	if err := tx.Model(&marketScript).Update("downloads", marketScript.Downloads+1).Error; err != nil {
		tx.Rollback()
		return response.NewError(response.ERROR, "更新下载次数失败")
	}

	if err := tx.Commit().Error; err != nil {
		return response.NewError(response.ERROR, "提交事务失败")
	}

	logger.Info(ctx, "脚本安装成功: script_id=%d, user_id=%d, max_devices=%d, paid_at=%v, expired_at=%v, completed_at=%v", scriptID, userID, maxDevices, paidAt, expiredAt, completedAt)
	return nil
}

// GetUserInstalledScripts 获取用户已安装的脚本
func (s *MarketScriptService) GetUserInstalledScripts(ctx context.Context, userID uint) ([]*model.MarketScript, error) {
	var scripts []*model.MarketScript

	query := `
		SELECT ms.* FROM market_scripts ms
		INNER JOIN user_scripts us ON ms.id = us.market_script_id
		WHERE us.user_id = ? AND us.status = ? AND ms.status = 1
		ORDER BY us.created_at DESC
	`

	if err := s.db.WithContext(ctx).Raw(query, userID, model.UserScriptStatusEnabled).Scan(&scripts).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询已安装脚本失败")
	}

	return scripts, nil
}

// RateScript 为脚本评分
func (s *MarketScriptService) RateScript(ctx context.Context, scriptID uint, userID uint, rating int8, comment string) error {
	// 验证评分范围
	if rating < 1 || rating > 5 {
		return response.NewError(response.ERROR, "评分必须在1-5之间")
	}

	// 检查脚本是否存在
	var marketScript model.MarketScript
	if err := s.db.WithContext(ctx).Where("id = ? AND status = ?", scriptID, 1).First(&marketScript).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return response.NewError(response.ERROR, "脚本不存在或已下架")
		}
		return response.NewError(response.ERROR, "查询脚本失败")
	}

	// 检查是否已安装过脚本（只有安装过的用户才能评分）
	var userScript model.UserScript
	if err := s.db.WithContext(ctx).Where("market_script_id = ? AND user_id = ? AND status = ?",
		scriptID, userID, model.UserScriptStatusEnabled).First(&userScript).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return response.NewError(response.ERROR, "只有安装过脚本的用户才能评分")
		}
		return response.NewError(response.ERROR, "查询脚本记录失败")
	}

	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查是否已经评分过
	var existingRating model.MarketScriptRating
	if err := tx.Where("market_script_id = ? AND user_id = ?", scriptID, userID).First(&existingRating).Error; err == nil {
		// 更新现有评分
		if err := tx.Model(&existingRating).Updates(map[string]interface{}{
			"rating":  rating,
			"comment": comment,
		}).Error; err != nil {
			tx.Rollback()
			return response.NewError(response.ERROR, "更新评分失败")
		}
	} else {
		// 创建新评分
		newRating := &model.MarketScriptRating{
			MarketScriptID: scriptID,
			UserID:         userID,
			Rating:         rating,
			Comment:        comment,
		}
		if err := tx.Create(newRating).Error; err != nil {
			tx.Rollback()
			return response.NewError(response.ERROR, "创建评分失败")
		}
	}

	// 重新计算脚本的平均评分
	var avgRating float64
	var ratingCount int64
	if err := tx.Model(&model.MarketScriptRating{}).Where("market_script_id = ?", scriptID).
		Select("AVG(rating) as avg_rating, COUNT(*) as rating_count").
		Scan(&struct {
			AvgRating   float64 `gorm:"column:avg_rating"`
			RatingCount int64   `gorm:"column:rating_count"`
		}{avgRating, ratingCount}).Error; err != nil {
		tx.Rollback()
		return response.NewError(response.ERROR, "计算平均评分失败")
	}

	// 更新脚本的评分信息
	if err := tx.Model(&marketScript).Updates(map[string]interface{}{
		"rating":       avgRating,
		"rating_count": ratingCount,
	}).Error; err != nil {
		tx.Rollback()
		return response.NewError(response.ERROR, "更新脚本评分失败")
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return response.NewError(response.ERROR, "提交事务失败")
	}

	// 清理相关缓存
	if s.redisService != nil {
		s.ClearScriptCache(ctx, scriptID)
	}

	logger.Info(ctx, "脚本评分成功: script_id=%d, user_id=%d, rating=%d", scriptID, userID, rating)
	return nil
}

// GetFeaturedScripts 获取推荐脚本
func (s *MarketScriptService) GetFeaturedScripts(ctx context.Context, limit int) ([]*model.MarketScript, error) {
	var scripts []*model.MarketScript

	if err := s.db.WithContext(ctx).Where("status = ? AND is_featured = ?", 1, true).
		Order("downloads DESC, rating DESC").
		Limit(limit).
		Find(&scripts).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询推荐脚本失败")
	}

	return scripts, nil
}

// ClearScriptCache 清理脚本相关缓存
func (s *MarketScriptService) ClearScriptCache(ctx context.Context, scriptID uint) error {
	if s.redisService != nil {
		return s.redisService.DeleteMarketScriptCache(ctx, scriptID)
	}
	return nil
}

// ClearAllScriptCache 清理所有脚本缓存
func (s *MarketScriptService) ClearAllScriptCache(ctx context.Context) error {
	if s.redisService != nil {
		return s.redisService.DeleteAllMarketScriptCache(ctx)
	}
	return nil
}
