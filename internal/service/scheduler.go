package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/robfig/cron/v3"

	"goadmin/internal/constant"
	"goadmin/internal/model"
	"goadmin/pkg/logger"
)

// SchedulerService 调度器服务
type SchedulerService struct {
	cron           *cron.Cron
	bindingService *BindingService
	configService  *ConfigService
	redisService   *RedisService
	deviceService  *DeviceService
	stopChan       chan struct{}
	isRunning      bool
}

// NewSchedulerService 创建调度器服务
func NewSchedulerService(bindingService *BindingService, configService *ConfigService, redisService *RedisService, deviceService *DeviceService) *SchedulerService {
	return &SchedulerService{
		cron:           cron.New(cron.WithSeconds()),
		bindingService: bindingService,
		configService:  configService,
		redisService:   redisService,
		deviceService:  deviceService,
		stopChan:       make(chan struct{}),
		isRunning:      false,
	}
}

// Start 启动调度器
func (s *SchedulerService) Start() {
	if s.isRunning {
		return
	}

	s.isRunning = true
	log.Println("调度器服务已启动")

	// 启动原来的定时任务
	s.startOriginalTasks()

	// 启动新的Redis相关定时任务
	go s.runRedisScheduler()
}

// Stop 停止调度器
func (s *SchedulerService) Stop() {
	if !s.isRunning {
		return
	}

	s.isRunning = false

	// 停止cron定时任务
	if s.cron != nil {
		ctx := s.cron.Stop()
		<-ctx.Done()
	}

	// 停止Redis调度器
	close(s.stopChan)
	log.Println("调度器服务已停止")
}

// startOriginalTasks 启动原来的定时任务
func (s *SchedulerService) startOriginalTasks() {
	// 每10分钟检查一次不活跃设备
	s.cron.AddFunc("0 */10 * * * *", func() {
		ctx := context.Background()
		// 获取配置的不活跃时间阈值（小时），默认为1小时
		inactiveHours, err := s.configService.GetIntConfig(ctx, "device_inactive_hours", 1)
		if err != nil {
			logger.Error(ctx, "Failed to get device_inactive_hours config: %v", err)
			return
		}

		if err := s.bindingService.CheckInactiveDevices(inactiveHours); err != nil {
			logger.Error(ctx, "Failed to check inactive devices: %v", err)
		}
	})

	// 每5分钟检查一次过期卡密
	s.cron.AddFunc("0 */5 * * * *", func() {
		ctx := context.Background()
		if err := s.bindingService.cardService.UpdateExpiredCards(); err != nil {
			logger.Error(ctx, "Failed to update expired cards: %v", err)
		}
	})

	// 每天凌晨2点清理过期的refresh tokens
	s.cron.AddFunc("0 0 2 * * *", func() {
		ctx := context.Background()
		if err := s.cleanupExpiredRefreshTokens(ctx); err != nil {
			logger.Error(ctx, "Failed to cleanup expired refresh tokens: %v", err)
		}
	})

	// 每5分钟清理过期的设备配额
	s.cron.AddFunc("0 */5 * * * *", func() {
		ctx := context.Background()
		quotaService := NewDeviceQuotaService(s.bindingService.db)
		if err := quotaService.CleanupExpiredQuotas(ctx); err != nil {
			logger.Error(ctx, "Failed to cleanup expired device quotas: %v", err)
		}
	})

	s.cron.Start()
}

// runRedisScheduler 运行Redis相关的调度器
func (s *SchedulerService) runRedisScheduler() {
	// Redis状态同步定时器
	redisSyncTicker := time.NewTicker(5 * time.Minute) // 每5分钟同步一次
	defer redisSyncTicker.Stop()

	// 过期连接清理定时器
	cleanupTicker := time.NewTicker(1 * time.Minute) // 每1分钟清理一次
	defer cleanupTicker.Stop()

	// 状态一致性检查定时器
	consistencyTicker := time.NewTicker(2 * time.Minute) // 每2分钟检查一次
	defer consistencyTicker.Stop()

	for {
		select {
		case <-s.stopChan:
			return
		case <-redisSyncTicker.C:
			s.syncRedisStatus()
		case <-cleanupTicker.C:
			s.cleanupExpiredConnections()
		case <-consistencyTicker.C:
			s.checkStatusConsistency()
		}
	}
}

// syncRedisStatus 同步Redis状态到数据库
func (s *SchedulerService) syncRedisStatus() {
	if s.redisService == nil {
		log.Println("Redis服务不可用，跳过状态同步")
		return
	}

	ctx := context.Background()
	log.Println("开始同步Redis状态到数据库...")

	// 获取所有用户的在线设备
	pattern := "device:online:*"
	keys, err := s.redisService.GetClient().GetClient().Keys(ctx, pattern).Result()
	if err != nil {
		log.Printf("获取Redis键失败: %v", err)
		return
	}

	totalUpdated := 0
	for _, key := range keys {
		// 从key中提取userID
		var userIDStr string
		fmt.Sscanf(key, "device:online:%s", &userIDStr)

		// 解析userID
		var userID uint
		fmt.Sscanf(userIDStr, "%d", &userID)

		// 获取该用户的在线设备
		onlineDevices, err := s.redisService.GetOnlineDevices(ctx, userID)
		if err != nil {
			log.Printf("获取用户 %d 在线设备失败: %v", userID, err)
			continue
		}

		// 批量更新数据库中的设备状态
		for deviceID := range onlineDevices {
			if err := s.deviceService.UpdateDeviceStatus(ctx, deviceID, 1); err != nil {
				log.Printf("更新设备 %s 状态失败: %v", deviceID, err)
			} else {
				totalUpdated++
			}
		}
	}

	log.Printf("Redis状态同步完成，更新了 %d 个设备状态", totalUpdated)
}

// cleanupExpiredConnections 清理过期连接
func (s *SchedulerService) cleanupExpiredConnections() {
	if s.redisService == nil {
		log.Println("Redis服务不可用，跳过过期连接清理")
		return
	}

	ctx := context.Background()

	// 清理45秒内没有心跳的连接
	timeoutSeconds := int64(45)

	if err := s.redisService.CleanupExpiredConnections(ctx, timeoutSeconds); err != nil {
		log.Printf("清理过期连接失败: %v", err)
	}
}

// checkStatusConsistency 检查Redis和数据库状态一致性
func (s *SchedulerService) checkStatusConsistency() {
	if s.redisService == nil {
		log.Println("Redis服务不可用，跳过状态一致性检查")
		return
	}

	ctx := context.Background()
	log.Println("开始检查Redis和数据库状态一致性...")

	// 获取所有用户的在线设备
	pattern := "device:online:*"
	keys, err := s.redisService.GetClient().GetClient().Keys(ctx, pattern).Result()
	if err != nil {
		log.Printf("获取Redis键失败: %v", err)
		return
	}

	inconsistencies := 0

	// 1. 检查Redis在线但数据库离线的设备
	for _, key := range keys {
		// 从key中提取userID
		var userIDStr string
		fmt.Sscanf(key, "device:online:%s", &userIDStr)

		// 解析userID
		var userID uint
		fmt.Sscanf(userIDStr, "%d", &userID)

		// 获取该用户的在线设备
		onlineDevices, err := s.redisService.GetOnlineDevices(ctx, userID)
		if err != nil {
			log.Printf("获取用户 %d 在线设备失败: %v", userID, err)
			continue
		}

		// 检查每个设备的数据库状态
		for deviceID := range onlineDevices {
			// 查询数据库中的设备状态
			var device model.Device
			if err := s.deviceService.db.Where("device_id = ?", deviceID).First(&device).Error; err != nil {
				log.Printf("查询设备 %s 数据库状态失败: %v", deviceID, err)
				continue
			}

			// 检查状态是否一致
			if device.Status != constant.DeviceStatusOnline {
				log.Printf("状态不一致: 设备 %s Redis=在线, 数据库=离线, 正在修复...", deviceID)
				// 修复数据库状态
				if err := s.deviceService.UpdateDeviceStatus(ctx, deviceID, constant.DeviceStatusOnline); err != nil {
					log.Printf("修复设备 %s 状态失败: %v", deviceID, err)
				} else {
					inconsistencies++
				}
			}
		}
	}

	// 2. 检查数据库在线但Redis离线的设备
	var devices []model.Device
	if err := s.deviceService.db.Where("status = ?", constant.DeviceStatusOnline).Find(&devices).Error; err != nil {
		log.Printf("查询在线设备失败: %v", err)
		return
	}

	for _, device := range devices {
		// 检查Redis中是否在线
		isOnline, err := s.redisService.IsDeviceOnline(ctx, device.UserID, device.DeviceID)
		if err != nil {
			log.Printf("检查设备 %s Redis状态失败: %v", device.DeviceID, err)
			continue
		}

		if !isOnline {
			log.Printf("状态不一致: 设备 %s 数据库=在线, Redis=离线, 正在修复...", device.DeviceID)
			// 修复数据库状态
			if err := s.deviceService.UpdateDeviceStatus(ctx, device.DeviceID, constant.DeviceStatusOffline); err != nil {
				log.Printf("修复设备 %s 状态失败: %v", device.DeviceID, err)
			} else {
				inconsistencies++
			}
		}
	}

	if inconsistencies > 0 {
		log.Printf("状态一致性检查完成，修复了 %d 个不一致的设备状态", inconsistencies)
	} else {
		log.Println("状态一致性检查完成，所有设备状态一致")
	}
}

// cleanupExpiredRefreshTokens 清理过期的refresh tokens
func (s *SchedulerService) cleanupExpiredRefreshTokens(ctx context.Context) error {
	log.Println("开始清理过期的refresh tokens...")

	// 删除过期的refresh tokens
	result := s.bindingService.db.WithContext(ctx).Where("expires_at < ?", time.Now()).Delete(&model.RefreshToken{})
	if result.Error != nil {
		log.Printf("清理过期refresh tokens失败: %v", result.Error)
		return result.Error
	}

	if result.RowsAffected > 0 {
		log.Printf("清理了 %d 个过期的refresh tokens", result.RowsAffected)
	}

	return nil
}
