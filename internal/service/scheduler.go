package service

import (
	"context"

	"github.com/robfig/cron/v3"

	"goadmin/pkg/logger"
)

type SchedulerService struct {
	cron           *cron.Cron
	bindingService *BindingService
	configService  *ConfigService
}

func NewSchedulerService(bindingService *BindingService, configService *ConfigService) *SchedulerService {
	return &SchedulerService{
		cron:           cron.New(cron.WithSeconds()),
		bindingService: bindingService,
		configService:  configService,
	}
}

// Start 启动定时任务
func (s *SchedulerService) Start() {
	// 每10分钟检查一次不活跃设备
	s.cron.AddFunc("0 */10 * * * *", func() {
		ctx := context.Background()
		// 获取配置的不活跃时间阈值（小时），默认为1小时
		inactiveHours, err := s.configService.GetIntConfig(ctx, "device_inactive_hours", 1)
		if err != nil {
			logger.Error(ctx, "Failed to get device_inactive_hours config: %v", err)
			return
		}

		if err := s.bindingService.CheckInactiveDevices(inactiveHours); err != nil {
			logger.Error(ctx, "Failed to check inactive devices: %v", err)
		}
	})

	// 每5分钟检查一次过期卡密
	s.cron.AddFunc("0 */5 * * * *", func() {
		ctx := context.Background()
		if err := s.bindingService.cardService.UpdateExpiredCards(); err != nil {
			logger.Error(ctx, "Failed to update expired cards: %v", err)
		}
	})

	s.cron.Start()
}

// Stop 停止定时任务
func (s *SchedulerService) Stop() {
	if s.cron != nil {
		ctx := s.cron.Stop()
		<-ctx.Done()
	}
}
