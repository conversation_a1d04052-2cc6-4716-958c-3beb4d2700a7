package service

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// SystemLogService 系统日志服务
type SystemLogService struct {
	db *gorm.DB
}

// NewSystemLogService 创建系统日志服务
func NewSystemLogService(db *gorm.DB) *SystemLogService {
	return &SystemLogService{
		db: db,
	}
}

// CreateLog 创建日志
func (s *SystemLogService) CreateLog(ctx context.Context, logType, level, message string, data interface{}, userID, deviceID, taskID *uint, ip string) error {
	var dataJSON string
	if data != nil {
		dataBytes, err := json.Marshal(data)
		if err != nil {
			dataJSON = ""
		} else {
			dataJSON = string(dataBytes)
		}
	}
	
	log := &model.SystemLog{
		Type:     logType,
		Level:    level,
		Message:  message,
		Data:     dataJSON,
		UserID:   userID,
		DeviceID: deviceID,
		TaskID:   taskID,
		IP:       ip,
	}
	
	if err := s.db.WithContext(ctx).Create(log).Error; err != nil {
		return response.NewError(response.ERROR, "创建日志失败")
	}
	
	return nil
}

// GetLogList 获取日志列表
func (s *SystemLogService) GetLogList(ctx context.Context, page, pageSize int, logType, level string, userID, deviceID, taskID *uint, startTime, endTime *time.Time) ([]*model.SystemLog, int64, error) {
	var logs []*model.SystemLog
	var total int64
	
	query := s.db.WithContext(ctx).Model(&model.SystemLog{})
	
	if logType != "" {
		query = query.Where("type = ?", logType)
	}
	
	if level != "" {
		query = query.Where("level = ?", level)
	}
	
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}
	
	if deviceID != nil {
		query = query.Where("device_id = ?", *deviceID)
	}
	
	if taskID != nil {
		query = query.Where("task_id = ?", *taskID)
	}
	
	if startTime != nil {
		query = query.Where("created_at >= ?", *startTime)
	}
	
	if endTime != nil {
		query = query.Where("created_at <= ?", *endTime)
	}
	
	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询日志总数失败")
	}
	
	// 查询日志列表
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&logs).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询日志列表失败")
	}
	
	return logs, total, nil
}

// DeleteOldLogs 删除旧日志
func (s *SystemLogService) DeleteOldLogs(ctx context.Context, beforeTime time.Time) (int64, error) {
	result := s.db.WithContext(ctx).Where("created_at < ?", beforeTime).Delete(&model.SystemLog{})
	if result.Error != nil {
		return 0, response.NewError(response.ERROR, "删除旧日志失败")
	}
	
	return result.RowsAffected, nil
}

// GetLogStatistics 获取日志统计信息
func (s *SystemLogService) GetLogStatistics(ctx context.Context, startTime, endTime *time.Time) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	query := s.db.WithContext(ctx).Model(&model.SystemLog{})
	
	if startTime != nil {
		query = query.Where("created_at >= ?", *startTime)
	}
	
	if endTime != nil {
		query = query.Where("created_at <= ?", *endTime)
	}
	
	// 按类型统计
	typeStats := make(map[string]int64)
	types := []string{"operation", "device", "task", "system"}
	for _, logType := range types {
		var count int64
		if err := query.Where("type = ?", logType).Count(&count).Error; err != nil {
			return nil, response.NewError(response.ERROR, "查询日志类型统计失败")
		}
		typeStats[logType] = count
	}
	stats["by_type"] = typeStats
	
	// 按级别统计
	levelStats := make(map[string]int64)
	levels := []string{"info", "warn", "error"}
	for _, level := range levels {
		var count int64
		if err := query.Where("level = ?", level).Count(&count).Error; err != nil {
			return nil, response.NewError(response.ERROR, "查询日志级别统计失败")
		}
		levelStats[level] = count
	}
	stats["by_level"] = levelStats
	
	// 总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询日志总数失败")
	}
	stats["total"] = total
	
	return stats, nil
}

// LogInfo 记录信息日志
func (s *SystemLogService) LogInfo(ctx context.Context, logType, message string, data interface{}, userID, deviceID, taskID *uint, ip string) error {
	return s.CreateLog(ctx, logType, "info", message, data, userID, deviceID, taskID, ip)
}

// LogWarn 记录警告日志
func (s *SystemLogService) LogWarn(ctx context.Context, logType, message string, data interface{}, userID, deviceID, taskID *uint, ip string) error {
	return s.CreateLog(ctx, logType, "warn", message, data, userID, deviceID, taskID, ip)
}

// LogError 记录错误日志
func (s *SystemLogService) LogError(ctx context.Context, logType, message string, data interface{}, userID, deviceID, taskID *uint, ip string) error {
	return s.CreateLog(ctx, logType, "error", message, data, userID, deviceID, taskID, ip)
}

// LogUserOperation 记录用户操作日志
func (s *SystemLogService) LogUserOperation(ctx context.Context, userID uint, operation, message string, data interface{}, ip string) error {
	return s.LogInfo(ctx, "operation", operation+": "+message, data, &userID, nil, nil, ip)
}

// LogDeviceEvent 记录设备事件日志
func (s *SystemLogService) LogDeviceEvent(ctx context.Context, deviceID uint, event, message string, data interface{}, ip string) error {
	return s.LogInfo(ctx, "device", event+": "+message, data, nil, &deviceID, nil, ip)
}

// LogTaskEvent 记录任务事件日志
func (s *SystemLogService) LogTaskEvent(ctx context.Context, taskID uint, event, message string, data interface{}, userID *uint, ip string) error {
	return s.LogInfo(ctx, "task", event+": "+message, data, userID, nil, &taskID, ip)
}

// LogSystemEvent 记录系统事件日志
func (s *SystemLogService) LogSystemEvent(ctx context.Context, event, message string, data interface{}, ip string) error {
	return s.LogInfo(ctx, "system", event+": "+message, data, nil, nil, nil, ip)
}

// GetRecentLogs 获取最近的日志
func (s *SystemLogService) GetRecentLogs(ctx context.Context, limit int, logType, level string) ([]*model.SystemLog, error) {
	var logs []*model.SystemLog
	
	query := s.db.WithContext(ctx).Model(&model.SystemLog{})
	
	if logType != "" {
		query = query.Where("type = ?", logType)
	}
	
	if level != "" {
		query = query.Where("level = ?", level)
	}
	
	if err := query.Order("created_at DESC").Limit(limit).Find(&logs).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询最近日志失败")
	}
	
	return logs, nil
}

// GetLogsByTimeRange 按时间范围获取日志
func (s *SystemLogService) GetLogsByTimeRange(ctx context.Context, startTime, endTime time.Time, logType, level string) ([]*model.SystemLog, error) {
	var logs []*model.SystemLog
	
	query := s.db.WithContext(ctx).Model(&model.SystemLog{}).
		Where("created_at >= ? AND created_at <= ?", startTime, endTime)
	
	if logType != "" {
		query = query.Where("type = ?", logType)
	}
	
	if level != "" {
		query = query.Where("level = ?", level)
	}
	
	if err := query.Order("created_at DESC").Find(&logs).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询时间范围日志失败")
	}
	
	return logs, nil
}

// CleanupLogs 清理日志（保留指定天数）
func (s *SystemLogService) CleanupLogs(ctx context.Context, retentionDays int) (int64, error) {
	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)
	return s.DeleteOldLogs(ctx, cutoffTime)
}
