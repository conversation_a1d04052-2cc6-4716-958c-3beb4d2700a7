package service

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"

	"goadmin/config"
)

type ThirdJWTService struct {
	cfg *config.Config
}

func NewThirdJWTService(cfg *config.Config) *ThirdJWTService {
	return &ThirdJWTService{
		cfg: cfg,
	}
}

// GenerateThirdToken 生成第三方API的JWT令牌
func (s *ThirdJWTService) GenerateThirdToken(secretId string, permissions string) (string, error) {
	claims := jwt.MapClaims{
		"secret_id":   secretId,
		"permissions": permissions,
		"type":        "third_party",
		"exp":         time.Now().Add(s.cfg.JWT.ExpiresTime).Unix(), // 使用配置文件中的过期时间
		"iat":         time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWT.ThirdSecretKey))
}

// ValidateThirdToken 验证第三方API的JWT令牌
func (s *ThirdJWTService) ValidateThirdToken(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.cfg.JWT.ThirdSecretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// 验证token类型
		if tokenType, ok := claims["type"].(string); !ok || tokenType != "third_party" {
			return nil, fmt.Errorf("invalid token type")
		}
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}
