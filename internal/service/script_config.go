package service

import (
	"context"
	"encoding/json"
	"strings"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// ScriptConfigService 脚本配置服务
type ScriptConfigService struct {
	db *gorm.DB
}

// NewScriptConfigService 创建脚本配置服务
func NewScriptConfigService(db *gorm.DB) *ScriptConfigService {
	return &ScriptConfigService{db: db}
}

// GetScriptConfigSchema 获取脚本的配置结构（通过市场脚本ID获取）
func (s *ScriptConfigService) GetScriptConfigSchema(ctx context.Context, scriptID uint) (interface{}, error) {
	// 首先尝试从用户脚本中获取
	var userScript model.UserScript
	if err := s.db.WithContext(ctx).Where("id = ?", scriptID).First(&userScript).Error; err == nil {
		return s.getConfigSchemaFromString(userScript.Config)
	}

	// 如果用户脚本中没有找到，尝试从市场脚本中获取（兼容性）
	var marketScript model.MarketScript
	if err := s.db.WithContext(ctx).Where("id = ?", scriptID).First(&marketScript).Error; err != nil {
		return nil, response.NewError(response.SCRIPT_NOT_FOUND, "脚本不存在")
	}

	return s.getConfigSchemaFromString(marketScript.Config)
}

// getConfigSchemaFromString 从配置字符串解析配置结构
func (s *ScriptConfigService) getConfigSchemaFromString(configStr string) (interface{}, error) {
	if configStr == "" {
		return nil, response.NewError(response.CONFIG_NOT_FOUND, "脚本未定义配置结构")
	}

	// 验证JSON格式
	var raw json.RawMessage
	if err := json.Unmarshal([]byte(configStr), &raw); err != nil {
		return nil, response.NewError(response.INVALID_PARAMS, "脚本配置结构格式错误")
	}

	// 检查是否以 '[' 开始（数组格式）
	trimmedConfig := strings.TrimSpace(configStr)
	if strings.HasPrefix(trimmedConfig, "[") {
		// 数组格式：直接解析为通用接口返回
		var configArray []interface{}
		if err := json.Unmarshal([]byte(configStr), &configArray); err != nil {
			return nil, response.NewError(response.INVALID_PARAMS, "解析配置数组失败")
		}
		return configArray, nil
	}

	// 如果不是数组格式，尝试解析为旧的schema格式
	var schema model.ConfigSchema
	if err := json.Unmarshal([]byte(configStr), &schema); err != nil {
		return nil, response.NewError(response.INVALID_PARAMS, "脚本配置结构格式错误")
	}

	return &schema, nil
}

// GetScriptConfigSchemaRaw 获取脚本的配置结构原始JSON字符串
func (s *ScriptConfigService) GetScriptConfigSchemaRaw(ctx context.Context, scriptID uint) (string, error) {
	// 首先尝试从用户脚本中获取
	var userScript model.UserScript
	if err := s.db.WithContext(ctx).Where("id = ?", scriptID).First(&userScript).Error; err == nil {
		return s.validateAndReturnConfigString(userScript.Config)
	}

	// 如果用户脚本中没有找到，尝试从市场脚本中获取（兼容性）
	var marketScript model.MarketScript
	if err := s.db.WithContext(ctx).Where("id = ?", scriptID).First(&marketScript).Error; err != nil {
		return "", response.NewError(response.SCRIPT_NOT_FOUND, "脚本不存在")
	}

	return s.validateAndReturnConfigString(marketScript.Config)
}

// validateAndReturnConfigString 验证并返回配置字符串
func (s *ScriptConfigService) validateAndReturnConfigString(configStr string) (string, error) {
	if configStr == "" {
		return "", response.NewError(response.CONFIG_NOT_FOUND, "脚本未定义配置结构")
	}

	// 验证JSON格式
	var raw json.RawMessage
	if err := json.Unmarshal([]byte(configStr), &raw); err != nil {
		return "", response.NewError(response.INVALID_PARAMS, "脚本配置结构格式错误")
	}

	// 直接返回原始JSON字符串，保持原始顺序
	return configStr, nil
}

// GetUserScriptConfigs 获取用户的脚本配置列表（包含设备关联信息）
func (s *ScriptConfigService) GetUserScriptConfigs(ctx context.Context, userID, scriptID uint) ([]*model.UserScriptConfig, error) {
	var configs []*model.UserScriptConfig

	err := s.db.WithContext(ctx).
		Where("user_id = ? AND script_id = ?", userID, scriptID).
		Order("created_at DESC").
		Find(&configs).Error

	if err != nil {
		return nil, err
	}

	// 为每个配置加载设备关联信息
	for _, config := range configs {
		// 使用自定义结构来接收JOIN查询结果
		type DeviceConfigResult struct {
			DeviceID     uint   `json:"device_id"`
			UserConfigID uint   `json:"user_config_id"`
			Priority     int    `json:"priority"`
			IsEnabled    bool   `json:"is_enabled"`
			DeviceName   string `json:"device_name"`
			Status       int    `json:"status"`
		}

		var results []DeviceConfigResult
		err := s.db.WithContext(ctx).
			Table("device_config_relations").
			Select("device_config_relations.device_id, device_config_relations.user_config_id, device_config_relations.priority, device_config_relations.is_enabled, devices.device_name, devices.status").
			Joins("JOIN devices ON device_config_relations.device_id = devices.id").
			Where("device_config_relations.user_config_id = ? AND device_config_relations.is_enabled = ?", config.ID, true).
			Order("device_config_relations.priority DESC").
			Find(&results).Error

		if err != nil {
			continue // 如果加载设备信息失败，继续处理其他配置
		}

		// 将设备信息添加到配置中
		var devices []map[string]interface{}
		for _, result := range results {
			devices = append(devices, map[string]interface{}{
				"id":          result.DeviceID,
				"device_name": result.DeviceName,
				"status":      result.Status,
				"priority":    result.Priority,
				"is_enabled":  result.IsEnabled,
			})
		}

		// 将设备信息存储到配置的临时字段中（通过JSON序列化）
		if len(devices) > 0 {
			devicesJSON, _ := json.Marshal(devices)
			config.DeviceIDs = model.JSONString(devicesJSON)
		}
	}

	return configs, nil
}

// GetUserScriptConfig 获取用户脚本配置详情
func (s *ScriptConfigService) GetUserScriptConfig(ctx context.Context, configID uint) (*model.UserScriptConfig, error) {
	var config model.UserScriptConfig

	err := s.db.WithContext(ctx).
		Preload("UserScript").
		Where("id = ?", configID).
		First(&config).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.CONFIG_NOT_FOUND, "配置不存在")
		}
		return nil, err
	}

	return &config, nil
}

// CreateUserScriptConfig 创建用户脚本配置
func (s *ScriptConfigService) CreateUserScriptConfig(ctx context.Context, userID, scriptID uint, configName string, configValues model.ConfigValue, deviceIDs []uint) (*model.UserScriptConfig, error) {
	// 验证脚本是否存在并获取配置结构
	schemaInterface, err := s.GetScriptConfigSchema(ctx, scriptID)
	if err != nil {
		return nil, err
	}

	// 验证配置值
	if schema, ok := schemaInterface.(*model.ConfigSchema); ok {
		if err := schema.ValidateConfig(configValues); err != nil {
			return nil, response.NewError(response.INVALID_PARAMS, "配置值验证失败")
		}
	}
	// 对于新的数组格式，暂时跳过验证

	// 序列化配置值
	configValuesJSON, err := json.Marshal(configValues)
	if err != nil {
		return nil, err
	}

	// 序列化设备ID列表
	deviceIDsJSON, err := json.Marshal(deviceIDs)
	if err != nil {
		return nil, err
	}

	// 创建配置
	config := &model.UserScriptConfig{
		UserID:       userID,
		ScriptID:     scriptID,
		ConfigName:   configName,
		ConfigValues: model.JSONString(configValuesJSON),
		DeviceIDs:    model.JSONString(deviceIDsJSON),
		IsActive:     true,
	}

	if err := s.db.WithContext(ctx).Create(config).Error; err != nil {
		return nil, err
	}

	// 创建设备关联关系
	if len(deviceIDs) > 0 {
		var relations []model.DeviceConfigRelation
		for i, deviceID := range deviceIDs {
			relations = append(relations, model.DeviceConfigRelation{
				DeviceID:     deviceID,
				UserConfigID: config.ID,
				Priority:     len(deviceIDs) - i, // 越靠前的设备优先级越高
				IsEnabled:    true,
			})
		}

		if err := s.db.WithContext(ctx).Create(&relations).Error; err != nil {
			return nil, err
		}
	}

	return config, nil
}

// UpdateUserScriptConfig 更新用户脚本配置
func (s *ScriptConfigService) UpdateUserScriptConfig(ctx context.Context, configID uint, configName string, configValues model.ConfigValue, deviceIDs []uint) (*model.UserScriptConfig, error) {
	// 获取现有配置
	config, err := s.GetUserScriptConfig(ctx, configID)
	if err != nil {
		return nil, err
	}

	// 验证配置值
	schemaInterface, err := s.GetScriptConfigSchema(ctx, config.ScriptID)
	if err != nil {
		return nil, err
	}

	if schema, ok := schemaInterface.(*model.ConfigSchema); ok {
		if err := schema.ValidateConfig(configValues); err != nil {
			return nil, response.NewError(response.INVALID_PARAMS, "配置值验证失败")
		}
	}
	// 对于新的数组格式，暂时跳过验证

	// 序列化新配置值
	configValuesJSON, err := json.Marshal(configValues)
	if err != nil {
		return nil, err
	}

	// 序列化设备ID列表
	deviceIDsJSON, err := json.Marshal(deviceIDs)
	if err != nil {
		return nil, err
	}

	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新配置
	updates := map[string]interface{}{
		"config_name":   configName,
		"config_values": model.JSONString(configValuesJSON),
		"device_ids":    model.JSONString(deviceIDsJSON),
	}

	if err := tx.Model(config).Updates(updates).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 更新设备关联关系
	// 先删除旧的关联关系
	if err := tx.Where("user_config_id = ?", configID).Delete(&model.DeviceConfigRelation{}).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 创建新的关联关系
	if len(deviceIDs) > 0 {
		var relations []model.DeviceConfigRelation
		for i, deviceID := range deviceIDs {
			relations = append(relations, model.DeviceConfigRelation{
				DeviceID:     deviceID,
				UserConfigID: configID,
				Priority:     len(deviceIDs) - i,
				IsEnabled:    true,
			})
		}

		if err := tx.Create(&relations).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 重新获取更新后的配置
	return s.GetUserScriptConfig(ctx, configID)
}

// DeleteUserScriptConfig 删除用户脚本配置
func (s *ScriptConfigService) DeleteUserScriptConfig(ctx context.Context, configID uint) error {
	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除设备关联关系
	if err := tx.Where("user_config_id = ?", configID).Delete(&model.DeviceConfigRelation{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除配置
	if err := tx.Where("id = ?", configID).Delete(&model.UserScriptConfig{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
