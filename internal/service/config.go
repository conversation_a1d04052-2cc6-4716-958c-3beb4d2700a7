package service

import (
	"context"
	"encoding/json"
	"strconv"

	"gopkg.in/yaml.v3"
	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/response"
)

type ConfigService struct {
	db *gorm.DB
}

func NewConfigService(db *gorm.DB) *ConfigService {
	return &ConfigService{
		db: db,
	}
}

// validateConfigValue 验证配置值格式
func (s *ConfigService) validateConfigValue(valueType model.ConfigValueType, value string) error {
	switch valueType {
	case model.ConfigValueTypeJSON:
		var js interface{}
		if err := json.Unmarshal([]byte(value), &js); err != nil {
			return response.NewError(response.INVALID_PARAMS, "无效的JSON格式")
		}
	case model.ConfigValueTypeYAML:
		var y interface{}
		if err := yaml.Unmarshal([]byte(value), &y); err != nil {
			return response.NewError(response.INVALID_PARAMS, "无效的YAML格式")
		}
	}
	return nil
}

// GetConfig 获取配置
func (s *ConfigService) GetConfig(ctx context.Context, key string) (*model.SystemConfig, error) {
	var config model.SystemConfig
	if err := s.db.WithContext(ctx).Where("`key` = ?", key).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.CONFIG_NOT_FOUND, "配置不存在")
		}
		return nil, err
	}
	return &config, nil
}

// GetIntConfig 获取整数类型配置
func (s *ConfigService) GetIntConfig(ctx context.Context, key string, defaultValue int) (int, error) {
	config, err := s.GetConfig(ctx, key)
	if err != nil {
		if e, ok := err.(*response.Error); ok && e.Code == response.CONFIG_NOT_FOUND {
			return defaultValue, nil
		}
		return 0, err
	}

	value, err := strconv.Atoi(config.Value)
	if err != nil {
		return defaultValue, nil
	}
	return value, nil
}

// SetConfig 设置配置
func (s *ConfigService) SetConfig(key, value string, valueType model.ConfigValueType, desc string) error {
	// 验证值格式
	if err := s.validateConfigValue(valueType, value); err != nil {
		return err
	}

	var config model.SystemConfig
	result := s.db.Where("`key` = ?", key).First(&config)
	if result.Error == nil {
		// 更新配置
		updates := map[string]interface{}{
			"value": value,
			"type":  valueType,
			"desc":  desc,
		}
		if err := s.db.Model(&config).Updates(updates).Error; err != nil {
			return response.NewError(response.ERROR, "更新配置失败")
		}
	} else if result.Error == gorm.ErrRecordNotFound {
		// 创建配置
		config = model.SystemConfig{
			Key:   key,
			Value: value,
			Type:  valueType,
			Desc:  desc,
		}
		if err := s.db.Create(&config).Error; err != nil {
			return response.NewError(response.ERROR, "创建配置失败")
		}
	} else {
		return response.NewError(response.ERROR, "查询配置失败")
	}

	return nil
}

// ListConfigs 获取配置列表
func (s *ConfigService) ListConfigs(page, pageSize int, key string) ([]*model.SystemConfig, int64, error) {
	var configs []*model.SystemConfig
	var total int64

	query := s.db.Model(&model.SystemConfig{})
	if key != "" {
		query = query.Where("`key` LIKE ?", "%"+key+"%")
	}

	query.Count(&total)
	if err := query.Offset((page - 1) * pageSize).Limit(pageSize).Find(&configs).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "获取配置列表失败")
	}

	return configs, total, nil
}

// DeleteConfig 删除配置
func (s *ConfigService) DeleteConfig(key string) error {
	result := s.db.Where("`key` = ?", key).Delete(&model.SystemConfig{})
	if result.Error != nil {
		return response.NewError(response.ERROR, "删除配置失败")
	}
	if result.RowsAffected == 0 {
		return response.NewError(response.CONFIG_NOT_FOUND, "配置不存在")
	}
	return nil
}
