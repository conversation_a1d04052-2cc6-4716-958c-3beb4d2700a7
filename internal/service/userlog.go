package service

import (
	"goadmin/internal/model"
	"goadmin/pkg/logger"

	"context"

	"gorm.io/gorm"
)

type UserLogService struct {
	db *gorm.DB
}

func NewUserLogService(db *gorm.DB) *UserLogService {
	return &UserLogService{
		db: db,
	}
}

// CreateLoginLog 异步创建登录日志
func (s *UserLogService) CreateLoginLog(cardKey string, deviceID string) {
	go func() {
		loginLog := &model.LoginLog{
			CardKey:  cardKey,
			DeviceID: deviceID,
		}
		if err := s.db.Create(loginLog).Error; err != nil {
			logger.Error(context.Background(), "Failed to create login log: %v", err)
		}
	}()
}

// GetDailyActiveUsers 获取最近30天的每日活跃用户数
func (s *UserLogService) GetDailyActiveUsers() ([]struct {
	Date     string `json:"date"`
	PlanName string `json:"plan_name"`
	Count    int64  `json:"count"`
}, error) {
	var stats []struct {
		Date     string `json:"date"`
		PlanName string `json:"plan_name"`
		Count    int64  `json:"count"`
	}

	if err := s.db.Raw(`
		SELECT 
			DATE_FORMAT(created_at, '%m-%d') as date,
			plan_name,
			COUNT(DISTINCT user_id) as count
		FROM user_logs
		WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
		GROUP BY DATE(created_at), plan_name
		ORDER BY DATE(created_at) ASC, plan_name ASC
	`).Scan(&stats).Error; err != nil {
		return nil, err
	}

	return stats, nil
}

// GetDailyActiveRobots 获取最近30天的每日活跃机器人数
func (s *UserLogService) GetDailyActiveRobots() ([]struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}, error) {
	var stats []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	if err := s.db.Raw(`
		SELECT 
			DATE_FORMAT(created_at, '%m-%d') as date,
			COUNT(DISTINCT robot_id) as count
		FROM user_logs
		WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
		GROUP BY DATE(created_at)
		ORDER BY DATE(created_at) ASC
	`).Scan(&stats).Error; err != nil {
		return nil, err
	}

	return stats, nil
}
