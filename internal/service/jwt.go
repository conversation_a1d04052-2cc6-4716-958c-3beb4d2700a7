package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"gorm.io/gorm"

	"goadmin/config"
	"goadmin/internal/model"
	"goadmin/pkg/logger"
)

type JWTService struct {
	cfg *config.Config
	db  *gorm.DB
}

func NewJWTService(cfg *config.Config, db *gorm.DB) *JWTService {
	return &JWTService{
		cfg: cfg,
		db:  db,
	}
}

// GenerateToken 生成管理员JWT令牌
func (s *JWTService) GenerateToken(adminID uint) (string, error) {
	claims := jwt.MapClaims{
		"admin_id": adminID,
		"exp":      time.Now().Add(s.cfg.JWT.ExpiresTime).Unix(),
		"iat":      time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWT.SecretKey))
}

// GenerateUserToken 生成用户JWT令牌
func (s *JWTService) GenerateUserToken(userID string) (string, error) {
	claims := jwt.MapClaims{
		"user_id": userID,
		"exp":     time.Now().Add(s.cfg.JWT.ExpiresTime).Unix(),
		"iat":     time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWT.SecretKey))
}

// ValidateToken 验证JWT令牌
func (s *JWTService) ValidateToken(tokenString string) (*jwt.Token, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.cfg.JWT.SecretKey), nil
	})

	if err != nil {
		return nil, err
	}

	return token, nil
}

func (s *JWTService) logUserActivity(ctx context.Context, userID string, robotID string, planName string, claims jwt.MapClaims) {
	go func() {
		claimsJSON, err := json.Marshal(claims)
		if err != nil {
			logger.Error(ctx, "Error marshalling claims: %v", err)
			return
		}

		userLog := model.UserLog{
			UserID:   userID,
			RobotID:  robotID,
			PlanName: planName,
			Claims:   string(claimsJSON),
		}

		if err := s.db.Create(&userLog).Error; err != nil {
			logger.Error(ctx, "Error logging user activity: %v", err)
		}
	}()
}

func (s *JWTService) ValidateHamibotToken(ctx context.Context, tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.cfg.JWT.SecretKey), nil
	})

	if err != nil {
		logger.Error(ctx, "Token validation error: %v", err)
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// 安全地获取嵌套字段，避免空指针异常
		var userID, robotID, planName string

		// 检查env字段是否存在且为map类型
		if env, ok := claims["env"].(map[string]interface{}); ok {
			// 安全地获取USER_ID
			if val, ok := env["USER_ID"].(string); ok {
				userID = val
			}
			// 安全地获取ROBOT_ID
			if val, ok := env["ROBOT_ID"].(string); ok {
				robotID = val
			}
		}

		// 检查plan字段是否存在且为map类型
		if plan, ok := claims["plan"].(map[string]interface{}); ok {
			// 安全地获取name
			if val, ok := plan["name"].(string); ok {
				planName = val
			}
		}

		s.logUserActivity(ctx, userID, robotID, planName, claims)
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// GenerateClientToken 生成客户端APP的token
func (s *JWTService) GenerateClientToken(cardKey string, deviceID string) (string, error) {
	claims := jwt.MapClaims{
		"card_key":  cardKey,
		"device_id": deviceID,
		"type":      "client",
		"exp":       time.Now().Add(s.cfg.JWT.ExpiresTime).Unix(),
		"iat":       time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWT.SecretKey))
}

// ValidateClientToken 验证客户端token
func (s *JWTService) ValidateClientToken(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.cfg.JWT.SecretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// 验证token类型
		if tokenType, ok := claims["type"].(string); !ok || tokenType != "client" {
			return nil, fmt.Errorf("invalid token type")
		}
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// ParseToken 解析JWT令牌并返回Claims
func (s *JWTService) ParseToken(tokenString string) (jwt.MapClaims, error) {
	token, err := s.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// RefreshToken 刷新JWT令牌
func (s *JWTService) RefreshToken(tokenString string) (string, error) {
	claims, err := s.ParseToken(tokenString)
	if err != nil {
		return "", err
	}

	// 检查是否为用户token
	if userID, ok := claims["user_id"].(string); ok {
		return s.GenerateUserToken(userID)
	}

	// 检查是否为管理员token
	if adminID, ok := claims["admin_id"].(float64); ok {
		return s.GenerateToken(uint(adminID))
	}

	return "", fmt.Errorf("invalid token type for refresh")
}
