package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"gorm.io/gorm"

	"goadmin/config"
	"goadmin/internal/constant"
	"goadmin/internal/model"
	"goadmin/pkg/logger"
)

type JWTService struct {
	cfg *config.Config
	db  *gorm.DB
}

func NewJWTService(cfg *config.Config, db *gorm.DB) *JWTService {
	return &JWTService{
		cfg: cfg,
		db:  db,
	}
}

// GenerateToken 生成管理员JWT令牌
func (s *JWTService) GenerateToken(adminID uint) (string, error) {
	claims := jwt.MapClaims{
		"admin_id": adminID,
		"exp":      time.Now().Add(s.cfg.JWT.ExpiresTime).Unix(),
		"iat":      time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWT.SecretKey))
}

// GenerateUserToken 生成用户JWT令牌
func (s *JWTService) GenerateUserToken(userID string) (string, error) {
	claims := jwt.MapClaims{
		"user_id": userID,
		"exp":     time.Now().Add(s.cfg.JWT.ExpiresTime).Unix(),
		"iat":     time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWT.SecretKey))
}

// GenerateWebSocketToken 生成WebSocket专用JWT令牌
func (s *JWTService) GenerateWebSocketToken(userID string, deviceID string, clientType string) (string, error) {
	claims := jwt.MapClaims{
		"user_id":     userID,
		"device_id":   deviceID,
		"client_type": clientType,
		"exp":         time.Now().Add(s.cfg.JWT.ExpiresTime).Unix(),
		"iat":         time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWT.SecretKey))
}

// GenerateAccessToken 生成访问令牌（短期）
func (s *JWTService) GenerateAccessToken(userID string, deviceID string, clientType string) (string, error) {
	claims := jwt.MapClaims{
		"user_id":     userID,
		"device_id":   deviceID,
		"client_type": clientType,
		"token_type":  "access",
		"exp":         time.Now().Add(s.cfg.JWT.ExpiresTime).Unix(),
		"iat":         time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWT.SecretKey))
}

// GenerateRefreshToken 生成刷新令牌（长期）
func (s *JWTService) GenerateRefreshToken(userID string, deviceID string, clientType string) (string, error) {
	claims := jwt.MapClaims{
		"user_id":     userID,
		"device_id":   deviceID,
		"client_type": clientType,
		"token_type":  constant.TokenTypeRefresh,
		"exp":         time.Now().AddDate(0, 0, 30).Unix(), // 30天有效期
		"iat":         time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWT.SecretKey))
}

// RefreshAccessToken 使用refresh token刷新access token
func (s *JWTService) RefreshAccessToken(refreshTokenString string) (string, string, error) {
	// 解析refresh token
	claims, err := s.ParseToken(refreshTokenString)
	if err != nil {
		return "", "", fmt.Errorf("invalid refresh token: %v", err)
	}

	// 验证token类型
	tokenType, ok := claims["token_type"].(string)
	if !ok || tokenType != constant.TokenTypeRefresh {
		return "", "", fmt.Errorf("invalid token type, expected refresh token")
	}

	// 提取用户信息
	userID, ok := claims["user_id"].(string)
	if !ok {
		return "", "", fmt.Errorf("missing user_id in refresh token")
	}

	deviceID, ok := claims["device_id"].(string)
	if !ok {
		return "", "", fmt.Errorf("missing device_id in refresh token")
	}

	clientType, ok := claims["client_type"].(string)
	if !ok {
		return "", "", fmt.Errorf("missing client_type in refresh token")
	}

	// 生成新的access token和refresh token
	newAccessToken, err := s.GenerateAccessToken(userID, deviceID, clientType)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate new access token: %v", err)
	}

	newRefreshToken, err := s.GenerateRefreshToken(userID, deviceID, clientType)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate new refresh token: %v", err)
	}

	return newAccessToken, newRefreshToken, nil
}

// ValidateToken 验证JWT令牌
func (s *JWTService) ValidateToken(tokenString string) (*jwt.Token, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.cfg.JWT.SecretKey), nil
	})

	if err != nil {
		return nil, err
	}

	return token, nil
}

func (s *JWTService) logUserActivity(ctx context.Context, userID string, robotID string, planName string, claims jwt.MapClaims) {
	go func() {
		claimsJSON, err := json.Marshal(claims)
		if err != nil {
			logger.Error(ctx, "Error marshalling claims: %v", err)
			return
		}

		userLog := model.UserLog{
			UserID:   userID,
			RobotID:  robotID,
			PlanName: planName,
			Claims:   string(claimsJSON),
		}

		if err := s.db.Create(&userLog).Error; err != nil {
			logger.Error(ctx, "Error logging user activity: %v", err)
		}
	}()
}

func (s *JWTService) ValidateHamibotToken(ctx context.Context, tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.cfg.JWT.SecretKey), nil
	})

	if err != nil {
		logger.Error(ctx, "Token validation error: %v", err)
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// 安全地获取嵌套字段，避免空指针异常
		var userID, robotID, planName string

		// 检查env字段是否存在且为map类型
		if env, ok := claims["env"].(map[string]interface{}); ok {
			// 安全地获取USER_ID
			if val, ok := env["USER_ID"].(string); ok {
				userID = val
			}
			// 安全地获取ROBOT_ID
			if val, ok := env["ROBOT_ID"].(string); ok {
				robotID = val
			}
		}

		// 检查plan字段是否存在且为map类型
		if plan, ok := claims["plan"].(map[string]interface{}); ok {
			// 安全地获取name
			if val, ok := plan["name"].(string); ok {
				planName = val
			}
		}

		s.logUserActivity(ctx, userID, robotID, planName, claims)
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// GenerateClientToken 生成客户端APP的token
func (s *JWTService) GenerateClientToken(cardKey string, deviceID string) (string, error) {
	claims := jwt.MapClaims{
		"card_key":  cardKey,
		"device_id": deviceID,
		"type":      constant.TokenTypeClient,
		"exp":       time.Now().Add(s.cfg.JWT.ExpiresTime).Unix(),
		"iat":       time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWT.SecretKey))
}

// ValidateClientToken 验证客户端token
func (s *JWTService) ValidateClientToken(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.cfg.JWT.SecretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// 验证token类型
		if tokenType, ok := claims["type"].(string); !ok || tokenType != constant.TokenTypeClient {
			return nil, fmt.Errorf("invalid token type")
		}
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// ParseToken 解析JWT令牌并返回Claims
func (s *JWTService) ParseToken(tokenString string) (jwt.MapClaims, error) {
	token, err := s.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// RefreshToken 刷新JWT令牌
func (s *JWTService) RefreshToken(tokenString string) (string, error) {
	claims, err := s.ParseToken(tokenString)
	if err != nil {
		return "", err
	}

	// 检查是否为用户token
	if userID, ok := claims["user_id"].(string); ok {
		return s.GenerateUserToken(userID)
	}

	// 检查是否为管理员token
	if adminID, ok := claims["admin_id"].(float64); ok {
		return s.GenerateToken(uint(adminID))
	}

	return "", fmt.Errorf("invalid token type for refresh")
}

// ValidateScriptToken 验证脚本token
func (s *JWTService) ValidateScriptToken(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.cfg.JWT.SecretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// 验证token类型
		if clientType, ok := claims["client_type"].(string); !ok || clientType != "script" {
			return nil, fmt.Errorf("invalid token type, expected script")
		}
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}
