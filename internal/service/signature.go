package service

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"time"
)

type SignatureService struct{}

func NewSignatureService() *SignatureService {
	return &SignatureService{}
}

// GenerateSignature 生成请求签名
// 参考阿里云API签名算法
func (s *SignatureService) GenerateSignature(method, uri string, params map[string]string, secretKey string, timestamp int64) string {
	// 1. 构造待签名字符串
	stringToSign := s.buildStringToSign(method, uri, params, timestamp)
	
	// 2. 使用HMAC-SHA256生成签名
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(stringToSign))
	signature := hex.EncodeToString(h.Sum(nil))
	
	return signature
}

// ValidateSignature 验证请求签名
func (s *SignatureService) ValidateSignature(method, uri string, params map[string]string, secretKey string, timestamp int64, signature string) bool {
	// 检查时间戳是否在有效范围内（5分钟）
	now := time.Now().Unix()
	if abs(now-timestamp) > 300 { // 5分钟
		return false
	}
	
	// 生成期望的签名
	expectedSignature := s.GenerateSignature(method, uri, params, secretKey, timestamp)
	
	// 比较签名
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// buildStringToSign 构造待签名字符串
func (s *SignatureService) buildStringToSign(method, uri string, params map[string]string, timestamp int64) string {
	// 1. HTTP方法（大写）
	method = strings.ToUpper(method)
	
	// 2. URI路径
	if uri == "" {
		uri = "/"
	}
	
	// 3. 添加时间戳到参数中
	if params == nil {
		params = make(map[string]string)
	}
	params["timestamp"] = fmt.Sprintf("%d", timestamp)
	
	// 4. 对参数进行排序和编码
	canonicalQueryString := s.buildCanonicalQueryString(params)
	
	// 5. 构造最终的待签名字符串
	stringToSign := fmt.Sprintf("%s\n%s\n%s", method, uri, canonicalQueryString)
	
	return stringToSign
}

// buildCanonicalQueryString 构造规范化查询字符串
func (s *SignatureService) buildCanonicalQueryString(params map[string]string) string {
	if len(params) == 0 {
		return ""
	}
	
	// 1. 对参数名进行排序
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	
	// 2. 构造查询字符串
	var parts []string
	for _, key := range keys {
		value := params[key]
		// URL编码
		encodedKey := url.QueryEscape(key)
		encodedValue := url.QueryEscape(value)
		parts = append(parts, fmt.Sprintf("%s=%s", encodedKey, encodedValue))
	}
	
	return strings.Join(parts, "&")
}

// abs 返回整数的绝对值
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}
