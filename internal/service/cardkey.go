package service

import (
	"context"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/constant"
	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// CardKeyResponse 卡密响应
type CardKeyResponse struct {
	*model.CardKey
	// 充值相关信息
	ParentCard     *model.CardKey   `json:"parent_card,omitempty"`     // 充值来源卡密
	RechargedCards []*model.CardKey `json:"recharged_cards,omitempty"` // 来源卡密列表
}

type CardKeyService struct {
	db *gorm.DB
}

func NewCardKeyService(db *gorm.DB) *CardKeyService {
	return &CardKeyService{
		db: db,
	}
}

// GenerateCardKey 生成卡密
func (s *CardKeyService) GenerateCardKey(ctx context.Context, cardKey string, cardType int8, value int, maxDevices int, expiredAt time.Time) error {
	card := &model.CardKey{
		CardKey:    cardKey,
		Status:     constant.CardKeyStatusUnused,
		Type:       cardType,
		Value:      value,
		MaxDevices: maxDevices,
	}

	if err := s.db.WithContext(ctx).Create(card).Error; err != nil {
		return err
	}

	return nil
}

// GetCardKey 获取卡密
func (s *CardKeyService) GetCardKey(ctx context.Context, cardKey string) (*model.CardKey, error) {
	var card model.CardKey
	if err := s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&card).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.CARD_NOT_FOUND, "卡密不存在")
		}
		return nil, err
	}

	if card.Status == constant.CardKeyStatusDisabled {
		return nil, response.NewError(response.CARD_DISABLED, "卡密已被禁用")
	}

	if card.Status == constant.CardKeyStatusExpired {
		return nil, response.NewError(response.CARD_EXPIRED, "卡密已过期")
	}

	return &card, nil
}

// ListCardKeys 列出卡密
func (s *CardKeyService) ListCardKeys(ctx context.Context, page, pageSize int, status, cardType int8, cardKey string) ([]*CardKeyResponse, int64, error) {
	var total int64
	query := s.db.WithContext(ctx).Model(&model.CardKey{})

	if status > 0 {
		query = query.Where("status = ?", status)
	}
	if cardType > 0 {
		query = query.Where("type = ?", cardType)
	}
	if cardKey != "" {
		query = query.Where("card_key LIKE ?", "%"+cardKey+"%")
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	var cards []*model.CardKey
	if err := query.
		Order("FIELD(status, 2, 1, 3)"). // 已使用(2)排最前，未使用(1)其次，已过期(3)排最后
		Order("max_devices DESC").       // 按最大设备数降序
		Order("created_at DESC").        // 同等条件下按创建时间降序
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&cards).Error; err != nil {
		return nil, 0, err
	}

	// 构建响应
	var responses []*CardKeyResponse
	for _, card := range cards {
		responses = append(responses, &CardKeyResponse{
			CardKey: card,
		})
	}

	return responses, total, nil
}

// GetCardKeyWithDevices 获取卡密及其绑定的设备
func (s *CardKeyService) GetCardKeyWithDevices(ctx context.Context, cardKey string, page, pageSize int) (*CardKeyResponse, []*model.CardDeviceBinding, int64, error) {
	var card model.CardKey
	if err := s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&card).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil, 0, response.NewError(response.CARD_NOT_FOUND, "卡密不存在")
		}
		return nil, nil, 0, response.NewError(response.ERROR, "查询卡密失败")
	}

	// 查询绑定的设备
	var bindings []*model.CardDeviceBinding
	var total int64

	offset := (page - 1) * pageSize
	if err := s.db.WithContext(ctx).
		Joins("JOIN card_keys ON card_device_bindings.card_key = card_keys.card_key").
		Where("card_device_bindings.card_key = ? AND card_keys.status = ?", cardKey, constant.CardKeyStatusUsed).
		Offset(offset).Limit(pageSize).
		Find(&bindings).Error; err != nil {
		return nil, nil, 0, response.NewError(response.ERROR, "查询绑定设备失败")
	}

	if err := s.db.Joins("JOIN card_keys ON card_device_bindings.card_key = card_keys.card_key").
		Where("card_device_bindings.card_key = ? AND card_keys.status = ?", cardKey, constant.CardKeyStatusUsed).
		Model(&model.CardDeviceBinding{}).Count(&total).Error; err != nil {
		return nil, nil, 0, response.NewError(response.ERROR, "查询绑定设备数量失败")
	}

	// 查询充值来源信息
	var parentCard *model.CardKey
	if card.ParentID != nil && *card.ParentID != "" {
		// 如果当前卡是被充值的卡，查询其充值来源卡的信息
		parentCard = &model.CardKey{}
		if err := s.db.WithContext(ctx).Where("card_key = ?", *card.ParentID).First(parentCard).Error; err != nil {
			// 如果查询失败，设置为nil
			parentCard = nil
		}
	}

	var rechargedCards []*model.CardKey
	if err := s.db.WithContext(ctx).Where("parent_id = ?", cardKey).Find(&rechargedCards).Error; err != nil {
		// 如果查询失败，设置为空数组
		rechargedCards = make([]*model.CardKey, 0)
	}

	response := &CardKeyResponse{
		CardKey:        &card,
		ParentCard:     parentCard,
		RechargedCards: rechargedCards,
	}

	return response, bindings, total, nil
}

// UpdateCardStatus 更新卡密状态
func (s *CardKeyService) UpdateCardStatus(ctx context.Context, cardKey string, status int8) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if status == constant.CardKeyStatusUsed {
		now := time.Now()
		updates["used_at"] = &now
	}

	if err := s.db.WithContext(ctx).Model(&model.CardKey{}).Where("card_key = ?", cardKey).Updates(updates).Error; err != nil {
		return err
	}

	return nil
}

// DeleteCardKey 删除卡密
func (s *CardKeyService) DeleteCardKey(ctx context.Context, cardKey string) error {
	if err := s.db.WithContext(ctx).Where("card_key = ?", cardKey).Delete(&model.CardKey{}).Error; err != nil {
		return err
	}
	return nil
}

// BatchDeleteCardKeys 删除卡密（支持批量）
func (s *CardKeyService) BatchDeleteCardKeys(ctx context.Context, cardKeys []string) error {
	if err := s.db.WithContext(ctx).Where("card_key IN ?", cardKeys).Delete(&model.CardKey{}).Error; err != nil {
		return err
	}
	return nil
}

// UpdateExpiredCards 更新过期的卡密状态
func (s *CardKeyService) UpdateExpiredCards() error {
	// 更新已过期但状态未更新的卡密
	result := s.db.Model(&model.CardKey{}).
		Where("status = ? AND expired_at < ? AND type = ?",
			constant.CardKeyStatusUsed,
			time.Now(),
			constant.CardKeyTypeTime).
		Updates(map[string]interface{}{
			"status": constant.CardKeyStatusExpired,
		})

	if result.Error != nil {
		return response.NewError(response.ERROR, "更新过期卡密状态失败")
	}

	return nil
}

// RechargeCardKey 以新卡密给已有卡密充值
func (s *CardKeyService) RechargeCardKey(ctx context.Context, targetCardKey string, newCardKey string) error {
	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 查询目标卡密
		var targetCard model.CardKey
		if err := tx.Where("card_key = ?", targetCardKey).First(&targetCard).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return response.NewError(response.CARD_NOT_FOUND, "目标卡密不存在")
			}
			return response.NewError(response.ERROR, "查询目标卡密失败")
		}

		// 查询新卡密
		var newCard model.CardKey
		if err := tx.Where("card_key = ?", newCardKey).First(&newCard).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return response.NewError(response.CARD_NOT_FOUND, "充值卡密不存在")
			}
			return response.NewError(response.ERROR, "查询充值卡密失败")
		}

		// 检查新卡密是否未使用
		if newCard.Status != constant.CardKeyStatusUnused {
			return response.NewError(response.CARD_USED, "充值卡密已被使用或禁用")
		}

		// 检查两张卡密类型是否一致
		if targetCard.Type != newCard.Type {
			return response.NewError(response.INVALID_PARAMS, "卡密类型不匹配，无法充值")
		}

		// 新增：禁止给已禁用的卡密充值
		if targetCard.Status == constant.CardKeyStatusDisabled {
			return response.NewError(response.CARD_USED, "目标卡密已被禁用，无法充值")
		}

		// 检查两张卡密的最大设备数是否一致
		if targetCard.MaxDevices != newCard.MaxDevices {
			return response.NewError(response.INVALID_PARAMS, "卡密最大设备数不匹配，无法充值")
		}

		// 更新目标卡密
		updates := map[string]interface{}{}

		if targetCard.Type == constant.CardKeyTypeTime {
			if targetCard.Status == constant.CardKeyStatusUnused {
				// 未使用的卡，直接增加天数
				updates["value"] = targetCard.Value + newCard.Value
			} else if targetCard.Status == constant.CardKeyStatusExpired {
				// 已过期的卡，从当前时间开始计算新的过期时间
				newExpiredAt := time.Now().Add(time.Duration(newCard.Value) * 24 * time.Hour)
				updates["expired_at"] = newExpiredAt
				updates["status"] = constant.CardKeyStatusUsed // 更新状态为已使用
			} else {
				// 已使用未过期的卡，在原过期时间基础上延长
				newExpiredAt := targetCard.ExpiredAt.Add(time.Duration(newCard.Value) * 24 * time.Hour)
				updates["expired_at"] = newExpiredAt
			}
		} else if targetCard.Type == constant.CardKeyTypeCount {
			// 次数卡增加次数
			updates["value"] = targetCard.Value + newCard.Value

			// 如果卡密已过期，更新状态为已使用
			if targetCard.Status == constant.CardKeyStatusExpired {
				updates["status"] = constant.CardKeyStatusUsed
			}
		}

		// 记录充值时间
		now := time.Now()
		updates["recharge_time"] = now

		// 更新目标卡密
		if err := tx.Model(&model.CardKey{}).Where("card_key = ?", targetCardKey).Updates(updates).Error; err != nil {
			return response.NewError(response.ERROR, "更新目标卡密失败")
		}

		// 将新卡密状态设置为已禁用，并记录其被用于充值的目标卡密
		newCardUpdates := map[string]interface{}{
			"status":        constant.CardKeyStatusDisabled,
			"parent_id":     targetCardKey,
			"recharge_time": now,
		}
		if err := tx.Model(&model.CardKey{}).Where("card_key = ?", newCardKey).Updates(newCardUpdates).Error; err != nil {
			return response.NewError(response.ERROR, "更新充值卡密状态失败")
		}

		return nil
	})
}
