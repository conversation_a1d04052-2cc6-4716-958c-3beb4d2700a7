package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"goadmin/config"
	"goadmin/internal/model"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"

	"gorm.io/gorm"
)

type GitHubService struct {
	token  string // GitHub API token
	client *http.Client
	db     *gorm.DB
	cfg    *config.Config
}

// GetCurrentEnvironment 获取当前环境
func (s *GitHubService) GetCurrentEnvironment() string {
	return s.cfg.Server.Env
}

// GetExpectedBranchForEnvironment 获取指定环境对应的分支
func (s *GitHubService) GetExpectedBranchForEnvironment(env string) string {
	// 如果配置了分支映射，则使用配置的映射关系
	if s.cfg.GitHub.BranchMapping != nil {
		if branch, ok := s.cfg.GitHub.BranchMapping[env]; ok {
			return branch
		}
	}

	// 默认映射关系 - 环境名称直接对应分支名称
	switch env {
	case "main":
		return "main"
	case "online":
		return "online"
	default:
		return ""
	}
}

// GetWebhookSecret 获取Webhook秘钥
func (s *GitHubService) GetWebhookSecret() string {
	return s.cfg.GitHub.WebhookSecret
}

func NewGitHubService(cfg *config.Config, db *gorm.DB) *GitHubService {
	return &GitHubService{
		token:  cfg.GitHub.Token,
		client: &http.Client{},
		db:     db,
		cfg:    cfg,
	}
}

type GitHubContentResponse struct {
	Content  string `json:"content"`
	Encoding string `json:"encoding"`
}

// fetchFromGitHub makes an HTTP request to the GitHub API
func (s *GitHubService) fetchFromGitHub(ctx context.Context, url string) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, response.NewError(response.ERROR, "创建请求失败")
	}

	req.Header.Set("Accept", "application/vnd.github.v3+json")

	// Add GitHub token if available
	if s.token != "" {
		req.Header.Set("Authorization", fmt.Sprintf("token %s", s.token))
	}

	resp, err := s.client.Do(req)
	if err != nil {
		logger.Error(ctx, "获取GitHub内容失败: %v", err)
		return nil, response.NewError(response.ERROR, "获取GitHub内容失败")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		logger.Error(ctx, "GitHub API返回错误: %d, 响应: %s", resp.StatusCode, string(body))
		return nil, response.NewError(response.ERROR, fmt.Sprintf("GitHub API返回错误: %d", resp.StatusCode))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error(ctx, "读取响应内容失败: %v", err)
		return nil, response.NewError(response.ERROR, "读取响应内容失败")
	}

	return body, nil
}

// ScriptType defines the type of script to fetch
type ScriptType string

const (
	ScriptTypeAutoJS      ScriptType = "autojs"
	ScriptTypeAutoJSLogin ScriptType = "autojs_login"
	ScriptTypeHamibot     ScriptType = "hamibot"
	ScriptTypeBot         ScriptType = "bot"
	ScriptTypeBotUI       ScriptType = "botui"
)

// BranchType defines the GitHub branch to use
type BranchType string

const (
	BranchOnline BranchType = "online"
	BranchMain   BranchType = "main"
)

// GetGitHubContent fetches content from GitHub repository
func (s *GitHubService) GetGitHubContent(ctx context.Context, scriptType ScriptType, branch BranchType) (string, error) {
	var path string

	switch scriptType {
	case ScriptTypeHamibot:
		path = "dist/app/hamibotjs.js"
	case ScriptTypeAutoJS:
		path = "dist/app/autojs.js"
	case ScriptTypeAutoJSLogin:
		path = "dist/app/autojs_login.js"
	case ScriptTypeBot:
		path = "dist/app/bot.js"
	case ScriptTypeBotUI:
		path = "dist/app/botui.js"
	default:
		return "", response.NewError(response.ERROR, "无效的脚本类型")
	}

	// 构建URL，包含分支信息
	url := fmt.Sprintf("https://api.github.com/repos/luckyleesir/autojs/contents/%s?ref=%s", path, branch)

	body, err := s.fetchFromGitHub(ctx, url)
	if err != nil {
		return "", err
	}

	var contentResp GitHubContentResponse
	if err := json.Unmarshal(body, &contentResp); err != nil {
		logger.Error(ctx, "解析响应内容失败: %v, 响应: %s", err, string(body))
		return "", response.NewError(response.ERROR, "解析响应内容失败")
	}

	if contentResp.Encoding != "base64" {
		logger.Error(ctx, "不支持的内容编码: %s", contentResp.Encoding)
		return "", response.NewError(response.ERROR, "不支持的内容编码")
	}

	// Return the base64 content directly without decoding
	return contentResp.Content, nil
}

// GetAutoJSContent fetches AutoJS content (for backward compatibility)
func (s *GitHubService) GetAutoJSContent(ctx context.Context) (string, error) {
	// AutoJS 始终使用online分支
	return s.GetScriptFromDB(ctx, ScriptTypeAutoJS, BranchOnline)
}

// GetAutoJSContentByBranch fetches AutoJS content from a specific branch
func (s *GitHubService) GetAutoJSContentByBranch(ctx context.Context, branch BranchType) (string, error) {
	// 添加日志记录当前使用的分支
	logger.Info(ctx, "获取AutoJS脚本，使用分支: %s", branch)

	return s.GetScriptFromDB(ctx, ScriptTypeAutoJS, branch)
}

// GetAutoJSLoginContentByBranch fetches AutoJS login content from a specific branch
func (s *GitHubService) GetAutoJSLoginContentByBranch(ctx context.Context, branch BranchType) (string, error) {
	// 添加日志记录当前使用的分支
	logger.Info(ctx, "获取AutoJS登录脚本，使用分支: %s", branch)

	return s.GetScriptFromDB(ctx, ScriptTypeAutoJSLogin, branch)
}

// GetHamibotContent fetches Hamibot content with branch selection
func (s *GitHubService) GetHamibotContent(ctx context.Context, isProduction bool) (string, error) {
	branch := BranchMain
	if isProduction {
		branch = BranchOnline
	}

	// 添加日志记录当前使用的分支
	logger.Info(ctx, "获取Hamibot脚本，使用分支: %s, isProduction: %v", branch, isProduction)

	return s.GetScriptFromDB(ctx, ScriptTypeHamibot, branch)
}

// GetBotContent fetches Bot content with branch selection
func (s *GitHubService) GetBotContent(ctx context.Context, isProduction bool) (string, error) {
	branch := BranchMain
	if isProduction {
		branch = BranchOnline
	}

	// 添加日志记录当前使用的分支
	logger.Info(ctx, "获取Bot脚本，使用分支: %s, isProduction: %v", branch, isProduction)

	return s.GetScriptFromDB(ctx, ScriptTypeBot, branch)
}

// GetBotUIContent fetches BotUI content with branch selection
func (s *GitHubService) GetBotUIContent(ctx context.Context, isProduction bool) (string, error) {
	branch := BranchMain
	if isProduction {
		branch = BranchOnline
	}

	// 添加日志记录当前使用的分支
	logger.Info(ctx, "获取BotUI脚本，使用分支: %s, isProduction: %v", branch, isProduction)

	return s.GetScriptFromDB(ctx, ScriptTypeBotUI, branch)
}

// GetScriptFromDB 从数据库获取脚本内容
func (s *GitHubService) GetScriptFromDB(ctx context.Context, scriptType ScriptType, branch BranchType) (string, error) {
	var script model.GitHubScript

	result := s.db.WithContext(ctx).Where("script_type = ? AND branch = ?", scriptType, branch).Order("updated_at DESC").First(&script)
	if result.Error != nil {
		logger.Error(ctx, "从数据库获取脚本失败: %v, 尝试从GitHub获取", result.Error)
		// 如果数据库中没有找到，则从GitHub获取
		return s.GetGitHubContent(ctx, scriptType, branch)
	}

	return script.Content, nil
}

// SaveScriptToDB 保存脚本内容到数据库
func (s *GitHubService) SaveScriptToDB(ctx context.Context, scriptType ScriptType, branch BranchType, fileName string, content string) error {
	script := model.GitHubScript{
		Branch:     string(branch),
		FileName:   fileName,
		ScriptType: string(scriptType),
		Content:    content,
	}

	result := s.db.WithContext(ctx).Where("script_type = ? AND branch = ?", scriptType, branch).FirstOrCreate(&script)
	if result.Error != nil {
		logger.Error(ctx, "保存脚本到数据库失败: %v", result.Error)
		return result.Error
	}

	// 如果记录已存在，更新内容
	if result.RowsAffected == 0 {
		result = s.db.WithContext(ctx).Model(&model.GitHubScript{}).Where("script_type = ? AND branch = ?", scriptType, branch).Updates(
			map[string]interface{}{
				"content":    content,
				"file_name":  fileName,
				"updated_at": time.Now(),
			})
		if result.Error != nil {
			logger.Error(ctx, "更新脚本内容失败: %v", result.Error)
			return result.Error
		}
	}

	return nil
}

// GitHubWebhookPayload GitHub webhook推送事件的payload结构
type GitHubWebhookPayload struct {
	Ref        string `json:"ref"`
	Repository struct {
		Name string `json:"name"`
	} `json:"repository"`
	Commits []struct {
		Added    []string `json:"added"`
		Modified []string `json:"modified"`
		Removed  []string `json:"removed"`
	} `json:"commits"`
}

// updateScript 更新脚本
func (s *GitHubService) updateScript(ctx context.Context, scriptType ScriptType, branch BranchType, file string) error {
	// 从GitHub获取脚本内容
	content, err := s.GetGitHubContent(ctx, scriptType, branch)
	if err != nil {
		logger.Error(ctx, "获取脚本内容失败: %v", err)
		return err
	}

	// 保存到数据库
	if err := s.SaveScriptToDB(ctx, scriptType, branch, file, content); err != nil {
		logger.Error(ctx, "保存脚本到数据库失败: %v", err)
		return err
	}

	logger.Info(ctx, "成功更新脚本: %s, 分支: %s", file, branch)
	return nil
}

// ProcessWebhook 处理来自GitHub的webhook请求，支持分支检查
func (s *GitHubService) ProcessWebhook(ctx context.Context, r *http.Request, expectedBranch string) (bool, error) {
	// 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Error(ctx, "读取webhook请求体失败: %v", err)
		return false, response.NewError(response.ERROR, "读取请求失败")
	}
	defer r.Body.Close()

	// 验证签名
	if s.cfg.GitHub.WebhookSecret != "" {
		signature := r.Header.Get("X-Hub-Signature-256")
		if signature == "" {
			logger.Error(ctx, "缺少签名信息")
			return false, response.NewError(response.ERROR, "缺少签名信息")
		}

		// 计算HMAC签名
		mac := hmac.New(sha256.New, []byte(s.cfg.GitHub.WebhookSecret))
		mac.Write(body)
		expectedMAC := "sha256=" + hex.EncodeToString(mac.Sum(nil))

		// 验证签名
		if !hmac.Equal([]byte(signature), []byte(expectedMAC)) {
			logger.Error(ctx, "签名验证失败")
			return false, response.NewError(response.ERROR, "签名验证失败")
		}
		logger.Info(ctx, "webhook签名验证成功")
	}

	// 解析payload
	var payload GitHubWebhookPayload
	if err := json.Unmarshal(body, &payload); err != nil {
		logger.Error(ctx, "解析webhook payload失败: %v", err)
		return false, response.NewError(response.ERROR, "解析请求失败")
	}

	// 确定分支
	refParts := strings.Split(payload.Ref, "/")
	if len(refParts) < 3 {
		logger.Error(ctx, "无效的ref格式: %s", payload.Ref)
		return false, response.NewError(response.ERROR, "无效的分支信息")
	}

	branchName := refParts[len(refParts)-1]

	// 检查分支是否匹配期望分支
	if branchName != expectedBranch {
		logger.Info(ctx, "分支 %s 不匹配期望分支 %s，不处理", branchName, expectedBranch)
		return false, nil
	}

	var branch BranchType
	switch branchName {
	case "online":
		branch = BranchOnline
	case "main":
		branch = BranchMain
	default:
		logger.Error(ctx, "不支持的分支: %s", branchName)
		return false, response.NewError(response.ERROR, "不支持的分支")
	}

	// 检查是否有脚本文件被修改
	scriptFiles := make(map[string]bool)
	for _, commit := range payload.Commits {
		for _, file := range append(commit.Added, commit.Modified...) {
			if strings.HasPrefix(file, "dist/app/") && strings.HasSuffix(file, ".js") {
				scriptFiles[file] = true
			}
		}
	}

	// 如果有脚本文件被修改，则获取并保存到数据库
	for file := range scriptFiles {
		var scriptType ScriptType
		switch file {
		case "dist/app/hamibotjs.js":
			scriptType = ScriptTypeHamibot
		case "dist/app/autojs.js":
			scriptType = ScriptTypeAutoJS
		case "dist/app/autojs_login.js":
			scriptType = ScriptTypeAutoJSLogin
		case "dist/app/bot.js":
			scriptType = ScriptTypeBot
		case "dist/app/botui.js":
			scriptType = ScriptTypeBotUI
		default:
			continue // 跳过不支持的脚本类型
		}

		// 更新脚本
		if err := s.updateScript(ctx, scriptType, branch, file); err != nil {
			logger.Error(ctx, "更新脚本失败: %v", err)
			continue
		}
	}

	return true, nil
}

// GetScriptList 获取脚本列表
func (s *GitHubService) GetScriptList(ctx context.Context) ([]model.GitHubScript, error) {
	var scripts []model.GitHubScript
	result := s.db.WithContext(ctx).Select("id, script_type, branch, file_name, updated_at").Find(&scripts)
	if result.Error != nil {
		logger.Error(ctx, "获取脚本列表失败: %v", result.Error)
		return nil, result.Error
	}
	return scripts, nil
}

// UpdateScript 手动更新脚本
func (s *GitHubService) UpdateScript(ctx context.Context, scriptType ScriptType, branch BranchType) error {
	var path string

	switch scriptType {
	case ScriptTypeHamibot:
		path = "dist/app/hamibotjs.js"
	case ScriptTypeAutoJS:
		path = "dist/app/autojs.js"
	case ScriptTypeAutoJSLogin:
		path = "dist/app/autojs_login.js"
	case ScriptTypeBot:
		path = "dist/app/bot.js"
	case ScriptTypeBotUI:
		path = "dist/app/botui.js"
	default:
		return response.NewError(response.ERROR, "无效的脚本类型")
	}

	return s.updateScript(ctx, scriptType, branch, path)
}
