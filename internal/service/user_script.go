package service

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// UserScriptService 用户脚本服务
type UserScriptService struct {
	db *gorm.DB
}

// NewUserScriptService 创建用户脚本服务
func NewUserScriptService(db *gorm.DB) *UserScriptService {
	return &UserScriptService{
		db: db,
	}
}

// GetUserScripts 获取用户的脚本列表
func (s *UserScriptService) GetUserScripts(ctx context.Context, userID uint) ([]*model.UserScript, error) {
	var scripts []*model.UserScript

	if err := s.db.WithContext(ctx).
		Where("user_id = ? AND status = ?", userID, model.UserScriptStatusEnabled).
		Preload("MarketScript").
		Order("created_at DESC").
		Find(&scripts).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询用户脚本失败")
	}

	return scripts, nil
}

// GetUserScriptByID 根据ID获取用户脚本
func (s *UserScriptService) GetUserScriptByID(ctx context.Context, userID, scriptID uint) (*model.UserScript, error) {
	var script model.UserScript

	if err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", scriptID, userID).
		Preload("MarketScript").
		First(&script).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "脚本不存在")
		}
		return nil, response.NewError(response.ERROR, "查询脚本失败")
	}

	return &script, nil
}

// GetUserScriptByMarketScriptID 根据市场脚本ID获取用户脚本
func (s *UserScriptService) GetUserScriptByMarketScriptID(ctx context.Context, userID, marketScriptID uint) (*model.UserScript, error) {
	var script model.UserScript

	if err := s.db.WithContext(ctx).
		Where("user_id = ? AND market_script_id = ? AND status = ?", userID, marketScriptID, model.UserScriptStatusEnabled).
		Preload("MarketScript").
		First(&script).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "脚本不存在")
		}
		return nil, response.NewError(response.ERROR, "查询脚本失败")
	}

	return &script, nil
}

// GetScriptPackageByID 根据套餐ID获取套餐信息
func (s *UserScriptService) GetScriptPackageByID(ctx context.Context, packageID uint) (*model.ScriptPackage, error) {
	var packageInfo model.ScriptPackage

	if err := s.db.WithContext(ctx).
		Where("id = ?", packageID).
		First(&packageInfo).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "套餐不存在")
		}
		return nil, response.NewError(response.ERROR, "查询套餐失败")
	}

	return &packageInfo, nil
}

// CreateUserScript 创建用户脚本
func (s *UserScriptService) CreateUserScript(ctx context.Context, userScript *model.UserScript) error {
	// 检查是否已存在相同的用户脚本
	var existing model.UserScript
	if err := s.db.WithContext(ctx).
		Where("user_id = ? AND market_script_id = ?", userScript.UserID, userScript.MarketScriptID).
		First(&existing).Error; err == nil {
		return response.NewError(response.ERROR, "脚本已存在")
	}

	// 如果没有设置MaxDevices，默认为1
	if userScript.MaxDevices <= 0 {
		userScript.MaxDevices = 1
	}

	if err := s.db.WithContext(ctx).Create(userScript).Error; err != nil {
		return response.NewError(response.ERROR, "创建用户脚本失败")
	}

	return nil
}

// UpdateUserScriptStatus 更新用户脚本状态
func (s *UserScriptService) UpdateUserScriptStatus(ctx context.Context, userID, scriptID uint, status int8) error {
	result := s.db.WithContext(ctx).
		Model(&model.UserScript{}).
		Where("id = ? AND user_id = ?", scriptID, userID).
		Update("status", status)

	if result.Error != nil {
		return response.NewError(response.ERROR, "更新脚本状态失败")
	}

	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "脚本不存在")
	}

	return nil
}

// DeleteUserScript 删除用户脚本（软删除，更新状态为已删除）
func (s *UserScriptService) DeleteUserScript(ctx context.Context, userID, scriptID uint) error {
	result := s.db.WithContext(ctx).
		Model(&model.UserScript{}).
		Where("id = ? AND user_id = ?", scriptID, userID).
		Update("status", model.UserScriptStatusDeleted)

	if result.Error != nil {
		return response.NewError(response.ERROR, "删除脚本失败")
	}

	if result.RowsAffected == 0 {
		return response.NewError(response.ERROR, "脚本不存在")
	}

	return nil
}

// GetUserScriptList 获取用户脚本列表（带分页）
func (s *UserScriptService) GetUserScriptList(ctx context.Context, page, pageSize int, userID *uint, keyword string, status *int8) ([]*model.UserScript, int64, error) {
	var scripts []*model.UserScript
	var total int64

	query := s.db.WithContext(ctx).Model(&model.UserScript{}).Preload("User").Preload("MarketScript")

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	if keyword != "" {
		query = query.Where("name LIKE ?", "%"+keyword+"%")
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询脚本总数失败")
	}

	// 查询脚本列表
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&scripts).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询脚本列表失败")
	}

	return scripts, total, nil
}

// GetScriptRunData 获取脚本运行数据
func (s *UserScriptService) GetScriptRunData(ctx context.Context, scriptID, userID uint) (*ScriptRunData, error) {
	// 获取用户脚本
	userScript, err := s.GetUserScriptByID(ctx, userID, scriptID)
	if err != nil {
		return nil, err
	}

	// 获取默认配置
	var defaultConfig model.UserScriptConfig
	if err := s.db.WithContext(ctx).
		Where("script_id = ? AND is_active = ?", scriptID, true).
		First(&defaultConfig).Error; err != nil && err != gorm.ErrRecordNotFound {
		return nil, response.NewError(response.ERROR, "查询脚本配置失败")
	}

	// 构建运行数据
	runData := &ScriptRunData{
		ScriptID:      userScript.ID,
		ScriptName:    userScript.Name,
		ScriptContent: userScript.Content,
		Version:       userScript.Version,
	}

	// 如果有默认配置，添加到运行数据中
	if defaultConfig.ID > 0 {
		var configValues model.ConfigValue
		if err := json.Unmarshal([]byte(defaultConfig.ConfigValues), &configValues); err == nil {
			runData.ConfigName = defaultConfig.ConfigName
			runData.Config = configValues
		}
	}

	return runData, nil
}

// CheckScriptUpdate 检查脚本是否有更新
func (s *UserScriptService) CheckScriptUpdate(ctx context.Context, userID, scriptID uint) (*ScriptUpdateInfo, error) {
	// 获取用户脚本
	userScript, err := s.GetUserScriptByID(ctx, userID, scriptID)
	if err != nil {
		return nil, err
	}

	// 获取市场脚本的最新版本
	var marketScript model.MarketScript
	if err := s.db.WithContext(ctx).
		Where("id = ? AND status = ?", userScript.MarketScriptID, 1).
		First(&marketScript).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "市场脚本不存在或已下架")
		}
		return nil, response.NewError(response.ERROR, "查询市场脚本失败")
	}

	// 比较版本
	hasUpdate := userScript.Version != marketScript.Version

	return &ScriptUpdateInfo{
		UserScriptID:   userScript.ID,
		MarketScriptID: marketScript.ID,
		CurrentVersion: userScript.Version,
		LatestVersion:  marketScript.Version,
		HasUpdate:      hasUpdate,
		UpdateContent:  marketScript.Description,
		UpdateTime:     marketScript.UpdatedAt,
	}, nil
}

// UpdateUserScript 更新用户脚本到最新版本
func (s *UserScriptService) UpdateUserScript(ctx context.Context, userID, scriptID uint) error {
	// 获取用户脚本
	userScript, err := s.GetUserScriptByID(ctx, userID, scriptID)
	if err != nil {
		return err
	}

	// 获取市场脚本的最新版本
	var marketScript model.MarketScript
	if err := s.db.WithContext(ctx).
		Where("id = ? AND status = ?", userScript.MarketScriptID, 1).
		First(&marketScript).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return response.NewError(response.ERROR, "市场脚本不存在或已下架")
		}
		return response.NewError(response.ERROR, "查询市场脚本失败")
	}

	// 检查是否需要更新
	if userScript.Version == marketScript.Version {
		return response.NewError(response.ERROR, "脚本已是最新版本")
	}

	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新用户脚本
	updates := map[string]interface{}{
		"content":        marketScript.Content,
		"config":         marketScript.Config,
		"default_values": marketScript.DefaultValues,
		"version":        marketScript.Version,
		"updated_at":     time.Now(),
	}

	if err := tx.Model(userScript).Updates(updates).Error; err != nil {
		tx.Rollback()
		return response.NewError(response.ERROR, "更新脚本失败")
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return response.NewError(response.ERROR, "提交更新失败")
	}

	return nil
}

// ScriptUpdateInfo 脚本更新信息
type ScriptUpdateInfo struct {
	UserScriptID   uint      `json:"user_script_id"`
	MarketScriptID uint      `json:"market_script_id"`
	CurrentVersion string    `json:"current_version"`
	LatestVersion  string    `json:"latest_version"`
	HasUpdate      bool      `json:"has_update"`
	UpdateContent  string    `json:"update_content"`
	UpdateTime     time.Time `json:"update_time"`
}

// ScriptRunData 脚本运行数据结构
type ScriptRunData struct {
	ScriptID      uint              `json:"script_id"`
	ScriptName    string            `json:"script_name"`
	ScriptContent string            `json:"script_content"`
	Version       string            `json:"version"`
	ConfigName    string            `json:"config_name,omitempty"`
	Config        model.ConfigValue `json:"config,omitempty"`
}
