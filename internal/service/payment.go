package service

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

// PaymentService 支付服务
type PaymentService struct {
	db                  *gorm.DB
	zpayService         *ZPAYService
	marketScriptService *MarketScriptService // 注入市场脚本服务
	// 并发控制：防止同一订单的重复回调处理
	processingOrders sync.Map
}

// NewPaymentService 创建支付服务
func NewPaymentService(db *gorm.DB, zpayService *ZPAYService, marketScriptService *MarketScriptService) *PaymentService {
	return &PaymentService{
		db:                  db,
		zpayService:         zpayService,
		marketScriptService: marketScriptService,
	}
}

// GetScriptPackages 获取脚本套餐列表
func (s *PaymentService) GetScriptPackages(ctx context.Context, scriptID uint) ([]*model.ScriptPackage, error) {
	var packages []*model.ScriptPackage

	err := s.db.WithContext(ctx).
		Where("market_script_id = ? AND status = ?", scriptID, 1).
		Preload("MarketScript").
		Order("price ASC").
		Find(&packages).Error

	if err != nil {
		return nil, err
	}

	return packages, nil
}

// CreatePaymentOrder 创建支付订单
func (s *PaymentService) CreatePaymentOrder(ctx context.Context, userID, packageID uint, payType string) (*model.PaymentOrder, error) {
	// 获取套餐信息
	var pkg model.ScriptPackage
	if err := s.db.WithContext(ctx).Where("id = ? AND status = ?", packageID, 1).First(&pkg).Error; err != nil {
		return nil, response.NewError(response.INVALID_PARAMS, "套餐不存在或已禁用")
	}

	// 新增：判断用户是否已有未过期的有效脚本
	var activeScript model.UserScript
	now := time.Now()
	err := s.db.WithContext(ctx).Where("user_id = ? AND market_script_id = ? AND status = ? AND (expired_at IS NULL OR expired_at > ?)",
		userID, pkg.MarketScriptID, model.UserScriptStatusEnabled, now).First(&activeScript).Error
	if err == nil {
		return nil, response.NewError(response.ERR_SCRIPT_ORDER_EXISTS, "当前脚本已安装且未过期，不允许重复下单")
	}
	if err != gorm.ErrRecordNotFound {
		return nil, response.NewError(response.ERROR, "查询脚本状态失败")
	}

	// 生成订单号
	orderNo := fmt.Sprintf("SP%d%d", userID, time.Now().UnixNano())

	// 创建订单
	order := &model.PaymentOrder{
		OrderNo:   orderNo,
		UserID:    userID,
		PackageID: packageID,
		Amount:    pkg.Price,
		PayType:   payType,
		Status:    0,                                // 未支付
		ExpiredAt: time.Now().Add(30 * time.Minute), // 30分钟过期
	}

	if err := s.db.WithContext(ctx).Create(order).Error; err != nil {
		return nil, err
	}

	// 调用ZPAY创建支付订单
	logger.Info(ctx, "开始调用ZPAY创建订单 - order_no: %s, amount: %.2f, name: %s, pay_type: %s",
		order.OrderNo, order.Amount, fmt.Sprintf("脚本套餐-%s", pkg.Name), payType)

	zpayResp, err := s.zpayService.CreateOrder(
		order.OrderNo,
		fmt.Sprintf("%.2f", order.Amount),
		fmt.Sprintf("脚本套餐-%s", pkg.Name),
		payType,
	)

	if err != nil {
		logger.Error(ctx, "ZPAY创建订单失败: %v", err)
		// 删除本地订单
		s.db.WithContext(ctx).Delete(order)
		return nil, fmt.Errorf("创建支付订单失败: %v", err)
	}

	logger.Info(ctx, "ZPAY创建订单成功 - trade_no: %s, pay_url: %s",
		zpayResp.TradeNo, zpayResp.PayURL)

	// 更新支付平台信息
	order.PaymentPlatform = model.PaymentPlatformZPAY
	order.ThirdPartyOrderNo = zpayResp.TradeNo
	// 保存ZPAY响应的JSON格式
	notifyJSON, _ := json.Marshal(zpayResp)
	order.NotifyData = string(notifyJSON)
	if err := s.db.WithContext(ctx).Save(order).Error; err != nil {
		return nil, err
	}

	// 创建订单后，异步轮询ZPAY订单状态，支付成功后自动处理业务
	orderNoCopy := order.OrderNo
	paymentService := s
	ctxCopy := context.Background() // 用新ctx避免主流程阻塞

	// 启动异步轮询
	go func() {
		maxTries := 20
		baseInterval := 5 * time.Second
		time.Sleep(30 * time.Second)
		for i := 0; i < maxTries; i++ {
			ok, _ := paymentService.OnOrderPaidAndInstallScript(ctxCopy, orderNoCopy)
			if ok {
				break // 已支付/已安装，退出循环
			}
			interval := time.Duration(i+1) * baseInterval
			time.Sleep(interval)
		}
	}()

	// 返回包含支付URL的响应
	orderResponse := &model.PaymentOrder{
		ID:                order.ID,
		OrderNo:           order.OrderNo,
		UserID:            order.UserID,
		PackageID:         order.PackageID,
		Amount:            order.Amount,
		PayType:           order.PayType,
		Status:            order.Status,
		PaymentPlatform:   order.PaymentPlatform,
		ThirdPartyOrderNo: order.ThirdPartyOrderNo,
		NotifyData:        order.NotifyData,
		PaidAt:            order.PaidAt,
		ExpiredAt:         order.ExpiredAt,
		CreatedAt:         order.CreatedAt,
		UpdatedAt:         order.UpdatedAt,
		// 添加ZPAY支付URL
		PayURL: zpayResp.PayURL,
	}

	return orderResponse, nil
}

// HandlePaymentNotify 处理支付回调
// 根据ZPAY官方文档要求实现：
// 1. 返回"success"字符串
// 2. 处理重复通知
// 3. 使用并发控制避免函数重入
// 4. 验证签名和金额
// 5. 5秒内返回响应
func (s *PaymentService) HandlePaymentNotify(ctx context.Context, params map[string]string) error {
	// 设置超时上下文（ZPAY要求5秒内返回）
	timeoutCtx, cancel := context.WithTimeout(ctx, 4*time.Second)
	defer cancel()

	logger.Info(ctx, "收到ZPAY支付回调 - 参数: %+v", params)

	// 1. 验证回调签名
	notifyData, err := s.zpayService.VerifyNotify(params)
	if err != nil {
		logger.Error(ctx, "ZPAY回调签名验证失败: %v", err)
		return fmt.Errorf("回调验证失败: %v", err)
	}

	logger.Info(ctx, "ZPAY回调签名验证成功 - 订单号: %s, 状态: %s, 金额: %s",
		notifyData.OutTradeNo, notifyData.Status, notifyData.Money)

	// 2. 查询订单
	var order model.PaymentOrder
	if err := s.db.WithContext(timeoutCtx).Where("order_no = ?", notifyData.OutTradeNo).First(&order).Error; err != nil {
		logger.Error(ctx, "订单不存在: %s, 错误: %v", notifyData.OutTradeNo, err)
		return fmt.Errorf("订单不存在: %v", err)
	}

	// 3. 检查订单状态 - 如果已处理过，直接返回成功
	if order.Status == 1 {
		logger.Info(ctx, "订单已支付，忽略重复回调 - 订单号: %s", notifyData.OutTradeNo)
		return nil
	}

	// 4. 并发控制：防止同一订单的重复处理
	orderKey := fmt.Sprintf("order_%s", notifyData.OutTradeNo)
	if _, loaded := s.processingOrders.LoadOrStore(orderKey, true); loaded {
		logger.Warn(ctx, "订单正在处理中，忽略重复回调 - 订单号: %s", notifyData.OutTradeNo)
		return nil
	}
	defer s.processingOrders.Delete(orderKey)

	// 5. 检查支付状态
	if notifyData.Status != "TRADE_SUCCESS" {
		logger.Warn(ctx, "支付未成功，状态: %s - 订单号: %s", notifyData.Status, notifyData.OutTradeNo)
		return fmt.Errorf("支付未成功，状态: %s", notifyData.Status)
	}

	// 6. 验证金额
	amount, err := strconv.ParseFloat(notifyData.Money, 64)
	if err != nil {
		logger.Error(ctx, "金额格式错误: %s - 订单号: %s", notifyData.Money, notifyData.OutTradeNo)
		return fmt.Errorf("金额格式错误: %v", err)
	}

	if amount != order.Amount {
		logger.Error(ctx, "金额不匹配 - 期望: %.2f, 实际: %.2f, 订单号: %s",
			order.Amount, amount, notifyData.OutTradeNo)
		return fmt.Errorf("金额不匹配 - 期望: %.2f, 实际: %.2f", order.Amount, amount)
	}

	logger.Info(ctx, "金额验证通过 - 订单号: %s, 金额: %.2f", notifyData.OutTradeNo, amount)

	// 7. 拉取ZPAY订单完成时间
	// var completedAt time.Time // 移除此行
	// zpayOrder, err := s.zpayService.QueryOrder(notifyData.OutTradeNo) // 移除此行
	// if err == nil && zpayOrder != nil && zpayOrder.EndTime != "" { // 移除此行
	// 	completedAt, _ = time.ParseInLocation("2006-01-02 15:04:05", zpayOrder.EndTime, time.Local) // 移除此行
	// } // 移除此行

	// 根据订单号前缀判断订单类型
	if strings.HasPrefix(notifyData.OutTradeNo, "SP") {
		// 脚本订单
		if ok, _ := s.OnOrderPaidAndInstallScript(context.Background(), notifyData.OutTradeNo); ok {
			logger.Info(ctx, "HandlePaymentNotify: 脚本订单已支付/已安装，处理完成 - 订单号: %s", notifyData.OutTradeNo)
			return nil
		}
	} else if strings.HasPrefix(notifyData.OutTradeNo, "SU") {
		// 升级订单
		if ok, _ := s.OnUpgradeOrderPaidAndUpdateScript(context.Background(), notifyData.OutTradeNo); ok {
			logger.Info(ctx, "HandlePaymentNotify: 升级订单已支付/已更新脚本，处理完成 - 订单号: %s", notifyData.OutTradeNo)
			return nil
		}
	} else if strings.HasPrefix(notifyData.OutTradeNo, "DQ") {
		// 配额订单
		if ok, _ := s.OnQuotaOrderPaidAndCreateQuota(context.Background(), notifyData.OutTradeNo); ok {
			logger.Info(ctx, "HandlePaymentNotify: 配额订单已支付/已创建配额，处理完成 - 订单号: %s", notifyData.OutTradeNo)
			return nil
		}
	} else if strings.HasPrefix(notifyData.OutTradeNo, "QR") {
		// 配额续费订单
		if err := s.OnQuotaRenewalOrderPaid(context.Background(), notifyData.OutTradeNo); err == nil {
			logger.Info(ctx, "HandlePaymentNotify: 配额续费订单已支付/已续费，处理完成 - 订单号: %s", notifyData.OutTradeNo)
			return nil
		} else {
			logger.Error(ctx, "HandlePaymentNotify: 配额续费订单处理失败 - 订单号: %s, 错误: %v", notifyData.OutTradeNo, err)
			return err
		}
	} else {
		logger.Warn(ctx, "HandlePaymentNotify: 未知订单类型 - 订单号: %s", notifyData.OutTradeNo)
		return fmt.Errorf("未知订单类型")
	}

	logger.Info(ctx, "ZPAY支付回调处理完成 - 订单号: %s", notifyData.OutTradeNo)
	return nil
}

// GetOrderDetail 获取订单详情（通用接口，支持所有类型订单）
func (s *PaymentService) GetOrderDetail(ctx context.Context, userID uint, orderNo string) (*model.PaymentOrder, error) {
	var order model.PaymentOrder
	if err := s.db.WithContext(ctx).Where("order_no = ? AND user_id = ?", orderNo, userID).First(&order).Error; err != nil {
		return nil, response.NewError(response.INVALID_PARAMS, "订单不存在")
	}
	return &order, nil
}

// OnOrderPaid 处理支付成功后的业务逻辑，幂等
func (s *PaymentService) OnOrderPaid(ctx context.Context, orderNo string) error {
	// 查询订单
	var order model.PaymentOrder
	if err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		logger.Error(ctx, "OnOrderPaid: 订单不存在: %s, 错误: %v", orderNo, err)
		return err
	}
	if order.Status == 1 {
		logger.Info(ctx, "OnOrderPaid: 订单已支付，忽略重复处理 - 订单号: %s", orderNo)
		return nil
	}

	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()
	order.Status = 1
	order.PaidAt = &now
	if err := tx.Save(&order).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnOrderPaid: 更新订单状态失败: %v", err)
		return err
	}

	// 获取套餐信息
	var pkg model.ScriptPackage
	if err := tx.Where("id = ?", order.PackageID).First(&pkg).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnOrderPaid: 获取套餐信息失败: %v", err)
		return err
	}

	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx, "OnOrderPaid: 提交事务失败: %v", err)
		return err
	}

	// 异步安装脚本
	go func() {
		asyncCtx := context.Background()
		logger.Info(asyncCtx, "OnOrderPaid: 开始异步安装脚本 - user_id: %d, script_id: %d, package_id: %d, order_id: %d", order.UserID, pkg.MarketScriptID, order.PackageID, order.ID)
		if err := s.marketScriptService.InstallScript(asyncCtx, pkg.MarketScriptID, order.UserID, time.Time{}, time.Time{}, time.Now(), pkg.DeviceLimit, order.PackageID, pkg.Name); err != nil {
			logger.Error(asyncCtx, "OnOrderPaid: 安装脚本失败: %v", err)
		} else {
			logger.Info(asyncCtx, "OnOrderPaid: 脚本安装成功 - user_id: %d, script_id: %d", order.UserID, pkg.MarketScriptID)
		}
	}()

	logger.Info(ctx, "OnOrderPaid: 支付成功业务处理完成 - 订单号: %s", orderNo)
	return nil
}

// OnOrderPaidAndInstallScript 统一处理支付成功和安装脚本的方法，内部主动查ZPAY订单
// 返回值：true=已支付/已安装，false=未支付/未安装
func (s *PaymentService) OnOrderPaidAndInstallScript(ctx context.Context, orderNo string) (bool, error) {
	// 查询本地订单
	var order model.PaymentOrder
	if err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		logger.Error(ctx, "OnOrderPaidAndInstallScript: 订单不存在: %s, 错误: %v", orderNo, err)
		return false, err
	}

	// 查询ZPAY订单，获取支付状态和endtime
	zpayOrder, err := s.zpayService.QueryOrder(orderNo)
	if err != nil {
		logger.Error(ctx, "OnOrderPaidAndInstallScript: 查询ZPAY订单失败: %v", err)
		return false, nil // 查询失败，继续重试
	}
	if zpayOrder == nil || zpayOrder.Status != "1" {
		logger.Warn(ctx, "OnOrderPaidAndInstallScript: ZPAY订单未支付 - 订单号: %s", orderNo)
		return false, nil // 未支付，继续重试
	}

	// 解析ZPAY endtime
	var completedAt time.Time
	if zpayOrder.EndTime != "" {
		completedAt, _ = time.ParseInLocation("2006-01-02 15:04:05", zpayOrder.EndTime, time.Local)
	} else {
		completedAt = time.Now()
	}

	// 幂等：如果本地订单已支付且paid_at不为空，直接返回true
	if order.Status == 1 && order.PaidAt != nil {
		logger.Info(ctx, "OnOrderPaidAndInstallScript: 订单已支付，忽略重复处理 - 订单号: %s", orderNo)
		return true, nil
	}

	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	order.Status = 1
	order.PaidAt = &completedAt
	// 保存ZPAY查询结果的JSON格式
	notifyJSON, _ := json.Marshal(zpayOrder)
	order.NotifyData = string(notifyJSON)
	if err := tx.Save(&order).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnOrderPaidAndInstallScript: 更新订单状态失败: %v", err)
		return false, err
	}

	// 获取套餐信息
	var pkg model.ScriptPackage
	if err := tx.Where("id = ?", order.PackageID).First(&pkg).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnOrderPaidAndInstallScript: 获取套餐信息失败: %v", err)
		return false, err
	}
	// 获取市场脚本信息
	var marketScript model.MarketScript
	if err := tx.Where("id = ?", pkg.MarketScriptID).First(&marketScript).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnOrderPaidAndInstallScript: 获取市场脚本失败: %v", err)
		return false, err
	}
	// 计算过期时间
	expiredAt := completedAt.Add(time.Duration(pkg.Duration) * 24 * time.Hour)

	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx, "OnOrderPaidAndInstallScript: 提交事务失败: %v", err)
		return false, err
	}

	// 安装脚本（含paid_at/expired_at/completed_at和设备限制）
	if err := s.marketScriptService.InstallScript(context.Background(), pkg.MarketScriptID, order.UserID, completedAt, expiredAt, completedAt, pkg.DeviceLimit, order.PackageID, pkg.Name); err != nil {
		logger.Error(ctx, "OnOrderPaidAndInstallScript: 安装脚本失败: %v", err)
		return false, err
	}

	logger.Info(ctx, "OnOrderPaidAndInstallScript: 订单支付和脚本安装成功 - 订单号: %s", orderNo)
	return true, nil
}

// CreateQuotaOrder 创建配额购买订单
func (s *PaymentService) CreateQuotaOrder(ctx context.Context, userID uint, quotaCount int, payType string) (*model.PaymentOrder, error) {
	// 生成订单号
	orderNo := fmt.Sprintf("DQ%d%d", userID, time.Now().UnixNano())

	// 计算金额（固定1元/个）
	amount := float64(quotaCount) * 1.00

	// 创建订单
	order := &model.PaymentOrder{
		OrderNo:   orderNo,
		UserID:    userID,
		PackageID: 0, // 配额订单不使用套餐ID
		Amount:    amount,
		PayType:   payType,
		Status:    0,                                // 未支付
		ExpiredAt: time.Now().Add(30 * time.Minute), // 30分钟过期
	}

	if err := s.db.WithContext(ctx).Create(order).Error; err != nil {
		return nil, err
	}

	// 调用ZPAY创建支付订单
	logger.Info(ctx, "开始调用ZPAY创建配额订单 - order_no: %s, amount: %.2f, name: %s, pay_type: %s",
		order.OrderNo, order.Amount, fmt.Sprintf("设备配额-%d个", quotaCount), payType)

	zpayResp, err := s.zpayService.CreateOrder(
		order.OrderNo,
		fmt.Sprintf("%.2f", order.Amount),
		fmt.Sprintf("设备配额-%d个", quotaCount),
		payType,
	)

	if err != nil {
		// 支付创建失败，删除订单
		s.db.WithContext(ctx).Delete(order)
		return nil, response.NewError(response.ERROR, "创建支付订单失败")
	}

	// 更新订单信息
	order.PaymentPlatform = "zpay"
	order.ThirdPartyOrderNo = zpayResp.TradeNo
	// 保存ZPAY响应的JSON格式
	notifyJSON, _ := json.Marshal(zpayResp)
	order.NotifyData = string(notifyJSON)

	if err := s.db.WithContext(ctx).Save(order).Error; err != nil {
		return nil, err
	}

	// 创建订单后，异步轮询ZPAY订单状态，支付成功后自动处理业务
	orderNoCopy := order.OrderNo
	paymentService := s
	ctxCopy := context.Background() // 用新ctx避免主流程阻塞

	// 启动异步轮询
	go func() {
		maxTries := 20
		baseInterval := 5 * time.Second
		time.Sleep(30 * time.Second)
		for i := 0; i < maxTries; i++ {
			ok, _ := paymentService.OnQuotaOrderPaidAndCreateQuota(ctxCopy, orderNoCopy)
			if ok {
				break // 已支付/已创建配额，退出循环
			}
			interval := time.Duration(i+1) * baseInterval
			time.Sleep(interval)
		}
	}()

	// 返回包含支付URL的响应
	orderResponse := &model.PaymentOrder{
		ID:                order.ID,
		OrderNo:           order.OrderNo,
		UserID:            order.UserID,
		PackageID:         order.PackageID,
		Amount:            order.Amount,
		PayType:           order.PayType,
		Status:            order.Status,
		PaymentPlatform:   order.PaymentPlatform,
		ThirdPartyOrderNo: order.ThirdPartyOrderNo,
		NotifyData:        order.NotifyData,
		PaidAt:            order.PaidAt,
		ExpiredAt:         order.ExpiredAt,
		CreatedAt:         order.CreatedAt,
		UpdatedAt:         order.UpdatedAt,
		// 添加ZPAY支付URL
		PayURL: zpayResp.PayURL,
	}

	return orderResponse, nil
}

// GetQuotaOrder 获取配额订单
func (s *PaymentService) GetQuotaOrder(ctx context.Context, userID uint, orderNo string) (*model.PaymentOrder, error) {
	var order model.PaymentOrder
	if err := s.db.WithContext(ctx).Where("order_no = ? AND user_id = ?", orderNo, userID).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "订单不存在")
		}
		return nil, err
	}

	return &order, nil
}

// OnQuotaOrderPaid 配额订单支付成功处理
func (s *PaymentService) OnQuotaOrderPaid(ctx context.Context, orderNo string) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取订单信息
		var order model.PaymentOrder
		if err := tx.Where("order_no = ?", orderNo).First(&order).Error; err != nil {
			return response.NewError(response.ERROR, "订单不存在")
		}

		// 检查订单状态
		if order.Status == 1 {
			logger.Info(ctx, "配额订单已处理: order_no=%s", orderNo)
			return nil
		}

		// 计算配额数量（金额/1元）
		quotaCount := int(order.Amount / 1.00)

		// 创建配额记录
		quotaService := NewDeviceQuotaService(s.db)
		_, err := quotaService.CreatePaidQuota(ctx, order.UserID, quotaCount, orderNo)
		if err != nil {
			logger.Error(ctx, "创建付费配额失败: user_id=%d, quota_count=%d, error=%v", order.UserID, quotaCount, err)
			return response.NewError(response.ERROR, "创建配额失败")
		}

		// 更新订单状态
		now := time.Now()
		order.Status = 1 // 已支付
		order.PaidAt = &now
		if err := tx.Save(&order).Error; err != nil {
			return response.NewError(response.ERROR, "更新订单状态失败")
		}

		logger.Info(ctx, "配额订单支付成功处理完成: order_no=%s, user_id=%d, quota_count=%d", orderNo, order.UserID, quotaCount)
		return nil
	})
}

// OnQuotaOrderPaidAndCreateQuota 统一处理配额订单支付成功和创建配额的方法，内部主动查ZPAY订单
// 返回值：true=已支付/已创建配额，false=未支付/未创建配额
func (s *PaymentService) OnQuotaOrderPaidAndCreateQuota(ctx context.Context, orderNo string) (bool, error) {
	// 查询本地订单
	var order model.PaymentOrder
	if err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		logger.Error(ctx, "OnQuotaOrderPaidAndCreateQuota: 订单不存在: %s, 错误: %v", orderNo, err)
		return false, err
	}

	// 查询ZPAY订单，获取支付状态和endtime
	zpayOrder, err := s.zpayService.QueryOrder(orderNo)
	if err != nil {
		logger.Error(ctx, "OnQuotaOrderPaidAndCreateQuota: 查询ZPAY订单失败: %v", err)
		return false, nil // 查询失败，继续重试
	}
	if zpayOrder == nil || zpayOrder.Status != "1" {
		logger.Warn(ctx, "OnQuotaOrderPaidAndCreateQuota: ZPAY订单未支付 - 订单号: %s", orderNo)
		return false, nil // 未支付，继续重试
	}

	// 解析ZPAY endtime
	var completedAt time.Time
	if zpayOrder.EndTime != "" {
		completedAt, _ = time.ParseInLocation("2006-01-02 15:04:05", zpayOrder.EndTime, time.Local)
	} else {
		completedAt = time.Now()
	}

	// 幂等：如果本地订单已支付且paid_at不为空，直接返回true
	if order.Status == 1 && order.PaidAt != nil {
		logger.Info(ctx, "OnQuotaOrderPaidAndCreateQuota: 订单已支付，忽略重复处理 - 订单号: %s", orderNo)
		return true, nil
	}

	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	order.Status = 1
	order.PaidAt = &completedAt
	// 保存ZPAY查询结果的JSON格式
	notifyJSON, _ := json.Marshal(zpayOrder)
	order.NotifyData = string(notifyJSON)
	if err := tx.Save(&order).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnQuotaOrderPaidAndCreateQuota: 更新订单状态失败: %v", err)
		return false, err
	}

	// 计算配额数量（金额/1元）
	quotaCount := int(order.Amount / 1.00)

	// 创建配额记录
	quotaService := NewDeviceQuotaService(s.db)
	_, err = quotaService.CreatePaidQuota(ctx, order.UserID, quotaCount, orderNo)
	if err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnQuotaOrderPaidAndCreateQuota: 创建付费配额失败: %v", err)
		return false, err
	}

	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx, "OnQuotaOrderPaidAndCreateQuota: 提交事务失败: %v", err)
		return false, err
	}

	logger.Info(ctx, "OnQuotaOrderPaidAndCreateQuota: 配额订单支付和配额创建成功 - 订单号: %s, 配额数量: %d", orderNo, quotaCount)
	return true, nil
}

// GetUpgradePackages 获取升级套餐列表
// 只显示比当前套餐更高级的套餐，避免降级
func (s *PaymentService) GetUpgradePackages(ctx context.Context, userID, scriptID uint) ([]*model.ScriptPackage, error) {
	// 获取用户当前的脚本信息
	var userScript model.UserScript
	if err := s.db.WithContext(ctx).Where("user_id = ? AND id = ?", userID, scriptID).First(&userScript).Error; err != nil {
		return nil, response.NewError(response.ERROR, "脚本不存在")
	}

	// 获取该脚本的所有套餐
	var packages []*model.ScriptPackage
	if err := s.db.WithContext(ctx).
		Where("market_script_id = ? AND status = ?", userScript.MarketScriptID, 1).
		Preload("MarketScript").
		Order("price ASC").
		Find(&packages).Error; err != nil {
		return nil, err
	}

	// 过滤出可升级的套餐（价格更高的套餐）
	var upgradePackages []*model.ScriptPackage
	for _, pkg := range packages {
		// 如果是免费版本，显示所有付费套餐
		if userScript.PackageID == 0 {
			if pkg.Price > 0 {
				upgradePackages = append(upgradePackages, pkg)
			}
		} else {
			// 获取用户当前套餐信息
			var currentPackage model.ScriptPackage
			if err := s.db.WithContext(ctx).Where("id = ?", userScript.PackageID).First(&currentPackage).Error; err != nil {
				logger.Error(ctx, "GetUpgradePackages: 获取当前套餐失败 - packageID: %d, error: %v", userScript.PackageID, err)
				continue
			}

			// 只显示价格更高的套餐（真正的升级）
			if pkg.Price > currentPackage.Price {
				upgradePackages = append(upgradePackages, pkg)
			}
		}
	}

	return upgradePackages, nil
}

// CalculateUpgradePrice 计算升级价格
// 升级价格 = 新套餐价格 - 当前套餐剩余价值
func (s *PaymentService) CalculateUpgradePrice(ctx context.Context, userID, scriptID, packageID uint) float64 {
	// 获取用户当前的脚本信息
	var userScript model.UserScript
	if err := s.db.WithContext(ctx).Where("user_id = ? AND id = ?", userID, scriptID).First(&userScript).Error; err != nil {
		logger.Error(ctx, "CalculateUpgradePrice: 获取用户脚本失败 - userID: %d, scriptID: %d, error: %v", userID, scriptID, err)
		return 0
	}

	// 获取新套餐信息
	var newPackage model.ScriptPackage
	if err := s.db.WithContext(ctx).Where("id = ?", packageID).First(&newPackage).Error; err != nil {
		logger.Error(ctx, "CalculateUpgradePrice: 获取新套餐失败 - packageID: %d, error: %v", packageID, err)
		return 0
	}

	// 计算当前脚本的剩余时间
	var remainingDays float64 = 0
	now := time.Now()
	if userScript.ExpiredAt != nil && userScript.ExpiredAt.After(now) {
		remainingDays = userScript.ExpiredAt.Sub(now).Hours() / 24
	}

	logger.Info(ctx, "CalculateUpgradePrice: 计算参数 - userID: %d, scriptID: %d, packageID: %d, currentPackageID: %d, newPackagePrice: %.2f, remainingDays: %.2f",
		userID, scriptID, packageID, userScript.PackageID, newPackage.Price, remainingDays)

	// 如果没有当前套餐（免费版本），直接返回新套餐价格
	if userScript.PackageID == 0 {
		logger.Info(ctx, "CalculateUpgradePrice: 免费版本升级 - 直接返回新套餐价格: %.2f", newPackage.Price)
		return newPackage.Price
	}

	// 获取用户当前套餐信息
	var currentPackage model.ScriptPackage
	if err := s.db.WithContext(ctx).Where("id = ?", userScript.PackageID).First(&currentPackage).Error; err != nil {
		logger.Error(ctx, "CalculateUpgradePrice: 获取当前套餐失败 - currentPackageID: %d, error: %v", userScript.PackageID, err)
		return newPackage.Price
	}

	// 计算当前套餐的剩余价值
	// 计算时间系数
	timeRatio := remainingDays / float64(newPackage.Duration)

	// 计算价格差异
	priceDifference := newPackage.Price - currentPackage.Price

	// 升级价格 = 价格差异 * 时间系数
	upgradePrice := priceDifference * timeRatio

	logger.Info(ctx, "CalculateUpgradePrice: 价格计算 - currentPackagePrice: %.2f, newPackagePrice: %.2f, priceDifference: %.2f, timeRatio: %.4f, upgradePrice: %.4f",
		currentPackage.Price, newPackage.Price, priceDifference, timeRatio, upgradePrice)

	// 确保最小费用
	if upgradePrice < 0.01 && upgradePrice > 0 {
		upgradePrice = 0.01
		logger.Info(ctx, "CalculateUpgradePrice: 设置最小费用: %.2f", upgradePrice)
	}

	// 四舍五入保留2位小数
	finalPrice := math.Round(upgradePrice*100) / 100
	logger.Info(ctx, "CalculateUpgradePrice: 最终升级价格: %.2f", finalPrice)

	return finalPrice
}

// CreateUpgradeOrder 创建升级订单
func (s *PaymentService) CreateUpgradeOrder(ctx context.Context, userID, scriptID, packageID uint, payType string) (*model.PaymentOrder, error) {
	// 验证用户脚本是否存在
	var userScript model.UserScript
	if err := s.db.WithContext(ctx).Where("user_id = ? AND id = ?", userID, scriptID).First(&userScript).Error; err != nil {
		return nil, response.NewError(response.ERROR, "脚本不存在")
	}

	// 获取套餐信息
	var pkg model.ScriptPackage
	if err := s.db.WithContext(ctx).Where("id = ? AND status = ?", packageID, 1).First(&pkg).Error; err != nil {
		return nil, response.NewError(response.INVALID_PARAMS, "套餐不存在或已禁用")
	}

	// 验证套餐是否属于同一个市场脚本
	if pkg.MarketScriptID != userScript.MarketScriptID {
		return nil, response.NewError(response.INVALID_PARAMS, "套餐与脚本不匹配")
	}

	// 检查是否已经是该套餐
	if userScript.PackageID == packageID {
		return nil, response.NewError(response.INVALID_PARAMS, "当前已是该套餐，无需升级")
	}

	// 计算升级价格
	upgradePrice := s.CalculateUpgradePrice(ctx, userID, scriptID, packageID)
	if upgradePrice <= 0 {
		return nil, response.NewError(response.INVALID_PARAMS, "升级价格计算错误")
	}

	// 生成订单号
	orderNo := fmt.Sprintf("SU%d%d", userID, time.Now().UnixNano())

	// 创建订单
	order := &model.PaymentOrder{
		OrderNo:   orderNo,
		UserID:    userID,
		PackageID: packageID,
		Amount:    upgradePrice, // 使用计算出的升级价格
		PayType:   payType,
		Status:    0,                                // 未支付
		ExpiredAt: time.Now().Add(30 * time.Minute), // 30分钟过期
	}

	if err := s.db.WithContext(ctx).Create(order).Error; err != nil {
		return nil, err
	}

	// 调用ZPAY创建支付订单
	logger.Info(ctx, "开始调用ZPAY创建升级订单 - order_no: %s, amount: %.2f, name: %s, pay_type: %s",
		order.OrderNo, order.Amount, fmt.Sprintf("脚本升级-%s", pkg.Name), payType)

	zpayResp, err := s.zpayService.CreateOrder(
		order.OrderNo,
		fmt.Sprintf("%.2f", order.Amount),
		fmt.Sprintf("脚本升级-%s", pkg.Name),
		payType,
	)
	if err != nil {
		// 删除本地订单
		s.db.WithContext(ctx).Delete(order)
		return nil, err
	}

	// 更新订单支付URL
	order.PayURL = zpayResp.PayURL
	if err := s.db.WithContext(ctx).Save(order).Error; err != nil {
		return nil, err
	}

	// 创建订单后，异步轮询ZPAY订单状态，支付成功后自动处理业务
	orderNoCopy := order.OrderNo
	paymentService := s
	ctxCopy := context.Background() // 用新ctx避免主流程阻塞

	// 启动异步轮询
	go func() {
		maxTries := 20
		baseInterval := 5 * time.Second
		time.Sleep(30 * time.Second)
		for i := 0; i < maxTries; i++ {
			ok, _ := paymentService.OnUpgradeOrderPaidAndUpdateScript(ctxCopy, orderNoCopy)
			if ok {
				break // 已支付/已更新脚本，退出循环
			}
			interval := time.Duration(i+1) * baseInterval
			time.Sleep(interval)
		}
	}()

	logger.Info(ctx, "升级订单创建成功 - order_no: %s, pay_url: %s", order.OrderNo, order.PayURL)
	return order, nil
}

// OnUpgradeOrderPaidAndUpdateScript 处理升级订单支付成功和更新脚本的方法
func (s *PaymentService) OnUpgradeOrderPaidAndUpdateScript(ctx context.Context, orderNo string) (bool, error) {
	// 查询本地订单
	var order model.PaymentOrder
	if err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		logger.Error(ctx, "OnUpgradeOrderPaidAndUpdateScript: 订单不存在: %s, 错误: %v", orderNo, err)
		return false, err
	}

	// 查询ZPAY订单，获取支付状态和endtime
	zpayOrder, err := s.zpayService.QueryOrder(orderNo)
	if err != nil {
		logger.Error(ctx, "OnUpgradeOrderPaidAndUpdateScript: 查询ZPAY订单失败: %v", err)
		return false, nil // 查询失败，继续重试
	}
	if zpayOrder == nil || zpayOrder.Status != "1" {
		logger.Warn(ctx, "OnUpgradeOrderPaidAndUpdateScript: ZPAY订单未支付 - 订单号: %s", orderNo)
		return false, nil // 未支付，继续重试
	}

	// 解析ZPAY endtime
	var completedAt time.Time
	if zpayOrder.EndTime != "" {
		completedAt, _ = time.ParseInLocation("2006-01-02 15:04:05", zpayOrder.EndTime, time.Local)
	} else {
		completedAt = time.Now()
	}

	// 幂等：如果本地订单已支付且paid_at不为空，直接返回true
	if order.Status == 1 && order.PaidAt != nil {
		logger.Info(ctx, "OnUpgradeOrderPaidAndUpdateScript: 订单已支付，忽略重复处理 - 订单号: %s", orderNo)
		return true, nil
	}

	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新订单状态
	order.Status = 1
	order.PaidAt = &completedAt
	// 保存ZPAY查询结果的JSON格式
	notifyJSON, _ := json.Marshal(zpayOrder)
	order.NotifyData = string(notifyJSON)
	if err := tx.Save(&order).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnUpgradeOrderPaidAndUpdateScript: 更新订单状态失败: %v", err)
		return false, err
	}

	// 获取套餐信息
	var pkg model.ScriptPackage
	if err := tx.Where("id = ?", order.PackageID).First(&pkg).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnUpgradeOrderPaidAndUpdateScript: 获取套餐信息失败: %v", err)
		return false, err
	}

	// 计算新的过期时间（从支付完成时间开始计算）
	expiredAt := completedAt.Add(time.Duration(pkg.Duration) * 24 * time.Hour)

	// 更新用户脚本的套餐信息和过期时间
	if err := tx.Model(&model.UserScript{}).
		Where("user_id = ? AND market_script_id = ?", order.UserID, pkg.MarketScriptID).
		Updates(map[string]interface{}{
			"package_id":   pkg.ID,
			"package_name": pkg.Name,
			"max_devices":  pkg.DeviceLimit,
			"expired_at":   expiredAt,
			"updated_at":   time.Now(),
		}).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnUpgradeOrderPaidAndUpdateScript: 更新脚本套餐信息失败: %v", err)
		return false, err
	}

	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx, "OnUpgradeOrderPaidAndUpdateScript: 提交事务失败: %v", err)
		return false, err
	}

	logger.Info(ctx, "OnUpgradeOrderPaidAndUpdateScript: 升级订单支付和脚本更新成功 - 订单号: %s, 套餐: %s", orderNo, pkg.Name)
	return true, nil
}

// CreateQuotaRenewalOrder 创建配额续费订单
func (s *PaymentService) CreateQuotaRenewalOrder(ctx context.Context, userID uint, quotaID int, payType string) (*model.PaymentOrder, error) {
	// 获取配额信息
	var quota model.DeviceQuota
	if err := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", quotaID, userID).First(&quota).Error; err != nil {
		return nil, response.NewError(response.INVALID_PARAMS, "配额不存在")
	}

	// 检查配额是否未过期
	if quota.ExpiresAt != nil && quota.ExpiresAt.Before(time.Now()) {
		return nil, response.NewError(response.INVALID_PARAMS, "配额已过期，无法续费")
	}

	// 计算续费价格（固定30天，1元/个）
	renewalPrice := float64(quota.TotalQuota) * 1.0

	// 生成订单号
	orderNo := fmt.Sprintf("QR%d%d", userID, time.Now().UnixNano())

	// 创建订单
	order := &model.PaymentOrder{
		OrderNo:   orderNo,
		UserID:    userID,
		QuotaID:   &quotaID,
		Amount:    renewalPrice,
		PayType:   payType,
		Status:    0,                                // 未支付
		OrderType: model.OrderTypeQuotaRenewal,      // 配额续费订单
		ExpiredAt: time.Now().Add(30 * time.Minute), // 30分钟过期
	}

	if err := s.db.WithContext(ctx).Create(order).Error; err != nil {
		return nil, err
	}

	// 调用ZPAY创建支付订单
	logger.Info(ctx, "开始调用ZPAY创建配额续费订单 - order_no: %s, amount: %.2f, name: %s, pay_type: %s",
		order.OrderNo, order.Amount, fmt.Sprintf("配额续费-%d个", quota.TotalQuota), payType)

	zpayResp, err := s.zpayService.CreateOrder(
		order.OrderNo,
		fmt.Sprintf("%.2f", order.Amount),
		fmt.Sprintf("配额续费-%d个", quota.TotalQuota),
		payType,
	)

	if err != nil {
		// 删除创建的订单
		s.db.WithContext(ctx).Delete(order)
		return nil, response.NewError(response.ERROR, "创建支付订单失败")
	}

	// 更新订单的支付URL
	order.PayURL = zpayResp.PayURL
	if err := s.db.WithContext(ctx).Save(order).Error; err != nil {
		return nil, response.NewError(response.ERROR, "更新订单失败")
	}

	// 创建订单后，异步轮询ZPAY订单状态，支付成功后自动处理业务
	orderNoCopy := order.OrderNo
	paymentService := s
	ctxCopy := context.Background() // 用新ctx避免主流程阻塞

	// 启动异步轮询
	go func() {
		maxTries := 20
		baseInterval := 5 * time.Second
		time.Sleep(30 * time.Second)
		for i := 0; i < maxTries; i++ {
			ok, _ := paymentService.OnQuotaRenewalOrderPaidAndRenewQuota(ctxCopy, orderNoCopy)
			if ok {
				break // 已支付/已续费，退出循环
			}
			interval := time.Duration(i+1) * baseInterval
			time.Sleep(interval)
		}
	}()

	logger.Info(ctx, "配额续费订单创建成功: order_no=%s, quota_id=%d, amount=%.2f", order.OrderNo, quotaID, order.Amount)

	return order, nil
}

// OnQuotaRenewalOrderPaid 配额续费订单支付成功后的处理
func (s *PaymentService) OnQuotaRenewalOrderPaid(ctx context.Context, orderNo string) error {
	// 获取订单信息
	var order model.PaymentOrder
	if err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		return response.NewError(response.ERROR, "订单不存在")
	}

	// 检查订单状态
	if order.Status != 1 {
		return response.NewError(response.ERROR, "订单状态异常")
	}

	// 检查订单类型
	if order.OrderType != model.OrderTypeQuotaRenewal {
		return response.NewError(response.ERROR, "订单类型错误")
	}

	// 获取配额信息
	var quota model.DeviceQuota
	if err := s.db.WithContext(ctx).Where("id = ?", *order.QuotaID).First(&quota).Error; err != nil {
		return response.NewError(response.ERROR, "配额不存在")
	}

	// 计算新的过期时间（在现有过期时间基础上延长30天）
	var newExpiresAt time.Time
	if quota.ExpiresAt != nil && quota.ExpiresAt.After(time.Now()) {
		// 如果配额未过期，在现有过期时间基础上延长30天
		newExpiresAt = quota.ExpiresAt.AddDate(0, 0, 30)
	} else {
		// 如果配额已过期，从当前时间开始计算30天
		newExpiresAt = time.Now().AddDate(0, 0, 30)
	}

	// 更新配额的过期时间
	if err := s.db.WithContext(ctx).Model(&quota).Update("expires_at", newExpiresAt).Error; err != nil {
		return response.NewError(response.ERROR, "更新配额过期时间失败")
	}

	logger.Info(ctx, "配额续费成功: quota_id=%d, new_expires_at=%v", quota.ID, newExpiresAt)

	return nil
}

// OnQuotaRenewalOrderPaidAndRenewQuota 配额续费订单支付成功后的处理（包含轮询逻辑）
func (s *PaymentService) OnQuotaRenewalOrderPaidAndRenewQuota(ctx context.Context, orderNo string) (bool, error) {
	// 查询本地订单
	var order model.PaymentOrder
	if err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		logger.Error(ctx, "OnQuotaRenewalOrderPaidAndRenewQuota: 订单不存在: %s, 错误: %v", orderNo, err)
		return false, err
	}

	// 查询ZPAY订单，获取支付状态
	zpayOrder, err := s.zpayService.QueryOrder(orderNo)
	if err != nil {
		logger.Error(ctx, "OnQuotaRenewalOrderPaidAndRenewQuota: 查询ZPAY订单失败: %v", err)
		return false, nil // 查询失败，继续重试
	}
	if zpayOrder == nil || zpayOrder.Status != "1" {
		logger.Warn(ctx, "OnQuotaRenewalOrderPaidAndRenewQuota: ZPAY订单未支付 - 订单号: %s", orderNo)
		return false, nil // 未支付，继续重试
	}

	// 解析ZPAY endtime
	var completedAt time.Time
	if zpayOrder.EndTime != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", zpayOrder.EndTime); err == nil {
			completedAt = t
		}
	}

	// 如果本地订单已支付，检查是否已经处理过续费
	if order.Status == 1 {
		// 检查是否已经处理过续费
		var quota model.DeviceQuota
		if err := s.db.WithContext(ctx).Where("id = ?", *order.QuotaID).First(&quota).Error; err != nil {
			return false, err
		}

		// 检查配额是否已经续费（通过比较过期时间）
		expectedExpiresAt := time.Now().AddDate(0, 0, 30)
		if quota.ExpiresAt != nil && quota.ExpiresAt.After(expectedExpiresAt) {
			logger.Info(ctx, "配额已续费: quota_id=%d, expires_at=%v", quota.ID, quota.ExpiresAt)
			return true, nil // 已续费，停止轮询
		}
	}

	// 更新本地订单状态
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()
	order.Status = 1
	order.PaidAt = &now
	if completedAt.After(now) {
		order.PaidAt = &completedAt
	}

	if err := tx.Save(&order).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnQuotaRenewalOrderPaidAndRenewQuota: 更新订单状态失败: %v", err)
		return false, err
	}

	// 执行续费逻辑
	if err := s.OnQuotaRenewalOrderPaid(ctx, orderNo); err != nil {
		tx.Rollback()
		logger.Error(ctx, "配额续费失败: order_no=%s, error=%v", orderNo, err)
		return false, err
	}

	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx, "OnQuotaRenewalOrderPaidAndRenewQuota: 提交事务失败: %v", err)
		return false, err
	}

	logger.Info(ctx, "配额续费处理完成: order_no=%s, quota_id=%d", orderNo, *order.QuotaID)
	return true, nil // 续费成功，停止轮询
}

// CreateScriptRenewalOrder 创建脚本续费订单
func (s *PaymentService) CreateScriptRenewalOrder(ctx context.Context, userID uint, scriptID uint, payType string) (*model.PaymentOrder, error) {
	// 获取脚本信息
	var script model.UserScript
	if err := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", scriptID, userID).First(&script).Error; err != nil {
		return nil, response.NewError(response.INVALID_PARAMS, "脚本不存在")
	}

	// 检查脚本是否未过期
	if script.ExpiredAt != nil && script.ExpiredAt.Before(time.Now()) {
		return nil, response.NewError(response.INVALID_PARAMS, "脚本已过期，无法续费")
	}

	// 获取脚本套餐信息
	var packageInfo *model.ScriptPackage
	if script.PackageID > 0 {
		var pkg model.ScriptPackage
		if err := s.db.WithContext(ctx).Where("id = ?", script.PackageID).First(&pkg).Error; err == nil {
			packageInfo = &pkg
		}
	}

	// 计算续费价格（固定30天，使用原套餐价格）
	renewalPrice := 0.01 // 默认价格
	if packageInfo != nil {
		renewalPrice = packageInfo.Price
	}

	// 生成订单号
	orderNo := fmt.Sprintf("SR%d%d", userID, time.Now().UnixNano())

	// 创建订单
	order := &model.PaymentOrder{
		OrderNo:   orderNo,
		UserID:    userID,
		ScriptID:  &scriptID,
		Amount:    renewalPrice,
		PayType:   payType,
		Status:    0,                                // 未支付
		OrderType: model.OrderTypeScriptRenewal,     // 脚本续费订单
		ExpiredAt: time.Now().Add(30 * time.Minute), // 30分钟过期
	}

	if err := s.db.WithContext(ctx).Create(order).Error; err != nil {
		return nil, err
	}

	// 调用ZPAY创建支付订单
	scriptName := script.Name
	if packageInfo != nil {
		scriptName = fmt.Sprintf("%s-%s", script.Name, packageInfo.Name)
	}

	logger.Info(ctx, "开始调用ZPAY创建脚本续费订单 - order_no: %s, amount: %.2f, name: %s, pay_type: %s",
		order.OrderNo, order.Amount, fmt.Sprintf("脚本续费-%s", scriptName), payType)

	zpayResp, err := s.zpayService.CreateOrder(
		order.OrderNo,
		fmt.Sprintf("%.2f", order.Amount),
		fmt.Sprintf("脚本续费-%s", scriptName),
		payType,
	)

	if err != nil {
		// 删除创建的订单
		s.db.WithContext(ctx).Delete(order)
		return nil, response.NewError(response.ERROR, "创建支付订单失败")
	}

	// 更新订单的支付URL
	order.PayURL = zpayResp.PayURL
	if err := s.db.WithContext(ctx).Save(order).Error; err != nil {
		return nil, response.NewError(response.ERROR, "更新订单失败")
	}

	// 创建订单后，异步轮询ZPAY订单状态，支付成功后自动处理业务
	orderNoCopy := order.OrderNo
	paymentService := s
	ctxCopy := context.Background() // 用新ctx避免主流程阻塞

	// 启动异步轮询
	go func() {
		maxTries := 20
		baseInterval := 5 * time.Second
		time.Sleep(30 * time.Second)
		for i := 0; i < maxTries; i++ {
			ok, _ := paymentService.OnScriptRenewalOrderPaidAndRenewScript(ctxCopy, orderNoCopy)
			if ok {
				break // 已支付/已续费，退出循环
			}
			interval := time.Duration(i+1) * baseInterval
			time.Sleep(interval)
		}
	}()

	logger.Info(ctx, "脚本续费订单创建成功: order_no=%s, script_id=%d, amount=%.2f", order.OrderNo, scriptID, order.Amount)

	return order, nil
}

// OnScriptRenewalOrderPaid 脚本续费订单支付成功后的处理
func (s *PaymentService) OnScriptRenewalOrderPaid(ctx context.Context, orderNo string) error {
	// 获取订单信息
	var order model.PaymentOrder
	if err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		return response.NewError(response.ERROR, "订单不存在")
	}

	// 检查订单类型
	if order.OrderType != model.OrderTypeScriptRenewal {
		return response.NewError(response.ERROR, "订单类型错误")
	}

	// 获取脚本信息
	var script model.UserScript
	if err := s.db.WithContext(ctx).Where("id = ?", *order.ScriptID).First(&script).Error; err != nil {
		return response.NewError(response.ERROR, "脚本不存在")
	}

	// 计算新的过期时间（在现有过期时间基础上延长30天）
	var newExpiresAt time.Time
	if script.ExpiredAt != nil && script.ExpiredAt.After(time.Now()) {
		// 如果脚本未过期，在现有过期时间基础上延长30天
		newExpiresAt = script.ExpiredAt.AddDate(0, 0, 30)
	} else {
		// 如果脚本已过期，从当前时间开始计算30天
		newExpiresAt = time.Now().AddDate(0, 0, 30)
	}

	// 更新脚本的过期时间
	if err := s.db.WithContext(ctx).Model(&script).Update("expired_at", newExpiresAt).Error; err != nil {
		return response.NewError(response.ERROR, "更新脚本过期时间失败")
	}

	logger.Info(ctx, "脚本续费成功: script_id=%d, new_expires_at=%v", script.ID, newExpiresAt)

	return nil
}

// OnScriptRenewalOrderPaidAndRenewScript 脚本续费订单支付成功后的处理（包含轮询逻辑）
func (s *PaymentService) OnScriptRenewalOrderPaidAndRenewScript(ctx context.Context, orderNo string) (bool, error) {
	// 查询本地订单
	var order model.PaymentOrder
	if err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		logger.Error(ctx, "OnScriptRenewalOrderPaidAndRenewScript: 订单不存在: %s, 错误: %v", orderNo, err)
		return false, err
	}

	// 查询ZPAY订单，获取支付状态
	zpayOrder, err := s.zpayService.QueryOrder(orderNo)
	if err != nil {
		logger.Error(ctx, "OnScriptRenewalOrderPaidAndRenewScript: 查询ZPAY订单失败: %v", err)
		return false, nil // 查询失败，继续重试
	}
	if zpayOrder == nil || zpayOrder.Status != "1" {
		logger.Warn(ctx, "OnScriptRenewalOrderPaidAndRenewScript: ZPAY订单未支付 - 订单号: %s", orderNo)
		return false, nil // 未支付，继续重试
	}

	// 解析ZPAY endtime
	var completedAt time.Time
	if zpayOrder.EndTime != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", zpayOrder.EndTime); err == nil {
			completedAt = t
		}
	}

	// 如果本地订单已支付，检查是否已经处理过续费
	if order.Status == 1 {
		// 检查是否已经处理过续费
		var script model.UserScript
		if err := s.db.WithContext(ctx).Where("id = ?", *order.ScriptID).First(&script).Error; err != nil {
			return false, err
		}

		// 检查脚本是否已经续费（通过比较过期时间）
		expectedExpiresAt := time.Now().AddDate(0, 0, 30)
		if script.ExpiredAt != nil && script.ExpiredAt.After(expectedExpiresAt) {
			logger.Info(ctx, "脚本已续费: script_id=%d, expires_at=%v", script.ID, script.ExpiredAt)
			return true, nil // 已续费，停止轮询
		}
	}

	// 更新本地订单状态
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()
	order.Status = 1
	order.PaidAt = &now
	if completedAt.After(now) {
		order.PaidAt = &completedAt
	}

	if err := tx.Save(&order).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "OnScriptRenewalOrderPaidAndRenewScript: 更新订单状态失败: %v", err)
		return false, err
	}

	// 执行续费逻辑
	if err := s.OnScriptRenewalOrderPaid(ctx, orderNo); err != nil {
		tx.Rollback()
		logger.Error(ctx, "脚本续费失败: order_no=%s, error=%v", orderNo, err)
		return false, err
	}

	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx, "OnScriptRenewalOrderPaidAndRenewScript: 提交事务失败: %v", err)
		return false, err
	}

	logger.Info(ctx, "脚本续费处理完成: order_no=%s, script_id=%d", orderNo, *order.ScriptID)
	return true, nil // 续费成功，停止轮询
}
