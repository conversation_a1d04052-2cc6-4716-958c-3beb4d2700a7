package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"goadmin/internal/constant"
	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// UserService 用户服务
type UserService struct {
	db *gorm.DB
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{
		db: db,
	}
}

// Register 用户注册
func (s *UserService) Register(ctx context.Context, username, password, email string) (*model.User, error) {
	// 检查用户名是否已存在
	var existUser model.User
	if err := s.db.WithContext(ctx).Where("username = ?", username).First(&existUser).Error; err == nil {
		return nil, response.NewError(response.ERROR, "用户名已存在")
	}

	// 检查邮箱是否已存在
	if email != "" {
		if err := s.db.WithContext(ctx).Where("email = ?", email).First(&existUser).Error; err == nil {
			return nil, response.NewError(response.ERROR, "邮箱已存在")
		}
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, response.NewError(response.ERROR, "密码加密失败")
	}

	// 创建用户
	user := &model.User{
		Username:     username,
		PasswordHash: string(hashedPassword),
		Email:        email,
		Nickname:     username,
		Status:       constant.UserStatusNormal,
	}

	if err := s.db.WithContext(ctx).Create(user).Error; err != nil {
		return nil, response.NewError(response.ERROR, "创建用户失败")
	}

	return user, nil
}

// Login 用户登录
func (s *UserService) Login(ctx context.Context, username, password string) (*model.User, error) {
	var user model.User
	if err := s.db.WithContext(ctx).Where("username = ? OR email = ?", username, username).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "用户不存在")
		}
		return nil, response.NewError(response.ERROR, "查询用户失败")
	}

	// 检查用户状态
	if user.Status != constant.UserStatusNormal {
		return nil, response.NewError(response.ERROR, "用户已被禁用")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password)); err != nil {
		return nil, response.NewError(response.ERROR, "密码错误")
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	s.db.WithContext(ctx).Model(&user).Update("last_login_at", now)

	return &user, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(ctx context.Context, id uint) (*model.User, error) {
	var user model.User
	if err := s.db.WithContext(ctx).First(&user, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "用户不存在")
		}
		return nil, response.NewError(response.ERROR, "查询用户失败")
	}
	return &user, nil
}

// UpdateProfile 更新用户资料
func (s *UserService) UpdateProfile(ctx context.Context, userID uint, nickname, email, avatar string) error {
	updates := make(map[string]interface{})

	if nickname != "" {
		updates["nickname"] = nickname
	}
	if email != "" {
		// 检查邮箱是否已被其他用户使用
		var existUser model.User
		if err := s.db.WithContext(ctx).Where("email = ? AND id != ?", email, userID).First(&existUser).Error; err == nil {
			return response.NewError(response.ERROR, "邮箱已被使用")
		}
		updates["email"] = email
	}
	if avatar != "" {
		updates["avatar"] = avatar
	}

	if len(updates) > 0 {
		if err := s.db.WithContext(ctx).Model(&model.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
			return response.NewError(response.ERROR, "更新用户资料失败")
		}
	}

	return nil
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(ctx context.Context, userID uint, oldPassword, newPassword string) error {
	var user model.User
	if err := s.db.WithContext(ctx).First(&user, userID).Error; err != nil {
		return response.NewError(response.ERROR, "用户不存在")
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(oldPassword)); err != nil {
		return response.NewError(response.ERROR, "原密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return response.NewError(response.ERROR, "密码加密失败")
	}

	// 更新密码
	if err := s.db.WithContext(ctx).Model(&user).Update("password_hash", string(hashedPassword)).Error; err != nil {
		return response.NewError(response.ERROR, "修改密码失败")
	}

	return nil
}

// CreateRefreshToken 创建刷新令牌
func (s *UserService) CreateRefreshToken(ctx context.Context, userID *uint, deviceID *uint, tokenType string, expiresAt time.Time) (string, error) {
	// 生成随机令牌
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", response.NewError(response.ERROR, "生成令牌失败")
	}
	token := hex.EncodeToString(bytes)

	// 删除旧的刷新令牌
	// 对于设备类型，只删除该设备的refresh token，不影响其他设备
	// 对于用户类型（如uniapp），如果有deviceID则只删除该设备的token，否则删除该用户的所有同类型token
	if tokenType == constant.TokenTypeDevice && deviceID != nil {
		// 设备类型：只删除该设备的refresh token
		s.db.WithContext(ctx).Where("device_id = ? AND type = ?", *deviceID, tokenType).Delete(&model.RefreshToken{})
	} else if tokenType == constant.TokenTypeUser && deviceID != nil {
		// 用户类型且有deviceID：只删除该设备的refresh token，支持多端登录
		s.db.WithContext(ctx).Where("device_id = ? AND type = ?", *deviceID, tokenType).Delete(&model.RefreshToken{})
	} else if userID != nil {
		// 其他类型或无deviceID：删除该用户的所有同类型token
		s.db.WithContext(ctx).Where("user_id = ? AND type = ?", *userID, tokenType).Delete(&model.RefreshToken{})
	}

	// 创建新的刷新令牌
	refreshToken := &model.RefreshToken{
		Token:     token,
		UserID:    userID,
		DeviceID:  deviceID,
		Type:      tokenType,
		ExpiresAt: expiresAt,
	}

	if err := s.db.WithContext(ctx).Create(refreshToken).Error; err != nil {
		return "", response.NewError(response.ERROR, "保存刷新令牌失败")
	}

	return token, nil
}

// ValidateRefreshToken 验证刷新令牌
func (s *UserService) ValidateRefreshToken(ctx context.Context, token string) (*model.RefreshToken, error) {
	var refreshToken model.RefreshToken
	if err := s.db.WithContext(ctx).Where("token = ?", token).First(&refreshToken).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.ERROR, "无效的刷新令牌")
		}
		return nil, response.NewError(response.ERROR, "查询刷新令牌失败")
	}

	// 检查是否过期
	if time.Now().After(refreshToken.ExpiresAt) {
		// 删除过期的令牌
		s.db.WithContext(ctx).Delete(&refreshToken)
		return nil, response.NewError(response.ERROR, "刷新令牌已过期")
	}

	return &refreshToken, nil
}

// DeleteRefreshToken 删除刷新令牌
func (s *UserService) DeleteRefreshToken(ctx context.Context, token string) error {
	if err := s.db.WithContext(ctx).Where("token = ?", token).Delete(&model.RefreshToken{}).Error; err != nil {
		return response.NewError(response.ERROR, "删除刷新令牌失败")
	}
	return nil
}

// GetUserList 获取用户列表
func (s *UserService) GetUserList(ctx context.Context, page, pageSize int, keyword string) ([]*model.User, int64, error) {
	var users []*model.User
	var total int64

	query := s.db.WithContext(ctx).Model(&model.User{})

	if keyword != "" {
		query = query.Where("username LIKE ? OR email LIKE ? OR nickname LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询用户总数失败")
	}

	// 查询用户列表
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&users).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "查询用户列表失败")
	}

	return users, total, nil
}

// UpdateUserStatus 更新用户状态
func (s *UserService) UpdateUserStatus(ctx context.Context, userID uint, status int8) error {
	if err := s.db.WithContext(ctx).Model(&model.User{}).Where("id = ?", userID).Update("status", status).Error; err != nil {
		return response.NewError(response.ERROR, "更新用户状态失败")
	}
	return nil
}

// GetUserStatistics 获取用户统计信息
func (s *UserService) GetUserStatistics(ctx context.Context) (map[string]int64, error) {
	stats := make(map[string]int64)

	// 总用户数
	var total int64
	if err := s.db.WithContext(ctx).Model(&model.User{}).Count(&total).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询用户总数失败")
	}
	stats["total"] = total

	// 活跃用户数（状态为1）
	var active int64
	if err := s.db.WithContext(ctx).Model(&model.User{}).Where("status = ?", constant.UserStatusNormal).Count(&active).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询活跃用户数失败")
	}
	stats["active"] = active

	// 今日注册用户数
	today := time.Now().Format("2006-01-02")
	var todayRegistered int64
	if err := s.db.WithContext(ctx).Model(&model.User{}).Where("DATE(created_at) = ?", today).Count(&todayRegistered).Error; err != nil {
		return nil, response.NewError(response.ERROR, "查询今日注册用户数失败")
	}
	stats["today_registered"] = todayRegistered

	return stats, nil
}
