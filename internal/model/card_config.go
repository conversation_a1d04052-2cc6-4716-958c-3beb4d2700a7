package model

import (
	"time"
)

// CardConfig 卡密配置关联表
type CardConfig struct {
	ID        int64     `json:"id" gorm:"primary_key"`
	Card<PERSON>ey   string    `json:"card_key" gorm:"column:card_key;uniqueIndex;size:32;not null"` // 卡密
	Config    string    `json:"config" gorm:"column:config;type:longtext;not null"`           // JSON配置项
	Version   int64     `json:"version" gorm:"column:version;default:1"`                      // 配置版本号
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TableName 指定表名
func (CardConfig) TableName() string {
	return "card_configs"
}
