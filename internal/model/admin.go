package model

import (
	"gorm.io/gorm"
	"time"
)

// Admin 管理员模型
type Admin struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	Username  string         `gorm:"type:varchar(32);uniqueIndex;not null" json:"username"`
	Password  string         `gorm:"type:varchar(255);not null" json:"password"`
	Status    int8           `gorm:"type:tinyint;default:1;not null" json:"status"` // 1:启用 2:禁用
	LastLogin *time.Time     `json:"last_login"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 表名
func (Admin) TableName() string {
	return "admin_users"
}
