package model

import (
	"time"
)

// ScriptPackage 市场脚本套餐模型
type ScriptPackage struct {
	ID             uint      `gorm:"primarykey" json:"id"`
	Name           string    `gorm:"type:varchar(100);not null" json:"name"`   // 套餐名称
	Description    string    `gorm:"type:text" json:"description"`             // 套餐描述
	Price          float64   `gorm:"type:decimal(10,2);not null" json:"price"` // 价格
	DeviceLimit    int       `gorm:"not null;default:1" json:"device_limit"`   // 设备数量限制
	Duration       int       `gorm:"not null;default:30" json:"duration"`      // 有效期（天）
	MarketScriptID uint      `gorm:"not null;index" json:"market_script_id"`   // 关联的市场脚本ID
	Status         int8      `gorm:"type:tinyint;default:1" json:"status"`     // 状态：1-启用 2-禁用
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`

	// 关联关系
	MarketScript *MarketScript `gorm:"foreignKey:MarketScriptID" json:"market_script,omitempty"`
}

// TableName 指定表名
func (ScriptPackage) TableName() string {
	return "market_script_packages"
}

// PaymentOrder 支付订单模型
type PaymentOrder struct {
	ID                uint       `gorm:"primarykey" json:"id"`
	OrderNo           string     `gorm:"type:varchar(50);uniqueIndex;not null" json:"order_no"`        // 订单号
	UserID            uint       `gorm:"not null;index" json:"user_id"`                                // 用户ID
	PackageID         uint       `gorm:"index" json:"package_id"`                                      // 套餐ID
	QuotaID           *int       `gorm:"index" json:"quota_id,omitempty"`                              // 配额ID（可为空，用于配额续费订单）
	ScriptID          *uint      `gorm:"index" json:"script_id,omitempty"`                             // 脚本ID（可为空，用于脚本续费订单）
	Amount            float64    `gorm:"type:decimal(10,2);not null" json:"amount"`                    // 支付金额
	PayType           string     `gorm:"type:varchar(20);not null" json:"pay_type"`                    // 支付方式：alipay/wxpay
	OrderType         string     `gorm:"type:varchar(20);not null;default:'script'" json:"order_type"` // 订单类型：script/quota/upgrade/quota_renewal
	Status            int8       `gorm:"type:tinyint;default:0" json:"status"`                         // 支付状态：0-未支付 1-已支付 2-已取消 3-已退款
	PaymentPlatform   string     `gorm:"type:varchar(20)" json:"payment_platform"`                     // 支付平台：zpay/alipay/wxpay等
	ThirdPartyOrderNo string     `gorm:"type:varchar(100)" json:"third_party_order_no"`                // 第三方支付平台订单号
	NotifyData        string     `gorm:"type:text" json:"notify_data"`                                 // 支付回调数据
	PaidAt            *time.Time `json:"paid_at"`                                                      // 支付时间
	ExpiredAt         time.Time  `json:"expired_at"`                                                   // 订单过期时间
	PayURL            string     `gorm:"-" json:"pay_url,omitempty"`                                   // 支付URL（不存储到数据库）
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`

	// 关联关系
	User    *User          `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Package *ScriptPackage `gorm:"foreignKey:PackageID" json:"package,omitempty"`
}

// TableName 指定表名
func (PaymentOrder) TableName() string {
	return "payment_orders"
}

// 支付平台常量
const (
	PaymentPlatformZPAY   = "zpay"   // ZPAY支付平台
	PaymentPlatformAlipay = "alipay" // 支付宝
	PaymentPlatformWxpay  = "wxpay"  // 微信支付
)

// 支付状态常量
const (
	PaymentStatusUnpaid   int8 = 0 // 未支付
	PaymentStatusPaid     int8 = 1 // 已支付
	PaymentStatusCanceled int8 = 2 // 已取消
	PaymentStatusRefunded int8 = 3 // 已退款
)

// 订单类型常量
const (
	OrderTypeScript        = "script"         // 脚本购买订单
	OrderTypeQuota         = "quota"          // 配额购买订单
	OrderTypeUpgrade       = "upgrade"        // 脚本升级订单
	OrderTypeQuotaRenewal  = "quota_renewal"  // 配额续费订单
	OrderTypeScriptRenewal = "script_renewal" // 脚本续费订单
)
