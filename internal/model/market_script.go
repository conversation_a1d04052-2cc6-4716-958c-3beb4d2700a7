package model

import (
	"time"
)

// MarketScript 脚本市场模型
type MarketScript struct {
	ID            uint    `gorm:"primarykey" json:"id"`
	Name          string  `gorm:"type:varchar(100);not null;index" json:"name"`    // 脚本名称
	Summary       string  `gorm:"type:varchar(100);default:''" json:"summary"`     // 脚本简介，用于列表页面显示（最多30个中文字符）
	Description   string  `gorm:"type:text" json:"description"`                    // 脚本描述
	Content       string  `gorm:"type:longtext;not null" json:"content"`           // 脚本内容
	Config        string  `gorm:"type:text" json:"config"`                         // JSON格式的配置参数
	DefaultValues string  `gorm:"type:text" json:"default_values"`                 // JSON格式的默认配置值（从Config Schema中提取的default值）
	Icon          string  `gorm:"type:varchar(255)" json:"icon"`                   // 脚本图标URL
	Version       string  `gorm:"type:varchar(20);default:'1.0.0'" json:"version"` // 脚本版本
	Tags          string  `gorm:"type:text" json:"tags"`                           // 标签，JSON数组格式
	Author        string  `gorm:"type:varchar(100);not null" json:"author"`        // 作者名称
	AuthorID      uint    `gorm:"index" json:"author_id"`                          // 作者ID
	Downloads     int     `gorm:"default:0" json:"downloads"`                      // 下载次数
	Rating        float64 `gorm:"type:decimal(3,2);default:0.00" json:"rating"`    // 评分
	RatingCount   int     `gorm:"default:0" json:"rating_count"`                   // 评分人数
	Status        int8    `gorm:"type:tinyint;default:1;index" json:"status"`      // 状态：1-上架 2-下架 3-审核中

	IsFeatured bool      `gorm:"default:false" json:"is_featured"` // 是否推荐脚本
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// 注意：MarketScriptInstall 模型已移除，功能已合并到 UserScript 模型中

// MarketScriptRating 脚本评分
type MarketScriptRating struct {
	ID             uint      `gorm:"primarykey" json:"id"`
	MarketScriptID uint      `gorm:"index" json:"market_script_id"`       // 市场脚本ID
	UserID         uint      `gorm:"index" json:"user_id"`                // 用户ID
	Rating         int8      `gorm:"type:tinyint;not null" json:"rating"` // 评分：1-5
	Comment        string    `gorm:"type:text" json:"comment"`            // 评论
	CreatedAt      time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (MarketScript) TableName() string {
	return "market_scripts"
}

func (MarketScriptRating) TableName() string {
	return "market_script_ratings"
}
