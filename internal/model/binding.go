package model

import (
	"time"
)

// CardDeviceBinding 卡密设备绑定关系
type CardDeviceBinding struct {
	ID         uint       `gorm:"primarykey" json:"id"`
	<PERSON><PERSON><PERSON>    string     `gorm:"type:varchar(32);uniqueIndex:idx_card_device;not null" json:"card_key"`
	DeviceID   string     `gorm:"type:varchar(64);uniqueIndex:idx_card_device;not null" json:"device_id"`
	Status     int        `gorm:"type:tinyint;default:1;index" json:"status"` // 状态：1-正常 2-离线
	LastActive *time.Time `gorm:"index" json:"last_active"`                   // 最近活跃时间
	CreatedAt  time.Time  `json:"created_at"`

	// 关联关系
	Card *CardKey `gorm:"foreignKey:CardKey;references:CardKey" json:"card"`
}

// TableName 表名
func (CardDeviceBinding) TableName() string {
	return "card_device_bindings"
}
