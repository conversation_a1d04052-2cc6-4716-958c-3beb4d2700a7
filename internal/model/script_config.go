package model

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"time"
)

// JSONString 自定义JSON类型，用于数据库存储
type JSONString string

// Value 实现driver.Valuer接口
func (j JSONString) Value() (driver.Value, error) {
	if j == "" {
		return nil, nil
	}
	return string(j), nil
}

// Scan 实现sql.Scanner接口
func (j *JSONString) Scan(value interface{}) error {
	if value == nil {
		*j = ""
		return nil
	}

	switch v := value.(type) {
	case []byte:
		*j = JSONString(v)
	case string:
		*j = JSONString(v)
	default:
		return errors.New("cannot scan non-string value into JSONString")
	}
	return nil
}

// UnmarshalJSON 解析JSON
func (j *JSONString) UnmarshalJSON(data []byte) error {
	*j = JSONString(data)
	return nil
}

// MarshalJSON 序列化JSON
func (j JSONString) MarshalJSON() ([]byte, error) {
	if j == "" {
		return []byte("null"), nil
	}
	// 直接返回原始JSON字符串，不需要再次编码
	return []byte(j), nil
}

// UserScriptConfig 用户脚本配置
type UserScriptConfig struct {
	ID           uint       `gorm:"primarykey" json:"id"`
	UserID       uint       `gorm:"not null;index" json:"user_id"`
	ScriptID     uint       `gorm:"not null;index" json:"script_id"`
	ConfigName   string     `gorm:"type:varchar(100);not null" json:"config_name"`
	ConfigValues JSONString `gorm:"type:json;not null" json:"config_values"`
	DeviceIDs    JSONString `gorm:"type:json" json:"device_ids"`
	IsActive     bool       `gorm:"default:true" json:"is_active"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`

	// 关联关系
	User       *User       `gorm:"foreignKey:UserID" json:"user,omitempty"`
	UserScript *UserScript `gorm:"foreignKey:ScriptID" json:"user_script,omitempty"`
}

// TableName 指定表名
func (UserScriptConfig) TableName() string {
	return "user_script_configs"
}

// DeviceConfigRelation 设备配置关联
type DeviceConfigRelation struct {
	ID           uint      `gorm:"primarykey" json:"id"`
	DeviceID     uint      `gorm:"not null;uniqueIndex:uk_device_config" json:"device_id"`
	UserConfigID uint      `gorm:"not null;uniqueIndex:uk_device_config;index" json:"user_config_id"`
	Priority     int       `gorm:"default:0;index" json:"priority"`
	IsEnabled    bool      `gorm:"default:true" json:"is_enabled"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// 关联关系
	Device     *Device           `gorm:"foreignKey:DeviceID" json:"device,omitempty"`
	UserConfig *UserScriptConfig `gorm:"foreignKey:UserConfigID" json:"user_config,omitempty"`
}

// TableName 指定表名
func (DeviceConfigRelation) TableName() string {
	return "device_config_relations"
}

// ConfigSchema 配置模式定义
type ConfigSchema struct {
	Type        string                 `json:"type"`
	Title       string                 `json:"title,omitempty"`
	Description string                 `json:"description,omitempty"`
	Properties  map[string]ConfigField `json:"properties,omitempty"`
	Required    []string               `json:"required,omitempty"`
}

// ConfigField 配置字段定义
type ConfigField struct {
	Type        string                 `json:"type"`
	Title       string                 `json:"title,omitempty"`
	Description string                 `json:"description,omitempty"`
	Default     interface{}            `json:"default,omitempty"`
	Enum        []string               `json:"enum,omitempty"`
	Minimum     *int                   `json:"minimum,omitempty"`
	Maximum     *int                   `json:"maximum,omitempty"`
	Format      string                 `json:"format,omitempty"`
	Items       *ConfigField           `json:"items,omitempty"`
	Properties  map[string]ConfigField `json:"properties,omitempty"`
}

// ConfigValue 配置值结构
type ConfigValue map[string]interface{}

// ValidateConfig 验证配置值是否符合模式
func (c *ConfigSchema) ValidateConfig(value ConfigValue) error {
	// 验证必填字段
	if c.Required != nil {
		for _, requiredField := range c.Required {
			if _, exists := value[requiredField]; !exists {
				return fmt.Errorf("必填字段 %s 不能为空", requiredField)
			}
		}
	}

	// 验证字段类型和值
	if c.Properties != nil {
		for fieldName, field := range c.Properties {
			if fieldValue, exists := value[fieldName]; exists {
				if err := validateField(fieldName, field, fieldValue); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// GetDefaultValues 获取默认配置值
func (c *ConfigSchema) GetDefaultValues() ConfigValue {
	defaults := make(ConfigValue)

	for fieldName, field := range c.Properties {
		if field.Default != nil {
			defaults[fieldName] = field.Default
		}
	}

	return defaults
}

// validateField 验证单个字段
func validateField(fieldName string, field ConfigField, value interface{}) error {
	switch field.Type {
	case "string":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("字段 %s 必须是字符串类型", fieldName)
		}
		// 验证枚举值
		if field.Enum != nil {
			strValue := value.(string)
			found := false
			for _, enumValue := range field.Enum {
				if enumValue == strValue {
					found = true
					break
				}
			}
			if !found {
				return fmt.Errorf("字段 %s 的值必须是以下之一: %v", fieldName, field.Enum)
			}
		}
	case "integer":
		if _, ok := value.(float64); !ok {
			return fmt.Errorf("字段 %s 必须是数字类型", fieldName)
		}
		numValue := value.(float64)
		// 验证最小值
		if field.Minimum != nil {
			if numValue < float64(*field.Minimum) {
				return fmt.Errorf("字段 %s 的值不能小于 %d", fieldName, *field.Minimum)
			}
		}
		// 验证最大值
		if field.Maximum != nil {
			if numValue > float64(*field.Maximum) {
				return fmt.Errorf("字段 %s 的值不能大于 %d", fieldName, *field.Maximum)
			}
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			return fmt.Errorf("字段 %s 必须是布尔类型", fieldName)
		}
	case "array":
		if _, ok := value.([]interface{}); !ok {
			return fmt.Errorf("字段 %s 必须是数组类型", fieldName)
		}
		// 验证数组元素类型
		if field.Items != nil {
			arrValue := value.([]interface{})
			for i, item := range arrValue {
				if err := validateField(fmt.Sprintf("%s[%d]", fieldName, i), *field.Items, item); err != nil {
					return err
				}
			}
		}
	case "object":
		if _, ok := value.(map[string]interface{}); !ok {
			return fmt.Errorf("字段 %s 必须是对象类型", fieldName)
		}
		// 验证对象属性
		if field.Properties != nil {
			objValue := value.(map[string]interface{})
			for propName, propField := range field.Properties {
				if propValue, exists := objValue[propName]; exists {
					if err := validateField(fmt.Sprintf("%s.%s", fieldName, propName), propField, propValue); err != nil {
						return err
					}
				}
			}
		}
	}
	return nil
}
