package model

import (
	"time"
)

// SystemLog 系统日志模型
type SystemLog struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Type      string    `gorm:"type:varchar(50);index" json:"type"` // operation, device, task, system
	Level     string    `gorm:"type:varchar(20);index" json:"level"` // info, warn, error
	Message   string    `gorm:"type:text" json:"message"`
	Data      string    `gorm:"type:text" json:"data"` // JSON格式的附加数据
	UserID    *uint     `gorm:"index" json:"user_id,omitempty"`
	DeviceID  *uint     `gorm:"index" json:"device_id,omitempty"`
	TaskID    *uint     `gorm:"index" json:"task_id,omitempty"`
	IP        string    `gorm:"type:varchar(45)" json:"ip"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (SystemLog) TableName() string {
	return "system_logs"
}
