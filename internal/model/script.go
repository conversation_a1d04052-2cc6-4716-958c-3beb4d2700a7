package model

import (
	"time"
)

// Script 脚本模型
type Script struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	Name        string    `gorm:"type:varchar(100);not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	Content     string    `gorm:"type:longtext" json:"content"`
	Config      string    `gorm:"type:text" json:"config"` // JSON格式的配置参数
	Type        string    `gorm:"type:varchar(50);default:'autox'" json:"type"`
	Status      int8      `gorm:"type:tinyint;default:1" json:"status"` // 1-启用 2-禁用
	OwnerID     uint      `gorm:"index" json:"owner_id"`
	Owner       User      `gorm:"foreignKey:OwnerID" json:"owner,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Script) TableName() string {
	return "scripts"
}
