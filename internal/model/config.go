package model

import (
	"time"
)

// ConfigValueType 配置值类型
type ConfigValueType string

const (
	ConfigValueTypeString ConfigValueType = "STRING" // 字符串类型
	ConfigValueTypeJSON   ConfigValueType = "JSON"   // JSON类型
	ConfigValueTypeYAML   ConfigValueType = "YAML"   // YAML类型
)

// SystemConfig 系统配置
type SystemConfig struct {
	ID        uint            `gorm:"primarykey" json:"id"`
	Key       string          `gorm:"type:varchar(64);uniqueIndex;not null" json:"key"`       // 配置键
	Value     string          `gorm:"type:text;not null" json:"value"`                        // 配置值
	Type      ConfigValueType `gorm:"type:varchar(10);not null;default:'STRING'" json:"type"` // 值类型：STRING/JSON/YAML
	Desc      string          `gorm:"type:varchar(255)" json:"desc"`                          // 配置描述
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
}

// TableName 表名
func (SystemConfig) TableName() string {
	return "system_configs"
}
