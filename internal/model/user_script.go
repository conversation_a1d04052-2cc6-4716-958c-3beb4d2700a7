package model

import (
	"time"
)

// UserScript 用户脚本模型（合并了原来的 Script 和 MarketScriptInstall）
type UserScript struct {
	ID             uint       `gorm:"primarykey" json:"id"`
	UserID         uint       `gorm:"not null;index" json:"user_id"`                   // 用户ID
	MarketScriptID uint       `gorm:"not null;index" json:"market_script_id"`          // 市场脚本ID
	PackageID      uint       `gorm:"index" json:"package_id"`                         // 套餐ID（可选，用于记录购买时的套餐信息）
	PackageName    string     `gorm:"type:varchar(100)" json:"package_name"`           // 套餐名称
	Name           string     `gorm:"type:varchar(100);not null" json:"name"`          // 脚本名称
	Content        string     `gorm:"type:longtext" json:"content"`                    // 脚本内容
	Config         string     `gorm:"type:text" json:"config"`                         // JSON格式的配置参数
	DefaultValues  string     `gorm:"type:text" json:"default_values"`                 // JSON格式的默认配置值（从Config Schema中提取的default值）
	Version        string     `gorm:"type:varchar(20);default:'1.0.0'" json:"version"` // 脚本版本
	Status         int8       `gorm:"type:tinyint;default:1" json:"status"`            // 状态：1-启用 2-禁用
	MaxDevices     int        `gorm:"type:int;default:1" json:"max_devices"`           // 最大设备限制数
	ExpiredAt      *time.Time `json:"expired_at,omitempty"`                            // 脚本可用截止时间
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`

	// 关联关系
	User         *User          `gorm:"foreignKey:UserID" json:"user,omitempty"`
	MarketScript *MarketScript  `gorm:"foreignKey:MarketScriptID" json:"market_script,omitempty"`
	Package      *ScriptPackage `gorm:"foreignKey:PackageID" json:"package,omitempty"`
}

// TableName 指定表名
func (UserScript) TableName() string {
	return "user_scripts"
}

// UserScriptWithConfigs 包含配置信息的用户脚本
type UserScriptWithConfigs struct {
	*UserScript
	Configs []*UserScriptConfig `json:"configs,omitempty"`
}

// UserScriptStatus 状态常量
const (
	UserScriptStatusEnabled int8 = 1 // 启用
	UserScriptStatusExpired int8 = 2 // 已过期
	UserScriptStatusDeleted int8 = 3 // 已删除
)

// UserScriptDeviceBinding 用户脚本设备绑定关系
type UserScriptDeviceBinding struct {
	ID           uint       `gorm:"primarykey" json:"id"`
	UserScriptID uint       `gorm:"not null;index" json:"user_script_id"` // 用户脚本ID
	DeviceID     uint64     `gorm:"not null;index" json:"device_id"`      // 设备ID
	Status       int8       `gorm:"type:tinyint;default:1" json:"status"` // 绑定状态：1-正常 2-禁用
	LastActive   *time.Time `gorm:"index" json:"last_active"`             // 最后活跃时间
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`

	// 关联关系
	UserScript *UserScript `gorm:"foreignKey:UserScriptID" json:"user_script,omitempty"`
	Device     *Device     `gorm:"foreignKey:DeviceID;references:ID" json:"device,omitempty"`
}

// TableName 指定表名
func (UserScriptDeviceBinding) TableName() string {
	return "user_script_device_bindings"
}

// UserScriptDeviceBindingStatus 绑定状态常量
const (
	UserScriptDeviceBindingStatusActive   int8 = 1 // 正常
	UserScriptDeviceBindingStatusDisabled int8 = 2 // 禁用
)
