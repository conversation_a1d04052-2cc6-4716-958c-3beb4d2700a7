package model

import (
	"time"
)

// GitHubScript 存储从GitHub webhook接收到的脚本内容
type GitHubScript struct {
	ID         uint      `gorm:"primaryKey" json:"id"`
	Branch     string    `gorm:"size:50;not null;index" json:"branch"`      // 分支名称：online或main
	FileName   string    `gorm:"size:255;not null;index" json:"file_name"`  // 文件名
	ScriptType string    `gorm:"size:20;not null;index" json:"script_type"` // 脚本类型：autojs, hamibot
	Content    string    `gorm:"type:longtext;not null" json:"content"`     // 脚本内容
	CreatedAt  time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 设置表名
func (GitHubScript) TableName() string {
	return "github_scripts"
}
