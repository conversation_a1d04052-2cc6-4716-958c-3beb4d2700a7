package model

import (
	"time"
)

// 配对状态常量
const (
	PairingStatusNone      = 0 // 未配对
	PairingStatusPending   = 1 // 配对中
	PairingStatusCompleted = 2 // 已配对
	PairingStatusExpired   = 3 // 已过期
)

// Device 设备模型
type Device struct {
	ID             uint64 `gorm:"primarykey" json:"id"`
	DeviceID       string `gorm:"type:varchar(100);uniqueIndex;not null" json:"device_id"`
	Name           string `gorm:"type:varchar(100)" json:"name"`
	DeviceModel    string `gorm:"type:varchar(100);column:device_model" json:"device_model"`      // 设备型号
	AndroidVersion string `gorm:"type:varchar(20);column:android_version" json:"android_version"` // 安卓版本
	AutoxVersion   string `gorm:"type:varchar(20);column:autox_version" json:"autox_version"`     // AutoX版本

	Status     int        `gorm:"type:int;default:0" json:"status"` // 0-离线 1-在线
	LastActive *time.Time `json:"last_active"`
	UserID     uint       `gorm:"index" json:"user_id"`
	User       User       `gorm:"foreignKey:UserID" json:"user,omitempty"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 配对相关字段
	PairingCode      *string    `gorm:"type:varchar(10);index" json:"pairing_code,omitempty"` // 配对码
	PairingStatus    int        `gorm:"type:int;default:0;index" json:"pairing_status"`       // 配对状态
	PairingExpiresAt *time.Time `gorm:"index" json:"pairing_expires_at,omitempty"`            // 配对过期时间

	// WebSocket通信相关字段
	Token          *string    `gorm:"type:varchar(255);index" json:"token,omitempty"` // WebSocket认证token
	TokenExpiresAt *time.Time `gorm:"index" json:"token_expires_at,omitempty"`        // Token过期时间

	// 配额信息（不存储在数据库中，仅用于API响应）
	QuotaInfo *DeviceQuotaInfo `gorm:"-" json:"quota_info,omitempty"`
}

// TableName 指定表名
func (Device) TableName() string {
	return "devices"
}

// IsPairingExpired 检查配对是否已过期
func (d *Device) IsPairingExpired() bool {
	if d.PairingExpiresAt == nil {
		return false
	}
	return time.Now().After(*d.PairingExpiresAt)
}

// IsPairingValid 检查配对是否有效（未过期且配对中）
func (d *Device) IsPairingValid() bool {
	return d.PairingStatus == PairingStatusPending && !d.IsPairingExpired()
}

// DeviceQuotaInfo 设备配额信息
type DeviceQuotaInfo struct {
	IsFree      bool       `json:"is_free"`            // 是否免费
	ExpiresAt   *time.Time `json:"expires_at"`         // 到期时间
	QuotaType   string     `json:"quota_type"`         // 配额类型：free/paid
	Description string     `json:"description"`        // 描述
	QuotaID     *uint      `json:"quota_id,omitempty"` // 配额ID（仅付费配额）
}
