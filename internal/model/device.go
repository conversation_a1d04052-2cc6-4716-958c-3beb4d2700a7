package model

import (
	"time"
)

// Device 设备模型
type Device struct {
	ID         uint      `gorm:"primarykey" json:"id"`
	DeviceID   string    `gorm:"type:varchar(100);uniqueIndex;not null" json:"device_id"`
	Name       string    `gorm:"type:varchar(100)" json:"name"`
	Type       string    `gorm:"type:varchar(50);default:'android'" json:"type"` // android, ios
	Status     int8      `gorm:"type:tinyint;default:0" json:"status"`           // 0-离线 1-在线 2-忙碌
	LastActive *time.Time `json:"last_active"`
	UserID     uint      `gorm:"index" json:"user_id"`
	User       User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	IP         string    `gorm:"type:varchar(45)" json:"ip"`
	UserAgent  string    `gorm:"type:varchar(255)" json:"user_agent"`
	Version    string    `gorm:"type:varchar(50)" json:"version"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Device) TableName() string {
	return "devices"
}

// DeviceGroup 设备分组模型
type DeviceGroup struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	Name        string    `gorm:"type:varchar(100);not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	UserID      uint      `gorm:"index" json:"user_id"`
	User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	DeviceIDs   string    `gorm:"type:text" json:"device_ids"` // JSON格式的设备ID列表
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (DeviceGroup) TableName() string {
	return "device_groups"
}
