package model

import (
	"time"
)

// User 用户模型
type User struct {
	ID           uint      `gorm:"primarykey" json:"id"`
	Username     string    `gorm:"type:varchar(50);uniqueIndex;not null" json:"username"`
	PasswordHash string    `gorm:"type:varchar(255);not null" json:"-"`
	Email        string    `gorm:"type:varchar(100);uniqueIndex" json:"email"`
	Nickname     string    `gorm:"type:varchar(50)" json:"nickname"`
	Avatar       string    `gorm:"type:varchar(255)" json:"avatar"`
	Status       int8      `gorm:"type:tinyint;default:1" json:"status"` // 1-正常 2-禁用
	LastLoginAt  *time.Time `json:"last_login_at"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// Device 设备模型
type Device struct {
	ID         uint      `gorm:"primarykey" json:"id"`
	DeviceID   string    `gorm:"type:varchar(100);uniqueIndex;not null" json:"device_id"`
	Name       string    `gorm:"type:varchar(100)" json:"name"`
	Type       string    `gorm:"type:varchar(50);default:'android'" json:"type"` // android, ios
	Status     int8      `gorm:"type:tinyint;default:0" json:"status"`           // 0-离线 1-在线 2-忙碌
	LastActive *time.Time `json:"last_active"`
	UserID     uint      `gorm:"index" json:"user_id"`
	User       User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	IP         string    `gorm:"type:varchar(45)" json:"ip"`
	UserAgent  string    `gorm:"type:varchar(255)" json:"user_agent"`
	Version    string    `gorm:"type:varchar(50)" json:"version"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Device) TableName() string {
	return "devices"
}

// Script 脚本模型
type Script struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	Name        string    `gorm:"type:varchar(100);not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	Content     string    `gorm:"type:longtext" json:"content"`
	Config      string    `gorm:"type:text" json:"config"` // JSON格式的配置参数
	Type        string    `gorm:"type:varchar(50);default:'autox'" json:"type"`
	Status      int8      `gorm:"type:tinyint;default:1" json:"status"` // 1-启用 2-禁用
	OwnerID     uint      `gorm:"index" json:"owner_id"`
	Owner       User      `gorm:"foreignKey:OwnerID" json:"owner,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Script) TableName() string {
	return "scripts"
}

// Task 任务模型
type Task struct {
	ID            uint      `gorm:"primarykey" json:"id"`
	Name          string    `gorm:"type:varchar(100);not null" json:"name"`
	Description   string    `gorm:"type:text" json:"description"`
	Type          string    `gorm:"type:varchar(50);default:'script'" json:"type"`
	Status        int8      `gorm:"type:tinyint;default:0" json:"status"` // 0-待执行 1-执行中 2-已完成 3-失败 4-已取消
	TargetDevices string    `gorm:"type:text" json:"target_devices"`      // JSON格式的设备ID列表
	ScriptID      uint      `gorm:"index" json:"script_id"`
	Script        Script    `gorm:"foreignKey:ScriptID" json:"script,omitempty"`
	Config        string    `gorm:"type:text" json:"config"`    // JSON格式的执行参数
	Progress      int       `gorm:"default:0" json:"progress"`  // 执行进度 0-100
	StartedAt     *time.Time `json:"started_at"`
	CompletedAt   *time.Time `json:"completed_at"`
	CreatedBy     uint      `gorm:"index" json:"created_by"`
	Creator       User      `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Task) TableName() string {
	return "tasks"
}

// TaskResult 任务结果模型
type TaskResult struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	TaskID    uint      `gorm:"index" json:"task_id"`
	Task      Task      `gorm:"foreignKey:TaskID" json:"task,omitempty"`
	DeviceID  uint      `gorm:"index" json:"device_id"`
	Device    Device    `gorm:"foreignKey:DeviceID" json:"device,omitempty"`
	Status    int8      `gorm:"type:tinyint;default:0" json:"status"` // 0-待执行 1-执行中 2-成功 3-失败
	Output    string    `gorm:"type:longtext" json:"output"`
	Error     string    `gorm:"type:text" json:"error"`
	StartedAt *time.Time `json:"started_at"`
	EndedAt   *time.Time `json:"ended_at"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TableName 指定表名
func (TaskResult) TableName() string {
	return "task_results"
}

// RefreshToken 刷新令牌模型
type RefreshToken struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Token     string    `gorm:"type:varchar(255);uniqueIndex;not null" json:"token"`
	UserID    *uint     `gorm:"index" json:"user_id,omitempty"`
	DeviceID  *uint     `gorm:"index" json:"device_id,omitempty"`
	Type      string    `gorm:"type:varchar(20);not null" json:"type"` // user, device
	ExpiresAt time.Time `gorm:"not null" json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (RefreshToken) TableName() string {
	return "refresh_tokens"
}

// SystemLog 系统日志模型
type SystemLog struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Type      string    `gorm:"type:varchar(50);index" json:"type"` // operation, device, task, system
	Level     string    `gorm:"type:varchar(20);index" json:"level"` // info, warn, error
	Message   string    `gorm:"type:text" json:"message"`
	Data      string    `gorm:"type:text" json:"data"` // JSON格式的附加数据
	UserID    *uint     `gorm:"index" json:"user_id,omitempty"`
	DeviceID  *uint     `gorm:"index" json:"device_id,omitempty"`
	TaskID    *uint     `gorm:"index" json:"task_id,omitempty"`
	IP        string    `gorm:"type:varchar(45)" json:"ip"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (SystemLog) TableName() string {
	return "system_logs"
}

// DeviceGroup 设备分组模型
type DeviceGroup struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	Name        string    `gorm:"type:varchar(100);not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	UserID      uint      `gorm:"index" json:"user_id"`
	User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	DeviceIDs   string    `gorm:"type:text" json:"device_ids"` // JSON格式的设备ID列表
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (DeviceGroup) TableName() string {
	return "device_groups"
}
