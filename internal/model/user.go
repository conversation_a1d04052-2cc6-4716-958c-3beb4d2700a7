package model

import (
	"time"
)

// User 用户模型
type User struct {
	ID           uint      `gorm:"primarykey" json:"id"`
	Username     string    `gorm:"type:varchar(50);uniqueIndex;not null" json:"username"`
	PasswordHash string    `gorm:"type:varchar(255);not null" json:"-"`
	Email        string    `gorm:"type:varchar(100);uniqueIndex" json:"email"`
	Nickname     string    `gorm:"type:varchar(50)" json:"nickname"`
	Avatar       string    `gorm:"type:varchar(255)" json:"avatar"`
	Status       int8      `gorm:"type:tinyint;default:1" json:"status"` // 1-正常 2-禁用
	LastLoginAt  *time.Time `json:"last_login_at"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// RefreshToken 刷新令牌模型
type RefreshToken struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Token     string    `gorm:"type:varchar(255);uniqueIndex;not null" json:"token"`
	UserID    *uint     `gorm:"index" json:"user_id,omitempty"`
	DeviceID  *uint     `gorm:"index" json:"device_id,omitempty"`
	Type      string    `gorm:"type:varchar(20);not null" json:"type"` // user, device
	ExpiresAt time.Time `gorm:"not null" json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (RefreshToken) TableName() string {
	return "refresh_tokens"
}


