package model

import (
	"time"
)

// Task 任务模型
type Task struct {
	ID            uint      `gorm:"primarykey" json:"id"`
	Name          string    `gorm:"type:varchar(100);not null" json:"name"`
	Description   string    `gorm:"type:text" json:"description"`
	Type          string    `gorm:"type:varchar(50);default:'script'" json:"type"`
	Status        int8      `gorm:"type:tinyint;default:0" json:"status"` // 0-待执行 1-执行中 2-已完成 3-失败 4-已取消
	TargetDevices string    `gorm:"type:text" json:"target_devices"`      // JSON格式的设备ID列表
	ScriptID      uint      `gorm:"index" json:"script_id"`
	Script        Script    `gorm:"foreignKey:ScriptID" json:"script,omitempty"`
	Config        string    `gorm:"type:text" json:"config"`    // JSON格式的执行参数
	Progress      int       `gorm:"default:0" json:"progress"`  // 执行进度 0-100
	StartedAt     *time.Time `json:"started_at"`
	CompletedAt   *time.Time `json:"completed_at"`
	CreatedBy     uint      `gorm:"index" json:"created_by"`
	Creator       User      `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Task) TableName() string {
	return "tasks"
}

// TaskResult 任务结果模型
type TaskResult struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	TaskID    uint      `gorm:"index" json:"task_id"`
	Task      Task      `gorm:"foreignKey:TaskID" json:"task,omitempty"`
	DeviceID  uint      `gorm:"index" json:"device_id"`
	Device    Device    `gorm:"foreignKey:DeviceID" json:"device,omitempty"`
	Status    int8      `gorm:"type:tinyint;default:0" json:"status"` // 0-待执行 1-执行中 2-成功 3-失败
	Output    string    `gorm:"type:longtext" json:"output"`
	Error     string    `gorm:"type:text" json:"error"`
	StartedAt *time.Time `json:"started_at"`
	EndedAt   *time.Time `json:"ended_at"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TableName 指定表名
func (TaskResult) TableName() string {
	return "task_results"
}
