package model

import (
	"time"
)

// DeviceQuota 设备配额模型
type DeviceQuota struct {
	ID             uint       `json:"id" gorm:"primaryKey"`
	UserID         uint       `json:"user_id" gorm:"not null"`
	TotalQuota     int        `json:"total_quota" gorm:"not null;default:1;comment:总配额数量"`
	UsedQuota      int        `json:"used_quota" gorm:"not null;default:0;comment:已使用配额数量"`
	AvailableQuota int        `json:"available_quota" gorm:"not null;default:1;comment:可用配额数量"`
	PricePerQuota  float64    `json:"price_per_quota" gorm:"type:decimal(10,2);default:1.00;comment:每个配额的价格"`
	TotalAmount    float64    `json:"total_amount" gorm:"type:decimal(10,2);not null;comment:总金额"`
	ExpiresAt      *time.Time `json:"expires_at" gorm:"comment:过期时间"`
	OrderNo        *string    `json:"order_no" gorm:"comment:关联的订单号"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}

// TableName 指定表名
func (DeviceQuota) TableName() string {
	return "device_quotas"
}

// DeviceQuotaUsage 设备配额使用记录模型
type DeviceQuotaUsage struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	QuotaID   uint      `json:"quota_id" gorm:"not null"`
	DeviceID  uint      `json:"device_id" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (DeviceQuotaUsage) TableName() string {
	return "device_quota_usage"
}

// 免费配额数量
const FreeQuotaCount = 3
