package app

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"goadmin/config"
	"goadmin/internal/handler"
	"goadmin/internal/middleware"
	"goadmin/internal/service"
)

type App struct {
	cfg              *config.Config
	db               *gorm.DB
	engine           *gin.Engine
	schedulerService *service.SchedulerService
}

func NewApp(cfg *config.Config, db *gorm.DB) *App {
	app := &App{
		cfg:    cfg,
		db:     db,
		engine: gin.Default(),
	}
	// Register RequestID middleware
	app.engine.Use(middleware.RequestID())
	// Apply the logging middleware
	app.engine.Use(middleware.RequestResponseLogger())

	app.setupRoutes()
	return app
}

func (a *App) Run(addr string) error {
	server := &http.Server{
		Addr:              addr,
		Handler:           a.engine,
		ReadTimeout:       10 * time.Second,
		WriteTimeout:      10 * time.Second,
		IdleTimeout:       120 * time.Second,
		MaxHeaderBytes:    1 << 20, // 1MB
		ReadHeaderTimeout: 5 * time.Second,
	}
	return server.ListenAndServe()
}

func (a *App) Shutdown() error {
	// 停止调度器
	if a.schedulerService != nil {
		a.schedulerService.Stop()
	}
	return nil
}

func (a *App) setupRoutes() {
	// 创建服务实例
	jwtService := service.NewJWTService(a.cfg, a.db)
	thirdAPIKeyService := service.NewThirdAPIKeyService(a.db)
	signatureService := service.NewSignatureService()
	adminService := service.NewAdminService(a.db)
	cardService := service.NewCardKeyService(a.db)
	configService := service.NewConfigService(a.db)
	bindService := service.NewBindingService(a.db, cardService, configService)
	userLogService := service.NewUserLogService(a.db)
	operationLogService := service.NewOperationLogService(a.db)
	githubService := service.NewGitHubService(a.cfg, a.db)
	cardConfigService := service.NewCardConfigService(a.db)
	userService := service.NewUserService(a.db)
	deviceService := service.NewDeviceService(a.db)
	scriptService := service.NewScriptService(a.db)
	taskService := service.NewTaskService(a.db)
	systemLogService := service.NewSystemLogService(a.db)

	// 创建调度器服务
	a.schedulerService = service.NewSchedulerService(bindService, configService)
	a.schedulerService.Start()

	// 创建处理器实例
	adminHandler := handler.NewAdminHandler(adminService, jwtService)
	cardHandler := handler.NewCardKeyHandler(cardService, bindService, jwtService, userLogService)
	bindHandler := handler.NewBindingHandler(bindService, jwtService)
	configHandler := handler.NewConfigHandler(configService)
	userLogHandler := handler.NewUserLogHandler(userLogService)
	githubHandler := handler.NewGitHubHandler(githubService, jwtService)
	thirdAPIHandler := handler.NewThirdAPIHandler(thirdAPIKeyService, cardService)
	cardConfigHandler := handler.NewCardConfigHandler(cardConfigService)
	userHandler := handler.NewUserHandler(userService, jwtService)
	deviceHandler := handler.NewDeviceHandler(deviceService)
	scriptHandler := handler.NewScriptHandler(scriptService)
	taskHandler := handler.NewTaskHandler(taskService)
	systemLogHandler := handler.NewSystemLogHandler(systemLogService)
	dashboardHandler := handler.NewDashboardHandler(userService, deviceService, scriptService, taskService, systemLogService)
	eventHandler := handler.NewEventHandler()

	// 设置OpenAPI路由（客户端API）
	api := a.engine.Group("/api/v1")
	// 注册操作日志中间件
	api.Use(middleware.OperationLog(operationLogService))
	{
		// 事件接口
		api.POST("/event", eventHandler.HandleEvent)

		// GitHub webhook接口
		api.POST("/github/webhook", githubHandler.HandleGitHubWebhook)

		// 新的Hamibot脚本接口 - 需要HamibotAuth鉴权
		scriptHamibotAuth := api.Group("/script")
		scriptHamibotAuth.Use(middleware.HamibotAuth(jwtService))
		{
			scriptHamibotAuth.POST("/hamibot", githubHandler.GetHamibotContent)
			scriptHamibotAuth.POST("/bot", githubHandler.GetBotContent)
			scriptHamibotAuth.POST("/botui", githubHandler.GetBotUIContent)
			scriptHamibotAuth.POST("/ppmt", githubHandler.GetPpmtContent)
		}

		// 卡密登录（公开路由）
		api.POST("/login", cardHandler.Login)
		api.POST("/script/autojs_login", githubHandler.GetAutoJSLoginContent)
		// 绑定管理
		bindings := api.Group("/bindings") // 保持在api组中
		{
			bindings.POST("/bind", bindHandler.BindDevice)             // 绑定设备，无需鉴权
			bindings.POST("/unbind_all", bindHandler.UnbindAllDevices) // 解绑所有设备
		}

		// 配置管理
		api.POST("/config", configHandler.GetConfig)

		// 卡密配置管理（需要ClientAuth验证）
		cardConfigs := api.Group("/card-configs")
		cardConfigs.Use(middleware.ClientAuth(jwtService))
		{
			cardConfigs.POST("/upload", cardConfigHandler.UploadConfig) // 上传配置
			cardConfigs.POST("/get", cardConfigHandler.GetConfig)       // 获取配置
		}

		// 用户认证相关
		auth := api.Group("/auth")
		{
			auth.POST("/register", userHandler.Register)       // 用户注册
			auth.POST("/login", userHandler.Login)             // 用户登录
			auth.POST("/refresh", userHandler.RefreshToken)    // 刷新令牌
			auth.POST("/logout", userHandler.Logout)           // 用户登出
		}

		// 需要用户认证的接口
		userAPI := api.Group("/user")
		userAPI.Use(middleware.UserAuth(jwtService))
		{
			// 用户资料
			userAPI.GET("/profile", userHandler.GetProfile)
			userAPI.PUT("/profile", userHandler.UpdateProfile)
			userAPI.PUT("/password", userHandler.ChangePassword)

			// 用户仪表盘
			userAPI.GET("/dashboard", dashboardHandler.GetUserDashboard)

			// 设备管理
			devices := userAPI.Group("/devices")
			{
				devices.POST("/register", deviceHandler.RegisterDevice)
				devices.GET("/list", deviceHandler.GetUserDevices)
				devices.PUT("/status", deviceHandler.UpdateDeviceStatus)
				devices.DELETE("", deviceHandler.DeleteDevice)
				devices.POST("/groups", deviceHandler.CreateDeviceGroup)
				devices.GET("/groups", deviceHandler.GetDeviceGroups)
				devices.PUT("/groups", deviceHandler.UpdateDeviceGroup)
				devices.DELETE("/groups", deviceHandler.DeleteDeviceGroup)
			}

			// 脚本管理
			scripts := userAPI.Group("/scripts")
			{
				scripts.POST("", scriptHandler.CreateScript)
				scripts.GET("/list", scriptHandler.GetUserScripts)
				scripts.GET("/:id", scriptHandler.GetScriptByID)
				scripts.PUT("", scriptHandler.UpdateScript)
				scripts.PUT("/status", scriptHandler.UpdateScriptStatus)
				scripts.DELETE("", scriptHandler.DeleteScript)
				scripts.GET("/:id/config", scriptHandler.GetScriptConfig)
				scripts.PUT("/config", scriptHandler.UpdateScriptConfig)
				scripts.POST("/clone", scriptHandler.CloneScript)
			}

			// 任务管理
			tasks := userAPI.Group("/tasks")
			{
				tasks.POST("", taskHandler.CreateTask)
				tasks.GET("/list", taskHandler.GetUserTasks)
				tasks.GET("/:id", taskHandler.GetTaskByID)
				tasks.PUT("/start", taskHandler.StartTask)
				tasks.PUT("/cancel", taskHandler.CancelTask)
				tasks.PUT("/progress", taskHandler.UpdateTaskProgress)
				tasks.GET("/:id/results", taskHandler.GetTaskResults)
				tasks.PUT("/result", taskHandler.UpdateTaskResult)
				tasks.DELETE("", taskHandler.DeleteTask)
				tasks.GET("/statistics", taskHandler.GetTaskStatistics)
			}
		}

		// 设备API（需要设备认证）
		deviceAPI := api.Group("/device")
		deviceAPI.Use(middleware.DeviceAuth(jwtService, deviceService))
		{
			deviceAPI.GET("/info", deviceHandler.GetDeviceInfo)
			deviceAPI.PUT("/status", deviceHandler.UpdateDeviceStatus)
			deviceAPI.GET("/tasks", taskHandler.GetUserTasks)
			deviceAPI.PUT("/task/result", taskHandler.UpdateTaskResult)
		}

		// 需要ClientAuth验证的路由
		authorized := api.Group("")
		authorized.Use(middleware.ClientAuth(jwtService))
		{
			// 脚本接口
			scriptGroup := authorized.Group("/script")
			{
				scriptGroup.POST("/autojs", githubHandler.GetAutoJSContent)
			}

			// 绑定管理
			authorizedBindings := authorized.Group("/bindings")
			{
				authorizedBindings.POST("/unbind", bindHandler.UnbindDevice) // 解绑设备，支持批量
			}

			// 卡密充值接口
			authorized.POST("/cardkeys/recharge", cardHandler.RechargeCardKey) // 以卡充卡
		}

		// 第三方API路由
		third := api.Group("/third")
		third.Use(middleware.SignatureAuth(thirdAPIKeyService, signatureService))
		{
			// 第三方充值接口（使用签名认证）
			third.POST("/recharge", thirdAPIHandler.RechargeCardKey)
			// 新增：获取卡密使用时间和到期时间
			third.POST("/cardkey/detail", thirdAPIHandler.GetCardKeyDetail)
		}
	}

	// 设置AdminAPI路由（后台管理API）
	admin := a.engine.Group("/admin/v1")
	{
		// 管理员登录（公开路由）
		admin.POST("/auth/login", adminHandler.Login)

		// 需要管理员权限的路由
		authorized := admin.Group("")
		authorized.Use(middleware.AdminAuth(jwtService))
		{
			// 仪表盘
			authorized.GET("/dashboard", dashboardHandler.GetOverview)
			authorized.GET("/system/status", dashboardHandler.GetSystemStatus)

			// 管理员管理
			admins := authorized.Group("/admins")
			{
				admins.POST("/list", adminHandler.ListAdmins)
				admins.POST("/create", adminHandler.CreateAdmin)
				admins.POST("/update", adminHandler.UpdateAdmin)
				admins.POST("/delete", adminHandler.DeleteAdmin)
				admins.POST("/status", adminHandler.UpdateStatus)
			}

			// 用户管理
			users := authorized.Group("/users")
			{
				users.GET("/list", userHandler.GetUserList)
				users.PUT("/status", userHandler.UpdateUserStatus)
				users.GET("/statistics", userHandler.GetUserStatistics)
			}

			// 设备管理
			devices := authorized.Group("/devices")
			{
				devices.GET("/list", deviceHandler.GetDeviceList)
				devices.GET("/:device_id", deviceHandler.GetDeviceInfo)
				devices.DELETE("", deviceHandler.DeleteDevice)
				devices.GET("/online/count", deviceHandler.GetOnlineDeviceCount)
			}

			// 脚本管理
			scripts := authorized.Group("/scripts")
			{
				scripts.GET("/list", scriptHandler.GetScriptList)
				scripts.GET("/:id", scriptHandler.GetScriptByID)
				scripts.PUT("/status", scriptHandler.UpdateScriptStatus)
				scripts.DELETE("", scriptHandler.DeleteScript)
				scripts.GET("/type/:type", scriptHandler.GetScriptsByType)
			}

			// 任务管理
			tasks := authorized.Group("/tasks")
			{
				tasks.GET("/list", taskHandler.GetTaskList)
				tasks.GET("/:id", taskHandler.GetTaskByID)
				tasks.PUT("/start", taskHandler.StartTask)
				tasks.PUT("/cancel", taskHandler.CancelTask)
				tasks.GET("/:id/results", taskHandler.GetTaskResults)
				tasks.DELETE("", taskHandler.DeleteTask)
				tasks.GET("/statistics", taskHandler.GetTaskStatistics)
			}

			// 系统日志管理
			systemLogs := authorized.Group("/system-logs")
			{
				systemLogs.GET("/list", systemLogHandler.GetLogList)
				systemLogs.GET("/statistics", systemLogHandler.GetLogStatistics)
				systemLogs.GET("/recent", systemLogHandler.GetRecentLogs)
				systemLogs.GET("/range", systemLogHandler.GetLogsByTimeRange)
				systemLogs.POST("/cleanup", systemLogHandler.CleanupLogs)
			}

			// 卡密管理
			cardkeys := authorized.Group("/cardkeys")
			{
				cardkeys.POST("/list", cardHandler.ListCardKeys)
				cardkeys.POST("/create", cardHandler.GenerateCardKey)
				cardkeys.POST("/delete", cardHandler.DeleteCardKey)
				cardkeys.POST("/detail", cardHandler.GetCardKeyWithDevices)
				cardkeys.POST("/recharge", cardHandler.RechargeCardKey)
			}

			// 设备绑定管理
			bindings := authorized.Group("/bindings")
			{
				bindings.POST("/unbind", bindHandler.UnbindDevice)         // 解绑设备，支持批量
				bindings.POST("/unbind_all", bindHandler.UnbindAllDevices) // 解绑所有设备
			}

			// 系统配置管理
			configs := authorized.Group("/configs")
			{
				configs.POST("/list", configHandler.ListConfigs)
				configs.POST("/set", configHandler.SetConfig)
				configs.POST("/delete", configHandler.DeleteConfig)
			}

			// 脚本管理
			scripts := authorized.Group("/github/scripts")
			{
				scripts.GET("", githubHandler.GetScriptList)
				scripts.POST("/update", githubHandler.UpdateScript)
			}

			// 第三方API密钥管理
			apiKeys := authorized.Group("/api-keys")
			{
				apiKeys.POST("/create", thirdAPIHandler.CreateAPIKey)
				apiKeys.POST("/list", thirdAPIHandler.ListAPIKeys)
				apiKeys.POST("/detail", thirdAPIHandler.GetAPIKeyDetail)
				apiKeys.POST("/delete", thirdAPIHandler.DeleteAPIKey)
				apiKeys.POST("/update-status", thirdAPIHandler.UpdateAPIKeyStatus)
			}

			// 统计数据
			authorized.GET("/stats", bindHandler.GetStats)
			authorized.GET("/stats/hamibot", userLogHandler.GetDailyActiveUsers)
			authorized.GET("/stats/hamibot/robots", userLogHandler.GetDailyActiveRobots)
		}
	}
}
