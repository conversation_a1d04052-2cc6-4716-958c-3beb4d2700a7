package app

import (
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"goadmin/config"
	"goadmin/internal/handler"
	"goadmin/internal/middleware"
	"goadmin/internal/service"
)

type App struct {
	cfg              *config.Config
	db               *gorm.DB
	engine           *gin.Engine
	schedulerService *service.SchedulerService
}

func NewApp(cfg *config.Config, db *gorm.DB) *App {
	app := &App{
		cfg:    cfg,
		db:     db,
		engine: gin.Default(),
	}

	// 添加 CORS 中间件
	app.engine.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.<PERSON>er("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Register RequestID middleware
	app.engine.Use(middleware.RequestID())
	// Apply the logging middleware
	app.engine.Use(middleware.RequestResponseLogger())

	app.setupRoutes()
	return app
}

func (a *App) Run(addr string) error {
	server := &http.Server{
		Addr:              addr,
		Handler:           a.engine,
		ReadTimeout:       10 * time.Second,
		WriteTimeout:      10 * time.Second,
		IdleTimeout:       120 * time.Second,
		MaxHeaderBytes:    1 << 20, // 1MB
		ReadHeaderTimeout: 5 * time.Second,
	}
	return server.ListenAndServe()
}

func (a *App) Shutdown() error {
	// 停止调度器
	if a.schedulerService != nil {
		a.schedulerService.Stop()
	}
	return nil
}

func (a *App) setupRoutes() {
	// 创建服务实例
	jwtService := service.NewJWTService(a.cfg, a.db)
	thirdAPIKeyService := service.NewThirdAPIKeyService(a.db)
	signatureService := service.NewSignatureService()
	adminService := service.NewAdminService(a.db)
	cardService := service.NewCardKeyService(a.db)
	configService := service.NewConfigService(a.db)
	bindService := service.NewBindingService(a.db, cardService, configService)
	userLogService := service.NewUserLogService(a.db)
	operationLogService := service.NewOperationLogService(a.db)
	githubService := service.NewGitHubService(a.cfg, a.db)
	cardConfigService := service.NewCardConfigService(a.db)
	userService := service.NewUserService(a.db)

	// 创建Redis服务
	redisService, err := service.NewRedisService(a.cfg)
	if err != nil {
		log.Printf("Redis服务初始化失败: %v", err)
		// 如果Redis不可用，可以降级到数据库模式
		redisService = nil
	}

	// 创建WebSocket服务（临时创建，用于解决循环依赖）
	webSocketService := service.NewWebSocketService(nil, nil, jwtService, redisService, a.cfg)

	// 创建配额服务
	quotaService := service.NewDeviceQuotaService(a.db)

	deviceService := service.NewDeviceService(a.db, jwtService, userService, redisService, webSocketService, quotaService)
	userScriptService := service.NewUserScriptService(a.db)
	scriptConfigService := service.NewScriptConfigService(a.db)
	marketScriptService := service.NewMarketScriptService(a.db, redisService)

	zpayService := service.NewZPAYService(a.cfg)
	paymentService := service.NewPaymentService(a.db, zpayService, marketScriptService)
	userScriptDeviceBindingService := service.NewUserScriptDeviceBindingService(a.db)

	// 重新创建WebSocket服务，传入正确的deviceService和userScriptService
	webSocketService = service.NewWebSocketService(deviceService, userScriptService, jwtService, redisService, a.cfg)

	// 创建调度器服务
	a.schedulerService = service.NewSchedulerService(bindService, configService, redisService, deviceService)
	a.schedulerService.Start()

	// 创建处理器实例
	adminHandler := handler.NewAdminHandler(adminService, jwtService)
	cardHandler := handler.NewCardKeyHandler(cardService, bindService, jwtService, userLogService)
	bindHandler := handler.NewBindingHandler(bindService, jwtService)
	configHandler := handler.NewConfigHandler(configService)
	userLogHandler := handler.NewUserLogHandler(userLogService)
	githubHandler := handler.NewGitHubHandler(githubService, jwtService)
	thirdAPIHandler := handler.NewThirdAPIHandler(thirdAPIKeyService, cardService)
	cardConfigHandler := handler.NewCardConfigHandler(cardConfigService)
	userHandler := handler.NewUserHandler(userService, jwtService)
	deviceHandler := handler.NewDeviceHandler(deviceService)
	userScriptHandler := handler.NewUserScriptHandler(userScriptService, userScriptDeviceBindingService, webSocketService, deviceService, jwtService)
	scriptConfigHandler := handler.NewScriptConfigHandler(scriptConfigService)
	marketScriptHandler := handler.NewMarketScriptHandler(marketScriptService)
	dashboardHandler := handler.NewDashboardHandler(userService, deviceService, userScriptService)
	eventHandler := handler.NewEventHandler()
	paymentHandler := handler.NewPaymentHandler(paymentService)
	quotaHandler := handler.NewDeviceQuotaHandler(quotaService, paymentService)
	userScriptDeviceBindingHandler := handler.NewUserScriptDeviceBindingHandler(userScriptDeviceBindingService, userScriptService, deviceService)
	webSocketHandler := handler.NewWebSocketHandler(webSocketService)

	// 设置OpenAPI路由（客户端API）
	api := a.engine.Group("/api/v1")
	// 注册操作日志中间件
	api.Use(middleware.OperationLog(operationLogService))
	{
		// 事件接口
		api.POST("/event", eventHandler.HandleEvent)

		// GitHub webhook接口
		api.POST("/github/webhook", githubHandler.HandleGitHubWebhook)

		// 新的Hamibot脚本接口 - 需要HamibotAuth鉴权
		scriptHamibotAuth := api.Group("/script")
		scriptHamibotAuth.Use(middleware.HamibotAuth(jwtService))
		{
			scriptHamibotAuth.POST("/hamibot", githubHandler.GetHamibotContent)
			scriptHamibotAuth.POST("/bot", githubHandler.GetBotContent)
			scriptHamibotAuth.POST("/botui", githubHandler.GetBotUIContent)
			scriptHamibotAuth.POST("/ppmt", githubHandler.GetPpmtContent)
		}

		// 新的脚本接口 - 需要ScriptAuth鉴权（使用设备JWT）
		scriptAuth := api.Group("/script")
		scriptAuth.Use(middleware.ScriptAuth(jwtService))
		{
			scriptAuth.POST("/autobot", githubHandler.GetBotContent)
			scriptAuth.POST("/autobotui", githubHandler.GetBotUIContent)
		}

		// 卡密登录（公开路由）
		api.POST("/login", cardHandler.Login)
		api.POST("/script/autojs_login", githubHandler.GetAutoJSLoginContent)

		// 设备配对（公开路由）
		api.POST("/devices/pairings/submit", deviceHandler.SubmitDeviceInfo)

		// WebSocket连接（公开路由）
		api.GET("/ws", webSocketHandler.HandleWebSocket)

		// 脚本市场（公开路由）
		api.POST("/market/scripts", marketScriptHandler.GetMarketScriptList)
		api.POST("/market/scripts/detail", marketScriptHandler.GetMarketScriptDetail)

		// 绑定管理
		bindings := api.Group("/bindings") // 保持在api组中
		{
			bindings.POST("/bind", bindHandler.BindDevice)             // 绑定设备，无需鉴权
			bindings.POST("/unbind_all", bindHandler.UnbindAllDevices) // 解绑所有设备
		}

		// 配置管理
		api.POST("/config", configHandler.GetConfig)

		// 卡密配置管理（需要ClientAuth验证）
		cardConfigs := api.Group("/card-configs")
		cardConfigs.Use(middleware.ClientAuth(jwtService))
		{
			cardConfigs.POST("/upload", cardConfigHandler.UploadConfig) // 上传配置
			cardConfigs.POST("/get", cardConfigHandler.GetConfig)       // 获取配置
		}

		// 用户认证相关
		auth := api.Group("/auth")
		{
			auth.POST("/register", userHandler.Register)    // 用户注册
			auth.POST("/login", userHandler.Login)          // 用户登录
			auth.POST("/refresh", userHandler.RefreshToken) // 刷新用户令牌
			auth.POST("/logout", userHandler.Logout)        // 用户登出
		}

		// 设备认证相关（公开路由）
		deviceAuth := api.Group("/auth/device")
		{
			deviceAuth.POST("/refresh", deviceHandler.RefreshDeviceToken) // 刷新设备令牌
		}

		// 需要用户认证的接口
		userAPI := api.Group("/user")
		userAPI.Use(middleware.UserAuth(jwtService))
		{
			// 用户资料
			userAPI.GET("/profile", userHandler.GetProfile)
			userAPI.POST("/profile", userHandler.UpdateProfile)
			userAPI.POST("/password", userHandler.ChangePassword)

			// 用户仪表盘
			userAPI.GET("/dashboard", dashboardHandler.GetUserDashboard)

			// 设备管理
			devices := userAPI.Group("/devices")
			{
				devices.POST("/list", deviceHandler.GetUserDevices)                   // 获取用户设备列表（支持状态筛选）
				devices.POST("/delete", deviceHandler.DeleteDevice)                   // 删除设备
				devices.POST("/update", deviceHandler.UpdateDeviceInfo)               // 更新设备信息
				devices.POST("/pairings/generate", deviceHandler.GeneratePairingCode) // 生成配对码
				devices.POST("/pairings/status", deviceHandler.GetPairingStatus)      // 获取配对状态
			}

			// 脚本管理
			scripts := userAPI.Group("/scripts")
			{
				scripts.POST("/list", userScriptHandler.GetUserScripts)            // 获取用户脚本列表
				scripts.POST("/package", userScriptHandler.GetScriptPackage)       // 根据套餐ID获取套餐信息
				scripts.POST("/run", userScriptHandler.RunScript)                  // 运行脚本（通过WebSocket发送给设备）
				scripts.POST("/stop", userScriptHandler.StopScript)                // 停止脚本
				scripts.POST("/delete", userScriptHandler.DeleteScript)            // 删除脚本
				scripts.POST("/check-update", userScriptHandler.CheckScriptUpdate) // 检查脚本更新
				scripts.POST("/update", userScriptHandler.UpdateScript)            // 更新脚本

				// 脚本配置管理
				scripts.POST("/config-schema", scriptConfigHandler.GetScriptConfigSchema)   // 获取脚本配置模式
				scripts.POST("/configs/list", scriptConfigHandler.GetUserScriptConfigs)     // 获取用户脚本配置列表
				scripts.POST("/configs/create", scriptConfigHandler.CreateUserScriptConfig) // 创建用户脚本配置
				scripts.POST("/configs/detail", scriptConfigHandler.GetUserScriptConfig)    // 获取用户脚本配置详情
				scripts.POST("/configs/update", scriptConfigHandler.UpdateUserScriptConfig) // 更新用户脚本配置
				scripts.POST("/configs/delete", scriptConfigHandler.DeleteUserScriptConfig) // 删除用户脚本配置

				// 设备绑定管理
				scripts.POST("/bind-device", userScriptDeviceBindingHandler.BindDevice)     // 绑定设备到脚本
				scripts.POST("/unbind-device", userScriptDeviceBindingHandler.UnbindDevice) // 解绑设备
				scripts.POST("/bindings", userScriptDeviceBindingHandler.GetScriptBindings) // 获取脚本绑定设备列表
			}

			// 设备配额管理
			quotas := userAPI.Group("/quotas")
			{
				quotas.GET("/info", quotaHandler.GetQuotaInfo) // 获取配额信息
				quotas.GET("/list", quotaHandler.GetQuotaList) // 获取配额列表
			}

		}

		// 需要ClientAuth验证的路由
		authorized := api.Group("")
		authorized.Use(middleware.ClientAuth(jwtService))
		{
			// 脚本接口
			scriptGroup := authorized.Group("/script")
			{
				scriptGroup.POST("/autojs", githubHandler.GetAutoJSContent)
			}

			// 绑定管理
			authorizedBindings := authorized.Group("/bindings")
			{
				authorizedBindings.POST("/unbind", bindHandler.UnbindDevice) // 解绑设备，支持批量
			}

			// 卡密充值接口
			authorized.POST("/cardkeys/recharge", cardHandler.RechargeCardKey) // 以卡充卡
		}

		// 第三方API路由
		third := api.Group("/third")
		third.Use(middleware.SignatureAuth(thirdAPIKeyService, signatureService))
		{
			// 第三方充值接口（使用签名认证）
			third.POST("/recharge", thirdAPIHandler.RechargeCardKey)
			// 新增：获取卡密使用时间和到期时间
			third.POST("/cardkey/detail", thirdAPIHandler.GetCardKeyDetail)
		}

		// 支付相关路由
		payment := api.Group("/payment")
		{
			// 公开接口
			payment.GET("/notify", paymentHandler.PaymentNotify)               // ZPAY回调，无需认证（GET请求）
			payment.POST("/script/packages", paymentHandler.GetScriptPackages) // 获取脚本套餐列表（无需认证）

			// 需要用户认证的接口
			paymentAuth := payment.Group("")
			paymentAuth.Use(middleware.UserAuth(jwtService))
			{
				paymentAuth.POST("/script/create-order", paymentHandler.CreatePaymentOrder)             // 脚本新购
				paymentAuth.POST("/script/upgrade/packages", paymentHandler.GetUpgradePackages)         // 获取升级套餐列表
				paymentAuth.POST("/script/upgrade/create-order", paymentHandler.CreateUpgradeOrder)     // 脚本升级
				paymentAuth.POST("/script/renewal/create-order", quotaHandler.CreateScriptRenewalOrder) // 脚本续费

				paymentAuth.POST("/quota/create-order", quotaHandler.CreateQuotaOrder)                // 配额新购
				paymentAuth.POST("/quota/renewal/list", quotaHandler.GetRenewableQuotas)              // 获取可续费的配额列表
				paymentAuth.POST("/quota/renewal/create-order", quotaHandler.CreateQuotaRenewalOrder) // 配额续费

				paymentAuth.POST("/order", paymentHandler.GetOrderDetail) // 获取订单详情（支持所有类型订单）
			}
		}
	}

	// 设置AdminAPI路由（后台管理API）
	admin := a.engine.Group("/admin/v1")
	{
		// 管理员登录（公开路由）
		admin.POST("/auth/login", adminHandler.Login)

		// 需要管理员权限的路由
		authorized := admin.Group("")
		authorized.Use(middleware.AdminAuth(jwtService))
		{
			// 仪表盘
			authorized.GET("/dashboard", dashboardHandler.GetOverview)
			authorized.GET("/system/status", dashboardHandler.GetSystemStatus)

			// 管理员管理
			admins := authorized.Group("/admins")
			{
				admins.POST("/list", adminHandler.ListAdmins)
				admins.POST("/create", adminHandler.CreateAdmin)
				admins.POST("/update", adminHandler.UpdateAdmin)
				admins.POST("/delete", adminHandler.DeleteAdmin)
				admins.POST("/status", adminHandler.UpdateStatus)
			}

			// 用户管理
			users := authorized.Group("/users")
			{
				users.GET("/list", userHandler.GetUserList)
				users.POST("/status", userHandler.UpdateUserStatus)
				users.GET("/statistics", userHandler.GetUserStatistics)
			}

			// 设备管理
			devices := authorized.Group("/devices")
			{
				devices.GET("/list", deviceHandler.GetDeviceList)
				devices.GET("/:device_id", deviceHandler.GetDeviceInfo)
				devices.POST("/delete", deviceHandler.DeleteDevice)
				devices.GET("/online/count", deviceHandler.GetOnlineDeviceCount)
			}

			// 脚本管理
			scripts := authorized.Group("/scripts")
			{
				scripts.GET("/list", userScriptHandler.GetUserScriptList)
			}

			// 卡密管理
			cardkeys := authorized.Group("/cardkeys")
			{
				cardkeys.POST("/list", cardHandler.ListCardKeys)
				cardkeys.POST("/create", cardHandler.GenerateCardKey)
				cardkeys.POST("/delete", cardHandler.DeleteCardKey)
				cardkeys.POST("/detail", cardHandler.GetCardKeyWithDevices)
				cardkeys.POST("/recharge", cardHandler.RechargeCardKey)
			}

			// 设备绑定管理
			bindings := authorized.Group("/bindings")
			{
				bindings.POST("/unbind", bindHandler.UnbindDevice)         // 解绑设备，支持批量
				bindings.POST("/unbind_all", bindHandler.UnbindAllDevices) // 解绑所有设备
			}

			// 系统配置管理
			configs := authorized.Group("/configs")
			{
				configs.POST("/list", configHandler.ListConfigs)
				configs.POST("/set", configHandler.SetConfig)
				configs.POST("/delete", configHandler.DeleteConfig)
			}

			// GitHub脚本管理
			githubScripts := authorized.Group("/github/scripts")
			{
				githubScripts.GET("", githubHandler.GetScriptList)
				githubScripts.POST("/update", githubHandler.UpdateScript)
			}

			// 第三方API密钥管理
			apiKeys := authorized.Group("/api-keys")
			{
				apiKeys.POST("/create", thirdAPIHandler.CreateAPIKey)
				apiKeys.POST("/list", thirdAPIHandler.ListAPIKeys)
				apiKeys.POST("/detail", thirdAPIHandler.GetAPIKeyDetail)
				apiKeys.POST("/delete", thirdAPIHandler.DeleteAPIKey)
				apiKeys.POST("/update-status", thirdAPIHandler.UpdateAPIKeyStatus)
			}

			// 统计数据
			authorized.GET("/stats", bindHandler.GetStats)
			authorized.GET("/stats/hamibot", userLogHandler.GetDailyActiveUsers)
			authorized.GET("/stats/hamibot/robots", userLogHandler.GetDailyActiveRobots)

		}
	}
}
