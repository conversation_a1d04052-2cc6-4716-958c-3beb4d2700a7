package app

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"goadmin/config"
	"goadmin/internal/handler"
	"goadmin/internal/middleware"
	"goadmin/internal/service"
)

type App struct {
	cfg              *config.Config
	db               *gorm.DB
	engine           *gin.Engine
	schedulerService *service.SchedulerService
}

func NewApp(cfg *config.Config, db *gorm.DB) *App {
	app := &App{
		cfg:    cfg,
		db:     db,
		engine: gin.Default(),
	}
	// Register RequestID middleware
	app.engine.Use(middleware.RequestID())
	// Apply the logging middleware
	app.engine.Use(middleware.RequestResponseLogger())

	app.setupRoutes()
	return app
}

func (a *App) Run(addr string) error {
	server := &http.Server{
		Addr:              addr,
		Handler:           a.engine,
		ReadTimeout:       10 * time.Second,
		WriteTimeout:      10 * time.Second,
		IdleTimeout:       120 * time.Second,
		MaxHeaderBytes:    1 << 20, // 1MB
		ReadHeaderTimeout: 5 * time.Second,
	}
	return server.ListenAndServe()
}

func (a *App) Shutdown() error {
	// 停止调度器
	if a.schedulerService != nil {
		a.schedulerService.Stop()
	}
	return nil
}

func (a *App) setupRoutes() {
	// 创建服务实例
	jwtService := service.NewJWTService(a.cfg, a.db)
	thirdAPIKeyService := service.NewThirdAPIKeyService(a.db)
	signatureService := service.NewSignatureService()
	adminService := service.NewAdminService(a.db)
	cardService := service.NewCardKeyService(a.db)
	configService := service.NewConfigService(a.db)
	bindService := service.NewBindingService(a.db, cardService, configService)
	userLogService := service.NewUserLogService(a.db)
	operationLogService := service.NewOperationLogService(a.db)
	githubService := service.NewGitHubService(a.cfg, a.db)
	cardConfigService := service.NewCardConfigService(a.db)

	// 创建调度器服务
	a.schedulerService = service.NewSchedulerService(bindService, configService)
	a.schedulerService.Start()

	// 创建处理器实例
	adminHandler := handler.NewAdminHandler(adminService, jwtService)
	cardHandler := handler.NewCardKeyHandler(cardService, bindService, jwtService, userLogService)
	bindHandler := handler.NewBindingHandler(bindService, jwtService)
	configHandler := handler.NewConfigHandler(configService)
	userLogHandler := handler.NewUserLogHandler(userLogService)
	githubHandler := handler.NewGitHubHandler(githubService, jwtService)
	thirdAPIHandler := handler.NewThirdAPIHandler(thirdAPIKeyService, cardService)
	cardConfigHandler := handler.NewCardConfigHandler(cardConfigService)
	eventHandler := handler.NewEventHandler()

	// 设置OpenAPI路由（客户端API）
	api := a.engine.Group("/api/v1")
	// 注册操作日志中间件
	api.Use(middleware.OperationLog(operationLogService))
	{
		// 事件接口
		api.POST("/event", eventHandler.HandleEvent)

		// GitHub webhook接口
		api.POST("/github/webhook", githubHandler.HandleGitHubWebhook)

		// 新的Hamibot脚本接口 - 需要HamibotAuth鉴权
		scriptHamibotAuth := api.Group("/script")
		scriptHamibotAuth.Use(middleware.HamibotAuth(jwtService))
		{
			scriptHamibotAuth.POST("/hamibot", githubHandler.GetHamibotContent)
			scriptHamibotAuth.POST("/bot", githubHandler.GetBotContent)
			scriptHamibotAuth.POST("/botui", githubHandler.GetBotUIContent)
			scriptHamibotAuth.POST("/ppmt", githubHandler.GetPpmtContent)
		}

		// 卡密登录（公开路由）
		api.POST("/login", cardHandler.Login)
		api.POST("/script/autojs_login", githubHandler.GetAutoJSLoginContent)
		// 绑定管理
		bindings := api.Group("/bindings") // 保持在api组中
		{
			bindings.POST("/bind", bindHandler.BindDevice)             // 绑定设备，无需鉴权
			bindings.POST("/unbind_all", bindHandler.UnbindAllDevices) // 解绑所有设备
		}

		// 配置管理
		api.POST("/config", configHandler.GetConfig)

		// 卡密配置管理（需要ClientAuth验证）
		cardConfigs := api.Group("/card-configs")
		cardConfigs.Use(middleware.ClientAuth(jwtService))
		{
			cardConfigs.POST("/upload", cardConfigHandler.UploadConfig) // 上传配置
			cardConfigs.POST("/get", cardConfigHandler.GetConfig)       // 获取配置
		}

		// 需要ClientAuth验证的路由
		authorized := api.Group("")
		authorized.Use(middleware.ClientAuth(jwtService))
		{
			// 脚本接口
			scriptGroup := authorized.Group("/script")
			{
				scriptGroup.POST("/autojs", githubHandler.GetAutoJSContent)
			}

			// 绑定管理
			authorizedBindings := authorized.Group("/bindings")
			{
				authorizedBindings.POST("/unbind", bindHandler.UnbindDevice) // 解绑设备，支持批量
			}

			// 卡密充值接口
			authorized.POST("/cardkeys/recharge", cardHandler.RechargeCardKey) // 以卡充卡
		}

		// 第三方API路由
		third := api.Group("/third")
		third.Use(middleware.SignatureAuth(thirdAPIKeyService, signatureService))
		{
			// 第三方充值接口（使用签名认证）
			third.POST("/recharge", thirdAPIHandler.RechargeCardKey)
			// 新增：获取卡密使用时间和到期时间
			third.POST("/cardkey/detail", thirdAPIHandler.GetCardKeyDetail)
		}
	}

	// 设置AdminAPI路由（后台管理API）
	admin := a.engine.Group("/admin/v1")
	{
		// 管理员登录（公开路由）
		admin.POST("/auth/login", adminHandler.Login)

		// 需要管理员权限的路由
		authorized := admin.Group("")
		authorized.Use(middleware.AdminAuth(jwtService))
		{
			// 管理员管理
			admins := authorized.Group("/admins")
			{
				admins.POST("/list", adminHandler.ListAdmins)
				admins.POST("/create", adminHandler.CreateAdmin)
				admins.POST("/update", adminHandler.UpdateAdmin)
				admins.POST("/delete", adminHandler.DeleteAdmin)
				admins.POST("/status", adminHandler.UpdateStatus)
			}

			// 卡密管理
			cardkeys := authorized.Group("/cardkeys")
			{
				cardkeys.POST("/list", cardHandler.ListCardKeys)
				cardkeys.POST("/create", cardHandler.GenerateCardKey)
				cardkeys.POST("/delete", cardHandler.DeleteCardKey)
				cardkeys.POST("/detail", cardHandler.GetCardKeyWithDevices)
				cardkeys.POST("/recharge", cardHandler.RechargeCardKey)
			}

			// 设备绑定管理
			bindings := authorized.Group("/bindings")
			{
				bindings.POST("/unbind", bindHandler.UnbindDevice)         // 解绑设备，支持批量
				bindings.POST("/unbind_all", bindHandler.UnbindAllDevices) // 解绑所有设备
			}

			// 系统配置管理
			configs := authorized.Group("/configs")
			{
				configs.POST("/list", configHandler.ListConfigs)
				configs.POST("/set", configHandler.SetConfig)
				configs.POST("/delete", configHandler.DeleteConfig)
			}

			// 脚本管理
			scripts := authorized.Group("/github/scripts")
			{
				scripts.GET("", githubHandler.GetScriptList)
				scripts.POST("/update", githubHandler.UpdateScript)
			}

			// 第三方API密钥管理
			apiKeys := authorized.Group("/api-keys")
			{
				apiKeys.POST("/create", thirdAPIHandler.CreateAPIKey)
				apiKeys.POST("/list", thirdAPIHandler.ListAPIKeys)
				apiKeys.POST("/detail", thirdAPIHandler.GetAPIKeyDetail)
				apiKeys.POST("/delete", thirdAPIHandler.DeleteAPIKey)
				apiKeys.POST("/update-status", thirdAPIHandler.UpdateAPIKeyStatus)
			}

			// 统计数据
			authorized.GET("/stats", bindHandler.GetStats)
			authorized.GET("/stats/hamibot", userLogHandler.GetDailyActiveUsers)
			authorized.GET("/stats/hamibot/robots", userLogHandler.GetDailyActiveRobots)
		}
	}
}
