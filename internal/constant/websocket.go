package constant

// WebSocket错误码常量 - 极简版本
const (
	// 可以重试的错误
	WS_ERROR_RETRY = 0 // 临时性错误，可以重试

	// 不可以重试的错误
	WS_ERROR_NO_RETRY = 1 // 配置错误，需要修复后重试
)

// WebSocket错误消息映射
var WSErrorMessages = map[int]string{
	WS_ERROR_RETRY:    "连接失败，请重试",
	WS_ERROR_NO_RETRY: "连接失败，需要检查配置",
}

// WebSocket错误响应结构
type WSErrorResponse struct {
	ErrorCode    int    `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	Details      string `json:"details,omitempty"`
	RetryAfter   int    `json:"retry_after,omitempty"` // 重试等待时间（秒）
	NotCanRetry  bool   `json:"not_can_retry"`         // 是否不可以重试
}

// GetWSErrorResponse 获取WebSocket错误响应
func GetWSErrorResponse(errorCode int, details string) WSErrorResponse {
	response := WSErrorResponse{
		ErrorCode:    errorCode,
		ErrorMessage: WSErrorMessages[errorCode],
		Details:      details,
		NotCanRetry:  errorCode == WS_ERROR_NO_RETRY, // 只有明确不可重试的错误才标记为true
	}

	// 为可重试错误添加默认重试时间
	if errorCode == WS_ERROR_RETRY {
		response.RetryAfter = 5 // 5秒后重试
	}

	return response
}

// IsRetryableError 判断错误是否可以重试
func IsRetryableError(errorCode int) bool {
	return errorCode != WS_ERROR_NO_RETRY // 除了明确不可重试的错误，其他都可以重试
}
