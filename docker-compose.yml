version: '3.8'

services:
  app:
    image: ${DOCKERHUB_USERNAME}/goadmin:${TAG:-latest}  # 使用环境变量，默认为 latest
    container_name: goadmin-${TAG:-latest}  # 容器名也使用标签区分
    restart: always
    network_mode: host  # 使用主机网络模式，可以直接访问宝塔 MySQL
    environment:
      - TZ=Asia/Shanghai
      - PORT=${PORT:-9090}  # 使用环境变量设置端口，默认 9090
      # MySQL配置需要根据宝塔面板中的实际配置修改
      - MYSQL_HOST=localhost
      - MYSQL_PORT=3306
      - MYSQL_USER=goadmin
      - MYSQL_PASSWORD=your_password
      - MYSQL_DATABASE=goadmin
    volumes:
      - ./config.yaml:/app/config.yaml
      - ./logs-${TAG:-latest}:/app/logs  # 日志目录也使用标签区分

volumes:
  mysql_data: 