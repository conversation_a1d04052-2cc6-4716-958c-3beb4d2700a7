# GoAdmin 第三方API接口文档

## 概述

GoAdmin 第三方API采用基于签名的安全认证机制，类似于阿里云、腾讯云等主流云服务商的API设计。通过SecretId和SecretKey进行身份认证，使用HMAC-SHA256算法生成请求签名，确保API调用的安全性。

## 认证机制

### 1. 获取API密钥

联系管理员为您的卡密创建API密钥，您将获得：
- **SecretId**: 用于标识API密钥（可公开传输）
- **SecretKey**: 用于生成签名（需要保密，不可传输）

### 2. 签名算法

#### 步骤1：构造待签名字符串
```
StringToSign = HTTPMethod + "\n" + URI + "\n" + CanonicalQueryString
```

其中：
- `HTTPMethod`: HTTP请求方法（大写），如 `POST`
- `URI`: 请求路径，如 `/api/v1/third/recharge`
- `CanonicalQueryString`: 规范化的查询字符串（包含请求参数和时间戳）

#### 步骤2：生成规范化查询字符串
1. 将所有请求参数（包括时间戳）按参数名进行字典序排序
2. 对参数名和参数值进行URL编码
3. 按照 `key1=value1&key2=value2` 的格式拼接

#### 步骤3：生成签名
```
Signature = HEX(HMAC-SHA256(SecretKey, StringToSign))
```

#### 步骤4：设置请求头
```
X-Secret-Id: {SecretId}
X-Timestamp: {Unix时间戳}
X-Signature: {生成的签名}
Content-Type: application/json
```

### 3. 时间戳验证

- 时间戳有效期为5分钟
- 请确保客户端时间与服务器时间同步

## API接口

### 充值接口

**接口地址**: `POST /api/v1/third/recharge`

**功能说明**: 使用指定卡密为目标卡密进行充值

**请求参数**:
```json
{
  "target_card_key": "目标卡密",
  "new_card_key": "充值卡密"
}
```

**安全限制**:
- 需要具有充值权限

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "充值成功"
  }
}
```

**错误响应**:
```json
{
  "code": 1001,
  "message": "卡密不存在"
}
```

## 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 无效的请求参数 | 检查请求参数格式 |
| 401 | 未授权 | 检查SecretId和签名 |
| 403 | 禁止访问 | 检查API密钥权限 |
| 1001 | 卡密不存在 | 检查卡密是否正确 |
| 1002 | 卡密已禁用 | 联系管理员 |
| 1005 | 签名验证失败 | 检查签名算法实现 |
| 1006 | 时间戳已过期 | 检查系统时间同步 |
| 1007 | 权限不足 | 检查API密钥权限配置 |

## 编程语言示例

### Go语言示例

```go
package main

import (
    "bytes"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "net/url"
    "sort"
    "strings"
    "time"
)

type RechargeRequest struct {
    TargetCardKey string `json:"target_card_key"`
    NewCardKey    string `json:"new_card_key"`
}

func generateSignature(method, uri string, params map[string]string, secretKey string, timestamp int64) string {
    // 添加时间戳
    params["timestamp"] = fmt.Sprintf("%d", timestamp)
    
    // 排序参数
    var keys []string
    for k := range params {
        keys = append(keys, k)
    }
    sort.Strings(keys)
    
    // 构造查询字符串
    var parts []string
    for _, key := range keys {
        value := params[key]
        encodedKey := url.QueryEscape(key)
        encodedValue := url.QueryEscape(value)
        parts = append(parts, fmt.Sprintf("%s=%s", encodedKey, encodedValue))
    }
    queryString := strings.Join(parts, "&")
    
    // 构造待签名字符串
    stringToSign := fmt.Sprintf("%s\n%s\n%s", strings.ToUpper(method), uri, queryString)
    
    // 生成签名
    h := hmac.New(sha256.New, []byte(secretKey))
    h.Write([]byte(stringToSign))
    return hex.EncodeToString(h.Sum(nil))
}

func main() {
    secretID := "AKID1234567890abcdef1234567890ab"
    secretKey := "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
    
    // 请求参数
    req := RechargeRequest{
        TargetCardKey: "TARGET_CARD",
        NewCardKey:    "SOURCE_CARD",
    }
    
    // 转换为map用于签名
    params := map[string]string{
        "target_card_key": req.TargetCardKey,
        "new_card_key":    req.NewCardKey,
    }
    
    timestamp := time.Now().Unix()
    signature := generateSignature("POST", "/api/v1/third/recharge", params, secretKey, timestamp)
    
    // 序列化请求体
    jsonData, _ := json.Marshal(req)
    
    // 创建HTTP请求
    httpReq, _ := http.NewRequest("POST", "https://your-domain.com/api/v1/third/recharge", bytes.NewBuffer(jsonData))
    httpReq.Header.Set("Content-Type", "application/json")
    httpReq.Header.Set("X-Secret-Id", secretID)
    httpReq.Header.Set("X-Timestamp", fmt.Sprintf("%d", timestamp))
    httpReq.Header.Set("X-Signature", signature)
    
    // 发送请求
    client := &http.Client{}
    resp, err := client.Do(httpReq)
    if err != nil {
        fmt.Printf("请求失败: %v\n", err)
        return
    }
    defer resp.Body.Close()
    
    body, _ := io.ReadAll(resp.Body)
    fmt.Printf("响应: %s\n", string(body))
}
```

### Python示例

```python
import hmac
import hashlib
import time
import urllib.parse
import json
import requests

def generate_signature(method, uri, params, secret_key, timestamp):
    # 添加时间戳到参数
    params['timestamp'] = str(timestamp)
    
    # 对参数进行排序和编码
    sorted_params = sorted(params.items())
    query_string = '&'.join([
        f"{urllib.parse.quote(k)}={urllib.parse.quote(v)}" 
        for k, v in sorted_params
    ])
    
    # 构造待签名字符串
    string_to_sign = f"{method.upper()}\n{uri}\n{query_string}"
    
    # 生成签名
    signature = hmac.new(
        secret_key.encode('utf-8'),
        string_to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    return signature

def recharge_card(secret_id, secret_key, target_card, new_card):
    method = "POST"
    uri = "/api/v1/third/recharge"
    params = {
        "target_card_key": target_card,
        "new_card_key": new_card
    }
    timestamp = int(time.time())
    
    signature = generate_signature(method, uri, params, secret_key, timestamp)
    
    headers = {
        'X-Secret-Id': secret_id,
        'X-Timestamp': str(timestamp),
        'X-Signature': signature,
        'Content-Type': 'application/json'
    }
    
    response = requests.post(
        'https://your-domain.com/api/v1/third/recharge',
        headers=headers,
        json=params
    )
    
    return response.json()

# 使用示例
if __name__ == "__main__":
    secret_id = "AKID1234567890abcdef1234567890ab"
    secret_key = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
    
    result = recharge_card(secret_id, secret_key, "TARGET_CARD", "SOURCE_CARD")
    print(json.dumps(result, indent=2, ensure_ascii=False))
```

### Java示例

```java
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.*;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import com.fasterxml.jackson.databind.ObjectMapper;

public class GoAdminClient {
    private final String secretId;
    private final String secretKey;
    private final String baseUrl;
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;

    public GoAdminClient(String secretId, String secretKey, String baseUrl) {
        this.secretId = secretId;
        this.secretKey = secretKey;
        this.baseUrl = baseUrl;
        this.httpClient = HttpClient.newHttpClient();
        this.objectMapper = new ObjectMapper();
    }

    private String generateSignature(String method, String uri, Map<String, String> params, long timestamp)
            throws NoSuchAlgorithmException, InvalidKeyException {
        // 添加时间戳
        params.put("timestamp", String.valueOf(timestamp));

        // 排序参数
        List<String> sortedKeys = new ArrayList<>(params.keySet());
        Collections.sort(sortedKeys);

        // 构造查询字符串
        StringBuilder queryString = new StringBuilder();
        for (int i = 0; i < sortedKeys.size(); i++) {
            String key = sortedKeys.get(i);
            String value = params.get(key);
            if (i > 0) {
                queryString.append("&");
            }
            queryString.append(URLEncoder.encode(key, StandardCharsets.UTF_8))
                      .append("=")
                      .append(URLEncoder.encode(value, StandardCharsets.UTF_8));
        }

        // 构造待签名字符串
        String stringToSign = method.toUpperCase() + "\n" + uri + "\n" + queryString.toString();

        // 生成签名
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));

        // 转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString();
    }

    public Map<String, Object> recharge(String targetCardKey, String newCardKey)
            throws Exception {
        String method = "POST";
        String uri = "/api/v1/third/recharge";

        Map<String, String> params = new HashMap<>();
        params.put("target_card_key", targetCardKey);
        params.put("new_card_key", newCardKey);

        long timestamp = Instant.now().getEpochSecond();
        String signature = generateSignature(method, uri, params, timestamp);

        // 构造请求体
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("target_card_key", targetCardKey);
        requestBody.put("new_card_key", newCardKey);
        String jsonBody = objectMapper.writeValueAsString(requestBody);

        // 创建HTTP请求
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(baseUrl + uri))
                .header("Content-Type", "application/json")
                .header("X-Secret-Id", secretId)
                .header("X-Timestamp", String.valueOf(timestamp))
                .header("X-Signature", signature)
                .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                .build();

        // 发送请求
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        // 解析响应
        return objectMapper.readValue(response.body(), Map.class);
    }

    public static void main(String[] args) {
        try {
            GoAdminClient client = new GoAdminClient(
                "AKID1234567890abcdef1234567890ab",
                "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                "https://your-domain.com"
            );

            Map<String, Object> result = client.recharge("TARGET_CARD", "SOURCE_CARD");
            System.out.println("充值结果: " + result);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

### JavaScript/Node.js示例

```javascript
const crypto = require('crypto');
const https = require('https');

class GoAdminClient {
    constructor(secretId, secretKey, baseUrl) {
        this.secretId = secretId;
        this.secretKey = secretKey;
        this.baseUrl = baseUrl;
    }

    generateSignature(method, uri, params, timestamp) {
        // 添加时间戳
        params.timestamp = timestamp.toString();

        // 排序参数
        const sortedKeys = Object.keys(params).sort();

        // 构造查询字符串
        const queryString = sortedKeys
            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
            .join('&');

        // 构造待签名字符串
        const stringToSign = `${method.toUpperCase()}\n${uri}\n${queryString}`;

        // 生成签名
        const signature = crypto
            .createHmac('sha256', this.secretKey)
            .update(stringToSign)
            .digest('hex');

        return signature;
    }

    async recharge(targetCardKey, newCardKey) {
        const method = 'POST';
        const uri = '/api/v1/third/recharge';

        const params = {
            target_card_key: targetCardKey,
            new_card_key: newCardKey
        };

        const timestamp = Math.floor(Date.now() / 1000);
        const signature = this.generateSignature(method, uri, params, timestamp);

        const requestBody = JSON.stringify(params);

        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(requestBody),
                'X-Secret-Id': this.secretId,
                'X-Timestamp': timestamp.toString(),
                'X-Signature': signature
            }
        };

        return new Promise((resolve, reject) => {
            const req = https.request(this.baseUrl + uri, options, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    try {
                        const result = JSON.parse(data);
                        resolve(result);
                    } catch (error) {
                        reject(error);
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.write(requestBody);
            req.end();
        });
    }
}

// 使用示例
async function main() {
    const client = new GoAdminClient(
        'AKID1234567890abcdef1234567890ab',
        '1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
        'https://your-domain.com'
    );

    try {
        const result = await client.recharge('TARGET_CARD', 'SOURCE_CARD');
        console.log('充值结果:', JSON.stringify(result, null, 2));
    } catch (error) {
        console.error('充值失败:', error);
    }
}

main();
```

### 浏览器JavaScript示例

```javascript
class GoAdminWebClient {
    constructor(secretId, secretKey, baseUrl) {
        this.secretId = secretId;
        this.secretKey = secretKey;
        this.baseUrl = baseUrl;
    }

    async generateSignature(method, uri, params, timestamp) {
        // 添加时间戳
        params.timestamp = timestamp.toString();

        // 排序参数
        const sortedKeys = Object.keys(params).sort();

        // 构造查询字符串
        const queryString = sortedKeys
            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
            .join('&');

        // 构造待签名字符串
        const stringToSign = `${method.toUpperCase()}\n${uri}\n${queryString}`;

        // 使用Web Crypto API生成签名
        const encoder = new TextEncoder();
        const keyData = encoder.encode(this.secretKey);
        const messageData = encoder.encode(stringToSign);

        const cryptoKey = await crypto.subtle.importKey(
            'raw',
            keyData,
            { name: 'HMAC', hash: 'SHA-256' },
            false,
            ['sign']
        );

        const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);

        // 转换为十六进制字符串
        const hashArray = Array.from(new Uint8Array(signature));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

        return hashHex;
    }

    async recharge(targetCardKey, newCardKey) {
        const method = 'POST';
        const uri = '/api/v1/third/recharge';

        const params = {
            target_card_key: targetCardKey,
            new_card_key: newCardKey
        };

        const timestamp = Math.floor(Date.now() / 1000);
        const signature = await this.generateSignature(method, uri, params, timestamp);

        const response = await fetch(this.baseUrl + uri, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-Secret-Id': this.secretId,
                'X-Timestamp': timestamp.toString(),
                'X-Signature': signature
            },
            body: JSON.stringify(params)
        });

        return await response.json();
    }
}

// 使用示例
async function rechargeExample() {
    const client = new GoAdminWebClient(
        'AKID1234567890abcdef1234567890ab',
        '1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
        'https://your-domain.com'
    );

    try {
        const result = await client.recharge('TARGET_CARD', 'SOURCE_CARD');
        console.log('充值结果:', result);
    } catch (error) {
        console.error('充值失败:', error);
    }
}
```

## 注意事项

1. **时间同步**: 确保客户端时间与服务器时间同步，时间戳误差不能超过5分钟
2. **签名安全**: SecretKey必须保密，不能在客户端代码中硬编码
3. **HTTPS**: 生产环境必须使用HTTPS协议
4. **错误处理**: 实现适当的错误处理和重试机制
5. **参数验证**: 在发送请求前验证参数的有效性

## 测试工具

您可以使用以下curl命令测试API：

```bash
# 注意：实际使用时需要正确计算签名
curl -X POST 'https://your-domain.com/api/v1/third/recharge' \
  -H 'X-Secret-Id: your-secret-id' \
  -H 'X-Timestamp: 1703123456' \
  -H 'X-Signature: calculated-signature' \
  -H 'Content-Type: application/json' \
  -d '{
    "target_card_key": "TARGET_CARD",
    "new_card_key": "SOURCE_CARD"
  }'
```
