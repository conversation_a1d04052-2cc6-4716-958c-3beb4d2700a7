# goadmin <-> AutoX 安卓 WebSocket 通信协议设计与对接文档

## 1. 概述
本协议用于 goadmin 服务端与 AutoX.js 安卓客户端之间的实时通信，支持设备注册、心跳、脚本下发、任务结果上报等功能，适用于大规模设备的高并发安全控制。

---

## 2. 设备配对流程（获取WebSocket Token）

### 2.1 配对流程概述
设备必须先通过HTTP配对流程获取 `deviceId` 和 `token`，然后才能建立WebSocket连接。

**配对流程步骤：**
1. **用户端**：通过UniApp生成配对码
2. **设备端**：提交设备信息完成配对
3. **获取Token**：配对成功后获得WebSocket认证token

### 2.2 HTTP配对接口

#### 2.2.1 生成配对码（用户端）
- **接口**：`POST /api/v1/user/devices/pairings/generate`
- **认证**：需要用户JWT token（Authorization: Bearer {token}）
- **请求体**：无
- **响应示例**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "pairing_code": "ABC123",
    "expires_at": "2024-01-15T10:30:00Z",
    "pairing_id": 123
  }
}
```

#### 2.2.2 获取配对状态（用户端）
- **接口**：`POST /api/v1/user/devices/pairings/status`
- **认证**：需要用户JWT token（Authorization: Bearer {token}）
- **请求体**：
```json
{
  "pairing_code": "ABC123"
}
```
- **响应示例**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "pairing": {
      "id": 123,
      "device_id": "temp_ABC123",
      "pairing_status": 1,
      "pairing_expires_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

#### 2.2.3 提交设备信息（设备端）
- **接口**：`POST /api/v1/devices/pairings/submit`
- **认证**：公开接口，无需认证
- **请求体**：
```json
{
  "pairing_code": "ABC123",
  "device_id": "android_device_001",
  "name": "我的安卓设备",  // 可选，不传则自动使用 device_model + android_version 组合
  "device_model": "Samsung Galaxy S21",
  "android_version": "12",
  "autox_version": "6.6.0"
}
```

**设备名称规则：**
- 如果提供 `name` 字段，则使用提供的名称
- 如果 `name` 字段为空或未提供：
  - 有 `android_version` 时：使用 `device_model + " " + android_version` 组合
  - 无 `android_version` 时：仅使用 `device_model`
- 示例：
  - `"name": "我的设备"` → 设备名称：`"我的设备"`
  - `"name": ""` + `"device_model": "Samsung Galaxy S21"` + `"android_version": "12"` → 设备名称：`"Samsung Galaxy S21 12"`
  - `"name": ""` + `"device_model": "Samsung Galaxy S21"` + `"android_version": ""` → 设备名称：`"Samsung Galaxy S21"`
- **响应示例**：
```json
{
  "code": 0,
  "message": "设备信息提交成功",
  "data": {
    "device_id": "android_device_001",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "a1b2c3d4e5f6...",
    "token_expires_at": "2024-02-15T10:30:00Z"
  }
}
```

### 2.3 配对状态说明
- `pairing_status = 0`：未配对
- `pairing_status = 1`：配对中（等待设备提交信息）
- `pairing_status = 2`：已配对（配对成功）
- `pairing_status = 3`：已过期（配对码过期）

### 2.4 配对流程时序图
```
用户端(UniApp)          服务端             设备端(AutoX)
     |                    |                    |
     |--生成配对码-------->|                    |
     |<--返回配对码--------|                    |
     |                    |                    |
     |                    |<--提交设备信息-----|
     |                    |--配对成功--------->|
     |                    |--返回token-------->|
     |                    |                    |
     |--查询配对状态------>|                    |
     |<--配对完成---------|                    |
```

---

## 3. 设备Token刷新机制

### 3.1 刷新设备Token
- **接口**：`POST /api/v1/auth/device/refresh`
- **认证**：公开接口，无需认证
- **请求体**：
```json
{
  "refresh_token": "a1b2c3d4e5f6..."
}
```
- **响应示例**：
```json
{
  "code": 0,
  "message": "设备token刷新成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "b2c3d4e5f6g7...",
    "expires_in": 3600
  }
}
```

### 3.2 Token说明
- **access_token**：用于WebSocket连接认证，有效期1小时
- **refresh_token**：用于刷新access_token，有效期30天
- **自动续期**：WebSocket连接时如果token过期，服务端会自动续期

---

## 4. WebSocket连接与认证

### 4.1 连接方式
- **协议**：`wss://`（强烈建议生产环境使用加密）
- **方法**：标准 WebSocket GET 升级
- **URL 示例**：
  ```
  wss://yourdomain.com/api/v1/ws?token=ACCESS_TOKEN
  ```
- **参数说明**：
  - `token`：设备配对后获得的access_token（JWT格式）

### 4.2 认证流程
1. 客户端通过配对流程获取 `access_token`。
2. 客户端发起 WebSocket 连接，URL 携带 `token` 参数。
3. 服务端解析JWT token，验证设备权限，认证通过后建立连接。
4. 认证失败时，服务端返回详细的错误码和错误信息。

### 4.3 WebSocket连接错误处理
当WebSocket连接失败时，服务端会返回JSON格式的错误响应：

```json
{
  "error_code": 0,
  "error_message": "连接失败，请重试",
  "details": "token已过期，需要刷新",
  "retry_after": 5,
  "can_retry": true
}
```

**错误码分类：**

| 错误码 | 重试策略 | 说明 |
|--------|----------|------|
| **0** | **可以重试** | 临时性错误，重试可能成功 |
| **1** | **不要重试** | 配置错误，需要修复后重试 |

**错误码说明：**

| 错误码 | 错误消息 | NotCanRetry | 重试 | 处理建议 |
|--------|----------|-------------|------|----------|
| **0** | 连接失败，请重试 | `false` | ✅ | 5秒后自动重试 |
| **1** | 连接失败，需要检查配置 | `true` | ❌ | 检查配置后手动重试 |

### 4.4 JWT Token结构
```json
{
  "user_id": "123",
  "device_id": "android_device_001",
  "client_type": "device",
  "exp": 1640995200,
  "iat": 1640991600
}
```

### 4.5 速率与连接数限制
- **IP限流**：每IP每60秒最多100次连接尝试
- **设备限流**：每设备每60秒最多5次连接尝试
- **同设备多连**：新连接会踢掉旧连接
- **心跳超时**：45秒无心跳自动断开
- **连接数限制**：每个用户最多200个连接（设备+UniApp分别统计）

### 4.6 设备状态管理
服务端会自动管理设备状态，确保状态与WebSocket连接状态同步：

**状态更新时机：**
- **设备注册**：设备发送 `REGISTER_REQ` 时，状态更新为在线 (`status = 1`)
- **心跳保持**：每次心跳成功时，保持在线状态并更新 `last_active` 时间
- **连接断开**：WebSocket连接断开时，状态自动更新为离线 (`status = 0`)

**状态值说明：**
- `status = 0`: 离线
- `status = 1`: 在线

---

## 5. 消息协议

### 5.1 通用消息结构
所有消息均为JSON格式：
```json
{
  "type": "事件类型",
  "seq": "唯一序列号",
  "timestamp": 1640995200000,
  "payload": { ... }
}
```
- `type`：事件类型（见下表）
- `seq`：消息唯一标识（建议用UUID或时间戳）
- `timestamp`：毫秒级时间戳
- `payload`：事件数据体

### 5.2 事件类型一览
| 方向   | type             | 说明                 |
|--------|------------------|----------------------|
| C->S   | REGISTER_REQ     | 设备注册请求         |
| S->C   | REGISTER_RESP    | 注册响应             |
| C->S   | HEARTBEAT        | 心跳                 |
| S->C   | HEARTBEAT_RESP   | 心跳响应             |
| S->C   | SCRIPT_RUN       | 运行脚本指令         |
| C->S   | SCRIPT_RUN       | UniApp运行脚本请求   |
| S->C   | SCRIPT_STOP      | 停止脚本指令         |
| C->S   | SCRIPT_STOP      | UniApp停止脚本请求   |
| C->S   | TASK_RESULT      | 任务执行结果         |
| C->S   | DEVICE_INFO      | 设备信息上报         |
| C->S   | ERROR            | 错误上报             |

---

## 6. 典型消息格式

### 6.1 设备注册
**客户端 -> 服务端**
```json
{
  "type": "REGISTER_REQ",
  "seq": "uuid-1234-5678",
  "timestamp": 1640995200000,
  "payload": {
    "device_id": "android_device_001",
    "device_model": "Samsung Galaxy S21",
    "android_version": "12",
    "autox_version": "4.1.1"
  }
}
```

**服务端 -> 客户端**
```json
{
  "type": "REGISTER_RESP",
  "seq": "uuid-1234-5678",
  "timestamp": 1640995200000,
  "payload": {
    "success": true,
    "message": "注册成功"
  }
}
```

### 6.2 心跳机制
**客户端 -> 服务端**
```json
{
  "type": "HEARTBEAT",
  "seq": "uuid-5678-9012",
  "timestamp": 1640995200000,
  "payload": {
    "device_id": "android_device_001",
    "status": "idle"
  }
}
```

**服务端 -> 客户端**
```json
{
  "type": "HEARTBEAT_RESP",
  "seq": "uuid-5678-9012",
  "timestamp": 1640995200000,
  "payload": {
    "success": true,
    "server_time": 1640995200000
  }
}
```

### 6.3 脚本下发
**服务端 -> 客户端**

**脚本下发参数说明：**
- `script_id`: 脚本ID（字符串格式）
- `action`: 操作类型（"run" 或 "stop"）
- `params`: 脚本参数对象，包含：
  - `script_id`: 脚本ID（数字格式）
  - `script_name`: 脚本名称
  - `script_content`: 完整的脚本内容
  - `version`: 脚本版本
  - `config_name`: 配置名称
  - `config`: 脚本配置参数（JSON对象）

#### 6.3.1 基础脚本下发
```json
{
  "type": "SCRIPT_RUN",
  "seq": "uuid-9012-3456",
  "timestamp": 1640995200000,
  "payload": {
    "script_id": "123",
    "action": "run",
    "params": {
      "script_id": 123,
      "script_name": "基础测试脚本",
    "script_content": "console.log('Hello AutoX');\nconsole.show();\nconsole.log('脚本开始执行');\n\n// 执行一些操作\nfor(let i = 0; i < 5; i++) {\n    console.log('执行第' + (i+1) + '次操作');\n    sleep(1000);\n}\n\nconsole.log('脚本执行完成');",
      "version": "1.0.0",
      "config_name": "默认配置",
      "config": {}
    }
  }
}
```

#### 6.3.2 带参数的脚本下发
```json
{
  "type": "SCRIPT_RUN",
  "seq": "uuid-9012-3457",
  "timestamp": 1640995200000,
  "payload": {
    "script_id": "124",
    "action": "run",
    "params": {
      "script_id": 124,
      "script_name": "微信自动点赞脚本",
    "script_content": "// 获取配置参数\nconst config = $config;\nconst targetApp = config.target_app || 'com.tencent.mm';\nconst clickCount = config.click_count || 1;\n\nconsole.log('目标应用:', targetApp);\nconsole.log('点击次数:', clickCount);\n\n// 启动目标应用\napp.launchApp(targetApp);\nsleep(2000);\n\n// 执行点击操作\nfor(let i = 0; i < clickCount; i++) {\n    click(500, 800);\n    console.log('执行第' + (i+1) + '次点击');\n    sleep(1000);\n}\n\nconsole.log('脚本执行完成');",
      "version": "1.0.0",
      "config_name": "微信点赞配置",
    "config": {
      "target_app": "com.tencent.mm",
      "click_count": 5,
      "screen_width": 1080,
      "screen_height": 1920
      }
    }
  }
}
```

#### 6.3.3 停止脚本
```json
{
  "type": "SCRIPT_STOP",
  "seq": "uuid-9012-3458",
  "timestamp": 1640995200000,
  "payload": {
    "script_id": "123",
    "action": "stop",
    "params": {
      "action": "stop"
    }
  }
}
```

#### 6.3.4 UniApp运行脚本请求
**UniApp客户端 -> 服务端**
```json
{
  "type": "SCRIPT_RUN",
  "seq": "uuid-9012-3459",
  "timestamp": 1640995200000,
  "payload": {
    "script_id": "123",
    "device_ids": ["android_device_001", "android_device_002"],
    "action": "run"
  }
}
```

**说明：**
- `script_id`: 脚本ID（字符串格式）
- `device_ids`: 目标设备ID数组
- `action`: 操作类型，固定为 "run"

**服务端处理：**
1. 验证UniApp客户端权限
2. 获取完整的脚本运行数据（脚本内容、配置等）
3. 将请求转发给指定的设备
4. 设备收到后执行脚本
5. 设备上报执行结果

#### 6.3.5 UniApp停止脚本请求
**UniApp客户端 -> 服务端**
```json
{
  "type": "SCRIPT_STOP",
  "seq": "uuid-9012-3460",
  "timestamp": 1640995200000,
  "payload": {
    "script_id": "123",
    "device_ids": ["android_device_001", "android_device_002"],
    "action": "stop"
  }
}
```

**说明：**
- `script_id`: 脚本ID（字符串格式）
- `device_ids`: 目标设备ID数组
- `action`: 操作类型，固定为 "stop"

**服务端处理：**
1. 验证UniApp客户端权限
2. 将请求转发给指定的设备
3. 设备收到后停止脚本
4. 设备上报停止结果

### 6.4 任务结果上报
**客户端 -> 服务端**

#### 6.4.1 成功结果上报
```json
{
  "type": "TASK_RESULT",
  "seq": "uuid-3456-7890",
  "timestamp": 1640995200000,
  "payload": {
    "script_id": "script_456",
    "success": true,
    "status": "completed",
    "result": "脚本执行成功",
    "execution_time": 1500,
    "start_time": 1640995200000,
    "end_time": 1640995201500,
    "logs": [
      "脚本开始执行",
      "执行第1次操作",
      "执行第2次操作",
      "执行第3次操作",
      "执行第4次操作",
      "执行第5次操作",
      "脚本执行完成"
    ],
    "data": {
      "click_count": 5,
      "success_rate": 100,
      "error_count": 0
    }
  }
}
```

#### 6.4.2 失败结果上报
```json
{
  "type": "TASK_RESULT",
  "seq": "uuid-3456-7891",
  "timestamp": 1640995200000,
  "payload": {
    "script_id": "script_457",
    "success": false,
    "status": "failed",
    "error_code": "SCRIPT_ERROR",
    "error_message": "脚本执行失败",
    "execution_time": 5000,
    "start_time": 1640995200000,
    "end_time": 1640995205000,
    "logs": [
      "脚本开始执行",
      "目标应用: com.tencent.mm",
      "点击次数: 5",
      "启动微信应用",
      "错误: 应用启动失败",
      "脚本执行失败"
    ],
    "data": {
      "click_count": 0,
      "success_rate": 0,
      "error_count": 1,
      "error_details": "应用com.tencent.mm未安装或无法启动"
    }
  }
}
```

#### 6.4.3 执行中状态上报
```json
{
  "type": "TASK_RESULT",
  "seq": "uuid-3456-7892",
  "timestamp": 1640995200000,
  "payload": {
    "script_id": "script_458",
    "success": null,
    "status": "running",
    "progress": 50,
    "execution_time": 15000,
    "start_time": 1640995200000,
    "current_step": "正在执行点赞操作",
    "logs": [
      "开始执行微信自动点赞脚本",
      "最大点赞数: 20",
      "点赞间隔: 3000ms",
      "启动微信",
      "进入朋友圈",
      "第1次点赞成功",
      "第2次点赞成功",
      "第3次点赞成功",
      "第4次点赞成功",
      "第5次点赞成功",
      "第6次点赞成功",
      "第7次点赞成功",
      "第8次点赞成功",
      "第9次点赞成功",
      "第10次点赞成功"
    ],
    "data": {
      "like_count": 10,
      "total_likes": 20,
      "progress_percent": 50
    }
  }
}
```

### 6.5 设备信息上报
**客户端 -> 服务端**
```json
{
  "type": "DEVICE_INFO",
  "seq": "uuid-7890-1234",
  "timestamp": 1640995200000,
  "payload": {
    "device_name": "我的设备",
    "android_version": "12",
    "app_version": "4.1.1",
    "screen_width": 1080,
    "screen_height": 1920
  }
}
```

### 6.6 错误上报
**客户端 -> 服务端**

#### 6.6.1 脚本执行错误
```json
{
  "type": "ERROR",
  "seq": "uuid-7890-1234",
  "timestamp": 1640995200000,
  "payload": {
    "error_code": "SCRIPT_ERROR",
    "error_message": "脚本执行失败",
    "error_type": "syntax_error",
    "details": "第10行语法错误: Unexpected token '}'",
    "script_id": "script_456",
    "line_number": 10,
    "stack_trace": "at eval (script:10:1)\nat executeScript (autox.js:45:2)"
  }
}
```

#### 6.6.2 网络连接错误
```json
{
  "type": "ERROR",
  "seq": "uuid-7890-1235",
  "timestamp": 1640995200000,
  "payload": {
    "error_code": "NETWORK_ERROR",
    "error_message": "网络连接失败",
    "error_type": "connection_timeout",
    "details": "连接服务器超时",
    "url": "https://api.example.com",
    "timeout": 30000
  }
}
```

#### 6.6.3 设备权限错误
```json
{
  "type": "ERROR",
  "seq": "uuid-7890-1236",
  "timestamp": 1640995200000,
  "payload": {
    "error_code": "PERMISSION_ERROR",
    "error_message": "权限不足",
    "error_type": "accessibility_service",
    "details": "无障碍服务未启用",
    "required_permission": "android.permission.BIND_ACCESSIBILITY_SERVICE",
    "suggestion": "请在设置中启用无障碍服务"
  }
}
```

---

## 7. 安卓端（AutoX.js）对接指引

### 7.1 连接建立
```javascript
// 1. 先通过HTTP配对获取token
const pairingResponse = await fetch('https://yourdomain.com/api/v1/devices/pairings/submit', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    pairing_code: 'ABC123',
    device_id: 'android_device_001',
    name: '我的设备',
    device_model: 'Samsung Galaxy S21',
    android_version: '12',
    autox_version: '6.6.0'
  })
});

const { access_token } = await pairingResponse.json();

// 2. 建立WebSocket连接
function connectWebSocket() {
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(`wss://yourdomain.com/api/v1/ws?token=${access_token}`);

ws.onopen = () => {
  console.log('WebSocket连接已建立');
  // 发送注册请求
  sendMessage({
    type: 'REGISTER_REQ',
    seq: generateUUID(),
    timestamp: Date.now(),
    payload: {
      device_id: 'android_device_001',
      device_model: 'Samsung Galaxy S21',
      android_version: '12',
      autox_version: '4.1.1'
    }
  });
      resolve(ws);
};
    
    ws.onerror = (error) => {
      console.error('WebSocket连接错误:', error);
      reject(error);
    };
    
    ws.onclose = (event) => {
      console.log('WebSocket连接关闭:', event);
      // 检查是否是HTTP错误响应
      if (event.code >= 1000 && event.code < 2000) {
        // 这是WebSocket协议错误码，需要特殊处理
        handleWebSocketError(event.code, event.reason);
      }
    };
  });
}

// 3. 错误处理函数
async function handleWebSocketError(code, reason) {
  console.log(`WebSocket错误: code=${code}, reason=${reason}`);
  
  // 尝试解析错误响应
  try {
    const errorResponse = JSON.parse(reason);
    const { error_code, error_message, details, retry_after, not_can_retry } = errorResponse;
    
    console.log(`错误码: ${error_code}, 错误信息: ${error_message}, 详情: ${details}, 不可重试: ${not_can_retry}`);
    
    if (!not_can_retry) {
      // 可以重试的错误（默认行为）
      console.error('连接失败，5秒后重试');
      setTimeout(() => {
        connectWebSocket();
      }, retry_after * 1000);
    } else {
      // 不可重试的错误（明确标记）
      console.error('连接失败，需要检查配置');
      // 提示用户检查配置或重新配对
      showErrorToUser('连接失败，请检查设备配置或重新配对');
    }
  } catch (e) {
    console.error('解析错误响应失败:', e);
  }
}

// 4. 显示错误给用户
function showErrorToUser(message) {
  // 在UI上显示错误信息给用户
  console.log('用户提示:', message);
}

// 4. 重新配对设备
async function rePairDevice() {
  console.log('开始重新配对设备...');
  // 实现重新配对逻辑
  // 这里需要用户重新生成配对码
}

// 5. 使用新token连接
async function connectWithNewToken(newToken) {
  console.log('使用新token重新连接...');
  // 实现使用新token连接的逻辑
}

// 6. 建立连接
try {
  const ws = await connectWebSocket();
  // 连接成功，继续其他操作
} catch (error) {
  console.error('连接失败:', error);
}
```

### 7.2 心跳维护
```javascript
// 启动心跳定时器
setInterval(() => {
  if (ws.readyState === WebSocket.OPEN) {
    sendMessage({
      type: 'HEARTBEAT',
      seq: generateUUID(),
      timestamp: Date.now(),
      payload: {
        device_id: 'android_device_001',
        status: 'idle'
      }
    });
  }
}, 25000); // 25秒发送一次心跳
```

### 7.3 消息处理
```javascript
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  
  switch (message.type) {
    case 'REGISTER_RESP':
      console.log('注册成功');
      break;
      
    case 'SCRIPT_RUN':
      handleScriptRun(message.payload);
      break;
      
    case 'SCRIPT_STOP':
      handleScriptStop(message.payload);
      break;
      
    case 'HEARTBEAT_RESP':
      console.log('心跳响应');
      break;
  }
};

function handleScriptRun(payload) {
  const { script_id, action, params } = payload;
  
  if (action === 'run') {
  // 执行脚本
  try {
      const scriptContent = params.script_content;
      const config = params.config; // 使用params.config而不是整个params
      
      // 将配置注入到脚本中
      const scriptWithConfig = scriptContent.replace('$config', JSON.stringify(config));
      
      // 执行脚本
      const result = eval(scriptWithConfig);
    
    // 上报执行结果
    sendMessage({
      type: 'TASK_RESULT',
      seq: generateUUID(),
      timestamp: Date.now(),
      payload: {
          script_id: script_id,
        success: true,
          status: 'completed',
        result: result,
          execution_time: Date.now() - message.timestamp,
          logs: ['脚本执行完成']
      }
    });
  } catch (error) {
    // 上报错误
    sendMessage({
      type: 'ERROR',
      seq: generateUUID(),
      timestamp: Date.now(),
      payload: {
        error_code: 'SCRIPT_ERROR',
          error_message: error.message,
          script_id: script_id
      }
    });
  }
}
}

function handleScriptStop(payload) {
  const { script_id } = payload;
  
  // 停止脚本执行
  // 这里需要根据具体的脚本执行机制来实现
  
  // 上报停止结果
  sendMessage({
    type: 'TASK_RESULT',
    seq: generateUUID(),
    timestamp: Date.now(),
    payload: {
      script_id: script_id,
      success: true,
      status: 'stopped',
      result: '脚本已停止'
    }
  });
}
```

### 7.4 断线重连
```javascript
ws.onclose = () => {
  console.log('WebSocket连接已断开，尝试重连...');
  setTimeout(reconnect, 5000);
};

function reconnect() {
  // 重新建立连接逻辑
  // 注意：token可能已过期，需要重新配对或刷新token
}
```

### 7.5 Token刷新
```javascript
// 当access_token过期时，使用refresh_token刷新
async function refreshToken(refreshToken) {
  try {
    const response = await fetch('https://yourdomain.com/api/v1/auth/device/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refresh_token: refreshToken })
    });
    
    const data = await response.json();
    if (data.code === 0) {
      // 更新token
      return data.data.access_token;
    }
  } catch (error) {
    console.error('刷新token失败:', error);
  }
  return null;
}
```

---

## 8. 常见问题与建议

### 8.1 错误处理
- **可重试错误（0）**：临时性错误，5秒后自动重试
- **不可重试错误（1）**：配置错误，需要检查配置后手动重试

### 8.3 断线重连
- **建议**：实现指数退避重连机制
- **注意**：重连前检查token是否过期

### 8.4 消息格式
- **要求**：严格遵循JSON格式
- **注意**：时间戳使用毫秒级

### 8.5 心跳丢失
- **建议**：客户端主动发送心跳
- **频率**：25秒发送一次，服务端45秒超时

### 8.6 设备状态同步
- **注意**：设备状态只在发送 `REGISTER_REQ` 或 `HEARTBEAT` 时更新
- **建议**：设备上线后立即发送注册请求

### 8.7 错误处理最佳实践
- **极简判断**：只看 `not_can_retry` 字段
- **默认重试**：`not_can_retry = false` 时自动5秒后重试（默认行为）
- **明确停止**：`not_can_retry = true` 时停止重试，提示用户检查配置
- **安全设计**：避免漏掉场景导致不重试，只有明确标记才停止
- **避免复杂逻辑**：不需要判断具体错误类型

### 8.8 脚本控制最佳实践
- **运行脚本**：优先使用WebSocket方式，实时性更高
- **停止脚本**：优先使用WebSocket方式，响应更快
- **降级策略**：WebSocket不可用时自动降级到HTTP方式
- **批量操作**：支持同时向多个设备发送脚本控制命令
- **状态同步**：设备执行结果实时上报，UniApp实时更新状态

---

## 9. 安全考虑

### 9.1 传输安全
- 生产环境必须使用 `wss://`
- 证书必须由权威CA签发

### 9.2 认证安全
- token具有时效性，定期更新
- 设备ID全局唯一，防止冲突
- JWT token包含设备权限信息

### 9.3 速率限制
- 防止恶意连接和DDoS攻击
- 保护服务端资源

### 9.4 数据验证
- 所有输入数据必须验证
- 防止脚本注入攻击

---

## 10. 参考与扩展

### 10.1 相关文档
- [WebSocket RFC 6455](https://tools.ietf.org/html/rfc6455)
- [AutoX.js 官方文档](https://doc.autoxjs.com/)
- [JWT RFC 7519](https://tools.ietf.org/html/rfc7519)

### 10.2 扩展功能
- 支持更多任务类型
- 增加设备分组功能
- 实现脚本版本管理
- 添加设备远程控制

### 10.3 性能优化
- 消息压缩
- 连接池管理
- 负载均衡 