# API密钥管理功能说明

## 功能概述

新增的API密钥管理模块提供了完整的第三方API密钥生命周期管理，包括创建、查看、启用/禁用、删除等功能。

## 前端功能

### 1. API密钥列表页面 (`/api-keys`)

**功能特点：**
- ✅ 分页显示API密钥列表
- ✅ 按名称和状态筛选
- ✅ 显示密钥权限标签
- ✅ 显示最后使用时间和过期时间
- ✅ 支持启用/禁用操作
- ✅ 支持删除操作
- ✅ SecretId一键复制功能

**主要组件：**
- 搜索表单：支持按名称和状态筛选
- 数据表格：展示密钥详细信息
- 操作按钮：查看密钥、启用/禁用、删除

### 2. 创建API密钥对话框

**功能特点：**
- ✅ 密钥名称输入
- ✅ 权限配置（充值权限、查询权限）
- ✅ 可选的过期时间设置
- ✅ 表单验证
- ✅ 创建成功后自动显示密钥详情

### 3. 密钥详情对话框

**功能特点：**
- ✅ 显示完整的SecretId和SecretKey
- ✅ 一键复制功能
- ✅ 安全提醒信息
- ✅ 仅在创建时显示SecretKey

### 4. API测试页面 (`/api-test`)

**功能特点：**
- ✅ 签名算法测试
- ✅ 第三方充值API测试
- ✅ 实时签名生成和验证
- ✅ 详细的调试信息显示

## 后端接口

### 1. 创建API密钥
```
POST /admin/v1/api-keys/create
```

**请求参数：**
```json
{
  "name": "测试密钥",
  "permissions": {
    "recharge": true,
    "query": false
  },
  "expires_at": "2024-12-31 23:59:59"
}
```

### 2. 获取API密钥列表
```
POST /admin/v1/api-keys/list
```

**请求参数：**
```json
{
  "page": 1,
  "page_size": 10,
  "name": "搜索名称",
  "status": 1
}
```

### 3. 删除API密钥
```
POST /admin/v1/api-keys/delete
```

**请求参数：**
```json
{
  "secret_id": "AKID1234567890abcdef1234567890ab"
}
```

### 4. 更新API密钥状态
```
POST /admin/v1/api-keys/update-status
```

**请求参数：**
```json
{
  "secret_id": "AKID1234567890abcdef1234567890ab",
  "status": 2
}
```

## 数据库表结构

```sql
CREATE TABLE IF NOT EXISTS `third_api_keys` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `secret_id` VARCHAR(32) NOT NULL COMMENT 'SecretId',
    `secret_key` VARCHAR(64) NOT NULL COMMENT 'SecretKey',
    `name` VARCHAR(100) NOT NULL COMMENT '密钥名称/描述',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用 2-禁用',
    `permissions` TEXT NOT NULL COMMENT '权限列表，JSON格式',
    `last_used_at` TIMESTAMP NULL COMMENT '最后使用时间',
    `expires_at` TIMESTAMP NULL COMMENT '过期时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_secret_id` (`secret_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='第三方API密钥表';
```

## 安全特性

### 1. 密钥生成
- SecretId：32字符，格式为 `AKID` + 28位随机十六进制字符
- SecretKey：64字符随机十六进制字符
- 使用加密安全的随机数生成器

### 2. 权限控制
- 基于权限的访问控制
- 支持细粒度权限配置
- 权限信息存储为JSON格式

### 3. 状态管理
- 支持启用/禁用状态
- 支持过期时间设置
- 记录最后使用时间

### 4. 前端安全
- SecretKey仅在创建时显示一次
- 敏感信息复制后自动清除
- 删除操作需要二次确认

## 使用流程

### 管理员操作流程：

1. **创建API密钥**
   - 访问 `/api-keys` 页面
   - 点击"创建API密钥"按钮
   - 填写密钥名称和权限配置
   - 可选设置过期时间
   - 点击创建，系统自动生成密钥对

2. **查看密钥详情**
   - 在列表中点击"查看密钥"
   - 复制SecretId和SecretKey
   - 提供给第三方开发者

3. **管理密钥状态**
   - 可以启用/禁用密钥
   - 可以删除不再使用的密钥
   - 查看密钥使用情况

### 第三方开发者使用流程：

1. **获取密钥**
   - 从管理员处获取SecretId和SecretKey

2. **集成API**
   - 使用提供的SDK或参考文档实现签名算法
   - 调用第三方API接口

3. **测试验证**
   - 可以使用 `/api-test` 页面进行测试
   - 验证签名算法实现是否正确

## 技术实现

### 前端技术栈
- Vue 3 + TypeScript
- Element Plus UI组件库
- Pinia状态管理
- Vue Router路由管理
- Crypto-JS加密库

### 后端技术栈
- Go + Gin框架
- GORM数据库ORM
- HMAC-SHA256签名算法
- JWT令牌（如需要）

### 关键文件
- 前端页面：`web/src/views/apikey/APIKeyList.vue`
- 前端测试：`web/src/views/apikey/APIKeyTest.vue`
- 前端API：`web/src/api/index.ts`
- 后端处理器：`internal/handler/third_api.go`
- 后端服务：`internal/service/third_api_key.go`
- 数据模型：`internal/model/third_api_key.go`

这个API密钥管理模块提供了完整的企业级功能，确保了安全性和易用性的平衡。
