# GoAdmin 群控系统

一个基于 Go + Vue3 + UniApp 的现代化群控管理系统，支持多端设备统一管理、脚本分发、实时监控等功能。

## 🚀 项目特色

- **多端支持**: 支持 Web 管理端、移动端 App、AutoJS 客户端
- **实时通信**: 基于 WebSocket 的实时双向通信
- **用户认证**: 完整的用户注册、登录、权限管理系统
- **设备管理**: 支持设备注册、分组、状态监控
- **脚本分发**: 支持脚本创建、分发、执行监控
- **任务调度**: 支持定时任务和批量操作
- **安全可靠**: JWT 认证、数据加密、权限控制

## 📁 项目结构

```
goadmin/
├── app/                    # UniApp 移动端应用
│   ├── src/
│   │   ├── pages/         # 页面文件
│   │   ├── stores/        # 状态管理
│   │   ├── utils/         # 工具函数
│   │   └── static/        # 静态资源
│   └── package.json
├── web/                   # Vue3 Web 管理端
│   ├── src/
│   │   ├── views/         # 页面组件
│   │   ├── api/           # API 接口
│   │   ├── router/        # 路由配置
│   │   └── stores/        # 状态管理
│   └── package.json
├── internal/              # Go 后端核心代码
│   ├── app/              # 应用配置
│   ├── handler/          # HTTP 处理器
│   ├── service/          # 业务逻辑层
│   ├── model/            # 数据模型
│   ├── middleware/       # 中间件
│   └── constant/         # 常量定义
├── pkg/                  # 公共包
├── config/               # 配置文件
├── deploy/               # 部署相关
├── docs/                 # 文档
└── scripts/              # 脚本文件
```

## 🛠️ 技术栈

### 后端 (Go)
- **框架**: Gin
- **数据库**: MySQL + GORM
- **认证**: JWT
- **WebSocket**: Gorilla WebSocket
- **配置**: Viper
- **日志**: Lumberjack

### Web 管理端 (Vue3)
- **框架**: Vue 3 + TypeScript
- **UI 库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **构建工具**: Vite

### 移动端 (UniApp)
- **框架**: UniApp + Vue 3
- **状态管理**: Pinia
- **UI 组件**: UniApp 原生组件
- **构建工具**: Vite

## 🚀 快速开始

### 环境要求

- Go 1.23+
- Node.js 18+
- MySQL 8.0+
- Redis (可选)

### 1. 克隆项目

```bash
git clone https://github.com/your-username/goadmin.git
cd goadmin
```

### 2. 后端启动

```bash
# 安装依赖
go mod tidy

# 配置数据库
cp config.yaml.example config.yaml
# 编辑 config.yaml 配置数据库连接

# 创建数据库表
mysql -u your_user -p your_database < deploy/create_tables.sql

# 启动服务
go run main.go
```

### 3. Web 管理端启动

```bash
cd web

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 4. 移动端启动

```bash
cd app

# 安装依赖
npm install

# 启动开发服务器 (H5)
npm run dev:h5

# 启动开发服务器 (微信小程序)
npm run dev:mp-weixin

# 构建生产版本
npm run build:h5
```

## 🔐 用户认证系统

### 认证流程

1. **用户注册** → 2. **用户登录** → 3. **获取 Token** → 4. **访问受保护资源**

### API 接口

#### 用户认证
```http
POST /api/v1/auth/register    # 用户注册
POST /api/v1/auth/login       # 用户登录
POST /api/v1/auth/refresh     # 刷新 Token
POST /api/v1/auth/logout      # 用户登出
```

#### 用户资料
```http
GET  /api/v1/user/profile     # 获取用户资料
PUT  /api/v1/user/profile     # 更新用户资料
PUT  /api/v1/user/password    # 修改密码
```

### 前端集成

#### Web 端
```javascript
import api from '@/api'

// 用户注册
const registerData = {
  username: 'testuser',
  password: 'password123',
  email: '<EMAIL>'
}
await api.userRegister(registerData)

// 用户登录
const loginData = {
  username: 'testuser',
  password: 'password123'
}
const response = await api.userLogin(loginData)
```

#### UniApp 端
```javascript
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 登录
const success = await userStore.login(username, password)

// 检查登录状态
if (userStore.isAuthenticated) {
  // 已登录
}
```

## 📱 移动端功能

### 主要页面

- **登录页面** (`/pages/login/index`) - 用户认证
- **仪表盘** (`/pages/dashboard/index`) - 数据概览
- **设备管理** - 设备列表和状态监控
- **脚本管理** - 脚本创建和分发
- **任务管理** - 任务执行和监控

### 状态管理

使用 Pinia 进行状态管理，支持：
- 用户信息管理
- 登录状态持久化
- Token 自动刷新
- 全局状态同步

## 🌐 Web 管理端功能

### 管理员功能
- 用户管理
- 设备管理
- 脚本管理
- 任务调度
- 系统配置
- 数据统计

### 用户功能
- 个人资料管理
- 设备管理
- 脚本管理
- 任务监控

## 🔌 WebSocket 实时通信

### 连接方式
```javascript
// 连接 WebSocket
const ws = new WebSocket('wss://your-domain.com/api/v1/ws?token=YOUR_TOKEN')

// 发送消息
ws.send(JSON.stringify({
  type: 'heartbeat',
  data: { timestamp: Date.now() }
}))
```

### 消息类型
- `heartbeat` - 心跳消息
- `status_update` - 状态更新
- `script_result` - 脚本执行结果
- `execute_script` - 执行脚本
- `disconnect` - 断开连接

## 📊 数据库设计

### 核心表结构

```sql
-- 用户表
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  email VARCHAR(100) UNIQUE,
  nickname VARCHAR(50),
  avatar VARCHAR(255),
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 设备表
CREATE TABLE devices (
  id INT PRIMARY KEY AUTO_INCREMENT,
  device_id VARCHAR(100) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  type VARCHAR(50),
  status TINYINT DEFAULT 1,
  user_id INT,
  last_active TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户脚本表
CREATE TABLE user_scripts (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  market_script_id BIGINT NOT NULL,
  name VARCHAR(100) NOT NULL,
  content LONGTEXT,
  config TEXT,
  version VARCHAR(20) DEFAULT '1.0.0',
  status TINYINT DEFAULT 1,
  expired_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_user_market_script (user_id, market_script_id)
);
```

### 设计特点

- **无强外键约束**: 为了提高性能和灵活性，数据库设计不包含强外键约束
- **软关联**: 通过索引实现表间关联，应用层处理数据一致性
- **高可用性**: 避免外键约束导致的性能瓶颈和锁竞争
- **易于扩展**: 支持分布式部署和分库分表

## 🔧 配置说明

### 后端配置 (config.yaml)

```yaml
server:
  port: 8080
  mode: debug

database:
  host: localhost
  port: 3306
  username: root
  password: your_password
  database: goadmin

jwt:
  secret_key: your-secret-key
  expires_time: 24h

websocket:
  enabled: true
  port: 8081
```

### 前端配置

#### Web 端 (vite.config.ts)
```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

#### UniApp 端 (manifest.json)
```json
{
  "h5": {
    "devServer": {
      "port": 3000,
      "proxy": {
        "/api": {
          "target": "http://localhost:8080",
          "changeOrigin": true
        }
      }
    }
  }
}
```

## 🚀 部署指南

### Docker 部署

```bash
# 构建镜像
docker build -t goadmin .

# 运行容器
docker run -d \
  --name goadmin \
  -p 8080:8080 \
  -e DB_HOST=your-db-host \
  -e DB_PASSWORD=your-db-password \
  goadmin
```

### 生产环境部署

1. **后端部署**
```bash
# 编译
go build -o goadmin main.go

# 运行
./goadmin -config config.yaml
```

2. **前端部署**
```bash
# Web 端
cd web
npm run build
# 将 dist 目录部署到 Web 服务器

# UniApp 端
cd app
npm run build:h5
# 将 dist/build/h5 目录部署到 Web 服务器
```

## 📝 API 文档

详细的 API 文档请参考：
- [WebSocket API 文档](docs/websocket-api.md)
- [用户认证指南](web/USER_AUTH_GUIDE.md)
- [第三方集成文档](README-third-party.md)

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有建议，请：

1. 查看 [Issues](../../issues) 页面
2. 创建新的 Issue
3. 联系开发团队

## 🔄 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🔐 完整的用户认证系统
- 📱 UniApp 移动端支持
- 🌐 Web 管理端
- 🔌 WebSocket 实时通信
- 📊 设备管理和监控
- 📝 脚本分发和执行

---

**GoAdmin 群控系统** - 让设备管理更简单、更高效！ 🚀
