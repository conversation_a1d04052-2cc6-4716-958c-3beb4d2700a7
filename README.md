# GoAdmin - 卡密管理系统

[![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)](https://golang.org)
[![Vue Version](https://img.shields.io/badge/Vue-3.5+-green.svg)](https://vuejs.org)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个基于 Go + Vue 3 的现代化卡密管理系统，支持多种认证方式、第三方API集成、脚本管理和设备绑定等功能。

## ✨ 主要特性

### 🔐 多重认证体系
- **管理员认证**: JWT令牌认证，支持后台管理
- **客户端认证**: 卡密登录，设备绑定验证
- **Hamibot认证**: 专用于Hamibot脚本的认证机制
- **签名认证**: 第三方API调用的HMAC-SHA256签名验证

### 💳 卡密管理
- **多类型卡密**: 支持时间卡、次数卡
- **设备绑定**: 限制同时在线设备数量
- **卡密充值**: 支持以卡充卡功能
- **状态管理**: 未使用、已使用、已过期状态跟踪

### 🔌 第三方API集成
- **API密钥管理**: 创建、管理第三方API密钥
- **权限控制**: 细粒度权限配置（充值、查询等）
- **签名验证**: 云厂商级别的API签名算法
- **使用统计**: 记录API调用次数和最后使用时间

### 📱 设备管理
- **设备绑定**: 卡密与设备ID绑定
- **在线状态**: 实时监控设备在线状态
- **批量操作**: 支持批量解绑设备

### 📊 数据统计
- **仪表盘**: 实时数据概览
- **使用统计**: Hamibot用户活跃度统计
- **操作日志**: 详细的API调用日志记录

### 🛠 系统管理
- **配置管理**: 动态系统配置
- **脚本管理**: GitHub集成的脚本管理
- **日志记录**: 完整的操作审计日志

## 🏗 技术架构

### 后端技术栈
- **框架**: Gin (Go Web框架)
- **数据库**: MySQL + GORM
- **认证**: JWT + 自定义签名算法
- **日志**: 结构化日志记录
- **配置**: YAML配置文件

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **构建工具**: Vite
- **图表**: ECharts

### 项目结构
```
goadmin/
├── cmd/                    # 命令行工具
├── config/                 # 配置文件处理
├── deploy/                 # 部署相关文件
│   ├── create_tables.sql   # 数据库表结构
│   └── *.sql              # 数据库迁移脚本
├── docs/                   # 项目文档
├── internal/               # 内部代码
│   ├── app/               # 应用程序入口
│   ├── constant/          # 常量定义
│   ├── handler/           # HTTP处理器
│   ├── middleware/        # 中间件
│   ├── model/            # 数据模型
│   └── service/          # 业务逻辑
├── pkg/                   # 公共包
├── web/                   # 前端代码
│   ├── src/
│   │   ├── api/          # API接口
│   │   ├── components/   # 组件
│   │   ├── router/       # 路由配置
│   │   ├── stores/       # 状态管理
│   │   └── views/        # 页面视图
│   └── package.json
├── config.yaml           # 配置文件
├── go.mod                # Go模块文件
└── main.go              # 程序入口
```

## 🚀 快速开始

### 环境要求
- Go 1.21+
- Node.js 18+
- MySQL 8.0+

### 1. 克隆项目
```bash
git clone <repository-url>
cd goadmin
```

### 2. 配置数据库
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE goadmin CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;"

# 导入表结构
mysql -u root -p goadmin < deploy/create_tables.sql
```

### 3. 配置文件
复制并修改配置文件：
```bash
cp config.yaml.example config.yaml
# 编辑 config.yaml，配置数据库连接等信息
```

### 4. 启动后端
```bash
# 安装依赖
go mod tidy

# 启动服务
go run main.go
```

### 5. 启动前端
```bash
cd web
npm install
npm run dev
```

### 6. 访问系统
- 前端地址: http://localhost:5173
- 后端API: http://localhost:8080
- 默认管理员账号: admin / admin123

## 📚 API文档

### 管理员API (`/admin/v1`)
- `POST /auth/login` - 管理员登录
- `POST /admins/*` - 管理员管理
- `POST /cardkeys/*` - 卡密管理
- `POST /api-keys/*` - API密钥管理
- `POST /configs/*` - 系统配置管理

### 客户端API (`/api/v1`)
- `POST /login` - 卡密登录
- `POST /bindings/*` - 设备绑定管理
- `POST /cardkeys/recharge` - 卡密充值
- `POST /card-configs/*` - 卡密配置管理

### 第三方API (`/api/v1/third`)
- `POST /recharge` - 第三方充值接口（需签名认证）

### Hamibot API (`/api/v1/script`)
- `POST /hamibot` - 获取Hamibot脚本
- `POST /bot` - 获取Bot脚本
- `POST /botui` - 获取BotUI脚本

详细API文档请参考：
- [第三方API文档](docs/card-config-api.md)
- [API密钥管理文档](docs/api-key-management.md)

## 🔧 配置说明

### 数据库配置
```yaml
database:
  host: localhost
  port: 3306
  username: root
  password: your_password
  database: goadmin
```

### 服务器配置
```yaml
server:
  port: "8080"
  mode: debug  # debug, release
```

### JWT配置
```yaml
jwt:
  secret: your_jwt_secret
  expire_hours: 24
```

### GitHub集成配置
```yaml
github:
  token: your_github_token
  owner: your_username
  repo: your_repository
```

## 🛡 安全特性

### 认证机制
1. **JWT令牌**: 管理员和客户端认证
2. **签名验证**: 第三方API使用HMAC-SHA256签名
3. **时间戳验证**: 防止重放攻击
4. **权限控制**: 基于角色的访问控制

### 数据安全
1. **密码加密**: 使用bcrypt加密存储
2. **API密钥**: 安全的密钥生成和管理
3. **操作日志**: 完整的审计日志记录
4. **参数验证**: 严格的输入验证

## 📊 监控和日志

### 操作日志
系统记录所有API调用的详细信息：
- 请求路径和方法
- 请求参数和响应结果
- HTTP状态码和业务响应码
- 客户端IP和User-Agent
- 请求时间和响应时间

### 统计数据
- 卡密使用统计
- 设备在线统计
- API调用统计
- 错误率监控

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有建议，请：
1. 查看 [文档](docs/)
2. 提交 [Issue](../../issues)
3. 联系维护者

## 📋 数据库表结构

系统包含以下主要数据表：

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `admins` | 管理员账户 | username, password, status |
| `cardkeys` | 卡密信息 | card_key, type, value, status, expires_at |
| `user_logs` | 用户登录日志 | card_key, device_id, ip, login_time |
| `device_bindings` | 设备绑定 | card_key, device_id, status |
| `third_api_keys` | 第三方API密钥 | secret_id, secret_key, permissions |
| `card_configs` | 卡密配置 | card_key, config (JSON) |
| `operation_logs` | 操作日志 | path, method, params, response, code |

## 🔍 核心功能详解

### 卡密系统
- **时间卡**: 按天数计费，支持累加充值
- **次数卡**: 按使用次数计费，支持累加充值
- **设备绑定**: 每个卡密可绑定指定数量的设备
- **在线检测**: 实时监控设备在线状态

### 认证系统
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   管理员    │    │   客户端    │    │  第三方API  │
│  JWT认证    │    │  卡密认证   │    │  签名认证   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────────────────────────────────────────────┐
│              统一认证中间件                          │
└─────────────────────────────────────────────────────┘
```

### API签名算法
第三方API使用云厂商级别的签名算法：
1. 构造待签名字符串：`METHOD\nURI\nQueryString`
2. 使用HMAC-SHA256生成签名
3. 验证时间戳防止重放攻击
4. 支持权限细粒度控制

## 🎯 使用场景

### 软件授权管理
- 软件开发商可以使用本系统管理软件授权
- 支持按时间或次数进行授权
- 防止盗版和多设备共享

### Hamibot脚本分发
- 专门为Hamibot平台设计的脚本分发系统
- 支持多分支脚本管理
- 与GitHub无缝集成

### 第三方集成
- 提供标准的REST API接口
- 支持第三方系统集成充值功能
- 完整的API文档和SDK

## 🔧 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t goadmin .

# 运行容器
docker run -d \
  --name goadmin \
  -p 8080:8080 \
  -v ./config.yaml:/app/config.yaml \
  goadmin
```

### 生产环境部署
```bash
# 编译生产版本
go build -ldflags="-s -w" -o goadmin main.go

# 构建前端
cd web && npm run build

# 使用systemd管理服务
sudo systemctl enable goadmin
sudo systemctl start goadmin
```

### 反向代理配置 (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/service

# 运行测试并生成覆盖率报告
go test -cover ./...
```

### API测试
项目提供了完整的API测试脚本，支持：
- Postman集合导入
- ApiFox自动化测试
- 签名算法验证工具

## 🔄 更新日志

### v1.0.0 (2025-06-19)
- ✨ 初始版本发布
- 🔐 完整的认证体系
- 💳 卡密管理功能
- 🔌 第三方API集成
- 📊 数据统计和监控
- 🛠 系统管理功能

## 🏆 致谢

感谢以下开源项目的支持：
- [Gin](https://github.com/gin-gonic/gin) - Go Web框架
- [GORM](https://github.com/go-gorm/gorm) - Go ORM库
- [Vue 3](https://github.com/vuejs/core) - 前端框架
- [Element Plus](https://github.com/element-plus/element-plus) - UI组件库
