# 多端群控系统正式架构文档

## 1. 架构目标
- 实现基于 Go 服务端（goadmin）、Autox 客户端（Android）、uniapp 多端前端（Web/iOS/Android）的群控系统。
- 支持设备注册、心跳、命令下发、状态回传、任务调度、实时监控等能力。
- 前端一套代码多端适配，后端统一管理与消息中转。

## 2. 系统组成

### 2.1 uniapp 控制台（Web/iOS/Android）
- 用户操作入口，支持用户注册登录、脚本管理、脚本配置、设备管理、任务下发、状态监控、日志查看等。
- 通过 HTTP API 与服务端通信。

### 2.2 Go 服务端（goadmin）
- 认证与权限管理（JWT/refreshToken）
- 设备注册、心跳、状态管理
- 群控任务调度与下发
- WebSocket 实时通信（前端、客户端）
- 消息路由与转发
- 数据存储与日志

### 2.3 Autox 客户端（Android/Agent）
- 设备注册、心跳、状态上报
- 接收命令、执行任务、回传结果
- 实现 token/refreshToken 认证机制
- 与服务端保持 WebSocket 长连接

## 3. 通信流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Uniapp as uniapp控制台
    participant Go as Go服务端
    participant Autox as Autox客户端

    User->>Uniapp: 操作（如下发任务）
    Uniapp->>Go: API/WebSocket请求（如下发命令、查询状态）
    Go->>Autox: WebSocket推送命令
    Autox->>Go: 执行结果/状态回传
    Go->>Uniapp: 状态/结果推送
    Uniapp->>User: 实时展示
```

## 4. 认证与安全机制

- 登录时，客户端/前端通过设备ID/卡密/账号等获取 accessToken + refreshToken。
- WebSocket 连接、API 请求均需携带 accessToken。
- accessToken 过期后，使用 refreshToken 换取新 token。
- refreshToken 失效需重新登录。
- 所有通信建议采用 HTTPS。

## 5. 主要 API 与消息流

### 5.1 登录/认证
- `POST /api/v1/login`  
  请求：设备ID/卡密/账号  
  响应：accessToken, refreshToken
- `POST /api/v1/token/refresh`  
  请求：refreshToken  
  响应：新 accessToken, 新 refreshToken

### 5.2 WebSocket 连接
- 连接参数或首条消息携带 accessToken
- 认证通过后允许通信

### 5.3 设备注册/心跳/状态
- Autox 客户端通过 WebSocket 定期发送心跳包，保持在线状态
- 状态变更/任务执行结果实时推送

### 5.4 群控任务下发
- 用户在 uniapp 端发起任务
- 服务端调度并通过 WebSocket 推送到目标 Autox 客户端
- 客户端执行后回传结果，服务端再推送给前端

## 6. 技术选型
- 服务端：Go + Gin + Gorm + JWT + WebSocket
- 前端：uniapp（支持 Web/iOS/Android）
- 客户端：Autox + Agent（Kotlin/Java/Node.js，支持 WebSocket）
- 通信协议：HTTP（管理）、WebSocket（实时）
- 数据存储：MySQL/SQLite

## 7. 扩展性与安全性建议
- 支持多端并发、分组、批量操作
- 设备与用户权限隔离
- 日志与操作审计
- 支持后续扩展 iOS 被控端、第三方 API
- 严格 token 生命周期与失效机制
- 所有敏感通信均加密

---

# 多端群控系统详细设计文档（后端与uniapp）

## 一、功能模块分解

### 1. uniapp 控制台
- 用户注册/登录
- 脚本管理（删除、配置）
- 设备管理（列表、分组、详情、状态）
- 群控任务（新建、下发、进度、结果）
- 实时监控（状态、日志、告警）
- 个人中心（信息、密码、退出）

### 2. Go 服务端
- 用户/设备/脚本/任务/日志 数据模型
- 认证与权限（JWT+refreshToken）
- HTTP API（管理、配置、查询）
- WebSocket 服务（实时通信、推送）
- 设备注册/心跳/状态管理
- 群控任务调度与消息路由
- 日志与操作审计

## 二、后端（Go/goadmin）详细设计

### 1. 数据模型（MySQL/SQLite）
- User（用户表）：id, username, password_hash, email, created_at, ...
- Device（设备表）：id, device_id, name, status, last_active, user_id, ...
- Script（脚本表）：id, name, content, config, owner_id, ...
- Task（任务表）：id, name, type, status, target_devices, script_id, created_by, ...
- TaskResult（任务结果表）：id, task_id, device_id, status, output, ...
- Token（refreshToken表）：id, user_id/device_id, token, expires_at, ...

### 2. 认证与安全
- 用户/设备登录时，服务端生成 accessToken（JWT）和 refreshToken（存库）。
- 所有 API/WebSocket 需带 accessToken，过期用 refreshToken 换新。
- 设备端与用户端认证逻辑一致，权限区分。

### 3. HTTP API 设计（RESTful）
- `POST /api/v1/register` 用户注册
- `POST /api/v1/login` 用户/设备登录，返回 accessToken/refreshToken
- `POST /api/v1/token/refresh` 刷新token
- `GET /api/v1/profile` 获取用户信息
- `POST /api/v1/profile/update` 修改信息/密码
- `GET /api/v1/scripts` 脚本列表
- `POST /api/v1/scripts` 新建脚本
- `PUT /api/v1/scripts/:id` 编辑脚本
- `DELETE /api/v1/scripts/:id` 删除脚本
- `POST /api/v1/scripts/:id/config` 配置脚本参数
- `GET /api/v1/devices` 设备列表
- `GET /api/v1/devices/:id` 设备详情
- `POST /api/v1/devices/bind` 绑定设备
- `POST /api/v1/devices/unbind` 解绑设备
- `POST /api/v1/devices/group` 分组/批量操作
- `GET /api/v1/tasks` 任务列表
- `POST /api/v1/tasks` 新建任务（选择脚本/设备/参数）
- `GET /api/v1/tasks/:id` 任务详情/进度
- `GET /api/v1/tasks/:id/results` 任务结果
- `GET /api/v1/logs` 操作/设备/任务日志
- `GET /api/v1/monitor` 实时状态流（可WebSocket）

### 4. WebSocket 服务
- `/ws/user` 用户端WebSocket（推送设备/任务状态、告警、日志）
- `/ws/device` 设备端WebSocket（注册、心跳、接收命令、上报状态）

#### 消息类型举例
- 用户端：`device_status_update`, `task_progress`, `alert`, ...
- 设备端：`register`, `heartbeat`, `command`, `result`, ...

### 5. 设备注册与心跳
- 设备端启动后通过 `/ws/device` 连接，发送 `register` 消息（带token、设备ID等）。
- 服务端校验token，登记设备在线。
- 设备定期发送 `heartbeat`，服务端更新 last_active。
- 断线/超时自动标记离线。

### 6. 群控任务下发与回传
- 用户在前端新建任务，服务端生成任务记录，推送 `command` 消息到目标设备。
- 设备收到后执行，执行结果通过 `result` 消息回传服务端。
- 服务端更新任务进度，并通过WebSocket推送给前端。

### 7. 日志与审计
- 所有关键操作、任务、设备状态变更均记录日志，便于追溯和监控。

## 三、前端（uniapp）详细设计

### 1. 目录结构建议
```
src/
  pages/
    login/
    register/
    dashboard/
    scripts/
      list.vue
      edit.vue
      config.vue
    devices/
      list.vue
      detail.vue
      group.vue
    tasks/
      list.vue
      create.vue
      detail.vue
    monitor/
      index.vue
    profile/
      index.vue
  components/
    DeviceCard.vue
    TaskCard.vue
    ScriptCard.vue
    StatCard.vue
    ...
  api/
    index.ts
    user.ts
    device.ts
    script.ts
    task.ts
    ws.ts
```

### 2. 关键页面与功能
- 登录/注册：表单校验，调用 `/api/v1/login` `/api/v1/register`，保存token，跳转首页
- 仪表盘：统计卡片、快捷入口
- 脚本管理：列表、上传、编辑、删除、配置参数，关联任务下发
- 设备管理：列表、分组、批量操作，详情（状态、日志、操作按钮），绑定/解绑
- 群控任务：新建任务（选择脚本、设备、参数），进度、结果展示，失败重试
- 实时监控：WebSocket 订阅设备/任务状态流，日志、告警推送
- 个人中心：信息展示、密码修改、退出登录

### 3. 通信实现
- HTTP API 封装：axios/fetch，自动带token，处理401自动刷新
- WebSocket 封装：与 `/ws/user` 连接，消息分发，断线重连、token过期自动刷新
- 状态管理：推荐 pinia/vuex 管理全局状态

### 4. 典型交互流程
- 登录：输入账号密码，调用 `/api/v1/login`，保存token，建立WebSocket连接，进入仪表盘
- 设备管理：获取设备列表，订阅状态流，详情页远程操作
- 群控任务：新建任务，服务端推送进度/结果，前端实时展示
- 实时监控：订阅 `/ws/user`，接收状态、日志、告警，页面实时刷新

## 四、开发步骤建议

### 1. 后端
1. 设计/建表（用户、设备、脚本、任务、日志、token等）
2. 实现认证与token机制（JWT+refreshToken）
3. 实现RESTful API（用户、设备、脚本、任务、日志）
4. 实现WebSocket服务（用户端、设备端）
5. 实现设备注册、心跳、状态管理
6. 实现群控任务调度与消息路由
7. 日志与操作审计
8. 单元测试与接口文档

### 2. 前端
1. uniapp项目初始化，配置多端打包
2. 封装API与WebSocket通信模块
3. 实现登录/注册/个人中心页面
4. 实现仪表盘、脚本管理、设备管理、任务管理、实时监控页面
5. 实现全局状态管理
6. 适配Web/iOS/Android多端
7. UI美化与交互优化
8. 测试与bug修复

## 五、关键注意事项
- token/refreshToken机制需前后端一致，注意安全性
- WebSocket需支持断线重连、token过期自动刷新
- 设备与用户权限严格隔离
- 日志与操作审计全覆盖
- 前端多端适配需充分测试
- 所有敏感通信均加密（HTTPS/WSS）

---

> 本文档为多端群控系统详细设计说明，后续如有变更请及时归档更新。 