package main

import (
	"context"
	"log"
	"os"
	"path/filepath"

	"goadmin/config"
	"goadmin/internal/app"
	"goadmin/pkg/database"
	"goadmin/pkg/logger"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logPath := filepath.Join("logs", "app.log")
	logger.InitLogger(logPath, 100, 10, 7) // 100MB, 10 backups, 7 days

	// 初始化数据库连接
	db, err := database.NewDB(cfg)
	if err != nil {
		logger.Error(context.TODO(), "Failed to connect to database: %v", err)
		os.Exit(1)
	}

	// 创建应用实例
	app := app.NewApp(cfg, db)
	port := os.Getenv("PORT")
	if port == "" {
		port = cfg.Server.Port
	}
	// 启动服务器
	if err := app.Run(":" + port); err != nil {
		logger.Error(context.TODO(), "Failed to start server: %v", err)
		os.Exit(1)
	}
}
