package main

import (
	"context"
	"log"
	"os"
	"path/filepath"

	"goadmin/config"
	"goadmin/internal/app"
	"goadmin/pkg/database"
	"goadmin/pkg/logger"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logPath := filepath.Join("logs", "app.log")
	logger.InitLogger(logPath, 100, 10, 7) // 100MB, 10 backups, 7 days

	// 初始化数据库连接
	db, err := database.NewDB(cfg)
	if err != nil {
		logger.Error(context.TODO(), "Failed to connect to database: %v", err)
		os.Exit(1)
	}

	// 创建应用实例
	app := app.NewApp(cfg, db)
	port := os.Getenv("PORT")
	if port == "" {
		port = cfg.Server.Port
	}
	// 根据环境决定监听地址
	var addr string
	if cfg.Server.Env == "dev" {
		// 开发环境：监听所有网络接口以支持局域网访问
		addr = "0.0.0.0:" + port
		logger.Info(context.TODO(), "Development mode: Starting server on %s (LAN accessible)", addr)
	} else {
		// 生产环境：只监听本地接口，确保安全
		addr = "127.0.0.1:" + port
		logger.Info(context.TODO(), "Production mode: Starting server on %s (localhost only)", addr)
	}

	if err := app.Run(addr); err != nil {
		logger.Error(context.TODO(), "Failed to start server: %v", err)
		os.Exit(1)
	}
}
