.PHONY: all build clean run build-x86 build-arm64

# Variables
APP_NAME=goadmin
VERSION=1.0.0

# Default target
all: build-x86 build-arm64

# Build for x86_64
build-x86:
	@echo "Building $(APP_NAME) $(VERSION) for x86_64..."
	@mkdir -p deploy/dist/x86_64
	@GOOS=linux GOARCH=amd64 go build -o deploy/dist/x86_64/$(APP_NAME) main.go
	@cp config.yaml deploy/dist/x86_64/
	@cp deploy/control.sh deploy/dist/x86_64/
	@cd deploy/dist/x86_64 && tar -czvf $(APP_NAME)-$(VERSION)-x86_64.tar.gz *
	@echo "Build complete: deploy/dist/x86_64/$(APP_NAME)-$(VERSION)-x86_64.tar.gz"

# Build for ARM64
build-arm64:
	@echo "Building $(APP_NAME) $(VERSION) for ARM64..."
	@mkdir -p deploy/dist/arm64
	@GOOS=linux GOARCH=arm64 go build -o deploy/dist/arm64/$(APP_NAME) main.go
	@cp config.yaml deploy/dist/arm64/
	@cp deploy/control.sh deploy/dist/arm64/
	@cd deploy/dist/arm64 && tar -czvf $(APP_NAME)-$(VERSION)-arm64.tar.gz *
	@echo "Build complete: deploy/dist/arm64/$(APP_NAME)-$(VERSION)-arm64.tar.gz"

# Clean
clean:
	@echo "Cleaning..."
	@rm -rf deploy/dist
	@echo "Clean complete"

# Run
run:
	@go run main.go