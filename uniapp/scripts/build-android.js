#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 开始构建Android应用...')

// 检查环境
function checkEnvironment() {
  console.log('📋 检查构建环境...')
  
  try {
    // 检查Node.js版本
    const nodeVersion = process.version
    console.log(`✅ Node.js版本: ${nodeVersion}`)
    
    // 检查package.json
    const packagePath = path.join(__dirname, '..', 'package.json')
    if (!fs.existsSync(packagePath)) {
      throw new Error('package.json不存在')
    }
    console.log('✅ package.json存在')
    
    // 检查manifest.json
    const manifestPath = path.join(__dirname, '..', 'src', 'manifest.json')
    if (!fs.existsSync(manifestPath)) {
      throw new Error('manifest.json不存在')
    }
    console.log('✅ manifest.json存在')
    
  } catch (error) {
    console.error('❌ 环境检查失败:', error.message)
    process.exit(1)
  }
}

// 安装依赖
function installDependencies() {
  console.log('📦 安装依赖...')
  
  try {
    execSync('npm install', { 
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit'
    })
    console.log('✅ 依赖安装完成')
  } catch (error) {
    console.error('❌ 依赖安装失败:', error.message)
    process.exit(1)
  }
}

// 切换到生产环境
function switchToProduction() {
  console.log('🌍 切换到生产环境...')
  
  try {
    execSync('npm run env:prod', { 
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit'
    })
    console.log('✅ 已切换到生产环境')
  } catch (error) {
    console.error('❌ 环境切换失败:', error.message)
    process.exit(1)
  }
}

// 构建Android应用
function buildAndroid() {
  console.log('🔨 构建Android应用...')
  
  try {
    execSync('npm run build:app-android', { 
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit'
    })
    console.log('✅ Android应用构建完成')
  } catch (error) {
    console.error('❌ Android构建失败:', error.message)
    process.exit(1)
  }
}

// 显示构建结果
function showBuildResult() {
  console.log('\n🎉 构建完成!')
  console.log('\n📱 构建结果:')
  console.log('   - 平台: Android')
  console.log('   - 环境: 生产环境')
  console.log('   - 输出目录: dist/build/app-android/')
  
  console.log('\n💡 下一步操作:')
  console.log('   1. 使用HBuilderX打开项目')
  console.log('   2. 点击"发行" -> "原生APP-云打包"')
  console.log('   3. 选择Android平台')
  console.log('   4. 配置证书信息')
  console.log('   5. 点击"打包"生成APK')
  
  console.log('\n🔗 相关文档:')
  console.log('   - 打包指南: project_document/uniapp_android_build_guide.md')
  console.log('   - 官方文档: https://uniapp.dcloud.io/quickstart-cli.html')
}

// 主函数
function main() {
  try {
    checkEnvironment()
    installDependencies()
    switchToProduction()
    buildAndroid()
    showBuildResult()
  } catch (error) {
    console.error('❌ 构建过程中发生错误:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main() 