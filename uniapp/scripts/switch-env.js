#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// 支持的环境
const ENVIRONMENTS = ['development', 'test', 'production']

// 获取命令行参数
const targetEnv = process.argv[2]

if (!targetEnv || !ENVIRONMENTS.includes(targetEnv)) {
  console.log('用法: node scripts/switch-env.js <environment>')
  console.log('支持的环境:', ENVIRONMENTS.join(', '))
  process.exit(1)
}

// 环境配置模板
const envTemplates = {
  development: {
    VITE_ENV: 'development',
    VITE_DEBUG: 'true',
    VITE_ENABLE_LOGS: 'true'
  },
  test: {
    VITE_ENV: 'test',
    VITE_DEBUG: 'true',
    VITE_ENABLE_LOGS: 'true'
  },
  production: {
    VITE_ENV: 'production',
    VITE_DEBUG: 'false',
    VITE_ENABLE_LOGS: 'false'
  }
}

// 生成环境变量文件内容
function generateEnvFile(env) {
  const config = envTemplates[env]
  let content = `# ${env} 环境配置\n`
  content += `# 生成时间: ${new Date().toISOString()}\n\n`
  
  Object.entries(config).forEach(([key, value]) => {
    content += `${key}=${value}\n`
  })
  
  return content
}

// 写入环境变量文件
function writeEnvFile(env) {
  const envFile = path.join(__dirname, '..', '.env')
  const content = generateEnvFile(env)
  
  try {
    fs.writeFileSync(envFile, content, 'utf8')
    console.log(`✅ 已切换到 ${env} 环境`)
    console.log(`📁 配置文件: ${envFile}`)
    console.log('\n📋 当前配置:')
    console.log(content)
  } catch (error) {
    console.error('❌ 写入环境配置文件失败:', error.message)
    process.exit(1)
  }
}

// 显示当前环境信息
function showEnvironmentInfo(env) {
  const config = envTemplates[env]
  console.log(`\n🌍 环境信息:`)
  console.log(`   环境名称: ${env}`)
  console.log(`   调试模式: ${config.VITE_DEBUG}`)
  console.log(`   日志输出: ${config.VITE_ENABLE_LOGS}`)
}

// 执行环境切换
console.log(`🔄 正在切换到 ${targetEnv} 环境...`)
writeEnvFile(targetEnv)
showEnvironmentInfo(targetEnv)

console.log('\n💡 提示:')
console.log('   - 重新启动开发服务器以应用新配置')
console.log('   - 运行 npm run dev:h5 启动开发服务器')
console.log('   - 运行 npm run build:h5 构建生产版本') 