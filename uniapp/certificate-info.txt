# nbibot 应用签名证书信息

## 证书文件
- 文件名: nbibot-release-key.keystore
- 位置: /Users/<USER>/github/goadmin/uniapp/nbibot-release-key.keystore

## 证书详情
- 别名: nbibot-key-alias
- 算法: RSA 2048位
- 有效期: 2025-08-03 至 2052-12-19 (约27年)
- 类型: 自签名证书

## 证书信息
- 所有者: CN=XTM, OU=XTM, O=XTM, L=HB, ST=WH, C=CN
- 发布者: CN=XTM, OU=XTM, O=XTM, L=HB, ST=WH, C=CN
- SHA1指纹: C1:B7:40:81:BE:96:91:B3:4A:84:07:3F:74:26:21:63:76:35:27:8F
- SHA256指纹: F4:09:64:F2:7E:3C:51:7B:92:68:EC:3C:5B:C6:76:4A:11:EF:98:0A:93:BA:98:4D:1E:63:C0:45:CC:C2:B3:8F

## 在HBuilderX中使用
1. 打开HBuilderX
2. 发行 -> 原生APP-云打包 -> Android
3. 点击"配置证书"
4. 选择"已有证书"
5. 选择证书文件: nbibot-release-key.keystore
6. 输入证书密码: [您设置的密码]
7. 输入别名: nbibot-key-alias
8. 输入别名密码: [您设置的密码]

## 注意事项
- 请妥善保管证书文件和密码
- 此证书用于应用签名，丢失将无法更新应用
- 证书有效期为27年，足够长期使用 