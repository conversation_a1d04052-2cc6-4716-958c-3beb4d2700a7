# GoAdmin 移动端应用

基于 UniApp + Vue 3 + TypeScript 开发的群控系统移动端应用，支持多平台部署。

## 🚀 支持平台

- **H5** - 移动端网页应用
- **微信小程序** - 微信生态内应用
- **App** - iOS/Android 原生应用 (需使用 HBuilderX 打包)
- **其他小程序** - 支付宝、百度、QQ等

## 📱 功能特性

### 🔐 用户认证
- 用户登录/注册
- JWT Token 管理
- 自动登录状态保持
- 安全退出

### 📊 数据监控
- 设备状态实时监控
- 脚本执行状态
- 任务执行进度
- 系统统计数据

### 🎛️ 设备管理
- 设备列表查看
- 设备状态切换
- 设备分组管理
- 批量操作

### 📝 脚本管理
- 脚本列表浏览
- 脚本详情查看
- 脚本执行控制
- 执行结果查看

### ⚡ 实时通信
- WebSocket 连接
- 实时消息推送
- 自动重连机制
- 心跳保活

## 🛠️ 技术栈

- **框架**: UniApp 3.0
- **语言**: Vue 3 + TypeScript
- **状态管理**: Pinia
- **UI组件**: UniApp 原生组件
- **构建工具**: Vite
- **样式**: SCSS

## 📁 项目结构

```
uniapp/
├── src/
│   ├── pages/              # 页面文件
│   │   ├── login/          # 登录页面
│   │   ├── dashboard/      # 仪表盘
│   │   ├── devices/        # 设备管理
│   │   ├── scripts/        # 脚本管理
│   │   └── profile/        # 个人中心
│   ├── stores/             # 状态管理
│   │   ├── user.ts         # 用户状态
│   │   ├── device.ts       # 设备状态
│   │   └── script.ts       # 脚本状态
│   ├── utils/              # 工具函数
│   │   ├── api.ts          # API 接口
│   │   ├── websocket.ts    # WebSocket 管理
│   │   └── index.ts        # 通用工具
│   ├── static/             # 静态资源
│   ├── styles/             # 样式文件
│   ├── App.vue             # 应用入口
│   ├── main.ts             # 主入口
│   ├── pages.json          # 页面配置
│   └── manifest.json       # 应用配置
├── package.json
└── README.md
```

## 🚀 快速开始

### 环境要求

- Node.js 18+
- HBuilderX (推荐) 或 VS Code
- 微信开发者工具 (微信小程序)

### 安装依赖

```bash
cd uniapp
npm install
```

### 开发调试

#### H5 开发 (推荐用于开发调试)
```bash
# 启动 H5 开发服务器
npm run dev:h5

# 构建 H5 版本
npm run build:h5
```

#### 微信小程序开发
```bash
# 启动微信小程序开发
npm run dev:mp-weixin

# 构建微信小程序版本
npm run build:mp-weixin
```

#### App 开发 (使用 HBuilderX)
```bash
# 1. 使用 HBuilderX 打开 uniapp 目录
# 2. 在 HBuilderX 中运行到手机或模拟器
# 3. 或使用 HBuilderX 云打包服务
```

### 其他平台

```bash
# 支付宝小程序
npm run dev:mp-alipay

# 百度小程序
npm run dev:mp-baidu

# QQ小程序
npm run dev:mp-qq

# 字节跳动小程序
npm run dev:mp-toutiao

# 快手小程序
npm run dev:mp-kuaishou

# 小红书小程序
npm run dev:mp-xhs
```

## 🔧 配置说明

### 页面配置 (pages.json)

```json
{
  "pages": [
    {
      "path": "pages/login/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "登录"
      }
    },
    {
      "path": "pages/dashboard/index",
      "style": {
        "navigationBarTitleText": "控制台"
      }
    }
  ],
  "tabBar": {
    "color": "#8f8f94",
    "selectedColor": "#007aff",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/dashboard/index",
        "text": "首页"
      }
    ]
  }
}
```

### 应用配置 (manifest.json)

```json
{
  "name": "GoAdmin群控系统",
  "appid": "__UNI__XXXXXXX",
  "description": "群控系统移动端应用",
  "versionName": "1.0.0",
  "versionCode": "100",
  "transformPx": false,
  "h5": {
    "devServer": {
      "port": 3000,
      "proxy": {
        "/api": {
          "target": "http://localhost:8080",
          "changeOrigin": true
        }
      }
    }
  }
}
```

## 🔐 用户认证

### 状态管理 (stores/user.ts)

```typescript
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const isLoggedIn = ref<boolean>(false)

  // 登录
  const login = async (username: string, password: string) => {
    // 实现登录逻辑
  }

  // 登出
  const logout = async () => {
    // 实现登出逻辑
  }

  return {
    user,
    token,
    isLoggedIn,
    login,
    logout
  }
})
```

### API 接口 (utils/api.ts)

```typescript
// 基础配置
const baseURL = 'https://your-api-domain.com/api/v1'

// 请求拦截器
const request = (options: any) => {
  const token = uni.getStorageSync('token')
  
  return new Promise((resolve, reject) => {
    uni.request({
      ...options,
      url: baseURL + options.url,
      header: {
        'Authorization': `Bearer ${token}`,
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(res)
        }
      },
      fail: reject
    })
  })
}

// API 方法
export const api = {
  // 用户认证
  login: (data: any) => request({
    url: '/auth/login',
    method: 'POST',
    data
  }),
  
  // 获取用户资料
  getProfile: () => request({
    url: '/user/profile',
    method: 'GET'
  })
}
```

## 📱 页面开发

### 登录页面示例

```vue
<template>
  <view class="login-container">
    <view class="login-form">
      <input 
        v-model="formData.username"
        placeholder="请输入用户名"
        class="form-input"
      />
      <input 
        v-model="formData.password"
        type="password"
        placeholder="请输入密码"
        class="form-input"
      />
      <button @click="handleLogin" class="login-btn">
        登录
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const formData = ref({
  username: '',
  password: ''
})

const handleLogin = async () => {
  const success = await userStore.login(
    formData.value.username,
    formData.value.password
  )
  
  if (success) {
    uni.reLaunch({
      url: '/pages/dashboard/index'
    })
  }
}
</script>
```

## 🔌 WebSocket 集成

### WebSocket 管理 (utils/websocket.ts)

```typescript
class WebSocketManager {
  private ws: any = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5

  connect(token: string) {
    const wsUrl = `wss://your-domain.com/api/v1/ws?token=${token}`
    
    this.ws = uni.connectSocket({
      url: wsUrl,
      success: () => {
        console.log('WebSocket 连接成功')
      }
    })

    this.ws.onOpen(() => {
      console.log('WebSocket 已连接')
      this.reconnectAttempts = 0
    })

    this.ws.onMessage((res: any) => {
      const message = JSON.parse(res.data)
      this.handleMessage(message)
    })

    this.ws.onClose(() => {
      console.log('WebSocket 连接关闭')
      this.reconnect()
    })
  }

  private reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++
        this.connect(uni.getStorageSync('token'))
      }, 3000)
    }
  }

  private handleMessage(message: any) {
    switch (message.type) {
      case 'heartbeat':
        this.sendHeartbeat()
        break
      case 'script_result':
        this.handleScriptResult(message.data)
        break
      default:
        console.log('收到消息:', message)
    }
  }

  sendHeartbeat() {
    this.send({
      type: 'heartbeat',
      data: { timestamp: Date.now() }
    })
  }

  send(message: any) {
    if (this.ws && this.ws.readyState === 1) {
      this.ws.send({
        data: JSON.stringify(message)
      })
    }
  }
}

export const wsManager = new WebSocketManager()
```

## 🎨 样式开发

### 全局样式 (styles/global.scss)

```scss
// 主题色彩
$primary-color: #007aff;
$success-color: #34c759;
$warning-color: #ff9500;
$error-color: #ff3b30;

// 布局
.container {
  padding: 20rpx;
}

// 表单样式
.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  
  &:focus {
    border-color: $primary-color;
  }
}

// 按钮样式
.btn-primary {
  background-color: $primary-color;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  
  &:disabled {
    background-color: #ccc;
  }
}
```

## 📦 构建部署

### H5 部署

```bash
# 构建 H5 版本
npm run build:h5

# 构建产物在 dist/build/h5 目录
# 将整个目录部署到 Web 服务器
```

### 微信小程序部署

```bash
# 构建微信小程序版本
npm run build:mp-weixin

# 构建产物在 dist/build/mp-weixin 目录
# 使用微信开发者工具上传代码
```

### App 打包 (使用 HBuilderX)

```bash
# 1. 使用 HBuilderX 打开 uniapp 目录
# 2. 点击 "发行" -> "原生App-云打包"
# 3. 选择平台 (iOS/Android)
# 4. 配置证书和签名
# 5. 开始打包
```

## 🔧 开发调试

### 调试工具

1. **H5 调试**: 浏览器开发者工具
2. **小程序调试**: 对应平台开发者工具
3. **App 调试**: HBuilderX 真机调试

### 常见问题

#### 1. 跨域问题
在 `manifest.json` 中配置代理：

```json
{
  "h5": {
    "devServer": {
      "proxy": {
        "/api": {
          "target": "http://localhost:8080",
          "changeOrigin": true
        }
      }
    }
  }
}
```

#### 2. 网络请求失败
检查网络权限配置：

```json
{
  "networkTimeout": {
    "request": 60000,
    "connectSocket": 60000,
    "uploadFile": 60000,
    "downloadFile": 60000
  }
}
```

#### 3. 样式不生效
确保在 `App.vue` 中引入全局样式：

```vue
<style lang="scss">
@import './styles/global.scss';
</style>
```

#### 4. 启动命令错误
确保使用正确的启动命令：

```bash
# ✅ 正确的 H5 启动命令
npm run dev:h5

# ❌ 错误的命令 (不存在)
npm run dev:app
```

## 📚 相关文档

- [UniApp 官方文档](https://uniapp.dcloud.net.cn/)
- [Vue 3 官方文档](https://vuejs.org/)
- [Pinia 状态管理](https://pinia.vuejs.org/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)
- [HBuilderX 官方文档](https://www.dcloud.io/hbuilderx.html)

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

---

**GoAdmin 移动端** - 随时随地管理您的设备！ 📱 