import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import { resolve } from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production'
  
  return {
  plugins: [uni()],
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
    // 开发服务器配置
    server: {
      port: 5173,
      proxy: {
        '/api/v1': {
          target: 'http://localhost:9000',
          changeOrigin: true,
          secure: false,
        },
        '/api/v1/ws': {
          target: 'ws://localhost:9000',
          ws: true,
          changeOrigin: true,
        }
      }
    },
  css: {
    preprocessorOptions: {
      scss: {
        // 抑制Sass弃用警告（来自UniApp框架内部依赖）
        silenceDeprecations: ["legacy-js-api"],
      },
    },
  },
    // 生产环境配置
    build: {
      // 生产环境移除console.log
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: isProduction, // 生产环境移除console.log
          drop_debugger: isProduction, // 生产环境移除debugger
          pure_funcs: isProduction ? ['console.log', 'console.info', 'console.debug'] : [], // 移除指定的函数调用
        },
      },
    },
    // 定义环境变量
    define: {
      __DEV__: !isProduction,
      __PROD__: isProduction,
    },
  }
});
