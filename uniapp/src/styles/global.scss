/* 支付宝风格全局样式 */

:root {
  /* 支付宝品牌色 */
  --primary-color: #1677ff;
  --primary-light: #4096ff;
  --primary-dark: #0958d9;
  
  /* 功能色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #1677ff;
  
  /* 中性色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-disabled: #cccccc;
  
  /* 背景色 */
  --background-color: #f5f5f5;
  --card-background: #ffffff;
  --mask-background: rgba(0, 0, 0, 0.5);
  
  /* 边框色 */
  --border-color: #f0f0f0;
  --border-light: #e8e8e8;
  
  /* 阴影 */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.15);
  
  /* 圆角 */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;
  --radius-xl: 16px;
  --radius-round: 50%;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--background-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--radius-medium);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  
  &:active {
    transform: scale(0.98);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
  
  /* 尺寸变体 */
  &.btn-small {
    height: 32px;
    padding: 0 12px;
    font-size: 12px;
  }
  
  &.btn-medium {
    height: 40px;
    padding: 0 16px;
    font-size: 14px;
  }
  
  &.btn-large {
    height: 48px;
    padding: 0 20px;
    font-size: 16px;
  }
  
  /* 颜色变体 */
  &.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
    
    &:hover {
      background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    }
  }
  
  &.btn-secondary {
    background: rgba(22, 119, 255, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(22, 119, 255, 0.2);
    
    &:hover {
      background: rgba(22, 119, 255, 0.15);
    }
  }
  
  &.btn-success {
    background: linear-gradient(135deg, var(--success-color), #73d13d);
    color: white;
    
    &:hover {
      background: linear-gradient(135deg, #389e0d, var(--success-color));
    }
  }
  
  &.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #ffc53d);
    color: white;
    
    &:hover {
      background: linear-gradient(135deg, #d48806, var(--warning-color));
    }
  }
  
  &.btn-danger {
    background: linear-gradient(135deg, var(--error-color), #ff4d4f);
    color: white;
    
    &:hover {
      background: linear-gradient(135deg, #cf1322, var(--error-color));
    }
  }
  
  &.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    
    &:hover {
      background: rgba(0, 0, 0, 0.02);
    }
  }
}

/* 卡片样式 */
.card {
  background: var(--card-background);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

/* 输入框样式 */
.input {
  width: 100%;
  height: 40px;
  padding: 0 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  font-size: 14px;
  color: var(--text-primary);
  background: var(--card-background);
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2);
  }
  
  &::placeholder {
    color: var(--text-tertiary);
  }
  
  &:disabled {
    background: var(--background-color);
    color: var(--text-disabled);
    cursor: not-allowed;
  }
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
  
  &.tag-primary {
    background: rgba(22, 119, 255, 0.1);
    color: var(--primary-color);
  }
  
  &.tag-success {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
  }
  
  &.tag-warning {
    background: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
  }
  
  &.tag-error {
    background: rgba(245, 34, 45, 0.1);
    color: var(--error-color);
  }
}

/* 状态指示器 */
.status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: var(--radius-round);
  margin-right: 4px;
  
  &.status-online {
    background: var(--success-color);
  }
  
  &.status-offline {
    background: var(--text-tertiary);
  }
  
  &.status-busy {
    background: var(--warning-color);
  }
  
  &.status-error {
    background: var(--error-color);
  }
}

/* 加载动画 */
.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 渐变背景 */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.gradient-success {
  background: linear-gradient(135deg, var(--success-color), #73d13d);
}

.gradient-warning {
  background: linear-gradient(135deg, var(--warning-color), #ffc53d);
}

.gradient-error {
  background: linear-gradient(135deg, var(--error-color), #ff4d4f);
}

/* 响应式网格 */
.grid {
  display: grid;
  gap: 16px;
}

.grid-1 { grid-template-columns: repeat(1, 1fr); }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
  .grid-2, .grid-3, .grid-4 {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-3, .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-card { background-color: var(--card-background); }

.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }

.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.flex-1 { flex: 1; }

.rounded { border-radius: var(--radius-medium); }
.rounded-lg { border-radius: var(--radius-large); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-round); }

.shadow { box-shadow: var(--shadow-light); }
.shadow-md { box-shadow: var(--shadow-medium); }
.shadow-lg { box-shadow: var(--shadow-heavy); }

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-color);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}
