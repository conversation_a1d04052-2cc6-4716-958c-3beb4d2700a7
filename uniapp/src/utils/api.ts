import config from '@/config/index'
import { log, error } from './logger'

// API 响应类型定义
interface ApiResponse<T = any> {
  code: number
  message?: string
  data: T
}

// 用户信息类型
interface UserInfo {
  id: number
  username: string
  email?: string
  nickname: string
  avatar?: string
  status: number
  created_at: string
  updated_at: string
}

// 登录响应类型
interface LoginResponse {
  user: UserInfo
  token: string
  refresh_token: string
}

// 处理token失效的通用函数
const handleTokenExpired = (reason?: string) => {
  console.log('Token失效，清除本地存储并跳转到登录页', reason ? `原因: ${reason}` : '')
  uni.removeStorageSync('token')
  uni.removeStorageSync('refresh_token')
  uni.removeStorageSync('user_info')
  
  uni.showToast({
    title: '登录已过期，请重新登录',
    icon: 'none',
    duration: 2000
  })
  
  // 延迟跳转，让用户看到提示
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/login/index'
    })
  }, 1000)
}

// 刷新token的函数
const refreshToken = async (): Promise<boolean> => {
  try {
    const refreshTokenValue = uni.getStorageSync('refresh_token')
    if (!refreshTokenValue) {
      return false
    }
    
    const response = await new Promise((resolve, reject) => {
      uni.request({
        url: config.API_URL + '/auth/refresh',
        method: 'POST',
        data: { refresh_token: refreshTokenValue },
        header: { 'Content-Type': 'application/json' },
        timeout: config.TIMEOUT,
        success: resolve,
        fail: reject
      })
    }) as any
    
    if (response.statusCode === 200 && response.data.code === 0) {
      // 更新token和refresh_token
      uni.setStorageSync('token', response.data.data.token)
      if (response.data.data.refresh_token) {
        uni.setStorageSync('refresh_token', response.data.data.refresh_token)
      }
      return true
    }
    
    return false
  } catch (error) {
    console.error('刷新token失败:', error)
    return false
  }
}

// 请求拦截器
const request = <T>(options: any): Promise<T> => {
  return new Promise(async (resolve, reject) => {
    // 获取存储的 token
    let token = uni.getStorageSync('token')
    
    // 设置请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    }
    
    // 如果有 token，添加到请求头
    if (token) {
      header.Authorization = `Bearer ${token}`
    }
    
    const makeRequest = async (isRetry = false) => {
      uni.request({
        url: options.url,
        method: options.method || 'GET',
        data: options.data,
        header,
        timeout: config.TIMEOUT,
        success: async (res) => {
          // 调试模式打印请求信息
          if (config.DEBUG) {
            log('API Request:', {
              url: options.url,
              method: options.method,
              data: options.data,
              response: res.data
            })
          }
          
          // 处理响应
          if (res.statusCode === 200) {
            const responseData = res.data as any
            
            // 检查业务逻辑错误码
            if (responseData.code === 1002) {
              // Token失效
              if (!isRetry) {
                // 尝试刷新token
                const refreshSuccess = await refreshToken()
                if (refreshSuccess) {
                  // 刷新成功，重试请求
                  const newToken = uni.getStorageSync('token')
                  header.Authorization = `Bearer ${newToken}`
                  makeRequest(true)
                  return
                }
              }
              // 刷新失败或已经是重试，跳转登录页
              handleTokenExpired(`业务错误码1002 - URL: ${options.url}`)
              reject(new Error('Token失效'))
              return
            }

            // 新增：非0错误码直接reject，便于前端catch到{code, message}
            if (responseData.code !== 0) {
              reject(responseData)
              return
            }

            resolve(responseData as T)
          } else if (res.statusCode === 401) {
            // HTTP 401 未授权
            if (!isRetry) {
              // 尝试刷新token
              const refreshSuccess = await refreshToken()
              if (refreshSuccess) {
                // 刷新成功，重试请求
                const newToken = uni.getStorageSync('token')
                header.Authorization = `Bearer ${newToken}`
                makeRequest(true)
                return
              }
            }
            
            // 刷新失败或已经是重试，跳转登录页
            handleTokenExpired(`HTTP 401 - URL: ${options.url}`)
            reject(new Error('未授权访问'))
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.errMsg}`))
          }
        },
        fail: (err) => {
          error('请求失败:', err)
          // 调试模式打印错误信息
          if (config.DEBUG) {
            error('API Error:', {
              url: options.url,
              method: options.method,
              error: err
            })
          }
          reject(err)
        }
      })
    }
    
    // 发起请求
    await makeRequest()
  })
}

// 设备配对相关 API
export const pairingAPI = {
  // 生成配对码
  generatePairingCode: () => request({
    url: config.API_URL + '/user/devices/pairings/generate',
    method: 'POST'
  }),
  
  // 获取配对状态
  getPairingStatus: (data: { pairing_code: string }) => 
    request({
      url: config.API_URL + '/user/devices/pairings/status',
      method: 'POST',
      data
    })
}

// 设备管理相关 API
export const deviceAPI = {
  // 获取用户设备列表（支持状态筛选）
  getUserDevices: (status?: number[]) => request({
    url: config.API_URL + '/user/devices/list',
    method: 'POST',
    data: { status: status || [] }
  }),
  
  // 注册设备
  registerDevice: (data: any) => request({
    url: config.API_URL + '/user/devices/register',
    method: 'POST',
    data
  }),
  
  // 更新设备状态
  updateDeviceStatus: (data: { device_id: string; status: number }) => 
    request({
      url: config.API_URL + '/user/devices/status',
      method: 'POST',
      data
    }),
  
  // 删除设备
  deleteDevice: (data: { id: number }) => 
    request({
      url: config.API_URL + '/user/devices/delete',
      method: 'POST',
      data
    }),

  // 设备配额相关API
  // 获取配额信息
  getQuotaInfo: () => 
    request({
      url: config.API_URL + '/user/quotas/info',
      method: 'GET'
    }),

  // 获取配额列表
  getQuotaList: () => 
    request({
      url: config.API_URL + '/user/quotas/list',
      method: 'GET'
    }),

  // 创建配额购买订单
  createQuotaOrder: (data: { quota_count: number; pay_type: string }) => 
    request({
      url: config.API_URL + '/payment/quota/create-order',
      method: 'POST',
      data
    }),



  // 获取可续费的配额列表
  getRenewableQuotas: () => 
    request({
      url: config.API_URL + '/payment/quota/renewal/list',
      method: 'POST'
    }),

  // 创建配额续费订单
  createQuotaRenewalOrder: (data: { quota_id: number; pay_type: string }) => 
    request({
      url: config.API_URL + '/payment/quota/renewal/create-order',
      method: 'POST',
      data
    }),



  // 创建脚本续费订单
  createScriptRenewalOrder: (data: { script_id: number; pay_type: string }) => 
    request({
      url: config.API_URL + '/payment/script/renewal/create-order',
      method: 'POST',
      data
    }),
  
  // 更新设备信息
  updateDeviceInfo: (data: { device_id: string; name: string }) => 
    request({
      url: config.API_URL + '/user/devices/update',
      method: 'POST',
      data
    }),
  

}

// 用户认证相关 API
export const authAPI = {
  // 用户注册
  userRegister: (data: { username: string; password: string; email: string }) => 
    request({
      url: config.API_URL + '/auth/register',
      method: 'POST',
      data
    }),
  
  // 用户登录
  userLogin: (data: { username: string; password: string }) => 
    request({
      url: config.API_URL + '/auth/login',
      method: 'POST',
      data
    }),
  
  // 用户登出
  userLogout: () => request({
    url: config.API_URL + '/auth/logout',
    method: 'POST'
  }),
  
  // 刷新 Token
  refreshToken: (data: { refresh_token: string }) => 
    request({
      url: config.API_URL + '/auth/refresh',
      method: 'POST',
      data
    })
}

// 用户相关 API
export const userAPI = {
  // 获取用户资料
  getUserProfile: () => request({
    url: config.API_URL + '/user/profile',
    method: 'GET'
  }),
  
  // 更新用户资料
  updateUserProfile: (data: { nickname?: string; email?: string }) => 
    request({
      url: config.API_URL + '/user/profile',
      method: 'POST',
      data
    }),
  
  // 修改密码
  changePassword: (data: { old_password: string; new_password: string }) => 
    request({
      url: config.API_URL + '/user/password',
      method: 'POST',
      data
    })
}

// 脚本相关 API
export const scriptAPI = {
  // 获取用户脚本列表
  getUserScripts: () => request({
    url: config.API_URL + '/user/scripts/list',
    method: 'POST'
  }),

  // 根据套餐ID获取套餐信息
  getScriptPackage: (packageId: number) => request({
    url: config.API_URL + '/user/scripts/package',
    method: 'POST',
    data: { package_id: packageId }
  }),
  
  // 更新脚本状态
  updateScriptStatus: (data: { id: number; status: number }) => 
    request({
      url: config.API_URL + '/user/scripts/status',
      method: 'POST',
      data
    }),
  
  // 创建脚本配置
  createScriptConfig: (data: any) => request({
    url: config.API_URL + '/user/scripts/configs/create',
    method: 'POST',
    data
  }),
  
  // 获取脚本配置结构
  getScriptConfigSchema: (scriptId: number) => request({
    url: config.API_URL + '/user/scripts/config-schema',
    method: 'POST',
    data: { script_id: scriptId }
  }),
  
  // 获取用户脚本配置列表
  getUserScriptConfigs: (scriptId: number) => request({
    url: config.API_URL + '/user/scripts/configs/list',
    method: 'POST',
    data: { script_id: scriptId }
  }),
  
  // 获取用户脚本配置详情
  getUserScriptConfig: (configId: number) => request({
    url: config.API_URL + '/user/scripts/configs/detail',
    method: 'POST',
    data: { config_id: configId }
  }),
  
  // 更新用户脚本配置
  updateUserScriptConfig: (configId: number, data: any) => request({
    url: config.API_URL + '/user/scripts/configs/update',
    method: 'POST',
    data: { ...data, config_id: configId }
  }),
  
  // 删除用户脚本配置
  deleteUserScriptConfig: (configId: number) => request({
    url: config.API_URL + '/user/scripts/configs/delete',
    method: 'POST',
    data: { config_id: configId }
  }),

  // 删除用户脚本
  deleteUserScript: (scriptId: number) => request({
    url: config.API_URL + '/user/scripts/delete',
    method: 'POST',
    data: { script_id: scriptId }
  }),

  // 检查脚本更新
  checkScriptUpdate: (scriptId: number) => request({
    url: config.API_URL + '/user/scripts/check-update',
    method: 'POST',
    data: { script_id: scriptId }
  }),

  // 更新脚本
  updateScript: (scriptId: number) => request({
    url: config.API_URL + '/user/scripts/update',
    method: 'POST',
    data: { script_id: scriptId }
  }),
  


  // 批量绑定设备到脚本
  bindDevice: (data: { user_script_id: number; device_ids: number[] }) => request({
    url: config.API_URL + '/user/scripts/bind-device',
    method: 'POST',
    data
  }),

  // 批量解绑设备
  unbindDevice: (data: { user_script_id: number; device_ids: number[] }) => request({
    url: config.API_URL + '/user/scripts/unbind-device',
    method: 'POST',
    data
  }),



  // 获取脚本绑定的设备列表
  getScriptBindings: (scriptId: number) => request({
    url: config.API_URL + '/user/scripts/bindings',
    method: 'POST',
    data: { user_script_id: scriptId }
  }),

  // 运行脚本（通过WebSocket发送给设备）
  runScript: (data: { script_id: number; device_ids: string[] }) => request({
    url: config.API_URL + '/user/scripts/run',
    method: 'POST',
    data
  }),

  // 停止脚本
  stopScript: (data: { script_id: number; device_ids: string[] }) => request({
    url: config.API_URL + '/user/scripts/stop',
    method: 'POST',
    data
  }),

  // 获取脚本订单列表
  getScriptOrders: () => request({
    url: config.API_URL + '/user/orders/scripts',
    method: 'GET'
  }),

  // 获取配额订单列表
  getQuotaOrders: () => request({
    url: config.API_URL + '/user/orders/quotas',
    method: 'GET'
  })
}

// 脚本市场相关 API（公开接口）
export const marketAPI = {
  // 获取脚本市场列表（公开）
  getMarketScripts: (params?: {
    page?: number
    page_size?: number
    keyword?: string
    is_featured?: boolean
    limit?: number
  }) => request<{
    list: any[]
    total: number
    page: number
    size: number
  }>({
    url: config.API_URL + '/market/scripts',
    method: 'POST',
    data: params
  }),
  
  // 获取脚本详情（公开）
  getMarketScriptDetail: (id: number) => request({
    url: config.API_URL + '/market/scripts/detail',
    method: 'POST',
    data: { id }
  })
}

// 脚本市场用户操作 API（需要登录）
// 注意：脚本安装功能已移除，统一通过支付流程完成
export const marketUserAPI = {}

// 支付相关 API
export const paymentAPI = {
  // 获取脚本套餐列表 - 根据市场脚本ID获取该脚本的所有可用套餐
  getScriptPackages: (marketScriptId: number) => request({
    url: config.API_URL + '/payment/script/packages',
    method: 'POST',
    data: { market_script_id: marketScriptId }
  }),
  
  // 创建脚本购买订单 - 用户选择套餐和支付方式后创建支付订单
  createPaymentOrder: (data: { package_id: number; pay_type: string }) => 
    request({
      url: config.API_URL + '/payment/script/create-order',
      method: 'POST',
      data
    }),
  
  // 获取订单详情 - 根据订单号获取订单的详细信息（支持所有类型订单）
  getOrderDetail: (orderNo: string) => request({
    url: config.API_URL + '/payment/order',
    method: 'POST',
    data: { order_no: orderNo }
  }),

  // 获取升级套餐列表 - 根据用户脚本ID获取可升级的套餐
  getUpgradePackages: (scriptId: number) => request({
    url: config.API_URL + '/payment/script/upgrade/packages',
    method: 'POST',
    data: { script_id: scriptId }
  }),

  // 创建升级订单 - 用户选择升级套餐和支付方式后创建升级订单
  createUpgradeOrder: (data: { script_id: number; package_id: number; pay_type: string }) => 
    request({
      url: config.API_URL + '/payment/script/upgrade/create-order',
      method: 'POST',
      data
    })
}


// 导出所有 API
export const api = {
  // 设备配对
  generatePairingCode: pairingAPI.generatePairingCode,
  getPairingStatus: pairingAPI.getPairingStatus,
  
  // 设备管理
  getUserDevices: deviceAPI.getUserDevices,
  registerDevice: deviceAPI.registerDevice,
  updateDeviceStatus: deviceAPI.updateDeviceStatus,
  updateDeviceInfo: deviceAPI.updateDeviceInfo,
  deleteDevice: deviceAPI.deleteDevice,

  
  // 用户认证
  userRegister: authAPI.userRegister,
  userLogin: authAPI.userLogin,
  userLogout: authAPI.userLogout,
  refreshToken: authAPI.refreshToken,
  
  // 用户资料
  getUserProfile: userAPI.getUserProfile,
  updateUserProfile: userAPI.updateUserProfile,
  changePassword: userAPI.changePassword,
  
  // 脚本管理
  getUserScripts: scriptAPI.getUserScripts,
  updateScriptStatus: scriptAPI.updateScriptStatus,
  getScriptConfigSchema: scriptAPI.getScriptConfigSchema,
  createScriptConfig: scriptAPI.createScriptConfig,
  getUserScriptConfigs: scriptAPI.getUserScriptConfigs,
  getUserScriptConfig: scriptAPI.getUserScriptConfig,
  updateUserScriptConfig: scriptAPI.updateUserScriptConfig,
  deleteUserScriptConfig: scriptAPI.deleteUserScriptConfig,
  
  // 脚本市场（公开接口）
  getMarketScripts: marketAPI.getMarketScripts,
  getMarketScriptDetail: marketAPI.getMarketScriptDetail,
  
  // 支付相关
  getScriptPackages: paymentAPI.getScriptPackages,
  createPaymentOrder: paymentAPI.createPaymentOrder,
  getOrderDetail: paymentAPI.getOrderDetail,
  getUpgradePackages: paymentAPI.getUpgradePackages,
  createUpgradeOrder: paymentAPI.createUpgradeOrder,
  
  // 订单相关
  getScriptOrders: scriptAPI.getScriptOrders,
  getQuotaOrders: scriptAPI.getQuotaOrders
} 