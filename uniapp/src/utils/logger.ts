import config from '@/config/index'

// 日志工具类
class Logger {
  private isDebug: boolean = false
  private isProduction: boolean = false
  private enableLogs: boolean = true

  constructor() {
    // 根据环境设置日志级别
    // #ifdef H5
    this.isDebug = import.meta.env.DEV || import.meta.env.VITE_DEBUG === 'true'
    this.isProduction = import.meta.env.PROD
    // #endif

    // #ifdef APP-PLUS
    // App环境下可以通过配置或编译时变量控制
    // @ts-ignore
    this.isDebug = typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development'
    // @ts-ignore
    this.isProduction = typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'production'
    // #endif

    // #ifdef MP
    // 小程序环境下可以通过配置控制
    this.isDebug = false // 小程序生产环境默认关闭调试日志
    this.isProduction = true
    // #endif

    // 从配置文件获取日志控制设置
    this.enableLogs = config.ENABLE_LOGS
  }

  log(...args: any[]): void {
    if (this.isDebug && this.enableLogs) {
      console.log(...args)
    }
  }

  warn(...args: any[]): void {
    if (this.isDebug && this.enableLogs) {
      console.warn(...args)
    }
  }

  error(...args: any[]): void {
    // 错误日志在生产环境下也保留，但可以控制输出格式
    if (this.isDebug && this.enableLogs) {
      console.error(...args)
    } else if (this.isProduction && this.enableLogs) {
      // 生产环境下只输出简化的错误信息
      console.error('Error:', args[0])
    }
  }

  info(...args: any[]): void {
    if (this.isDebug && this.enableLogs) {
      console.info(...args)
    }
  }

  debug(...args: any[]): void {
    if (this.isDebug && this.enableLogs) {
      console.debug(...args)
    }
  }

  // 强制输出（用于关键错误）
  forceLog(...args: any[]): void {
    console.log(...args)
  }

  forceError(...args: any[]): void {
    console.error(...args)
  }
}

// 创建全局日志实例
export const logger = new Logger()

// 导出便捷方法
export const log = (...args: any[]) => logger.log(...args)
export const warn = (...args: any[]) => logger.warn(...args)
export const error = (...args: any[]) => logger.error(...args)
export const info = (...args: any[]) => logger.info(...args)
export const debug = (...args: any[]) => logger.debug(...args)
export const forceLog = (...args: any[]) => logger.forceLog(...args)
export const forceError = (...args: any[]) => logger.forceError(...args) 