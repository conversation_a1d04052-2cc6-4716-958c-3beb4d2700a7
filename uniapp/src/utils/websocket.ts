import config from '@/config/index'
import { log, error, warn } from './logger'

// WebSocket消息类型定义
export interface TaskMessage {
  type: string
  seq: string
  timestamp: number
  payload: any
}

// 设备状态更新类型
export interface DeviceStatusUpdate {
  device_id: string
  status: number // 0: 离线, 1: 在线
  user_id: number
  time: number
}

// WebSocket连接状态
export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting'
}

// WebSocket事件类型
export enum WebSocketEvent {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  DEVICE_STATUS_UPDATE = 'device_status_update',
  ERROR = 'error',
  MESSAGE = 'message'
}

// WebSocket事件监听器类型
type EventListener = (data: any) => void

export class WebSocketService {
  private ws: any = null
  private url: string
  private status: ConnectionStatus = ConnectionStatus.DISCONNECTED
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 10
  private reconnectInterval: number = 3000 // 3秒
  private reconnectTimer: any = null
  private heartbeatTimer: any = null
  private isManualClose: boolean = false
  private eventListeners: Map<WebSocketEvent, Function[]> = new Map()

  constructor(token: string) {
    // 生成平台特定的设备ID
    const platformDeviceID = this.generatePlatformDeviceID()
    
    // 处理WebSocket URL
    let wsUrl = config.WS_URL
    if (wsUrl.startsWith('/')) {
      // 相对路径，需要转换为完整URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.host
      wsUrl = `${protocol}//${host}${wsUrl}`
    }
    
    this.url = `${wsUrl}?token=${token}&device_id=${platformDeviceID}`
    
    // 调试日志
    log('WebSocket URL:', this.url)
    log('当前协议:', window.location.protocol)
    log('当前主机:', window.location.host)
  }

  // 生成平台特定的设备ID
  private generatePlatformDeviceID(): string {
    // 获取用户ID（从token中解析或从localStorage获取）
    let userId = 'unknown'
    try {
      const token = uni.getStorageSync('token')
      if (token) {
        // 简单的token解析获取用户ID（实际项目中应该从JWT中解析）
        // 这里暂时使用时间戳作为用户标识
        userId = Date.now().toString(36)
      }
    } catch (error) {
      console.warn('获取用户ID失败:', error)
    }

    // #ifdef H5
    // H5环境：使用浏览器信息生成唯一ID
    const userAgent = navigator.userAgent
    const screenInfo = `${screen.width}x${screen.height}`
    const timeInfo = Date.now().toString(36)
    const browserHash = this.hashString(userAgent + screenInfo + timeInfo)
    return `uniapp_h5_${userId}_${browserHash}`
    // #endif

    // #ifdef APP-PLUS
    // App环境：使用设备信息生成唯一ID
    const systemInfo = uni.getSystemInfoSync()
    const deviceHash = this.hashString(systemInfo.platform + systemInfo.model + systemInfo.system)
    return `uniapp_app_${userId}_${deviceHash}`
    // #endif

    // #ifdef MP
    // 小程序环境：使用小程序信息生成唯一ID
    const accountInfo = uni.getAccountInfoSync()
    const mpHash = this.hashString(accountInfo.miniProgram.appId + accountInfo.miniProgram.envVersion)
    return `uniapp_mp_${userId}_${mpHash}`
    // #endif

    // 默认情况
    return `uniapp_${userId}_${Date.now().toString(36)}`
  }

  // 简单的字符串哈希函数
  private hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  // 连接WebSocket
  connect(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      if (this.status === ConnectionStatus.CONNECTED || this.status === ConnectionStatus.CONNECTING) {
        resolve()
        return
      }

      this.status = ConnectionStatus.CONNECTING
      this.isManualClose = false

      log('WebSocket连接中...', this.url)

      // #ifdef H5
      // H5环境使用原生WebSocket
      this.ws = new WebSocket(this.url)
      // #endif

      // #ifdef APP-PLUS
      // App环境使用uni.connectSocket
      this.ws = uni.connectSocket({
        url: this.url,
        success: () => {
          log('WebSocket连接创建成功')
        },
        fail: (err: any) => {
          error('WebSocket连接创建失败:', err)
          reject(err)
        }
      })
      // #endif

      // #ifdef H5
      this.ws.onopen = () => {
        log('WebSocket连接成功')
        this.status = ConnectionStatus.CONNECTED
        this.reconnectAttempts = 0
        this.startHeartbeat()
        this.emit(WebSocketEvent.CONNECT)
        resolve()
      }

      this.ws.onclose = (event: any) => {
        log('WebSocket连接关闭:', event)
        this.status = ConnectionStatus.DISCONNECTED
        this.stopHeartbeat()
        this.emit(WebSocketEvent.DISCONNECT, event)
        
        if (!this.isManualClose && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnect()
        }
      }

      this.ws.onerror = (err: any) => {
        error('WebSocket连接错误:', err)
        this.emit(WebSocketEvent.ERROR, err)
        reject(err)
      }

      this.ws.onmessage = (event: any) => {
        try {
          const message: TaskMessage = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (err: any) {
          error('解析WebSocket消息失败:', err)
        }
      }
      // #endif

      // #ifdef APP-PLUS
      // App环境的事件监听
      uni.onSocketOpen(() => {
        log('WebSocket连接成功')
        this.status = ConnectionStatus.CONNECTED
        this.reconnectAttempts = 0
        this.startHeartbeat()
        this.emit(WebSocketEvent.CONNECT)
        resolve()
      })

      uni.onSocketClose((event) => {
        log('WebSocket连接关闭:', event)
        this.status = ConnectionStatus.DISCONNECTED
        this.stopHeartbeat()
        this.emit(WebSocketEvent.DISCONNECT, event)
        
        if (!this.isManualClose && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnect()
        }
      })

      uni.onSocketError((err: any) => {
        error('WebSocket连接错误:', err)
        this.emit(WebSocketEvent.ERROR, err)
        reject(err)
      })

      uni.onSocketMessage((event) => {
        try {
          const message: TaskMessage = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (err: any) {
          error('解析WebSocket消息失败:', err)
        }
      })
      // #endif
    })
  }

  // 断开连接
  disconnect(): void {
    this.isManualClose = true
    this.stopHeartbeat()
    
    // 清除重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.ws) {
      // #ifdef H5
      this.ws.close()
      // #endif
      
      // #ifdef APP-PLUS
      uni.closeSocket()
      // #endif
    }
    
    this.status = ConnectionStatus.DISCONNECTED
    console.log('WebSocket连接已断开')
  }

  // 重连
  private reconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('WebSocket重连次数已达上限，停止重连')
      return
    }

    this.reconnectAttempts++
    this.status = ConnectionStatus.RECONNECTING
    
    console.log(`WebSocket重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    // 清除之前的重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }
    
    // 使用指数退避策略
    const delay = Math.min(this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1), 30000)
    
    this.reconnectTimer = setTimeout(() => {
      this.connect().catch((error) => {
        console.error('WebSocket重连失败:', error)
      })
    }, delay)
  }

  // 发送消息
  send(message: TaskMessage): void {
    if (this.status !== ConnectionStatus.CONNECTED) {
      console.warn('WebSocket未连接，无法发送消息')
      return
    }

    const messageStr = JSON.stringify(message)
    
    // #ifdef H5
    this.ws.send(messageStr)
    // #endif
    
    // #ifdef APP-PLUS
    uni.sendSocketMessage({
      data: messageStr,
      success: () => {
        console.log('WebSocket消息发送成功:', message.type)
      },
      fail: (error) => {
        console.error('WebSocket消息发送失败:', error)
      }
    })
    // #endif
  }

  // 处理接收到的消息
  private handleMessage(message: TaskMessage): void {
    console.log('收到WebSocket消息:', message)
    
    switch (message.type) {
      case 'DEVICE_STATUS_UPDATE':
        const statusUpdate = message.payload as DeviceStatusUpdate
        this.emit(WebSocketEvent.DEVICE_STATUS_UPDATE, statusUpdate)
        break
        
      case 'HEARTBEAT_RESP':
        console.log('收到心跳响应:', message.payload)
        break
        
      default:
        this.emit(WebSocketEvent.MESSAGE, message)
        break
    }
  }

  // 开始心跳
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      const heartbeatMessage: TaskMessage = {
        type: 'HEARTBEAT',
        seq: Date.now().toString(),
        timestamp: Date.now(),
        payload: {
          client_type: 'uniapp'
        }
      }
      this.send(heartbeatMessage)
    }, 30000) // 30秒发送一次心跳
  }

  // 停止心跳
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  // 添加事件监听器
  on(event: WebSocketEvent, listener: EventListener): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(listener)
  }

  // 移除事件监听器
  off(event: WebSocketEvent, listener: EventListener): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  // 触发事件
  private emit(event: WebSocketEvent, data?: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error('事件监听器执行错误:', error)
        }
      })
    }
  }

  // 获取连接状态
  getStatus(): ConnectionStatus {
    return this.status
  }

  // 检查是否已连接
  isConnected(): boolean {
    return this.status === ConnectionStatus.CONNECTED
  }

  // 手动重连
  async manualReconnect(): Promise<void> {
    console.log('手动重连WebSocket...')
    this.isManualClose = false
    this.reconnectAttempts = 0
    
    if (this.status === ConnectionStatus.CONNECTED) {
      this.disconnect()
    }
    
    await this.connect()
  }
}

// 全局WebSocket实例
let globalWebSocket: WebSocketService | null = null

// 创建WebSocket服务实例
export const createWebSocketService = (token: string): WebSocketService => {
  if (globalWebSocket) {
    globalWebSocket.disconnect()
  }
  
  globalWebSocket = new WebSocketService(token)
  return globalWebSocket
}

// 获取全局WebSocket实例
export const getWebSocketService = (): WebSocketService | null => {
  return globalWebSocket
}

// 销毁WebSocket服务
export const destroyWebSocketService = (): void => {
  if (globalWebSocket) {
    globalWebSocket.disconnect()
    globalWebSocket = null
  }
} 