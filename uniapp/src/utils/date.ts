/**
 * 统一的日期格式化工具函数
 * 确保在APP端显示中文格式的日期
 */

/**
 * 格式化相对时间（几分钟前、几小时前等）
 */
export const formatRelativeTime = (time: string): string => {
  if (!time) return '未知'
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  
  // 超过1天显示具体日期
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  return `${year}/${month}/${day}`
}

/**
 * 格式化日期时间（包含时分）
 */
export const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return '未知'
  const date = new Date(dateTimeStr)
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hour}:${minute}`
}

/**
 * 格式化日期（仅年月日）
 */
export const formatDate = (dateStr: string): string => {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  return `${year}/${month}/${day}`
}

/**
 * 格式化到期时间
 */
export const formatExpiry = (expiredAt: string): string => {
  if (!expiredAt) return '永久有效'
  
  const expireDate = new Date(expiredAt)
  const now = new Date()
  const diff = expireDate.getTime() - now.getTime()
  
  if (diff <= 0) return '已过期'
  
  const year = expireDate.getFullYear()
  const month = String(expireDate.getMonth() + 1).padStart(2, '0')
  const day = String(expireDate.getDate()).padStart(2, '0')
  
  return `${year}/${month}/${day} 到期`
}

/**
 * 格式化配额到期时间（包含相对时间）
 */
export const formatQuotaExpiry = (expiresAt: string): string => {
  if (!expiresAt) return '未知'
  const date = new Date(expiresAt)
  const now = new Date()
  const diff = date.getTime() - now.getTime()
  
  if (diff < 0) return '已过期'
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时后到期`
  if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天后到期`
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  return `${year}/${month}/${day} 到期`
}

/**
 * 检查是否已过期
 */
export const isExpired = (expiredAt: string): boolean => {
  if (!expiredAt) return false
  return new Date(expiredAt).getTime() <= new Date().getTime()
}

/**
 * 计算续费后的到期时间
 */
export const calculateRenewalExpiry = (currentExpiresAt: string): string => {
  if (!currentExpiresAt) return ''
  const currentDate = new Date(currentExpiresAt)
  const now = new Date()
  
  // 如果当前配额已过期，从当前时间开始计算30天
  if (currentDate.getTime() < now.getTime()) {
    return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString()
  }
  
  // 如果未过期，在现有到期时间基础上延长30天
  return new Date(currentDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString()
} 