<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/user";
import { useDeviceStore } from "@/stores/device";
import { ConnectionStatus } from "@/utils/websocket";

onLaunch(async () => {
  console.log("App Launch");
  
  // 检查登录状态并初始化WebSocket连接
  const userStore = useUserStore()
  try {
    await userStore.checkLoginStatus()
  } catch (error) {
    console.warn('应用启动时检查登录状态失败:', error)
  }
});

onShow(async () => {
  console.log("App Show");
  
  // 应用显示时，如果已登录但WebSocket未连接，尝试重新连接
  const userStore = useUserStore()
  if (userStore.isAuthenticated) {
    const deviceStore = useDeviceStore()
    if (deviceStore.websocketStatus === ConnectionStatus.DISCONNECTED) {
      try {
        console.log('应用显示，尝试重新连接WebSocket...')
        await deviceStore.initWebSocket(userStore.token!)
      } catch (error) {
        console.warn('应用显示时重新连接WebSocket失败:', error)
      }
    }
  }
});

onHide(() => {
  console.log("App Hide");
});
</script>

<style lang="scss">
@use "@/styles/global.scss";
</style>
