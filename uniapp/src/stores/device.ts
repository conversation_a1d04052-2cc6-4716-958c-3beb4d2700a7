import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/utils/api'
import { useUserStore } from './user'
import { 
  WebSocketService, 
  WebSocketEvent, 
  ConnectionStatus,
  createWebSocketService
} from '@/utils/websocket'
import type { DeviceStatusUpdate } from '@/utils/websocket'
import { log, error } from '@/utils/logger'

// 设备信息类型
export interface DeviceInfo {
  id: number
  device_id: string
  name: string // 后端返回的是 name，不是 device_name
  device_model?: string // 后端返回的是 device_model
  android_version?: string
  autox_version?: string // AutoX版本
  screen_width?: number
  screen_height?: number
  app_version?: string
  status: number // 0: 离线, 1: 在线
  last_active?: string
  created_at: string
  updated_at: string
}

// 设备统计信息
export interface DeviceStats {
  total: number
  online: number
  offline: number
}

// API响应类型
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export const useDeviceStore = defineStore('device', () => {
  // 状态
  const devices = ref<DeviceInfo[]>([])
  const deviceStats = ref<DeviceStats>({ total: 0, online: 0, offline: 0 })
  const loading = ref(false)
  const websocketService = ref<WebSocketService | null>(null)
  const websocketStatus = ref<ConnectionStatus>(ConnectionStatus.DISCONNECTED)

  // 计算属性
  const onlineDevices = computed(() => 
    devices.value.filter(device => device.status === 1)
  )
  
  const offlineDevices = computed(() => 
    devices.value.filter(device => device.status === 0)
  )

  // 初始化WebSocket连接
  const initWebSocket = async (token: string) => {
    try {
      // 创建WebSocket服务
      websocketService.value = createWebSocketService(token)
      
      // 监听设备状态更新
      websocketService.value.on(WebSocketEvent.DEVICE_STATUS_UPDATE, handleDeviceStatusUpdate)
      
      // 监听连接状态变化
      websocketService.value.on(WebSocketEvent.CONNECT, () => {
        websocketStatus.value = ConnectionStatus.CONNECTED
        log('WebSocket连接成功')
      })
      
      websocketService.value.on(WebSocketEvent.DISCONNECT, () => {
        log('WebSocket连接断开')
      })
      
      websocketService.value.on(WebSocketEvent.ERROR, async (err: any) => {
        error('WebSocket连接错误:', err)
        websocketStatus.value = ConnectionStatus.DISCONNECTED
        
        // 如果是token过期错误，尝试刷新token后重新连接
        if (err && typeof err === 'object' && 'details' in err && err.details === 'token已过期') {
          log('检测到token过期，尝试刷新token...')
          try {
            const userStore = useUserStore()
            const refreshSuccess = await userStore.refreshUserToken()
            if (refreshSuccess) {
              log('token刷新成功，重新连接WebSocket...')
              // 使用新的token重新连接
              await initWebSocket(userStore.token!)
            }
          } catch (refreshError) {
            error('刷新token失败:', refreshError)
          }
        }
      })

      // 连接WebSocket
      await websocketService.value.connect()
      
    } catch (err: any) {
      error('初始化WebSocket失败:', err)
      
      // 如果是token过期错误，尝试刷新token后重新连接
      if (err && typeof err === 'object' && 'details' in err && err.details === 'token已过期') {
        log('检测到token过期，尝试刷新token...')
        try {
          const userStore = useUserStore()
          const refreshSuccess = await userStore.refreshUserToken()
          if (refreshSuccess) {
            log('token刷新成功，重新连接WebSocket...')
            // 使用新的token重新连接
            await initWebSocket(userStore.token!)
          }
        } catch (refreshError) {
          error('刷新token失败:', refreshError)
        }
      }
    }
  }

  // 处理设备状态更新
  const handleDeviceStatusUpdate = (statusUpdate: DeviceStatusUpdate) => {
    log('收到设备状态更新:', statusUpdate)
    
    // 更新设备列表中的设备状态
    const deviceIndex = devices.value.findIndex(device => device.device_id === statusUpdate.device_id)
    if (deviceIndex !== -1) {
      devices.value[deviceIndex].status = statusUpdate.status
      devices.value[deviceIndex].last_active = new Date(statusUpdate.time).toISOString()
      
      // 重新计算统计信息
      updateDeviceStats()
      
      log(`设备 ${statusUpdate.device_id} 状态已更新为: ${statusUpdate.status === 1 ? '在线' : '离线'}`)
    }
  }

  // 更新设备统计信息
  const updateDeviceStats = () => {
    const total = devices.value.length
    const online = devices.value.filter(device => device.status === 1).length
    const offline = total - online
    
    deviceStats.value = { total, online, offline }
  }

  // 获取设备列表
  const fetchDevices = async (status?: number[]) => {
    try {
      loading.value = true
      const response = await api.getUserDevices(status) as ApiResponse<DeviceInfo[]>
      
      if (response.code === 0) {
        // 后端直接返回设备数组，不是 {list, total} 结构
        devices.value = response.data
        updateDeviceStats()
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (err: any) {
      error('获取设备列表失败:', err)
      return { success: false, message: '获取设备列表失败' }
    } finally {
      loading.value = false
    }
  }

  // 删除设备
  const deleteDevice = async (deviceID: string): Promise<{ success: boolean; message: string }> => {
    try {
      // 根据device_id找到设备的数据库id
      const device = devices.value.find(d => d.device_id === deviceID)
      if (!device) {
        return { success: false, message: '设备不存在' }
      }
      
      const response = await api.deleteDevice({ id: device.id }) as ApiResponse
      if (response.code === 0) {
        // 从本地列表中移除设备
        devices.value = devices.value.filter(device => device.device_id !== deviceID)
        updateDeviceStats()
        return { success: true, message: '删除成功' }
      } else {
        return { success: false, message: response.message }
      }
    } catch (err: any) {
      error('删除设备失败:', err)
      return { success: false, message: '删除设备失败' }
    }
  }

  // 更新设备信息
  const updateDeviceInfo = async (deviceID: string, deviceName: string): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await api.updateDeviceInfo({ device_id: deviceID, name: deviceName }) as ApiResponse
      if (response.code === 0) {
        // 更新本地设备信息
        const device = devices.value.find(d => d.device_id === deviceID)
        if (device) {
          device.name = deviceName
        }
        return { success: true, message: '更新成功' }
      } else {
        return { success: false, message: response.message }
      }
    } catch (err: any) {
      error('更新设备信息失败:', err)
      return { success: false, message: '更新设备信息失败' }
    }
  }

  // 根据设备ID获取设备信息
  const getDeviceById = (deviceId: string): DeviceInfo | undefined => {
    return devices.value.find(device => device.device_id === deviceId)
  }

  // 根据设备ID列表获取设备信息
  const getDevicesByIds = (deviceIds: string[]): DeviceInfo[] => {
    return devices.value.filter(device => deviceIds.includes(device.device_id))
  }

  // 检查设备是否在线
  const isDeviceOnline = (deviceId: string): boolean => {
    const device = getDeviceById(deviceId)
    return device ? device.status === 1 : false
  }

  // 断开WebSocket连接
  const disconnectWebSocket = () => {
    if (websocketService.value) {
      websocketService.value.disconnect()
      websocketService.value = null
    }
    websocketStatus.value = ConnectionStatus.DISCONNECTED
  }

  // 手动重连WebSocket
  const manualReconnectWebSocket = async () => {
    if (websocketService.value) {
      try {
        await websocketService.value.manualReconnect()
      } catch (err: any) {
        error('手动重连WebSocket失败:', err)
      }
    }
  }

  // 清理store
  const clearStore = () => {
    devices.value = []
    deviceStats.value = { total: 0, online: 0, offline: 0 }
    disconnectWebSocket()
  }

  return {
    // 状态
    devices,
    deviceStats,
    loading,
    websocketStatus,
    
    // 计算属性
    onlineDevices,
    offlineDevices,
    
    // 方法
    initWebSocket,
    fetchDevices,
    deleteDevice,
    updateDeviceInfo,
    getDeviceById,
    getDevicesByIds,
    isDeviceOnline,
    disconnectWebSocket,
    manualReconnectWebSocket,
    clearStore
  }
}) 