import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/utils/api'
import { useDeviceStore } from './device'
import { useScriptStore } from './script'



export interface UserInfo {
  id: number
  username: string
  nickname?: string
  email?: string
  avatar?: string
  created_at: string
  updated_at: string
}

interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const isLoading = ref(false)

  // 计算属性：判断是否已登录
  const isAuthenticated = computed(() => {
    return !!token.value && !!userInfo.value
  })

  // 登录
  const login = async (username: string, password: string) => {
    try {
      isLoading.value = true
      const response = await api.userLogin({ username, password }) as ApiResponse<{
        token: string
        refresh_token: string
      }>
      
      if (response.code === 0) {
        token.value = response.data.token
        refreshToken.value = response.data.refresh_token
        
        // 保存到本地存储
        uni.setStorageSync('token', token.value)
        uni.setStorageSync('refresh_token', refreshToken.value)
        
        // 获取用户信息
        await getUserProfile()
        
        // 初始化WebSocket连接
        const deviceStore = useDeviceStore()
        await deviceStore.initWebSocket(token.value)
        
        // 初始化脚本store的WebSocket监听
        const scriptStore = useScriptStore()
        scriptStore.initWebSocketListener()
        
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, message: '登录失败' }
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (username: string, password: string, email: string) => {
    try {
      isLoading.value = true
      const response = await api.userRegister({ username, password, email }) as ApiResponse
      
      if (response.code === 0) {
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('注册失败:', error)
      return { success: false, message: '注册失败' }
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户信息
  const getUserProfile = async () => {
    try {
      const response = await api.getUserProfile() as ApiResponse<UserInfo>
      
      if (response.code === 0) {
        userInfo.value = response.data
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return { success: false, message: '获取用户信息失败' }
    }
  }

  // 更新用户信息
  const updateUserProfile = async (data: any) => {
    try {
      const response = await api.updateUserProfile(data) as ApiResponse
      
      if (response.code === 0) {
        await getUserProfile() // 重新获取用户信息
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return { success: false, message: '更新用户信息失败' }
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      const response = await api.changePassword({ old_password: oldPassword, new_password: newPassword }) as ApiResponse
      
      if (response.code === 0) {
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('修改密码失败:', error)
      return { success: false, message: '修改密码失败' }
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await api.userLogout()
      }
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      // 清除本地数据
      token.value = null
      refreshToken.value = null
      userInfo.value = null
      
      // 清除本地存储
      uni.removeStorageSync('token')
      uni.removeStorageSync('refresh_token')
      
      // 断开WebSocket连接并清理设备store
      const deviceStore = useDeviceStore()
      deviceStore.clearStore()
      
      // 清理脚本store
      const scriptStore = useScriptStore()
      scriptStore.clearStore()
    }
  }

  // 检查登录状态
  const checkLoginStatus = async () => {
    const storedToken = uni.getStorageSync('token')
    const storedRefreshToken = uni.getStorageSync('refresh_token')
    
    if (!storedToken) {
      return false
    }
    
    token.value = storedToken
    refreshToken.value = storedRefreshToken
    
    // 如果已经有用户信息，直接返回true（避免不必要的API调用）
    if (userInfo.value) {
      // 尝试初始化WebSocket连接（但不影响登录状态）
      try {
        const deviceStore = useDeviceStore()
        await deviceStore.initWebSocket(storedToken)
        
        // 初始化脚本store的WebSocket监听
        const scriptStore = useScriptStore()
        scriptStore.initWebSocketListener()
      } catch (wsError) {
        console.warn('WebSocket连接失败，但不影响登录状态:', wsError)
        // WebSocket连接失败不影响HTTP登录状态
      }
      
      return true
    }
    
    // 只有在没有用户信息时才请求用户信息
    try {
      await getUserProfile()
      
      // 如果已登录，尝试初始化WebSocket连接（但不影响登录状态）
      try {
        const deviceStore = useDeviceStore()
        await deviceStore.initWebSocket(storedToken)
        
        // 初始化脚本store的WebSocket监听
        const scriptStore = useScriptStore()
        scriptStore.initWebSocketListener()
      } catch (wsError) {
        console.warn('WebSocket连接失败，但不影响登录状态:', wsError)
        // WebSocket连接失败不影响HTTP登录状态
      }
      
      return true
    } catch (error) {
      console.error('检查登录状态失败:', error)
      // 只有获取用户信息失败才清除token
      logout()
      return false
    }
  }

  // 刷新 token
  const refreshUserToken = async () => {
    if (!refreshToken.value) {
      return false
    }
    
    try {
      const response = await api.refreshToken({ refresh_token: refreshToken.value }) as ApiResponse<{
        token: string
        refresh_token: string
      }>
      
      if (response.code === 0) {
        token.value = response.data.token
        refreshToken.value = response.data.refresh_token
        
        // 更新本地存储
        uni.setStorageSync('token', token.value)
        uni.setStorageSync('refresh_token', refreshToken.value)
        
        return true
      } else {
        // 刷新失败，需要重新登录
        logout()
        return false
      }
    } catch (error) {
      console.error('刷新 token 失败:', error)
      logout()
      return false
    }
  }

  return {
    userInfo,
    token,
    refreshToken,
    isLoading,
    isAuthenticated,
    login,
    register,
    getUserProfile,
    updateUserProfile,
    changePassword,
    logout,
    checkLoginStatus,
    refreshUserToken
  }
})
