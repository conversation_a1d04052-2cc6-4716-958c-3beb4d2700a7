import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { scriptAPI } from '@/utils/api'
import type { DeviceStatusUpdate } from '@/utils/websocket'
import { WebSocketEvent } from '@/utils/websocket'
import { getWebSocketService } from '@/utils/websocket'

// 脚本信息类型
export interface ScriptInfo {
  id: number
  script_id: number
  script_name: string
  script_description?: string
  status: number
  created_at: string
  updated_at: string
}

// 脚本配置类型
export interface ScriptConfig {
  id: number
  user_script_id: number
  config_name: string
  config_data: any
  created_at: string
  updated_at: string
}

// 脚本绑定设备类型
export interface ScriptBinding {
  id: number
  user_script_id: number
  device_id: number
  device: {
    id: number
    device_id: string
    name: string
    device_model?: string
    status: number // 0: 离线, 1: 在线
    last_active: string
  }
  status: number
  last_active?: string
  created_at: string
  updated_at: string
}

// API响应类型
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export const useScriptStore = defineStore('script', () => {
  // 状态
  const scripts = ref<ScriptInfo[]>([])
  const scriptConfigs = ref<ScriptConfig[]>([])
  const scriptBindings = ref<ScriptBinding[]>([])
  const loading = ref(false)

  // 计算属性
  const onlineBindings = computed(() => 
    scriptBindings.value.filter(binding => binding.device.status === 1)
  )
  
  const offlineBindings = computed(() => 
    scriptBindings.value.filter(binding => binding.device.status === 0)
  )

  // 处理设备状态更新
  const handleDeviceStatusUpdate = (statusUpdate: DeviceStatusUpdate) => {
    console.log('脚本store收到设备状态更新:', statusUpdate)
    
    // 更新脚本绑定中的设备状态
    const bindingIndex = scriptBindings.value.findIndex(
      binding => binding.device && binding.device.device_id === statusUpdate.device_id
    )
    
    if (bindingIndex !== -1) {
      const binding = scriptBindings.value[bindingIndex]
      if (binding.device) {
        binding.device.status = statusUpdate.status
        console.log(`脚本绑定设备 ${statusUpdate.device_id} 状态已更新为: ${statusUpdate.status === 1 ? '在线' : '离线'}`)
      }
    }
  }

  // 获取用户脚本列表
  const fetchUserScripts = async () => {
    try {
      loading.value = true
      const response = await scriptAPI.getUserScripts() as ApiResponse<{
        list: ScriptInfo[]
        total: number
      }>
      
      if (response.code === 0) {
        scripts.value = response.data.list
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('获取用户脚本列表失败:', error)
      return { success: false, message: '获取用户脚本列表失败' }
    } finally {
      loading.value = false
    }
  }

  // 获取脚本配置列表
  const fetchScriptConfigs = async (scriptId: number) => {
    try {
      const response = await scriptAPI.getUserScriptConfigs(scriptId) as ApiResponse<{
        list: ScriptConfig[]
        total: number
      }>
      
      if (response.code === 0) {
        scriptConfigs.value = response.data.list
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('获取脚本配置列表失败:', error)
      return { success: false, message: '获取脚本配置列表失败' }
    }
  }

  // 获取脚本绑定的设备列表
  const fetchScriptBindings = async (scriptId: number) => {
    try {
      const response = await scriptAPI.getScriptBindings(scriptId) as ApiResponse<{
        bindings: ScriptBinding[]
        script: any
        binding_count: number
        max_devices: number
      }>
      
      if (response.code === 0) {
        scriptBindings.value = response.data.bindings || []
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('获取脚本绑定设备列表失败:', error)
      return { success: false, message: '获取脚本绑定设备列表失败' }
    }
  }

  // 绑定设备到脚本
  const bindDeviceToScript = async (scriptId: number, deviceIds: string[]) => {
    try {
      // 将string[]转换为number[]，这里假设deviceIds是数字字符串
      const deviceIdsNumbers = deviceIds.map(id => parseInt(id, 10))
      const response = await scriptAPI.bindDevice({
        user_script_id: scriptId,
        device_ids: deviceIdsNumbers
      }) as ApiResponse
      
      if (response.code === 0) {
        // 重新获取绑定列表
        await fetchScriptBindings(scriptId)
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('绑定设备失败:', error)
      return { success: false, message: '绑定设备失败' }
    }
  }

  // 解绑设备
  const unbindDeviceFromScript = async (scriptId: number, deviceIds: string[]) => {
    try {
      // 将string[]转换为number[]，这里假设deviceIds是数字字符串
      const deviceIdsNumbers = deviceIds.map(id => parseInt(id, 10))
      const response = await scriptAPI.unbindDevice({
        user_script_id: scriptId,
        device_ids: deviceIdsNumbers
      }) as ApiResponse
      
      if (response.code === 0) {
        // 重新获取绑定列表
        await fetchScriptBindings(scriptId)
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('解绑设备失败:', error)
      return { success: false, message: '解绑设备失败' }
    }
  }



  // 运行脚本
  const runScript = async (scriptId: number, deviceIds: string[]) => {
    try {
      const response = await scriptAPI.runScript({
        script_id: scriptId,
        device_ids: deviceIds
      }) as ApiResponse
      
      if (response.code === 0) {
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('运行脚本失败:', error)
      return { success: false, message: '运行脚本失败' }
    }
  }

  // 停止脚本
  const stopScript = async (scriptId: number, deviceIds: string[]) => {
    try {
      const response = await scriptAPI.stopScript({
        script_id: scriptId,
        device_ids: deviceIds
      }) as ApiResponse
      
      if (response.code === 0) {
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('停止脚本失败:', error)
      return { success: false, message: '停止脚本失败' }
    }
  }

  // 根据脚本ID获取脚本信息
  const getScriptById = (scriptId: number): ScriptInfo | undefined => {
    return scripts.value.find(script => script.id === scriptId)
  }

  // 根据设备ID获取绑定信息
  const getBindingByDeviceId = (deviceId: string): ScriptBinding | undefined => {
    return scriptBindings.value.find(binding => binding.device && binding.device.device_id === deviceId)
  }

  // 检查设备是否在线
  const isDeviceOnline = (deviceId: string): boolean => {
    const binding = getBindingByDeviceId(deviceId)
    return binding && binding.device ? binding.device.status === 1 : false
  }

  // 获取在线设备数量
  const getOnlineDeviceCount = (): number => {
    return onlineBindings.value.length
  }

  // 获取离线设备数量
  const getOfflineDeviceCount = (): number => {
    return offlineBindings.value.length
  }

  // 初始化WebSocket监听
  const initWebSocketListener = () => {
    // 监听设备状态更新
    const websocketService = getWebSocketService()
    if (websocketService) {
      websocketService.on(WebSocketEvent.DEVICE_STATUS_UPDATE, handleDeviceStatusUpdate)
    }
  }

  // 清理store
  const clearStore = () => {
    scripts.value = []
    scriptConfigs.value = []
    scriptBindings.value = []
  }

  return {
    // 状态
    scripts,
    scriptConfigs,
    scriptBindings,
    loading,
    
    // 计算属性
    onlineBindings,
    offlineBindings,
    
    // 方法
    fetchUserScripts,
    fetchScriptConfigs,
    fetchScriptBindings,
    bindDeviceToScript,
    unbindDeviceFromScript,
    runScript,
    stopScript,
    getScriptById,
    getBindingByDeviceId,
    isDeviceOnline,
    getOnlineDeviceCount,
    getOfflineDeviceCount,
    initWebSocketListener,
    clearStore
  }
}) 