import { envConfig } from './environments'

// 应用配置
const config = {
  // 当前环境信息
  ENV: envConfig.name,
  
  // API基础URL
  BASE_URL: envConfig.API_BASE_URL,
  
  // 调试模式
  DEBUG: envConfig.DEBUG,
  
  // 日志控制
  ENABLE_LOGS: envConfig.ENABLE_LOGS,
  
  // WebSocket配置
  WS_URL: envConfig.WS_URL,
  
  // 请求超时时间
  TIMEOUT: 10000,
  
  // API版本
  API_VERSION: '/api/v1',
  
  // 完整API地址
  get API_URL() {
    return this.BASE_URL
  }
}

export default config 