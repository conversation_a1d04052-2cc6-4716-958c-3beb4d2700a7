// 环境配置
interface Environment {
  name: string
  API_BASE_URL: string
  WS_URL: string
  DEBUG: boolean
  ENABLE_LOGS: boolean
}

// 获取平台信息
const getPlatform = (): string => {
  // #ifdef H5
  return 'h5'
  // #endif
  
  // #ifdef APP-PLUS
  return 'app'
  // #endif
  
  // #ifdef MP
  return 'mp'
  // #endif
  
  return 'h5' // 默认
}

// 环境配置映射
const environments: Record<string, Environment> = {
  development: {
    name: '开发环境',
    API_BASE_URL: '/api/v1',
    WS_URL: '/api/v1/ws',
    DEBUG: true,
    ENABLE_LOGS: true
  },
  test: {
    name: '测试环境',
    API_BASE_URL: '/api/v1',
    WS_URL: '/api/v1/ws',
    DEBUG: true,
    ENABLE_LOGS: true
  },
  production: {
    name: '生产环境',
    API_BASE_URL: '/api/v1', // 将在下面根据平台动态设置
    WS_URL: '/api/v1/ws',    // 将在下面根据平台动态设置
    DEBUG: false,
    ENABLE_LOGS: false
  }
}

// 获取当前环境
const getCurrentEnvironment = (): string => {
  // 优先使用环境变量
  if (import.meta.env.VITE_ENV) {
    return import.meta.env.VITE_ENV
  }
  
  // 根据构建模式判断
  if (import.meta.env.PROD) {
    return 'production'
  }
  
  return 'development'
}

// 获取环境配置
const getEnvironmentConfig = (): Environment => {
  const env = getCurrentEnvironment()
  const platform = getPlatform()
  const config = environments[env] || environments.development
  
  // 生产环境下根据平台调整配置
  if (env === 'production') {
    if (platform === 'app') {
      // APP环境：直接访问后端
      return {
        ...config,
        API_BASE_URL: 'https://app.l888.eu.org/api/v1',
        WS_URL: 'wss://app.l888.eu.org/api/v1/ws'
      }
    } else {
      // H5/小程序环境：使用Nginx代理
      return {
        ...config,
        API_BASE_URL: '/api/v1',
        WS_URL: '/api/v1/ws'
      }
    }
  }
  
  return config
}

// 导出环境配置
export const envConfig = getEnvironmentConfig()
export const currentEnv = getCurrentEnvironment()

// 导出所有环境配置（用于调试）
export { environments }

// 环境切换工具
export const switchEnvironment = (env: string): Environment => {
  if (!environments[env]) {
    console.warn(`环境 ${env} 不存在，使用默认环境`)
    return environments.development
  }
  return environments[env]
} 