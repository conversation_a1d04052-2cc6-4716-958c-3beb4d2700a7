<template>
  <view class="register-container">
    <!-- 注册表单 -->
    <view class="register-form">
      <!-- Logo -->
      <view class="logo-section">
        <view class="logo">🚀</view>
        <text class="app-name">群控系统控制台</text>
      </view>

      <!-- 表单 -->
      <view class="form-section">
        <view class="form-item">
          <view class="input-wrapper">
            <uni-icons type="person" size="20" color="#999" />
            <input
              v-model="formData.username"
              class="form-input"
              type="text"
              placeholder="请输入用户名"
              :disabled="loading"
            />
          </view>
        </view>

        <view class="form-item">
          <view class="input-wrapper">
            <uni-icons type="email" size="20" color="#999" />
            <input
              v-model="formData.email"
              class="form-input"
              type="email"
              placeholder="请输入邮箱"
              :disabled="loading"
            />
          </view>
        </view>

        <view class="form-item">
          <view class="input-wrapper">
            <uni-icons type="locked" size="20" color="#999" />
            <input
              v-model="formData.password"
              class="form-input"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              :disabled="loading"
            />
            <uni-icons 
              :type="showPassword ? 'eye-slash' : 'eye'" 
              size="20" 
              color="#999"
              @click="togglePassword"
            />
          </view>
        </view>

        <view class="form-item">
          <view class="input-wrapper">
            <uni-icons type="locked" size="20" color="#999" />
            <input
              v-model="formData.confirmPassword"
              class="form-input"
              :type="showConfirmPassword ? 'text' : 'password'"
              placeholder="请确认密码"
              :disabled="loading"
            />
            <uni-icons 
              :type="showConfirmPassword ? 'eye-slash' : 'eye'" 
              size="20" 
              color="#999"
              @click="toggleConfirmPassword"
            />
          </view>
        </view>

        <view class="form-item">
          <button
            class="register-btn"
            :class="{ 'btn-loading': loading }"
            :disabled="loading || !canSubmit"
            @click="handleRegister"
          >
            {{ loading ? '注册中...' : '注册' }}
          </button>
        </view>

        <view class="form-actions">
          <text class="link-text" @click="goLogin">已有账号？去登录</text>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer">
      <text class="footer-text">© 2024 群控系统. All rights reserved.</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// 已移除 Icon 导入，使用 uni-icons 替代
import { useUserStore } from '@/stores/user'
import { showError, showSuccess } from '@/utils/index'

// 表单数据
const formData = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 控制状态
const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)

// 用户状态管理
const userStore = useUserStore()

// 计算属性
const canSubmit = computed(() => {
  return formData.value.username.trim() && 
         formData.value.email.trim() &&
         formData.value.password.trim() && 
         formData.value.confirmPassword.trim()
})

// 获取系统信息
onMounted(() => {
})

// 切换密码显示状态
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 切换确认密码显示状态
const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

const handleRegister = async () => {
  if (!canSubmit.value || loading.value) return

  if (!formData.value.username || !formData.value.email || !formData.value.password) {
    showError('用户名、邮箱和密码必填')
    return
  }
  
  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(formData.value.email)) {
    showError('请输入正确的邮箱格式')
    return
  }
  
  if (formData.value.password !== formData.value.confirmPassword) {
    showError('两次输入的密码不一致')
    return
  }
  if (formData.value.password.length < 6) {
    showError('密码至少6个字符')
    return
  }
  loading.value = true
  const success = await userStore.register({
    username: formData.value.username,
    password: formData.value.password,
    email: formData.value.email
  })
  loading.value = false
  if (success) {
    showSuccess('注册成功，请登录')
    setTimeout(() => {
      uni.reLaunch({ url: '/pages/login/index' })
    }, 1000)
  }
}

const goLogin = () => {
  uni.reLaunch({ url: '/pages/login/index' })
}
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.register-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 60rpx 60rpx 120rpx;
}

.logo-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.form-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  max-width: 800rpx;
  margin: 0 auto;
  width: 100%;
}

.form-item {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 0 30rpx;
  height: 96rpx;
}

.input-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.form-input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  border: none;
  background: transparent;
}

.toggle-password {
  cursor: pointer;
  margin-left: 20rpx;
  margin-right: 0;
}

.register-btn {
  width: 100%;
  max-width: 400rpx;
  margin: 0 auto;
  height: 96rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &:not(:disabled):active {
    background: #0056cc;
    transform: scale(0.98);
  }

  &:disabled {
    background: #ccc;
    color: #999;
  }

  &.btn-loading {
    background: #0056cc;
  }
}

.form-actions {
  text-align: center;
  margin-top: 40rpx;
}

.link-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.footer {
  padding: 40rpx;
  text-align: center;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}
</style> 