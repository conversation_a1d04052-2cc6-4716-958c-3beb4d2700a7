<template>
  <view class="dashboard">
    <!-- 数据统计表格 -->
    <view class="data-section">
      <!-- 设备统计表格 -->
      <view class="table-container">
        <view class="table-header">
          <view class="table-cell header-cell">设备名称</view>
          <view class="table-cell header-cell">状态</view>
          <view class="table-cell header-cell">总收益</view>
          <view class="table-cell header-cell">今日收益</view>
          <view class="table-cell header-cell">运行APP</view>
          <view class="table-cell header-cell">操作</view>
        </view>
        
        <!-- 设备行和详情 -->
        <template v-for="(device, index) in deviceStats" :key="device.id">
          <!-- 设备行 -->
          <view class="table-row" :class="{ 'row-hover': true }">
            <view class="table-cell device-name">{{ device.name }}</view>
            <view class="table-cell">
              <text :class="['status-badge', getStatusClass(device.status)]">
                {{ getStatusText(device.status) }}
            </text>
          </view>
            <view class="table-cell money-cell">{{ formatMoney(device.total_earnings) }}</view>
            <view class="table-cell money-cell">{{ formatMoney(device.today_earnings) }}</view>
            <view class="table-cell app-count">{{ device.running_apps }}个</view>
            <view class="table-cell">
              <text 
                class="detail-btn"
                @click="toggleDeviceDetail(device.id)"
              >
                {{ expandedDevices.includes(device.id) ? '收起' : '详情' }}
            </text>
          </view>
        </view>
        
          <!-- 设备详情展开 -->
          <view 
            v-show="expandedDevices.includes(device.id)"
            class="device-detail"
          >
            <!-- APP收益表格 -->
            <view class="app-earnings-table">
              <view class="app-table-header">
                <view class="app-table-cell">APP名称</view>
                <view class="app-table-cell">今日收益</view>
                <view class="app-table-cell">总收益</view>
                <view class="app-table-cell">签到状态</view>
          </view>
              <view 
                v-for="app in device.app_earnings" 
                :key="app.id"
                class="app-table-row"
              >
                <view class="app-table-cell app-name">{{ app.name }}</view>
                <view class="app-table-cell money-cell">{{ formatMoney(app.today_earnings) }}</view>
                <view class="app-table-cell money-cell">{{ formatMoney(app.total_earnings) }}</view>
                <view class="app-table-cell">
                  <text :class="['status-badge', getStatusClass(app.checkin_status)]">
                    {{ getStatusText(app.checkin_status) }}
            </text>
        </view>
      </view>
    </view>
          </view>
        </template>

        <!-- 空状态 -->
        <view v-if="deviceStats.length === 0" class="empty-state">
          <view class="empty-icon">📱</view>
          <text class="empty-text">暂无设备数据</text>
          <text class="empty-desc">请先添加设备或检查网络连接</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const deviceStats = ref<any[]>([])
const expandedDevices = ref<number[]>([])

// 生命周期
onMounted(() => {
  loadDeviceStats()
})

// 方法
const toggleDeviceDetail = (deviceId: number) => {
  const index = expandedDevices.value.indexOf(deviceId)
  if (index > -1) {
    expandedDevices.value.splice(index, 1)
  } else {
    expandedDevices.value.push(deviceId)
  }
}

const loadDeviceStats = async () => {
  try {
    // 模拟后端返回的设备统计数据
    const response = await mockGetDeviceStats()
    deviceStats.value = response.data.devices
  } catch (error) {
    console.error('加载设备统计失败:', error)
    deviceStats.value = []
  }
}

// 工具方法
const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    'online': 'online',
    'offline': 'offline',
    'completed': 'completed',
    'pending': 'pending',
    'running': 'running',
    'stopped': 'stopped'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'online': '在线',
    'offline': '离线',
    'completed': '已签到',
    'pending': '未签到',
    'running': '运行中',
    'stopped': '已停止'
  }
  return textMap[status] || status
}

const formatMoney = (value: number | string) => {
  if (typeof value === 'string') {
    return value
  }
  return value.toFixed(2)
}

// 模拟API
const mockGetDeviceStats = () => {
  return Promise.resolve({
    data: {
      devices: [
        {
          id: 1,
          name: '设备001',
          status: 'online',
          total_earnings: 1234.50,
          today_earnings: 156.80,
          running_apps: 4,
          app_earnings: [
      {
        id: 1,
              name: '抖音极速版',
              today_earnings: 45.60,
              total_earnings: 456.80,
              checkin_status: 'completed'
      },
      {
        id: 2,
              name: '快手极速版',
              today_earnings: 38.90,
              total_earnings: 389.20,
              checkin_status: 'completed'
      },
      {
        id: 3,
              name: '今日头条',
              today_earnings: 32.40,
              total_earnings: 324.50,
              checkin_status: 'pending'
      },
      {
        id: 4,
              name: '趣头条',
              today_earnings: 28.70,
              total_earnings: 287.30,
              checkin_status: 'completed'
      }
    ]
        },
        {
          id: 2,
          name: '设备002',
          status: 'offline',
          total_earnings: 856.30,
          today_earnings: 89.20,
          running_apps: 2,
          app_earnings: [
            {
              id: 1,
              name: '抖音极速版',
              today_earnings: 52.10,
              total_earnings: 521.50,
              checkin_status: 'completed'
            },
            {
              id: 2,
              name: '快手极速版',
              today_earnings: 37.10,
              total_earnings: 334.80,
              checkin_status: 'completed'
            }
          ]
        },
        {
          id: 3,
          name: '设备003',
          status: 'online',
          total_earnings: 1892.70,
          today_earnings: 234.60,
          running_apps: 5,
          app_earnings: [
            {
              id: 1,
              name: '抖音极速版',
              today_earnings: 67.80,
              total_earnings: 678.90,
              checkin_status: 'completed'
            },
            {
              id: 2,
              name: '快手极速版',
              today_earnings: 58.40,
              total_earnings: 584.20,
              checkin_status: 'completed'
            },
            {
              id: 3,
              name: '今日头条',
              today_earnings: 42.30,
              total_earnings: 423.50,
              checkin_status: 'completed'
            },
            {
              id: 4,
              name: '趣头条',
              today_earnings: 35.20,
              total_earnings: 352.80,
              checkin_status: 'pending'
            },
            {
              id: 5,
              name: '番茄小说',
              today_earnings: 30.90,
              total_earnings: 309.30,
              checkin_status: 'completed'
            }
          ]
        }
      ]
    }
  })
}
</script>

<style lang="scss" scoped>
.dashboard {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 数据统计表格 */
.data-section {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.table-container {
  border-radius: 16rpx;
  overflow: hidden;
  background: white;
  border: 1rpx solid #eee;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  padding: 8rpx 20rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #eee;
  font-size: 24rpx;
}

.table-row {
  display: flex;
  padding: 8rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  align-items: center;
  transition: all 0.2s ease;
  position: relative;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  text-align: left;
  padding: 4rpx 0;
  font-size: 24rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
}

.header-cell {
  font-weight: 600;
  color: #333;
  font-size: 24rpx;
  justify-content: flex-start;
}

.device-name {
  font-weight: 600;
  color: #333;
  justify-content: flex-start;
}

.money-cell {
  justify-content: flex-start;
  font-weight: 600;
  color: #28a745;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.app-count {
  justify-content: center;
  font-weight: 500;
  color: #666;
}

.detail-btn {
  color: #667eea;
  font-size: 22rpx;
  font-weight: 500;
  cursor: pointer;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  background: rgba(102, 126, 234, 0.1);
  transition: all 0.2s ease;
  justify-content: center;
}

.detail-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.status-badge {
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  color: white;
  display: inline-block;
  transition: all 0.2s ease;
}

.status-badge.online {
  background: #4ecdc4;
}

.status-badge.offline {
  background: #ff6b6b;
}

.status-badge.completed {
  background: #4ecdc4;
}

.status-badge.pending {
  background: #feca57;
}

.status-badge.running {
  background: #667eea;
}

.status-badge.stopped {
  background: #999;
}

.status-badge.default {
  background: #999;
}

/* 设备详情展开 */
.device-detail {
  background: #f8f9fa;
  border-top: 1rpx solid #eee;
  padding: 8rpx 20rpx;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* APP收益表格 */
.app-earnings-table {
  border-radius: 8rpx;
  overflow: hidden;
  border: 1rpx solid #eee;
  background: white;
}

.app-table-header {
  display: flex;
  background: #f8f9fa;
  padding: 6rpx 16rpx;
  font-weight: 600;
  color: #333;
  font-size: 22rpx;
  border-bottom: 1rpx solid #eee;
}

.app-table-row {
  display: flex;
  padding: 6rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
  align-items: center;
  font-size: 22rpx;
  transition: background-color 0.2s ease;
}

.app-table-row:hover {
  background: #f8f9fa;
}

.app-table-row:last-child {
  border-bottom: none;
}

.app-table-cell {
  flex: 1;
  text-align: left;
  padding: 2rpx 0;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.app-table-cell.money-cell {
  justify-content: flex-start;
  font-weight: 600;
  color: #28a745;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.app-name {
  font-weight: 500;
  color: #333;
  justify-content: flex-start;
}

.empty-state {
  padding: 80rpx 30rpx;
  text-align: center;
  background: #f8f9fa;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 10rpx;
  display: block;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  display: block;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .dashboard {
    padding: 10rpx;
}

  .data-section {
    padding: 16rpx;
}

  .table-header,
  .table-row {
    padding: 6rpx 16rpx;
  }
  
  .table-cell {
    font-size: 22rpx;
  }
  
  .device-detail {
    padding: 6rpx 16rpx;
}

  .app-table-header,
  .app-table-row {
    padding: 4rpx 12rpx;
    font-size: 20rpx;
  }
  
  .detail-btn {
    padding: 4rpx 8rpx;
    font-size: 20rpx;
  }
}
</style>

