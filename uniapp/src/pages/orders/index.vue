<template>
  <view class="orders-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-left" @click="goBack">
          <uni-icons type="left" size="20" color="#666" />
      </view>
      <view class="header-title">
        <text class="title-text">订单</text>
      </view>
      <view class="header-right" @click="refreshOrders">
          <uni-icons type="refresh" size="20" color="#666" />
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="orders-content">
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <view v-else-if="!orders || orders.length === 0" class="empty-container">
        <uni-icons type="receipt" size="48" color="#ccc" />
        <text class="empty-title">暂无订单</text>
        <text class="empty-desc">您还没有任何订单记录</text>
      </view>
      
      <view v-else class="orders-list">
        <view 
          v-for="order in orders" 
          :key="order.id"
          class="order-card card"
          @click="showOrderDetail(order)"
        >
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-type">
              <text :class="['type-badge', getOrderTypeClass(order.type)]">
                {{ getOrderTypeText(order.type) }}
              </text>
            </view>
            <view class="order-status">
              <text :class="['status-text', getStatusClass(order.status)]">
                {{ getStatusText(order.status) }}
              </text>
            </view>
          </view>
          
          <!-- 订单内容 -->
          <view class="order-content">
            <view class="order-title">{{ order.title }}</view>
            <view class="order-desc">{{ order.description }}</view>
            <view class="order-meta">
              <text class="order-amount">¥{{ order.amount }}</text>
                              <text class="order-time">{{ formatRelativeTime(order.created_at) }}</text>
            </view>
          </view>
          
          <!-- 订单操作 -->
          <view class="order-actions" v-if="order.status === 0">
            <button class="btn btn-small btn-secondary" @click.stop="cancelOrder(order)">
              取消
            </button>
            <button class="btn btn-small btn-primary" @click.stop="payOrder(order)">
              支付
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单详情弹窗 -->
    <uni-popup ref="orderDetailPopup" type="center" :mask-click="true">
      <view class="order-detail-modal card">
        <view class="modal-header">
          <text class="modal-title">订单详情</text>
          <uni-icons type="close" size="20" color="#999" class="modal-close" @click="closeOrderDetail" />
        </view>
        
        <view class="modal-content" v-if="selectedOrder">
          <view class="detail-item">
            <text class="detail-label">订单号</text>
            <text class="detail-value">{{ selectedOrder.order_no }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">订单类型</text>
            <text class="detail-value">{{ getOrderTypeText(selectedOrder.type) }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">订单状态</text>
            <text :class="['detail-value', getStatusClass(selectedOrder.status)]">
              {{ getStatusText(selectedOrder.status) }}
            </text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">订单金额</text>
            <text class="detail-value amount">¥{{ selectedOrder.amount }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">支付方式</text>
            <text class="detail-value">{{ selectedOrder.pay_type || '未选择' }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">创建时间</text>
            <text class="detail-value">{{ formatRelativeTime(selectedOrder.created_at) }}</text>
          </view>
          
          <view class="detail-item" v-if="selectedOrder.paid_at">
            <text class="detail-label">支付时间</text>
            <text class="detail-value">{{ formatRelativeTime(selectedOrder.paid_at) }}</text>
          </view>
          
          <view class="detail-item" v-if="selectedOrder.expired_at">
            <text class="detail-label">过期时间</text>
            <text class="detail-value">{{ formatRelativeTime(selectedOrder.expired_at) }}</text>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn btn-secondary" @click="closeOrderDetail">
            关闭
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
// 已移除 Icon 导入，使用 uni-icons 替代
import { userAPI } from '@/utils/api'
import { formatRelativeTime } from '@/utils/date'

// 响应式数据
const orders = ref<any[]>([])
const loading = ref(false)
const selectedOrder = ref<any>(null)

// 方法
const loadOrders = async () => {
  loading.value = true
  try {
    // 获取脚本订单
    const scriptOrdersResponse = await userAPI.getScriptOrders()
    const scriptOrders = scriptOrdersResponse.code === 0 ? scriptOrdersResponse.data.orders || [] : []
    
    // 获取配额订单
    const quotaOrdersResponse = await userAPI.getQuotaOrders()
    const quotaOrders = quotaOrdersResponse.code === 0 ? quotaOrdersResponse.data.orders || [] : []
    
    // 合并订单并按时间排序
    const allOrders = [...scriptOrders, ...quotaOrders].map(order => {
      // 根据订单号前缀判断订单类型
      let type = 'script'
      if (order.order_no && order.order_no.startsWith('DQ')) {
        type = 'quota'
      } else if (order.order_no && order.order_no.startsWith('SU')) {
        type = 'upgrade'
      } else if (order.package_id) {
        type = 'script'
      } else {
        type = 'quota'
      }
      
      return {
        ...order,
        type
      }
    })
    
    orders.value = allOrders.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )
  } catch (error) {
    console.error('加载订单失败:', error)
    uni.showToast({
      title: '加载订单失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const getOrderTypeText = (type: string) => {
  switch (type) {
    case 'script': return '脚本订单'
    case 'quota': return '配额订单'
    case 'upgrade': return '升级订单'
    default: return '未知订单'
  }
}

const getOrderTypeClass = (type: string) => {
  switch (type) {
    case 'script': return 'script-type'
    case 'quota': return 'quota-type'
    case 'upgrade': return 'upgrade-type'
    default: return 'unknown-type'
  }
}

const getStatusText = (status: number) => {
  switch (status) {
    case 0: return '未支付'
    case 1: return '已支付'
    case 2: return '已取消'
    case 3: return '已退款'
    default: return '未知'
  }
}

const getStatusClass = (status: number) => {
  switch (status) {
    case 0: return 'status-pending'
    case 1: return 'status-paid'
    case 2: return 'status-cancelled'
    case 3: return 'status-refunded'
    default: return 'status-unknown'
  }
}

// 使用统一的日期格式化函数

const goBack = () => {
  uni.navigateBack()
}

const refreshOrders = () => {
  loadOrders()
}

const showOrderDetail = (order: any) => {
  selectedOrder.value = order
  // 这里需要根据实际的uni-popup组件API来调用
  console.log('显示订单详情:', order)
}

const closeOrderDetail = () => {
  selectedOrder.value = null
  console.log('关闭订单详情')
}

const cancelOrder = (order: any) => {
  uni.showModal({
    title: '取消订单',
    content: '确定要取消这个订单吗？',
    success: (res) => {
      if (res.confirm) {
        // 这里调用取消订单API
        console.log('取消订单:', order.id)
        uni.showToast({
          title: '订单已取消',
          icon: 'success'
        })
        loadOrders() // 重新加载订单列表
      }
    }
  })
}

const payOrder = (order: any) => {
  // 跳转到支付页面
  uni.navigateTo({
    url: `/pages/payment/index?order_id=${order.id}&type=${order.type}`
  })
}

// 生命周期
onMounted(() => {
  loadOrders()
})

// 页面显示时刷新数据
onShow(() => {
  loadOrders()
})
</script>

<style lang="scss" scoped>
.orders-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: env(safe-area-inset-bottom);
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left, .header-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}



.header-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.orders-content {
  padding: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f0f0f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}



.empty-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #666;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  
  &.script-type {
    background: #e6f7ff;
    color: #1890ff;
  }
  
  &.quota-type {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.upgrade-type {
    background: #fff7e6;
    color: #fa8c16;
  }
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  
  &.status-pending {
    color: #faad14;
  }
  
  &.status-paid {
    color: #52c41a;
  }
  
  &.status-cancelled {
    color: #ff4d4f;
  }
  
  &.status-refunded {
    color: #666;
  }
  
  &.status-unknown {
    color: #999;
  }
}

.order-content {
  margin-bottom: 12px;
}

.order-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.order-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.order-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-amount {
  font-size: 16px;
  font-weight: 600;
  color: #ff4d4f;
}

.order-time {
  font-size: 12px;
  color: #999;
}

.order-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.btn {
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &.btn-small {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  &.btn-secondary {
    background: #f0f0f0;
    color: #666;
    
    &:active {
      background: #e0e0e0;
    }
  }
  
  &.btn-primary {
    background: #667eea;
    color: white;
    
    &:active {
      background: #5a6fd8;
    }
  }
}

.order-detail-modal {
  width: 90vw;
  max-width: 400px;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  .modal-close {
    font-size: 24px;
    color: #ccc;
    cursor: pointer;
    padding: 4px;
  }
  
  .modal-content {
    padding: 20px;
  }
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 14px;
  color: #666;
}

.detail-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  
  &.amount {
    color: #ff4d4f;
  }
  
  &.status-pending {
    color: #faad14;
  }
  
  &.status-paid {
    color: #52c41a;
  }
  
  &.status-cancelled {
    color: #ff4d4f;
  }
  
  &.status-refunded {
    color: #666;
  }
}

.modal-actions {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  
  .btn {
    width: 100%;
    height: 40px;
    font-size: 14px;
  }
}
</style> 