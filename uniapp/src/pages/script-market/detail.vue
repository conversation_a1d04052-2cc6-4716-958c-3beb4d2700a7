<template>
  <view class="script-detail">
    <!-- 脚本基本信息 -->
    <view class="script-header">
      <image :src="script.icon || '/static/logo.png'" class="script-icon" />
      <view class="script-info">
        <text class="script-name">{{script.name}}</text>
        <text class="script-author">作者: {{script.author}}</text>
        <view class="script-meta">
          <text class="script-downloads">{{script.downloads}}次下载</text>
          <text class="script-rating">⭐{{script.rating}}</text>
          <text class="script-category">{{script.category}}</text>
        </view>
        <view class="script-version-info">
          <text class="version-text">v{{script.version}}</text>
          <text class="update-time">{{formatDate(script.updated_at)}}</text>
          <text class="file-size">{{script.file_size}}</text>
        </view>
        <view class="script-badges">
          <text v-if="script.is_official" class="badge official">官方</text>
          <text v-if="script.is_featured" class="badge featured">推荐</text>
        </view>
      </view>
    </view>

    <!-- 脚本描述 -->
    <view class="script-description">
      <view class="description-content">
        <rich-text 
          :nodes="markdownContent" 
          class="markdown-content"
          @tap="handleRichTextTap"
        ></rich-text>
      </view>
    </view>

    <!-- 脚本功能 -->
    <view class="script-features" v-if="script.features && script.features.length > 0">
      <text class="section-title">主要功能</text>
      <view class="features-list">
        <view 
          v-for="(feature, index) in script.features"
          :key="index"
          class="feature-item"
        >
          <text class="feature-dot">•</text>
          <text class="feature-text">{{feature}}</text>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="script-usage" v-if="script.usage">
      <view class="usage-content">
        <rich-text 
          :nodes="usageMarkdownContent" 
          class="markdown-content"
          @tap="handleRichTextTap"
        ></rich-text>
      </view>
    </view>

    <!-- 套餐选择 -->
    <view class="packages-section" v-if="packages.length > 0">
      <text class="section-title">选择套餐</text>
      <view class="packages-list">
        <view 
          v-for="pkg in packages" 
          :key="pkg.id"
          class="package-item"
          :class="{ 'selected': selectedPackage?.id === pkg.id }"
          @click="selectPackage(pkg)"
        >
          <view class="package-header">
            <text class="package-name">{{pkg.name}}</text>
            <text class="package-price">¥{{pkg.price}}/月</text>
          </view>
          <view class="package-details">
            <text class="package-limit">{{pkg.device_limit}}个设备</text>
          </view>

        </view>
      </view>
    </view>



    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-buttons">
        <button 
          @click="purchaseScript" 
          class="purchase-btn"
          :disabled="!canPurchase"
        >
          {{ purchaseButtonText }}
        </button>
      </view>
    </view>

    <!-- 登录提示 -->
    <view class="login-tip" v-if="!isLoggedIn">
      <text class="tip-text">登录后可以购买和安装脚本</text>
      <button @click="goToLogin" class="login-btn">立即登录</button>
    </view>

    <!-- 加载中遮罩 -->
    <view class="loading-mask" v-if="isLoading">
      <view class="loading-content">
        <text class="loading-text">{{loadingText}}</text>
      </view>
    </view>

    <!-- 支付弹窗 -->
    <view v-if="showPayDialog" class="pay-dialog-mask">
      <view class="pay-dialog-content">
        <text class="pay-dialog-title">订单已创建</text>
        <text class="pay-dialog-desc">请点击下方按钮前往支付</text>
        <button class="pay-dialog-btn" @click="handleGoPay">去支付</button>
        <button class="pay-dialog-cancel" @click="handleCancelPay">取消</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '@/utils/api'
import MarkdownIt from 'markdown-it'

// 初始化Markdown解析器
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true
})

// 自定义链接渲染，使其可点击
md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
  const token = tokens[idx]
  const href = token.attrs.find(attr => attr[0] === 'href')
  if (href) {
    // 添加点击事件和样式
    token.attrSet('onclick', `handleLinkClick('${href[1]}')`)
    token.attrSet('style', 'color: #007aff; text-decoration: underline; cursor: pointer;')
    token.attrSet('class', 'markdown-link')
  }
  return self.renderToken(tokens, idx, options)
}

// 响应式数据
const script = ref<any>({})
const packages = ref<any[]>([])
const selectedPackage = ref<any>(null)
const isLoggedIn = ref(false)
const scriptId = ref(0)
const isLoading = ref(false)
const loadingText = ref('')
const showPayDialog = ref(false)
const pendingPayUrl = ref('')
const pendingOrderNo = ref('')

// 计算属性
const markdownContent = computed(() => {
  if (!script.value.description) return ''
  return md.render(script.value.description)
})

const usageMarkdownContent = computed(() => {
  if (!script.value.usage) return ''
  return md.render(script.value.usage)
})

const canPurchase = computed(() => {
  return isLoggedIn.value && selectedPackage.value
})

const purchaseButtonText = computed(() => {
  if (!isLoggedIn.value) return '请先登录'
  if (!selectedPackage.value) return '请选择套餐'
  return `立即购买 ¥${selectedPackage.value?.price || 0}`
})

// 生命周期
onMounted(() => {
  // 将链接点击处理函数添加到全局
  // #ifdef H5
  ;(window as any).handleLinkClick = handleLinkClick
  // #endif
  
  checkLoginStatus()
  loadScriptDetail()
  loadPackages()
})

// 方法
const checkLoginStatus = () => {
  const token = uni.getStorageSync('token')
  isLoggedIn.value = !!token
}

const loadScriptDetail = async () => {
  try {
    // 获取页面参数
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const options = currentPage.options
    scriptId.value = parseInt(options.id || '0')
    
    if (!scriptId.value) {
      uni.showToast({
        title: '脚本ID无效',
        icon: 'none'
      })
      return
    }

    const res = await api.getMarketScriptDetail(scriptId.value)
    script.value = res.data || {}
  } catch (error) {
    console.error('加载脚本详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

const loadPackages = async () => {
  try {
    if (!scriptId.value) return
    
    const res = await api.getScriptPackages(scriptId.value)
    packages.value = res.data || []
    
    // 不默认选择套餐，让用户手动选择
    selectedPackage.value = null
  } catch (error) {
    console.error('加载套餐失败:', error)
    uni.showToast({
      title: '加载套餐失败',
      icon: 'none'
    })
  }
}

const selectPackage = (pkg: any) => {
  selectedPackage.value = pkg
}

// 全局链接点击处理函数
const handleLinkClick = (href: string) => {
  // 显示确认对话框
  uni.showModal({
    title: '打开链接',
    content: `是否打开链接：${href}`,
    success: (res) => {
      if (res.confirm) {
        // 打开链接
        // #ifdef H5
        window.open(href, '_blank')
        // #endif
        
        // #ifdef APP-PLUS
        plus.runtime.openURL(href)
        // #endif
        
        // #ifdef MP
        // 小程序中复制链接到剪贴板
        uni.setClipboardData({
          data: href,
          success: () => {
            uni.showToast({
              title: '链接已复制到剪贴板',
              icon: 'success'
            })
          }
        })
        // #endif
      }
    }
  })
}

// 处理rich-text中的链接点击
const handleRichTextTap = (e: any) => {
  // 检查点击的是否是链接
  const target = e.target || e.currentTarget
  if (target && target.dataset && target.dataset.href) {
    const href = target.dataset.href
    handleLinkClick(href)
  }
}



const formatDate = (date: string) => {
  if (!date) return '未知'
  return new Date(date).toLocaleDateString()
}



const purchaseScript = () => {
  if (!canPurchase.value) {
    if (!isLoggedIn.value) {
      uni.showModal({
        title: '提示',
        content: '请先登录后再购买脚本',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            goToLogin()
          }
        }
      })
    }
    return
  }

  isLoading.value = true
  loadingText.value = '正在创建订单...'

  api.createPaymentOrder({
    package_id: selectedPackage.value.id,
    pay_type: 'alipay'
  }).then(orderRes => {
    const order = orderRes.data
    if (!order || !order.pay_url) {
      uni.showToast({ title: '创建订单失败', icon: 'none' })
      return
    }
    // 传递所有订单详情参数
    uni.navigateTo({
      url: `/pages/payment/index?pay_url=${encodeURIComponent(order.pay_url)}&order_no=${order.order_no}&amount=${order.amount}&pay_type=${order.pay_type}&created_at=${encodeURIComponent(order.created_at || '')}`
    })
  }).catch(error => {
    if (error && error.code === 2012) {
      uni.showToast({ title: error.message, icon: 'none' })
    } else {
      uni.showToast({ title: error.message || '购买失败', icon: 'none' })
    }
  }).finally(() => {
    isLoading.value = false
  })
}

const handleGoPay = () => {
  // #ifdef H5
  window.open(pendingPayUrl.value, '_blank')
  uni.navigateTo({ url: `/pages/payment/index?order_no=${pendingOrderNo.value}` })
  // #endif
  // #ifdef APP-PLUS
  plus.runtime.openURL(pendingPayUrl.value)
  uni.navigateTo({ url: `/pages/payment/index?order_no=${pendingOrderNo.value}` })
  // #endif
  // #ifndef H5 && !APP-PLUS
  uni.navigateTo({ url: `/pages/payment/index?pay_url=${encodeURIComponent(pendingPayUrl.value)}&order_no=${pendingOrderNo.value}` })
  // #endif
  showPayDialog.value = false
}
const handleCancelPay = () => {
  showPayDialog.value = false
}

// H5下主页面轮询订单状态
// #ifdef H5
function startPollingOrderStatus(orderNo) {
  let pollCount = 0
  const maxPolls = 60
  const pollInterval = 5000
  const poll = async () => {
    try {
      pollCount++
              const orderRes = await api.getOrderDetail(orderNo)
      const order = orderRes.data
      if (order && order.status === 1) {
        uni.showToast({ title: '支付成功，已自动激活', icon: 'success' })
        setTimeout(() => {
          uni.switchTab({ url: '/pages/script/index' })
        }, 1000)
        return
      }
      if (pollCount >= maxPolls) {
        uni.showToast({ title: '支付超时，请重试', icon: 'none' })
        return
      }
      setTimeout(poll, pollInterval)
    } catch (error) {
      if (pollCount >= maxPolls) {
        uni.showToast({ title: '支付超时，请重试', icon: 'none' })
      } else {
        setTimeout(poll, pollInterval)
      }
    }
  }
  setTimeout(poll, pollInterval)
}
// #endif

const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/index'
  })
}
</script>

<style lang="scss" scoped>
.script-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  overflow-x: hidden; // 防止水平滚动
  width: 100%;
  box-sizing: border-box;
}

.script-header {
  background: white;
  padding: 40rpx;
  display: flex;
  align-items: flex-start;
  gap: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.script-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  flex-shrink: 0;
}

.script-info {
  flex: 1;
}

.script-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.script-author {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.script-meta {
  display: flex;
  gap: 20rpx;
  margin-bottom: 15rpx;
}

.script-downloads,
.script-rating,
.script-category {
  font-size: 24rpx;
  color: #999;
}

.script-version-info {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.version-text,
.update-time,
.file-size {
  font-size: 24rpx;
  color: #666;
}

.script-badges {
  display: flex;
  gap: 10rpx;
}

.badge {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  color: white;
  
  &.official {
    background-color: #007aff;
  }
  
  &.featured {
    background-color: #ff6b35;
  }
}

.script-description,
.script-features,
.script-usage,
.packages-section,
.payment-section {
  background: white;
  margin: 20rpx 0;
  padding: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.description-content,
.usage-content {
  line-height: 1.6;
  overflow-x: hidden; // 防止内容溢出
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.markdown-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  
  // 标题样式
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #333;
    margin: 20rpx 0 10rpx 0;
  }
  
  h1 { font-size: 36rpx; }
  h2 { font-size: 32rpx; }
  h3 { font-size: 28rpx; }
  h4, h5, h6 { font-size: 26rpx; }
  
  // 段落样式
  p {
    margin: 10rpx 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  // 列表样式
  ul, ol {
    margin: 10rpx 0;
    padding-left: 30rpx;
  }
  
  li {
    margin: 5rpx 0;
  }
  
  // 代码样式
  code {
    background: #f5f5f5;
    padding: 4rpx 8rpx;
    border-radius: 4rpx;
    font-family: 'Courier New', monospace;
    font-size: 24rpx;
  }
  
  pre {
    background: #f5f5f5;
    padding: 20rpx;
    border-radius: 8rpx;
    overflow-x: auto;
    margin: 10rpx 0;
    
    code {
      background: none;
      padding: 0;
    }
  }
  
  // 引用样式
  blockquote {
    border-left: 4rpx solid #007aff;
    padding-left: 20rpx;
    margin: 10rpx 0;
    color: #666;
    font-style: italic;
  }
  
  // 链接样式
  a {
    color: #007aff;
    text-decoration: none;
  }
  
  // Markdown链接样式
  .markdown-link {
    color: #007aff !important;
    text-decoration: underline !important;
    cursor: pointer !important;
    padding: 2rpx 4rpx;
    border-radius: 4rpx;
    transition: background-color 0.2s;
  }
  
  .markdown-link:hover {
    background-color: rgba(0, 122, 255, 0.1);
  }
  
  // 表格样式
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 10rpx 0;
    font-size: 24rpx;
  }
  
  th, td {
    border: 1rpx solid #ddd;
    padding: 10rpx;
    text-align: left;
  }
  
  th {
    background: #f5f5f5;
    font-weight: 600;
  }
  
  // 图片样式 - 关键修复
  img {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 8rpx;
    margin: 10rpx 0;
    display: block;
    box-sizing: border-box;
  }
  
  // 防止图片溢出容器
  * {
    max-width: 100%;
    box-sizing: border-box;
  }
  
  // 处理长文本
  * {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 10rpx;
}

.feature-dot {
  color: #007aff;
  font-weight: bold;
  margin-top: 2rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

.packages-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.package-item {
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  padding: 20rpx;
  position: relative;
  transition: all 0.3s;
  
  &.selected {
    border-color: #007aff;
    background-color: #f0f8ff;
  }
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.package-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.package-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b35;
}

.package-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.package-limit {
  font-size: 24rpx;
  color: #666;
}



.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 40rpx;
  border-top: 1rpx solid #eee;
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.purchase-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: 2rpx solid #007aff;
  background-color: #007aff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:disabled {
    background-color: #f5f5f5;
    color: #999;
    border: 2rpx solid #eee;
  }
}

.login-tip {
  background: white;
  margin: 20rpx;
  padding: 40rpx;
  border-radius: 16rpx;
  text-align: center;
}

.tip-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.login-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  font-size: 28rpx;
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 60rpx;
  border-radius: 20rpx;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #333;
}

.pay-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pay-dialog-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  font-size: 36rpx;
  color: #333;
  min-width: 400rpx;
}
.pay-dialog-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  display: block;
}
.pay-dialog-desc {
  font-size: 28rpx;
  margin-bottom: 40rpx;
  display: block;
}
.pay-dialog-btn {
  background: #007aff;
  color: #fff;
  font-size: 36rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  width: 80%;
}
.pay-dialog-cancel {
  background: #eee;
  color: #333;
  font-size: 32rpx;
  border-radius: 8rpx;
  width: 80%;
}
</style>
