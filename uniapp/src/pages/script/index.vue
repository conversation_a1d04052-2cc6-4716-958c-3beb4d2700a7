<template>
  <view class="script-page">
    <!-- 功能模块区域 -->
    <view class="modules-section">
      <view class="modules-grid">
        <!-- 配置组模块 -->
        <view class="module-item disabled">
          <view class="module-icon config-icon">
            <uni-icons type="gear" size="24" color="#ccc" />
          </view>
          <text class="module-name">配置组</text>
          <text class="module-status">开发中</text>
        </view>

        <!-- 订单模块 -->
        <view class="module-item disabled">
          <view class="module-icon order-icon">
            <uni-icons type="receipt" size="24" color="#ccc" />
          </view>
          <text class="module-name">订单</text>
          <text class="module-status">开发中</text>
        </view>

        <!-- 预留模块1 -->
        <view class="module-item disabled">
          <view class="module-icon module1-icon">
            <uni-icons type="chart" size="24" color="#ccc" />
          </view>
          <text class="module-name">统计</text>
          <text class="module-status">开发中</text>
        </view>

        <!-- 预留模块2 -->
        <view class="module-item disabled">
          <view class="module-icon module2-icon">
            <uni-icons type="wrench" size="24" color="#ccc" />
          </view>
          <text class="module-name">工具</text>
          <text class="module-status">开发中</text>
        </view>
      </view>
    </view>

    <!-- 脚本列表内容 -->
    <view class="script-content">
      <view class="script-list">
        <view v-if="loading" class="loading">
          <view class="loading-icon"></view>
          <text>加载中...</text>
        </view>
        
        <view v-else-if="scripts.length === 0" class="empty-state">
          <uni-icons type="file" size="48" color="#ccc" />
          <text class="empty-text">暂无脚本</text>
          <text class="empty-desc">脚本将通过其他方式添加</text>
        </view>
        
        <view v-else class="script-grid">
          <view 
            v-for="script in scripts" 
            :key="script.id"
            class="script-card"
            :class="{ 'expired': isExpired(script.expired_at) }"
          >
            <!-- 卡片头部 -->
            <view class="card-header">
              <view class="script-info">
                <view class="script-icon-wrapper">
                  <view class="script-icon" @click="goToScriptMarketDetail(script)">
                    <text class="icon-text">{{ getScriptIcon(script.name) }}</text>
                  </view>
                  <view 
                    v-if="script.hasUpdate" 
                    class="update-indicator"
                  >
                    <uni-icons type="refresh" size="10" color="white" />
                  </view>
                </view>
                
                <view class="script-details">
                  <view class="script-title">
                    <text class="script-name">{{ script.name }}</text>
                    <view 
                      v-if="script.hasUpdate" 
                      class="update-scroll-indicator"
                    >
                      <view class="scroll-bar">
                        <view class="scroll-dot"></view>
                      </view>
                      <text class="update-hint">有新版本</text>
                    </view>
                    <view 
                      v-if="script.hasUpdate" 
                      class="update-badge"
                      @click.stop="updateScript(script)"
                    >
                      <uni-icons type="refresh" size="12" color="#ff6b35" />
                      <text class="update-text">更新脚本</text>
                    </view>
                  </view>
                  
                  <view class="script-meta">
                    <view class="meta-tags">
                      <text class="meta-tag version">v{{ script.version || '1.0.0' }}</text>
                      <text v-if="script.package_name" class="meta-tag package">{{ script.package_name }}</text>
                      <text 
                        class="meta-tag expiry" 
                        :class="{ 'expired': isExpired(script.expired_at) }"
                      >
                        {{ formatExpiry(script.expired_at) }}
                      </text>
                      <text class="meta-tag binding">
                        已绑 {{ script.binding_count || 0 }}/{{ script.max_devices === -1 ? '∞' : (script.max_devices || 1) }}
                      </text>
                    </view>
                  </view>
                </view>
              </view>
              

            </view>
            
            <!-- 卡片操作区 -->
            <view class="card-actions">
              <view class="action-buttons">
                <!-- 运行操作菜单 -->
                <view class="run-menu">
                  <button 
                    class="action-btn run-btn"
                    :class="{ 'disabled': isExpired(script.expired_at) }"
                    @click.stop="toggleRunDropdown(script, $event)"
                    :disabled="isExpired(script.expired_at)"
                  >
                    <text class="btn-text">运行</text>
                  </button>
                  
                  <!-- 运行下拉菜单 -->
                  <view 
                    v-if="script.showRunDropdown" 
                    class="dropdown-menu"
                    @click.stop=""
                  >
                    <view v-if="getScriptBoundOnlineDevices(script).length === 0" class="menu-item disabled">
                      <uni-icons type="info" size="16" color="#999" />
                      <text class="menu-text">
                        {{ !script.deviceBindings || script.deviceBindings.length === 0 ? '暂无绑定设备' : '绑定的设备都不在线' }}
                      </text>
                    </view>
                    <view v-else>
                      <!-- 所有设备选项 -->
                      <view 
                        class="menu-item"
                        @click="runScriptOnAllDevices(script)"
                      >
                        <uni-icons type="play" size="16" color="#52c41a" />
                        <text class="menu-text">所有在线 ({{ getScriptBoundOnlineDevices(script).length }})</text>
                      </view>
                      <!-- 单个设备列表 -->
                      <view 
                        v-for="device in getScriptBoundOnlineDevices(script)" 
                        :key="device.device_id"
                        class="menu-item"
                        @click="runScriptOnDevice(script, device)"
                      >
                        <uni-icons type="phone" size="16" color="#1890ff" />
                        <text class="menu-text">{{ device.name || device.device_id }}</text>
                      </view>
                    </view>
                  </view>
                </view>
                
                <!-- 停止操作菜单 -->
                <view class="stop-menu">
                  <button 
                    class="action-btn stop-btn"
                    @click.stop="toggleStopDropdown(script, $event)"
                  >
                    <text class="btn-text">停止</text>
                  </button>
                  
                  <!-- 停止下拉菜单 -->
                  <view 
                    v-if="script.showStopDropdown" 
                    class="dropdown-menu"
                    @click.stop=""
                  >
                    <view v-if="getScriptBoundOnlineDevices(script).length === 0" class="menu-item disabled">
                      <uni-icons type="info" size="16" color="#999" />
                      <text class="menu-text">
                        {{ !script.deviceBindings || script.deviceBindings.length === 0 ? '暂无绑定设备' : '绑定的设备都不在线' }}
                      </text>
                    </view>
                    <view v-else>
                      <!-- 所有设备选项 -->
                      <view 
                        class="menu-item"
                        @click="stopScriptOnAllDevices(script)"
                      >
                        <uni-icons type="stop" size="16" color="#ff4d4f" />
                        <text class="menu-text">所有在线 ({{ getScriptBoundOnlineDevices(script).length }})</text>
                      </view>
                      <!-- 单个设备列表 -->
                      <view 
                        v-for="device in getScriptBoundOnlineDevices(script)" 
                        :key="device.device_id"
                        class="menu-item"
                        @click="stopScriptOnDevice(script, device)"
                      >
                        <uni-icons type="phone" size="16" color="#1890ff" />
                        <text class="menu-text">{{ device.name || device.device_id }}</text>
                      </view>
                    </view>
                  </view>
                </view>
                
                <!-- 配置操作菜单 -->
                <view class="config-menu">
                  <button 
                    class="action-btn config-btn"
                    @click.stop="configScript(script)"
                  >
                    <text class="btn-text">配置</text>
                  </button>
                </view>
                
                <!-- 绑定操作菜单 -->
                <view class="bind-menu">
                  <button 
                    class="action-btn bind-btn"
                    @click.stop="showDeviceBindingDialog(script)"
                  >
                    <text class="btn-text">绑定</text>
                  </button>
                </view>
                
                <!-- 更多操作菜单 -->
                <view class="more-menu">
                  <button 
                    class="action-btn more-btn"
                    @click.stop="toggleMoreDropdown(script, $event)"
                  >
                    <uni-icons type="more" size="18" color="white" />
                  </button>
                  
                  <!-- 下拉菜单 -->
                  <view 
                    v-if="script.showMoreDropdown" 
                    class="dropdown-menu"
                    @click.stop=""
                  >
                    <view class="menu-item" @click="showUpgradeDialog(script)">
                      <uni-icons type="up" size="16" color="#1890ff" />
                      <text class="menu-text">升级套餐</text>
                    </view>
                    
                    <view 
                      class="menu-item" 
                      @click="showRenewalDialog(script)"
                      :class="{ 'disabled': isExpired(script.expired_at) }"
                    >
                      <uni-icons type="refresh" size="16" color="#fa8c16" />
                      <text class="menu-text">续费</text>
                    </view>
                    
                    <view class="menu-item danger" @click="showDeleteScriptDialog(script)">
                      <uni-icons type="trash" size="16" color="#f5222d" />
                      <text class="menu-text">删除</text>
                    </view>
                  </view>
                </view>
              </view>
              

            </view>
          </view>
        </view>
      </view>
    </view>



    <!-- 脚本配置弹窗 -->
    <uni-popup ref="configPopup" type="center" :mask-click="true">
      <view class="config-modal card">
        <!-- 固定头部 -->
        <view class="modal-header fixed-header">
          <text class="modal-title">
            {{ selectedScript?.defaultConfig ? '编辑配置' : '新建配置' }}
          </text>
                          <uni-icons type="close" size="20" color="#999" class="modal-close" @click="closeConfig" />
        </view>
        
        <!-- 可滚动内容区域 -->
        <view class="modal-content-scrollable" v-if="selectedScript">
          <ScriptConfigFormOptimized
            ref="configFormRef"
            :script-id="selectedScript.id"
            :config-id="selectedScript.defaultConfig?.id"
            :initial-values="selectedScript.defaultConfig?.config_values"
            @submit="handleConfigSubmit"
            @cancel="closeConfig"
          />
        </view>
        
        <!-- 固定底部按钮 -->
        <view class="modal-footer fixed-footer">
          <button @click="closeConfig" class="btn btn-cancel">取消</button>
          <button @click="submitConfig" class="btn btn-primary">
            {{ selectedScript?.defaultConfig ? '更新配置' : '保存配置' }}
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 编辑配置弹窗 -->
    <uni-popup ref="editConfigPopup" type="center" :mask-click="true">
      <view class="config-modal card">
        <!-- 固定头部 -->
        <view class="modal-header fixed-header">
          <text class="modal-title">编辑配置</text>
                          <uni-icons type="close" size="20" color="#999" class="modal-close" @click="closeEditConfig" />
        </view>
        
        <!-- 可滚动内容区域 -->
        <view class="modal-content-scrollable" v-if="editingConfig">
          <ScriptConfigFormOptimized
            ref="editConfigFormRef"
            :script-id="editingConfig.script_id"
            :config-id="editingConfig.id"
            :initial-values="editingConfig.config_values"
            @submit="handleEditConfigSubmit"
            @cancel="closeEditConfig"
          />
        </view>
        
        <!-- 固定底部按钮 -->
        <view class="modal-footer fixed-footer">
          <button @click="closeEditConfig" class="btn btn-cancel">取消</button>
          <button @click="submitEditConfig" class="btn btn-primary">保存配置</button>
        </view>
      </view>
    </uni-popup>



    <!-- 设备绑定弹窗 -->
    <uni-popup ref="bindingPopup" type="center" :mask-click="true">
      <view class="binding-modal card">
        <view class="modal-header">
          <text class="modal-title">设备绑定管理</text>
                          <uni-icons type="close" size="20" color="#999" class="modal-close" @click="closeBindingDialog" />
        </view>
        
        <view class="modal-content" v-if="selectedScript">
          
          <!-- 可绑定设备列表 -->
          <view class="available-devices">
            <view class="section-title">
              可绑定设备
              <view class="section-actions">
                <button 
                  class="btn btn-small"
                  @click="toggleSelectAllAvailableDevices"
                >
                  {{ isAllAvailableDevicesSelected ? '取消全选' : '全选' }}
                </button>
                <button 
                  v-if="selectedDevices.length > 0"
                  class="btn btn-small btn-primary"
                  @click="confirmBindSelectedDevices"
                  :disabled="bindingLoading"
                >
                  {{ bindingLoading ? '绑定中...' : `绑定 (${selectedDevices.length})` }}
                </button>
              </view>
            </view>
            <view v-if="availableDevices.length === 0" class="no-devices">
              <text>暂无可绑定的设备</text>
            </view>
            <view v-else class="device-list compact">
              <view 
                v-for="device in availableDevices" 
                :key="device.id"
                class="device-item compact"
                :class="{ 'selected': selectedDevices.includes(device.id) }"
                @click="toggleDeviceSelection(device)"
              >
                <view class="device-checkbox">
                  <text class="checkbox" :class="{ 'checked': selectedDevices.includes(device.id) }">
                    {{ selectedDevices.includes(device.id) ? '✓' : '' }}
                  </text>
                </view>
                <view class="device-info">
                  <text class="device-name">{{ device.name || device.device_id }}</text>
                </view>
                <view class="device-status-wrapper">
                  <text class="device-status" :class="`status-${device.status}`">
                    {{ getDeviceStatusText(device.status) }}
                  </text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 已绑定设备列表 -->
          <view v-if="selectedScript.deviceBindings && selectedScript.deviceBindings.length > 0" class="bound-devices">
            <view class="section-title">
              已绑定设备 ({{ selectedScript.deviceBindings.length }}/{{ selectedScript.max_devices || 1 }})
              <view class="section-actions">
                <button 
                  class="btn btn-small"
                  @click="toggleSelectAllBoundDevices"
                >
                  {{ isAllBoundDevicesSelected ? '取消全选' : '全选' }}
                </button>
                <button 
                  v-if="selectedBoundDevices.length > 0"
                  class="btn btn-small btn-danger"
                  @click="confirmUnbindSelectedDevices"
                  :disabled="unbindingLoading"
                >
                  {{ unbindingLoading ? '解绑中...' : `解绑 (${selectedBoundDevices.length})` }}
                </button>
              </view>
            </view>
            <view class="device-list compact">
              <view 
                v-for="binding in selectedScript.deviceBindings" 
                :key="binding.id"
                class="device-item bound compact"
                :class="{ 'selected': selectedBoundDevices.includes(binding.device_id) }"
                @click="toggleBoundDeviceSelection(binding)"
              >
                <view class="device-checkbox">
                  <text class="checkbox" :class="{ 'checked': selectedBoundDevices.includes(binding.device_id) }">
                    {{ selectedBoundDevices.includes(binding.device_id) ? '✓' : '' }}
                  </text>
                </view>
                <view class="device-info">
                  <text class="device-name">{{ binding.device?.name || binding.device?.device_id || '未知设备' }}</text>
                </view>
                <view class="device-status-wrapper">
                  <text class="device-status" :class="`status-${binding.device?.status || 0}`">
                    {{ getDeviceStatusText(binding.device?.status || 0) }}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <view class="footer-tips">
            <text class="tip-text">
              💡 点击设备进行选择，然后点击"绑定"按钮确认操作
            </text>
          </view>
          <button @click="closeBindingDialog" class="btn btn-cancel">关闭</button>
        </view>
      </view>
    </uni-popup>

    <!-- 升级套餐弹窗 -->
    <uni-popup ref="upgradePopup" type="center" :mask-click="true">
      <view class="upgrade-modal card">
        <view class="modal-header">
          <text class="modal-title">升级套餐</text>
                          <uni-icons type="close" size="20" color="#999" class="modal-close" @click="closeUpgradeDialog" />
        </view>
        
        <view class="modal-content" v-if="selectedScript">
          <view class="current-package-info">
            <text class="section-title">当前套餐</text>
            <view class="current-package">
              <text class="package-name">{{ selectedScript.package_name || '免费版本' }}</text>
              <text class="package-details">
                设备限制: {{ selectedScript.max_devices === -1 ? '∞' : selectedScript.max_devices }} | 
                到期时间: {{ formatExpiry(selectedScript.expired_at) }}
              </text>
            </view>
          </view>
          
          <view class="upgrade-packages">
            <text class="section-title">可升级套餐</text>
            <view v-if="upgradeLoading" class="loading-packages">
              <text>加载套餐中...</text>
            </view>
            <view v-else-if="upgradePackages.length === 0" class="no-upgrade-packages">
              <text>暂无可升级套餐</text>
            </view>
            <view v-else class="package-list">
              <view 
                v-for="pkg in upgradePackages" 
                :key="pkg.id"
                class="package-item"
                :class="{ 'selected': selectedPackage?.id === pkg.id }"
                @click="selectPackage(pkg)"
              >
                <view class="package-header">
                  <text class="package-name">{{ pkg.name }}</text>
                  <text class="package-price">{{ pkg.upgrade_price === 0 ? '免费' : `¥${formatPrice(pkg.upgrade_price)}` }}</text>
                </view>
                <text class="package-description">{{ pkg.description }}</text>
                <view class="package-features">
                  <text class="feature">设备限制: {{ pkg.device_limit === -1 ? '∞' : pkg.device_limit }}</text>
                  <text class="feature">使用时长: {{ pkg.duration }}天</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button @click="closeUpgradeDialog" class="btn btn-cancel">取消</button>
          <button 
            v-if="selectedPackage"
            @click="confirmUpgrade"
            class="btn btn-upgrade"
            :disabled="upgradeOrderLoading"
          >
            <text v-if="upgradeOrderLoading" class="loading-icon">⏳</text>
            <text>{{ upgradeOrderLoading ? '创建订单中...' : (selectedPackage.upgrade_price === 0 ? '免费升级' : `立即升级 ¥${formatPrice(selectedPackage.upgrade_price)}`) }}</text>
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 脚本续费弹窗 -->
    <uni-popup ref="renewalPopup" type="center" :mask-click="true">
      <view class="renewal-modal card">
        <view class="modal-header">
          <text class="modal-title">脚本续费</text>
                          <uni-icons type="close" size="20" color="#999" class="modal-close" @click="closeRenewalDialog" />
        </view>
        
        <view class="modal-content" v-if="selectedScript">
          <view class="script-card">
            <view class="script-header">
              <view class="script-icon"></view>
              <view class="script-info">
                <text class="script-name">{{ selectedScript.name }}</text>
                <text class="script-status">即将到期</text>
              </view>
            </view>
          </view>
          
          <view class="renewal-details">
            <view class="detail-item">
              <view class="detail-label">
                <text class="label-icon"></text>
                <text>当前到期时间</text>
              </view>
              <text class="detail-value">{{ formatExpiry(selectedScript.expired_at) }}</text>
            </view>
            
            <view class="detail-item">
              <view class="detail-label">
                <text class="label-icon"></text>
                <text>续费价格</text>
              </view>
              <text class="detail-value price">¥{{ selectedScript.renewal_price || getRenewalPrice(selectedScript) }}</text>
            </view>
            
            <view class="detail-item">
              <view class="detail-label">
                <text class="label-icon"></text>
                <text>续费时长</text>
              </view>
              <text class="detail-value">30天</text>
            </view>
          </view>
          
          <view class="renewal-benefits">
            <view class="benefit-item">
              <text class="benefit-icon"></text>
              <text class="benefit-text">延长30天使用时间</text>
            </view>
            <view class="benefit-item">
              <text class="benefit-icon"></text>
              <text class="benefit-text">保持所有配置不变</text>
            </view>
            <view class="benefit-item">
              <text class="benefit-icon"></text>
              <text class="benefit-text">立即生效，无需重新配置</text>
            </view>
          </view>
        </view>
        
        <view class="modal-footer" v-if="selectedScript">
          <button @click="closeRenewalDialog" class="btn btn-cancel">取消</button>
          <button 
            @click="confirmRenewal"
            class="btn btn-renewal"
            :disabled="renewalOrderLoading"
          >
            <text v-if="renewalOrderLoading" class="loading-icon"></text>
            <text>{{ renewalOrderLoading ? '创建订单中...' : `立即续费 ¥${selectedScript.renewal_price || getRenewalPrice(selectedScript)}` }}</text>
          </button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 删除脚本确认弹窗 -->
    <uni-popup ref="deleteScriptPopup" type="center" :mask-click="true">
      <view class="delete-modal card">
        <view class="modal-header">
          <text class="modal-title">删除脚本</text>
                          <uni-icons type="close" size="20" color="#999" class="modal-close" @click="closeDeleteScriptDialog" />
        </view>
        
        <view class="modal-content" v-if="selectedScript">
          <view class="delete-warning">
            <text class="warning-icon">⚠️</text>
            <text class="warning-text">确定要删除脚本"{{ selectedScript.name }}"吗？</text>
            <text class="warning-desc">删除后脚本将无法使用，但相关配置和设备绑定信息会保留。</text>
          </view>
        </view>
        
        <view class="modal-footer">
          <button @click="closeDeleteScriptDialog" class="btn btn-cancel">取消</button>
          <button @click="confirmDeleteScript" class="btn btn-danger">确认删除</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 脚本更新弹窗 -->
    <uni-popup ref="updateScriptPopup" type="center" :mask-click="true">
      <view class="update-modal card">
        <view class="modal-header">
          <text class="modal-title">脚本更新</text>
                          <uni-icons type="close" size="20" color="#999" class="modal-close" @click="closeUpdateDialog" />
        </view>
        
        <view class="modal-content" v-if="updateInfo">
          <view class="update-info">
            <view class="version-comparison">
              <view class="version-item">
                <text class="version-label">当前版本</text>
                <text class="version-value current">v{{ updateInfo.current_version }}</text>
              </view>
                              <view class="version-arrow">
                  <uni-icons type="right" size="20" color="#666" />
                </view>
              <view class="version-item">
                <text class="version-label">最新版本</text>
                <text class="version-value latest">v{{ updateInfo.latest_version }}</text>
              </view>
            </view>
            
            <view class="update-content">
              <text class="update-title">更新内容</text>
              <text class="update-description">{{ updateInfo.update_content || '暂无更新说明' }}</text>
            </view>
            
            <view class="update-time">
              <text class="time-label">更新时间</text>
              <text class="time-value">{{ formatDateTime(updateInfo.update_time) }}</text>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button @click="closeUpdateDialog" class="btn btn-cancel">取消</button>
          <button 
            @click="confirmUpdateScript"
            class="btn btn-update"
            :disabled="updateLoading"
          >
            <text v-if="updateLoading" class="loading-icon">⏳</text>
            <text>{{ updateLoading ? '更新中...' : '立即更新' }}</text>
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { onShow } from '@dcloudio/uni-app'
// 已移除 Icon 导入，使用 uni-icons 替代
import { scriptAPI, deviceAPI, paymentAPI } from '@/utils/api'
import { getWebSocketService } from '@/utils/websocket'
import ScriptConfigForm from '@/components/ScriptConfigForm.vue'
import ScriptConfigFormOptimized from '@/components/ScriptConfigFormOptimized.vue'
import { useScriptStore } from '@/stores/script'
import { useDeviceStore } from '@/stores/device'
import { formatExpiry, isExpired } from '@/utils/date'

// 使用store
const scriptStore = useScriptStore()
const deviceStore = useDeviceStore()

// 响应式数据
const scripts = ref<any[]>([])
const scriptConfigs = ref<any[]>([])
const loading = ref(false)
const configLoading = ref(false)
const selectedScript = ref<any>(null)
const editingConfig = ref<any>(null)
const lastRefreshTime = ref(0)
const configPopup = ref<any>(null)
const editConfigPopup = ref<any>(null)
const configFormRef = ref<any>(null)
const editConfigFormRef = ref<any>(null)

// 设备相关数据（已移除不必要的预加载）

// 设备绑定相关数据
const bindingPopup = ref<any>(null)
const availableDevices = ref<any[]>([])
const bindingLoading = ref(false)
const selectedDevices = ref<number[]>([])
const selectedBoundDevices = ref<number[]>([])
const unbindingLoading = ref(false)

// 升级套餐相关数据
const upgradePopup = ref<any>(null)
const upgradePackages = ref<any[]>([])
const selectedPackage = ref<any>(null)
const upgradeLoading = ref(false)
const upgradeOrderLoading = ref(false)

// 续费相关数据
const renewalPopup = ref<any>(null)
const renewalOrderLoading = ref(false)

// 删除脚本相关数据
const deleteScriptPopup = ref<any>(null)
const deleteScriptLoading = ref(false)

// 更新脚本相关数据
const updateScriptPopup = ref<any>(null)
const updateInfo = ref<any>(null)
const updateLoading = ref(false)

// 生成UUID函数
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

// 获取续费价格
const getRenewalPrice = (script: any) => {
  // 使用后端返回的续费价格，如果没有则使用默认价格
  if (!script) return 0.01
  return script.renewal_price || 0.01
}

// 模块点击方法
const goToConfigGroup = () => {
  uni.showToast({
    title: '配置组功能暂未开发',
    icon: 'none'
  })
}

const goToOrders = () => {
  uni.navigateTo({
    url: '/pages/payment/orders'
  })
}

const goToModule1 = () => {
  uni.showToast({
    title: '统计功能开发中',
    icon: 'none'
  })
}

const goToModule2 = () => {
  uni.showToast({
    title: '工具功能开发中',
    icon: 'none'
  })
}

const loadScripts = async () => {
  loading.value = true
  try {
    const response = await scriptAPI.getUserScripts()
    if (response.code === 0) {
      const scriptList = (response.data.scripts || []).map(script => ({
        ...script,
        isRunning: false, // 添加运行状态
        showRunDropdown: false, // 添加运行下拉显示状态
        showStopDropdown: false, // 添加停止下拉显示状态
        showMoreDropdown: false, // 添加更多下拉显示状态
        deviceBindings: [], // 初始化设备绑定列表
        bindingsLoaded: false, // 标记绑定信息是否已加载
        hasUpdate: false // 初始化更新状态
      }))
      
      scripts.value = scriptList
      
      // 异步加载每个脚本的设备绑定信息
      loadScriptsBindings(scriptList)
      
      // 异步检查每个脚本的更新状态
      checkScriptsUpdate(scriptList)
    }
  } catch (error) {
    console.error('加载脚本失败:', error)
    uni.showToast({
      title: '加载脚本失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 异步加载所有脚本的绑定信息
const loadScriptsBindings = async (scriptList: any[]) => {
  // 并发加载所有脚本的绑定信息，但不影响主界面显示
  const bindingPromises = scriptList.map(async (script) => {
    try {
      await loadScriptBindings(script)
  } catch (error) {
      console.error(`加载脚本 ${script.id} 的绑定信息失败:`, error)
      // 单个脚本绑定信息加载失败不影响其他脚本
  }
  })
  
  // 等待所有绑定信息加载完成（或失败）
  await Promise.allSettled(bindingPromises)
  console.log('所有脚本绑定信息加载完成')
}

// 异步检查所有脚本的更新状态
const checkScriptsUpdate = async (scriptList: any[]) => {
  // 并发检查所有脚本的更新状态，但不影响主界面显示
  const updatePromises = scriptList.map(async (script) => {
    try {
      const response = await scriptAPI.checkScriptUpdate(script.id)
      if (response.code === 0 && response.data.has_update) {
        // 找到对应的脚本并更新状态
        const targetScript = scripts.value.find(s => s.id === script.id)
        if (targetScript) {
          targetScript.hasUpdate = true
        }
      }
    } catch (error) {
      console.error(`检查脚本 ${script.id} 更新状态失败:`, error)
      // 单个脚本更新检查失败不影响其他脚本
    }
  })
  
  // 等待所有更新检查完成（或失败）
  await Promise.allSettled(updatePromises)
  console.log('所有脚本更新状态检查完成')
}

// 设备列表加载函数已移除，因为脚本列表页面不需要预加载设备列表

const loadScriptConfigs = async () => {
  configLoading.value = true
  try {
    // 使用已加载的脚本列表，避免重复请求
    const currentScripts = scripts.value.length > 0 ? scripts.value : []
    
    if (currentScripts.length === 0) {
      // 如果没有脚本数据，先加载脚本
      await loadScripts()
    }
    
    // 为每个脚本获取配置
    const configPromises = (scripts.value || []).map(async (script: any) => {
      try {
        const configResponse = await scriptAPI.getUserScriptConfigs(script.id)
        let configs = []
        if (configResponse.code === 0) {
          configs = (configResponse.data.configs || []).map((config: any) => {
            // 解析设备信息
            let devices = []
            if (config.device_ids) {
              try {
                devices = JSON.parse(config.device_ids)
              } catch (e) {
                console.error('解析设备信息失败:', e)
                devices = []
              }
            }
            return {
              ...config,
              devices
            }
          })
        }
        return {
          script,
          configs
        }
      } catch (error) {
        console.error(`获取脚本 ${script.id} 配置失败:`, error)
        return {
          script,
          configs: []
        }
      }
    })
    
    const results = await Promise.all(configPromises)
    scriptConfigs.value = results.filter(item => item.configs.length > 0)
  } catch (error) {
    console.error('加载脚本配置失败:', error)
    uni.showToast({
      title: '加载配置失败',
      icon: 'none'
    })
  } finally {
    configLoading.value = false
  }
}

const getScriptIcon = (name: string) => {
  // 根据脚本名称返回对应的图标
  const icons = ['📱', '🤖', '⚡', '🔧', '📜', '🎯', '🚀', '💡'] // 保留emoji作为脚本图标，因为这是动态生成的
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
  return icons[hash % icons.length]
}

const getDeviceStatusText = (status: number) => {
  const statusMap: { [key: number]: string } = {
    0: '离线',
    1: '在线'
  }
  return statusMap[status] || '未知'
}

// 获取脚本绑定的在线设备列表
const getScriptBoundOnlineDevices = (script: any) => {
  if (!script.deviceBindings || script.deviceBindings.length === 0) {
    return []
  }
  
  // 使用script store的实时数据更新设备状态
  const onlineDevices = script.deviceBindings
    .filter(binding => {
      // 检查binding.device是否存在
      if (!binding.device) {
        return false
      }
      
      // 从script store获取最新的设备状态
      const storeBinding = scriptStore.getBindingByDeviceId(binding.device.device_id)
      if (storeBinding) {
        // 更新本地绑定数据的状态
        binding.device.status = storeBinding.device.status
        return storeBinding.device.status === 1
      }
      // 如果store中没有找到，使用本地数据
      return binding.device.status === 1
    })
    .map(binding => binding.device)
    .filter(device => device !== null && device !== undefined) // 过滤掉null和undefined
  
  return onlineDevices
}

// 切换运行下拉菜单
const toggleRunDropdown = async (script: any, event?: any) => {
  // 关闭其他脚本的下拉菜单
  scripts.value.forEach(s => {
    if (s.id !== script.id) {
      s.showRunDropdown = false
      s.showStopDropdown = false
      s.showMoreDropdown = false
    }
  })
  
  // 关闭当前脚本的其他下拉菜单
  script.showStopDropdown = false
  script.showMoreDropdown = false
  
  // 切换当前脚本的运行下拉菜单
  script.showRunDropdown = !script.showRunDropdown
}

// 切换停止下拉菜单
const toggleStopDropdown = async (script: any, event?: any) => {
  // 关闭其他脚本的下拉菜单
  scripts.value.forEach(s => {
    if (s.id !== script.id) {
      s.showRunDropdown = false
      s.showStopDropdown = false
      s.showMoreDropdown = false
    }
  })
  
  // 关闭当前脚本的其他下拉菜单
  script.showRunDropdown = false
  script.showMoreDropdown = false
  
  // 切换当前脚本的停止下拉菜单
  script.showStopDropdown = !script.showStopDropdown
}

// 切换更多下拉菜单
const toggleMoreDropdown = async (script: any, event?: any) => {
  // 关闭其他脚本的下拉菜单
  scripts.value.forEach(s => {
    if (s.id !== script.id) {
      s.showRunDropdown = false
      s.showStopDropdown = false
      s.showMoreDropdown = false
    }
  })
  
  // 关闭当前脚本的其他下拉菜单
  script.showRunDropdown = false
  script.showStopDropdown = false
  
  // 切换当前脚本的更多下拉菜单
  script.showMoreDropdown = !script.showMoreDropdown
}

// 设置下拉菜单位置 - 使用CSS定位，避免DOM操作
const setDropdownPosition = (script: any, event: any) => {
  // 在 uni-app 中，我们使用 CSS 定位来处理下拉菜单
  // 下拉菜单会相对于按钮自动定位
  // 这里不需要手动计算位置，让 CSS 来处理
}

// 在指定设备上运行脚本
const runScriptOnDevice = async (script: any, device: any) => {
  // 关闭下拉菜单
  script.showRunDropdown = false
  
  // 检查脚本是否已过期
  if (isExpired(script.expired_at)) {
    uni.showToast({
      title: '脚本已过期，无法运行',
      icon: 'none'
    })
    return
  }

  try {
    // 显示加载提示
    uni.showLoading({
      title: '正在运行脚本...'
    })
    
    // 优先使用WebSocket方式（更快速）
    const wsService = getWebSocketService()
    if (wsService && wsService.isConnected()) {
      // 通过WebSocket发送运行脚本消息
      wsService.send({
        type: 'SCRIPT_RUN',
        seq: generateUUID(),
        timestamp: Date.now(),
        payload: {
          script_id: script.id.toString(),
          device_ids: [device.device_id || device.id],
          action: 'run'
        }
      })
      
      uni.hideLoading()
      
      // 更新脚本状态
      script.isRunning = true
      script.runningDevice = device
      
      uni.showToast({
        title: '脚本运行命令已发送',
        icon: 'success'
      })
    } else {
      // WebSocket不可用时，降级到HTTP方式
      const response = await scriptAPI.runScript({
        script_id: script.id,
        device_ids: [device.device_id || device.id]
      })
      
      uni.hideLoading()
      
      if (response.code === 0) {
        // 更新脚本状态
        script.isRunning = true
        script.runningDevice = device
        
        uni.showToast({
          title: '脚本运行命令已发送',
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: response.message || '运行脚本失败',
          icon: 'none'
        })
      }
    }
    
  } catch (error) {
    uni.hideLoading()
    console.error('运行脚本失败:', error)
    uni.showToast({
      title: '运行脚本失败',
      icon: 'none'
    })
  }
}

// 在所有设备上运行脚本
const runScriptOnAllDevices = async (script: any) => {
  // 关闭下拉菜单
  script.showRunDropdown = false
  
  // 检查脚本是否已过期
  if (isExpired(script.expired_at)) {
    uni.showToast({
      title: '脚本已过期，无法运行',
      icon: 'none'
      })
      return
    }
    
  const onlineDevices = getScriptBoundOnlineDevices(script)
  
  if (onlineDevices.length === 0) {
    uni.showToast({
      title: '暂无在线设备',
      icon: 'none'
    })
    return
  }

  try {
    // 显示加载提示
    uni.showLoading({
      title: `正在${onlineDevices.length}个设备上运行脚本...`
    })
    
    // 获取所有在线设备的ID
    const deviceIds = onlineDevices.map(device => device.device_id || device.id)
    
    // 优先使用WebSocket方式（更快速）
    const wsService = getWebSocketService()
    if (wsService && wsService.isConnected()) {
      // 通过WebSocket发送运行脚本消息
      wsService.send({
        type: 'SCRIPT_RUN',
        seq: generateUUID(),
        timestamp: Date.now(),
        payload: {
          script_id: script.id.toString(),
          device_ids: deviceIds,
          action: 'run'
        }
      })
      
      uni.hideLoading()
      
      // 更新脚本状态
      script.isRunning = true
      script.runningDevice = null // 表示在所有设备上运行
      
      uni.showToast({
        title: `脚本运行命令已发送到${deviceIds.length}个设备`,
        icon: 'success'
      })
    } else {
      // WebSocket不可用时，降级到HTTP方式
      const response = await scriptAPI.runScript({
        script_id: script.id,
        device_ids: deviceIds
      })
      
      uni.hideLoading()
      
      if (response.code === 0) {
        // 更新脚本状态
        script.isRunning = true
        script.runningDevice = null // 表示在所有设备上运行
        
        uni.showToast({
          title: `脚本运行命令已发送到${response.data.total_devices}个设备`,
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: response.message || '运行脚本失败',
          icon: 'none'
        })
      }
    }
    
  } catch (error) {
    uni.hideLoading()
    console.error('在所有设备上运行脚本失败:', error)
    uni.showToast({
      title: '运行脚本失败',
      icon: 'none'
    })
  }
}

// 停止脚本
const stopScript = async (script: any) => {
  try {
    // 获取绑定的在线设备
    const onlineDevices = getScriptBoundOnlineDevices(script)
    
    if (onlineDevices.length === 0) {
      uni.showToast({
        title: '暂无在线设备',
        icon: 'none'
      })
      return
    }

    // 显示加载提示
    uni.showLoading({
      title: '正在停止脚本...'
    })
    
    // 获取所有在线设备的ID
    const deviceIds = onlineDevices.map(device => device.device_id || device.id)
    
    // 优先使用WebSocket方式（更快速）
    const wsService = getWebSocketService()
    if (wsService && wsService.isConnected()) {
      // 通过WebSocket发送停止脚本消息
      wsService.send({
        type: 'SCRIPT_STOP',
        seq: generateUUID(),
        timestamp: Date.now(),
        payload: {
          script_id: script.id.toString(),
          device_ids: deviceIds,
          action: 'stop'
        }
      })
      
      uni.hideLoading()
      
      // 更新脚本状态
      script.isRunning = false
      script.runningDevice = null
      
      uni.showToast({
        title: `脚本停止命令已发送到${deviceIds.length}个设备`,
        icon: 'success'
      })
    } else {
      // WebSocket不可用时，降级到HTTP方式
      const response = await scriptAPI.stopScript({
        script_id: script.id,
        device_ids: deviceIds
      })
      
      uni.hideLoading()
      
      if (response.code === 0) {
        // 更新脚本状态
        script.isRunning = false
        script.runningDevice = null
        
        uni.showToast({
          title: `脚本停止命令已发送到${response.data.total_devices}个设备`,
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: response.message || '停止脚本失败',
          icon: 'none'
        })
      }
    }
    
  } catch (error) {
    uni.hideLoading()
    console.error('停止脚本失败:', error)
    uni.showToast({
      title: '停止脚本失败',
      icon: 'none'
    })
  }
}

// 在指定设备上停止脚本
const stopScriptOnDevice = async (script: any, device: any) => {
  // 关闭下拉菜单
  script.showStopDropdown = false
  
  try {
    // 显示加载提示
    uni.showLoading({
      title: '正在停止脚本...'
    })
    
    // 优先使用WebSocket方式（更快速）
    const wsService = getWebSocketService()
    if (wsService && wsService.isConnected()) {
      // 通过WebSocket发送停止脚本消息
      wsService.send({
        type: 'SCRIPT_STOP',
        seq: generateUUID(),
        timestamp: Date.now(),
        payload: {
          script_id: script.id.toString(),
          device_ids: [device.device_id || device.id],
          action: 'stop'
        }
      })
      
      uni.hideLoading()
      
      // 更新脚本状态
      script.isRunning = false
      script.runningDevice = null
      
      uni.showToast({
        title: `脚本停止命令已发送到${device.name || device.device_id}`,
        icon: 'success'
      })
    } else {
      // WebSocket不可用时，降级到HTTP方式
      const response = await scriptAPI.stopScript({
        script_id: script.id,
        device_ids: [device.device_id || device.id]
      })
      
      uni.hideLoading()
      
      if (response.code === 0) {
        // 更新脚本状态
        script.isRunning = false
        script.runningDevice = null
        
        uni.showToast({
          title: `脚本停止命令已发送到${device.name || device.device_id}`,
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: response.message || '停止脚本失败',
          icon: 'none'
        })
      }
    }
    
  } catch (error) {
    uni.hideLoading()
    console.error('停止脚本失败:', error)
    uni.showToast({
      title: '停止脚本失败',
      icon: 'none'
    })
  }
}

// 在所有设备上停止脚本
const stopScriptOnAllDevices = async (script: any) => {
  // 关闭下拉菜单
  script.showStopDropdown = false
  
  const onlineDevices = getScriptBoundOnlineDevices(script)
  
  if (onlineDevices.length === 0) {
    uni.showToast({
      title: '暂无在线设备',
      icon: 'none'
    })
    return
  }

  try {
    // 显示加载提示
    uni.showLoading({
      title: `正在${onlineDevices.length}个设备上停止脚本...`
    })
    
    // 获取所有在线设备的ID
    const deviceIds = onlineDevices.map(device => device.device_id || device.id)
    
    // 优先使用WebSocket方式（更快速）
    const wsService = getWebSocketService()
    if (wsService && wsService.isConnected()) {
      // 通过WebSocket发送停止脚本消息
      wsService.send({
        type: 'SCRIPT_STOP',
        seq: generateUUID(),
        timestamp: Date.now(),
        payload: {
          script_id: script.id.toString(),
          device_ids: deviceIds,
          action: 'stop'
        }
      })
      
      uni.hideLoading()
      
      // 更新脚本状态
      script.isRunning = false
      script.runningDevice = null
      
      uni.showToast({
        title: `脚本停止命令已发送到${deviceIds.length}个设备`,
        icon: 'success'
      })
    } else {
      // WebSocket不可用时，降级到HTTP方式
      const response = await scriptAPI.stopScript({
        script_id: script.id,
        device_ids: deviceIds
      })
      
      uni.hideLoading()
      
      if (response.code === 0) {
        // 更新脚本状态
        script.isRunning = false
        script.runningDevice = null
        
        uni.showToast({
          title: `脚本停止命令已发送到${response.data.total_devices}个设备`,
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: response.message || '停止脚本失败',
          icon: 'none'
        })
      }
    }
    
  } catch (error) {
    uni.hideLoading()
    console.error('在所有设备上停止脚本失败:', error)
    uni.showToast({
      title: '停止脚本失败',
      icon: 'none'
    })
  }
}

// 检查设备绑定状态
const checkDeviceBinding = async (script: any, device: any) => {
  // 这里需要调用API检查设备是否绑定到脚本
  // 暂时返回true，实际应该调用后端API
  return true
}

// 导航到设备绑定页面
const navigateToDeviceBinding = (script: any, device: any) => {
  // 跳转到设备绑定页面
  uni.navigateTo({
    url: `/pages/device/bind?scriptId=${script.id}&deviceId=${device.device_id}&deviceName=${encodeURIComponent(device.name || device.device_id)}`
  })
}

// 在设备上执行脚本
const executeScriptOnDevice = async (script: any, device: any) => {
  // 这里调用实际的脚本执行API
  script.isRunning = true
  script.runningDevice = device
  
  uni.showToast({
    title: `脚本正在${device.name || device.device_id}上运行`,
    icon: 'success'
  })
  
  // 这里应该调用后端API启动脚本执行
  // await scriptAPI.executeScript({ scriptId: script.id, deviceId: device.device_id })
}



// 旧的runScript方法保留，但现在主要用于兼容性
const runScript = async (script: any) => {
  // 检查脚本是否已过期
  if (isExpired(script.expired_at)) {
    uni.showToast({
      title: '脚本已过期，无法运行',
      icon: 'none'
    })
    return
  }

  // 如果有在线设备，显示设备选择
  await loadDevices()
  if (onlineDevices.value.length > 0) {
    toggleRunDropdown(script)
    return
  }
  
  // 没有在线设备
  uni.showToast({
    title: '暂无在线设备',
    icon: 'none'
  })
}

const configScript = async (script: any) => {
  // 关闭下拉菜单
  script.showRunDropdown = false
  script.showStopDropdown = false
  
  try {
    selectedScript.value = { ...script } // 复制一份避免直接修改原数据
    
    // 获取该脚本的用户配置列表
    console.log('获取脚本配置列表, script_id:', script.id)
    const configResponse = await scriptAPI.getUserScriptConfigs(script.id)
    
    if (configResponse.code === 0) {
      const configs = configResponse.data || []
      console.log('获取到的配置列表:', configs)
      
      if (configs.length > 0) {
        // 如果有已保存的配置，使用最新的配置作为初始值
        const latestConfig = configs[0] // 按创建时间降序，第一个是最新的
        console.log('使用最新配置作为初始值:', latestConfig)
        
        // 解析配置值
        let configValues = {}
        if (latestConfig.config_values) {
          try {
            if (typeof latestConfig.config_values === 'string') {
              configValues = JSON.parse(latestConfig.config_values)
            } else {
              configValues = latestConfig.config_values
            }
          } catch (e) {
            console.error('解析配置值失败:', e)
            configValues = {}
          }
        }
        
        // 设置默认配置
        selectedScript.value.defaultConfig = {
          id: latestConfig.id,
          config_name: latestConfig.config_name,
          config_values: configValues
        }
      } else {
        console.log('该脚本暂无已保存的配置，将使用脚本的默认配置值')
        // 尝试使用脚本的默认配置值
        let defaultConfigValues = {}
        if (script.default_values) {
          try {
            if (typeof script.default_values === 'string') {
              defaultConfigValues = JSON.parse(script.default_values)
            } else {
              defaultConfigValues = script.default_values
            }
            console.log('使用脚本默认配置值:', defaultConfigValues)
          } catch (e) {
            console.error('解析脚本默认配置值失败:', e)
            defaultConfigValues = {}
          }
        } else {
          // 如果没有default_values，尝试从config schema中提取默认值
          if (script.config) {
            try {
              const configSchema = typeof script.config === 'string' ? JSON.parse(script.config) : script.config
              if (configSchema && configSchema.properties) {
                for (const [fieldName, field] of Object.entries(configSchema.properties)) {
                  if (field.default !== undefined) {
                    defaultConfigValues[fieldName] = field.default
                  }
                }
                console.log('从配置结构中提取的默认值:', defaultConfigValues)
              }
            } catch (e) {
              console.error('从配置结构中提取默认值失败:', e)
            }
          }
        }
        
        selectedScript.value.defaultConfig = {
          id: null,
          config_name: '默认配置',
          config_values: defaultConfigValues
        }
      }
      
      // 打开配置弹窗
      // @ts-ignore
      if (configPopup.value) {
        configPopup.value.open()
      }
    } else {
      console.error('获取配置列表失败:', configResponse.message)
      uni.showToast({
        title: '获取配置失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取脚本配置失败:', error)
    uni.showToast({
      title: '获取配置失败',
      icon: 'none'
    })
  }
}

const addConfigForScript = (script: any) => {
  selectedScript.value = { ...script }
  // @ts-ignore
  if (configPopup.value) {
    configPopup.value.open()
  }
}

const editConfig = async (config: any) => {
  try {
    // 调用详情接口获取完整的配置数据
    console.log('获取配置详情, ID:', config.id)
    const response = await scriptAPI.getUserScriptConfig(config.id)
    
    if (response.code === 0) {
      const fullConfig = response.data
      console.log('配置详情接口返回:', fullConfig)
      
      // 解析配置值
      let configValues = {}
      if (fullConfig.config_values) {
        try {
          // 如果 config_values 是字符串，需要解析
          if (typeof fullConfig.config_values === 'string') {
            configValues = JSON.parse(fullConfig.config_values)
          } else {
            // 如果已经是对象，直接使用
            configValues = fullConfig.config_values
          }
        } catch (e) {
          console.error('解析配置值失败:', e, 'config_values:', fullConfig.config_values)
          configValues = {}
        }
      }
      
      console.log('编辑配置 - 解析后的配置值:', configValues)
      
      editingConfig.value = { 
        ...fullConfig,
        config_values: configValues
      }
      
      // @ts-ignore
      if (editConfigPopup.value) {
        editConfigPopup.value.open()
      }
    } else {
      console.error('获取配置详情失败:', response.message)
      uni.showToast({
        title: '获取配置详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取配置详情失败:', error)
    uni.showToast({
      title: '获取配置详情失败',
      icon: 'none'
    })
  }
}

const deleteConfig = async (config: any) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除配置"${config.config_name}"吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await scriptAPI.deleteUserScriptConfig(config.id)
          if (response.code === 0) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
            loadScriptConfigs() // 重新加载配置列表
          }
        } catch (error) {
          console.error('删除配置失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

const linkDeviceToConfig = (config: any) => {
  // 跳转到设备关联页面
  uni.navigateTo({
    url: `/pages/device/link-config?configId=${config.id}&configName=${encodeURIComponent(config.config_name)}`
  })
}

const closeConfig = () => {
  selectedScript.value = null
  // @ts-ignore
  if (configPopup.value) {
    configPopup.value.close()
  }
}

const closeEditConfig = () => {
  editingConfig.value = null
  // @ts-ignore
  if (editConfigPopup.value) {
    editConfigPopup.value.close()
  }
}

const handleConfigSubmit = async (configData: any) => {
  try {
    let response
    
    console.log('处理配置提交:', configData)
    
    // 根据是否有config_id决定是创建还是更新
    if (configData.config_id) {
      // 更新现有配置
      console.log('更新现有配置, ID:', configData.config_id)
      response = await scriptAPI.updateUserScriptConfig(configData.config_id, configData)
    } else {
      // 创建新配置
      console.log('创建新配置')
      response = await scriptAPI.createScriptConfig(configData)
    }
    
    if (response.code === 0) {
      uni.showToast({
        title: configData.config_id ? '配置更新成功' : '配置保存成功',
        icon: 'success'
      })
      closeConfig()
      // 重新加载配置列表（用于统计）
      loadScriptConfigs()
    } else {
      uni.showToast({
        title: response.message || '保存失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('保存脚本配置失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}

const handleEditConfigSubmit = async (configData: any) => {
  try {
    // 调用更新配置的API
    const response = await scriptAPI.updateUserScriptConfig(editingConfig.value.id, configData)
    if (response.code === 0) {
      uni.showToast({
        title: '配置更新成功',
        icon: 'success'
      })
      closeEditConfig()
      loadScriptConfigs() // 重新加载配置列表
    }
  } catch (error) {
    console.error('更新脚本配置失败:', error)
    uni.showToast({
      title: '更新失败',
      icon: 'none'
    })
  }
}

const submitConfig = async () => {
  if (selectedScript.value) {
    // 获取配置表单组件实例
    if (configFormRef.value && configFormRef.value.getConfigData) {
      const configData = configFormRef.value.getConfigData()
      console.log('提交配置数据:', configData)
      await handleConfigSubmit(configData)
    } else {
      console.error('配置表单组件实例未找到')
      uni.showToast({
        title: '配置表单组件未找到',
        icon: 'none'
      })
    }
  }
}

const submitEditConfig = async () => {
  if (editingConfig.value) {
    // 获取配置表单组件实例
    if (editConfigFormRef.value && editConfigFormRef.value.getConfigData) {
      const configData = editConfigFormRef.value.getConfigData()
      console.log('提交编辑配置数据:', configData)
      await handleEditConfigSubmit(configData)
    } else {
      console.error('编辑配置表单组件实例未找到')
      uni.showToast({
        title: '配置表单组件未找到',
        icon: 'none'
      })
    }
  }
}

const goToScriptMarketDetail = (script: any) => {
  // 跳转到脚本市场详情页面
  uni.navigateTo({
    url: `/pages/script-market/detail?id=${script.id}&name=${encodeURIComponent(script.name)}`
  })
}

const formatTime = (time: string) => {
  if (!time) return '未知'
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`
  return date.toLocaleDateString()
}

// 使用统一的日期格式化函数

// 初始化数据加载
const initData = async () => {
  const now = Date.now()
  // 避免短时间内重复加载
  if (now - lastRefreshTime.value < 2000) {
    return
  }
  lastRefreshTime.value = now
  
  // 初始化WebSocket监听
  scriptStore.initWebSocketListener()
  
  // 只加载脚本列表
  await loadScripts()
}

// 点击其他地方关闭下拉菜单
const closeAllDropdowns = () => {
  scripts.value.forEach(script => {
    script.showRunDropdown = false
  })
}

// 设备绑定相关方法
const showDeviceBindingDialog = async (script: any) => {
  // 关闭下拉菜单
  script.showRunDropdown = false
  script.showStopDropdown = false
  
  selectedScript.value = script
  
  // 只有在绑定信息未加载时才重新加载
  if (!script.bindingsLoaded) {
    await loadScriptBindings(script)
  }
  
  // 加载可绑定的设备列表（排除已绑定的设备）
  await loadAvailableDevices(script)
  
  bindingPopup.value?.open()
}

// 处理下拉菜单中的绑定按钮点击
const handleBindDeviceClick = async (script: any) => {
  // 打开绑定弹窗（showDeviceBindingDialog内部会关闭下拉菜单）
  await showDeviceBindingDialog(script)
}

const closeBindingDialog = () => {
  bindingPopup.value?.close()
  selectedScript.value = null
  availableDevices.value = []
  selectedDevices.value = []
}

const loadScriptBindings = async (script: any) => {
  try {
    // 使用script store的方法获取绑定信息
    const result = await scriptStore.fetchScriptBindings(script.id)
    
    if (result.success) {
      // 从script store获取最新的绑定数据
      script.deviceBindings = scriptStore.scriptBindings
      script.bindingsLoaded = true // 标记已加载
    } else {
      // 如果store方法失败，回退到直接API调用
    const response = await scriptAPI.getScriptBindings(script.id)
    if (response.code === 0) {
      script.deviceBindings = response.data.bindings || []
      script.bindingsLoaded = true // 标记已加载
      }
    }
  } catch (error) {
    console.error('加载脚本绑定失败:', error)
    uni.showToast({
      title: '加载绑定信息失败',
      icon: 'error'
    })
  }
}

const loadAvailableDevices = async (script: any) => {
  try {
    // 使用device store获取设备列表
    const result = await deviceStore.fetchDevices([1, 2]) // 只获取在线和忙碌状态的设备
    if (result.success) {
      const allDevices = deviceStore.devices
      
      // 过滤掉已绑定的设备
      const boundDeviceIds = (script.deviceBindings || [])
        .filter((binding: any) => binding.device && binding.device.device_id) // 过滤掉没有device或device_id的绑定
        .map((binding: any) => binding.device.device_id)
      availableDevices.value = allDevices.filter((device: any) => !boundDeviceIds.includes(device.device_id))
    } else {
      // 如果store方法失败，回退到直接API调用
      const response = await deviceAPI.getUserDevices([1, 2])
      if (response.code === 0) {
        const allDevices = response.data || []
      const boundDeviceIds = (script.deviceBindings || []).map((binding: any) => binding.device_id)
      availableDevices.value = allDevices.filter((device: any) => !boundDeviceIds.includes(device.id))
      }
    }
  } catch (error) {
    console.error('加载可用设备失败:', error)
  }
}

// 设备选择相关方法
const toggleDeviceSelection = (device: any) => {
  const deviceId = device.id
  const index = selectedDevices.value.indexOf(deviceId)
  
  if (index > -1) {
    // 如果已选中，则取消选择
    selectedDevices.value.splice(index, 1)
  } else {
    // 检查是否超过设备限制
    const currentBindings = selectedScript.value?.deviceBindings || []
    const maxDevices = selectedScript.value?.max_devices || 1
    const canSelectCount = maxDevices - currentBindings.length
    
    if (selectedDevices.value.length >= canSelectCount) {
      uni.showToast({
        title: `最多还能选择${canSelectCount}个设备`,
        icon: 'error'
      })
      return
    }
    
    // 添加到选择列表
    selectedDevices.value.push(deviceId)
  }
}



// 直接更新本地解绑状态，避免重新调用接口
const updateLocalUnbindState = (script: any, unboundBinding: any) => {
  // 1. 从弹窗中的脚本绑定信息移除
  if (script.deviceBindings) {
    script.deviceBindings = script.deviceBindings.filter(
      binding => binding.device_id !== unboundBinding.device_id
    )
  }
  
  // 2. 从主列表中对应脚本的绑定信息移除
  const scriptInList = scripts.value.find(s => s.id === script.id)
  if (scriptInList && scriptInList.deviceBindings) {
    scriptInList.deviceBindings = scriptInList.deviceBindings.filter(
      binding => binding.device_id !== unboundBinding.device_id
    )
    // 更新绑定数量 - 直接使用实际的绑定数量
    scriptInList.binding_count = scriptInList.deviceBindings.length
    // 确保绑定信息已加载标记为true
    scriptInList.bindingsLoaded = true
  }
  
  // 3. 将设备添加回可用设备列表（如果设备信息存在）
  if (unboundBinding.device) {
    // 检查设备是否已经在可用列表中，避免重复
    const existsInAvailable = availableDevices.value.some(
      device => device.id === unboundBinding.device.id
    )
    if (!existsInAvailable) {
      availableDevices.value.push(unboundBinding.device)
    }
  }
}



// 直接更新本地绑定状态，避免重新调用接口
const updateLocalBindingState = (script: any, boundDevices: any[]) => {
  // 1. 创建新的绑定记录
  const newBindings = boundDevices.map(device => ({
    id: Date.now() + Math.random(), // 临时ID，实际应用中服务端会返回真实ID
    user_script_id: script.id,
    device_id: device.id,
    device: device,
    status: 1, // 正常状态
    last_active: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }))
  
  // 2. 更新弹窗中的脚本绑定信息
  if (!script.deviceBindings) {
    script.deviceBindings = []
  }
  
  // 过滤掉已存在的绑定，避免重复添加
  const existingDeviceIds = script.deviceBindings.map((binding: any) => binding.device_id)
  const uniqueNewBindings = newBindings.filter(binding => 
    !existingDeviceIds.includes(binding.device_id)
  )
  
  script.deviceBindings.push(...uniqueNewBindings)
  
  // 3. 更新主列表中对应脚本的绑定信息
  const scriptInList = scripts.value.find(s => s.id === script.id)
  if (scriptInList) {
    if (!scriptInList.deviceBindings) {
      scriptInList.deviceBindings = []
    }
    
    // 同样过滤掉已存在的绑定
    const listExistingDeviceIds = scriptInList.deviceBindings.map((binding: any) => binding.device_id)
    const listUniqueNewBindings = newBindings.filter(binding => 
      !listExistingDeviceIds.includes(binding.device_id)
    )
    
    scriptInList.deviceBindings.push(...listUniqueNewBindings)
    // 更新绑定数量 - 直接使用实际的绑定数量，而不是增量计算
    scriptInList.binding_count = scriptInList.deviceBindings.length
    // 确保绑定信息已加载标记为true
    scriptInList.bindingsLoaded = true
  }
  
  // 4. 从可用设备列表中移除已绑定的设备
  const boundDeviceIds = boundDevices.map(device => device.id)
  availableDevices.value = availableDevices.value.filter(device => 
    !boundDeviceIds.includes(device.id)
  )
}

const confirmBindSelectedDevices = async () => {
  if (selectedDevices.value.length === 0) {
    uni.showToast({
      title: '请先选择要绑定的设备',
      icon: 'none'
    })
    return
  }
  
  const script = selectedScript.value
  if (!script) return
  
  // 前端验证：检查是否会超过设备限制
  const currentBindings = script.deviceBindings || []
  const maxDevices = script.max_devices || 1
  const willExceedLimit = currentBindings.length + selectedDevices.value.length > maxDevices
  
  if (willExceedLimit) {
    uni.showToast({
      title: `设备数量将超过限制（${maxDevices}个）`,
      icon: 'error'
    })
    return
  }
  
  bindingLoading.value = true
  
  try {
    // 获取选中的设备信息，用于前端状态更新
    const selectedDeviceInfos = availableDevices.value.filter(device => 
      selectedDevices.value.includes(device.id)
    )
    
    // 使用批量绑定API
    const response = await scriptAPI.bindDevice({
      user_script_id: script.id,
      device_ids: selectedDevices.value
    })
    
    if (response.code === 0) {
      // 显示批量绑定结果
      const results = response.data.results || {}
      const successCount = Object.values(results).filter(result => result === '绑定成功').length
      const alreadyBoundCount = Object.values(results).filter(result => result === '已绑定').length
      
      let message = `批量绑定完成`
      if (successCount > 0) {
        message += `，成功绑定${successCount}个设备`
      }
      if (alreadyBoundCount > 0) {
        message += `，${alreadyBoundCount}个设备已绑定`
      }
      
      uni.showToast({
        title: message,
        icon: 'success'
      })
      
      // 只更新真正绑定成功的设备状态
      const successfullyBoundDevices = selectedDeviceInfos.filter(device => 
        results[device.id] === '绑定成功'
      )
      
      if (successfullyBoundDevices.length > 0) {
        updateLocalBindingState(script, successfullyBoundDevices)
      }
      
      // 清除选择状态
      selectedDevices.value = []
      
      // 重新加载绑定信息以确保数据同步
      await loadScriptBindings(script)
      await loadAvailableDevices(script)
    } else {
      throw new Error(response.message || '绑定失败')
    }
    
  } catch (error) {
    console.error('绑定设备失败:', error)
    uni.showToast({
      title: error.message || '绑定失败',
      icon: 'error'
    })
  } finally {
    bindingLoading.value = false
  }
}

const bindDeviceToScript = async (script: any, devices: any[]) => {
  if (!devices || devices.length === 0) {
    uni.showToast({
      title: '请选择要绑定的设备',
      icon: 'none'
    })
    return
  }

  // 检查是否将达到设备限制
  const currentBindings = script.deviceBindings || []
  const maxDevices = script.max_devices || 1
  
  // 过滤掉已绑定的设备
  const newDevices = devices.filter(device => 
    !currentBindings.some(binding => binding.device_id === device.id)
  )
  
  if (newDevices.length === 0) {
    uni.showToast({
      title: '所选设备已全部绑定',
      icon: 'none'
    })
    return
  }
  
  if (maxDevices > 0 && currentBindings.length + newDevices.length > maxDevices) {
    uni.showToast({
      title: `最多只能绑定${maxDevices}个设备，当前已绑定${currentBindings.length}个`,
      icon: 'none'
    })
    return
  }
  
  bindingLoading.value = true
  try {
    const response = await scriptAPI.bindDevice({
      user_script_id: script.id,
      device_ids: newDevices.map(device => device.id)
    })
    
    if (response.code === 0) {
      // 显示批量绑定结果
      const results = response.data.results || {}
      const successCount = Object.values(results).filter(result => result === '绑定成功').length
      const alreadyBoundCount = Object.values(results).filter(result => result === '已绑定').length
      
      let message = `批量绑定完成`
      if (successCount > 0) {
        message += `，成功绑定${successCount}个设备`
      }
      if (alreadyBoundCount > 0) {
        message += `，${alreadyBoundCount}个设备已绑定`
      }
      
      uni.showToast({
        title: message,
        icon: 'success'
      })
      
      // 重新加载绑定信息
      await loadScriptBindings(script)
      await loadAvailableDevices(script)
      
      // 更新脚本列表中的绑定信息
      await initData()
    } else {
      uni.showToast({
        title: response.message || '绑定失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('绑定设备失败:', error)
    uni.showToast({
      title: '绑定失败',
      icon: 'error'
    })
  } finally {
    bindingLoading.value = false
  }
}

const unbindDevice = async (script: any, binding: any) => {
  uni.showModal({
    title: '确认解绑',
    content: `确定要解绑设备"${binding.device?.name || binding.device?.device_id || '未知设备'}"吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await scriptAPI.unbindDevice({
            user_script_id: script.id,
            device_ids: [binding.device_id]
          })
          
          if (response.code === 0) {
            uni.showToast({
              title: '解绑成功',
              icon: 'success'
            })
            
            // 直接更新本地状态，避免重新调用接口
            updateLocalUnbindState(script, binding)
            
            // 运行下拉菜单使用 script.deviceBindings，无需重新加载设备列表
          } else {
            uni.showToast({
              title: response.message || '解绑失败',
              icon: 'error'
            })
          }
        } catch (error) {
          console.error('解绑设备失败:', error)
          uni.showToast({
            title: '解绑失败',
            icon: 'error'
          })
        }
      }
    }
  })
}



// 全局点击事件处理
const handleGlobalClick = (event: any) => {
  // 检查点击是否在运行、停止或更多菜单内
  const isRunMenuClick = event.target.closest('.run-menu')
  const isStopMenuClick = event.target.closest('.stop-menu')
  const isMoreMenuClick = event.target.closest('.more-menu')
  
  // 检查点击是否在弹窗内
  const isPopupClick = event.target.closest('.uni-popup') || event.target.closest('.config-modal') || event.target.closest('.binding-modal')
  
  // 如果点击不在任何菜单内且不在弹窗内，则关闭所有下拉菜单
  if (!isRunMenuClick && !isStopMenuClick && !isMoreMenuClick && !isPopupClick) {
    // 关闭所有脚本的下拉菜单
    scripts.value.forEach(script => {
      script.showRunDropdown = false
      script.showStopDropdown = false
      script.showMoreDropdown = false
    })
  }
}

// 生命周期
onMounted(() => {
  initData()
  
  // 添加全局点击事件来关闭下拉菜单
  document.addEventListener('click', handleGlobalClick)
  
  // 监听WebSocket状态更新，强制刷新页面数据
  const handleWebSocketUpdate = () => {
    // 当WebSocket状态更新时，强制刷新所有已加载的脚本绑定数据
    scripts.value.forEach(script => {
      if (script.bindingsLoaded) {
        // 触发响应式更新
        script.deviceBindings = [...script.deviceBindings]
      }
    })
  }
  
  // 监听script store的状态变化
  scriptStore.$subscribe(() => {
    handleWebSocketUpdate()
  })
})

onUnmounted(() => {
  // 移除全局点击事件监听器
  document.removeEventListener('click', handleGlobalClick)
})

// 页面显示时刷新数据
onShow(() => {
  // 只有在页面重新显示且距离上次刷新超过5秒时才刷新
  const now = Date.now()
  if (now - lastRefreshTime.value > 5000) {
    initData()
  }
})

const toggleSelectAllBoundDevices = () => {
  if (isAllBoundDevicesSelected.value) {
    // 如果已全选，则取消全选
    selectedBoundDevices.value = []
  } else {
    // 如果未全选，则全选
    selectedBoundDevices.value = selectedScript.value.deviceBindings.map(binding => binding.device_id)
  }
}

const confirmUnbindSelectedDevices = async () => {
  if (selectedBoundDevices.value.length === 0) {
    uni.showToast({
      title: '请先选择要解绑的设备',
      icon: 'none'
    })
    return
  }
  
  const script = selectedScript.value
  if (!script) return
  
  unbindingLoading.value = true
  
  try {
    // 使用批量解绑API
    const response = await scriptAPI.unbindDevice({
      user_script_id: script.id,
      device_ids: selectedBoundDevices.value
    })
    
    if (response.code === 0) {
      // 显示批量解绑结果
      const results = response.data.results || {}
      const successCount = Object.values(results).filter(result => result === '解绑成功').length
      const alreadyUnboundCount = Object.values(results).filter(result => result === '已解绑').length
      
      let message = `批量解绑完成`
      if (successCount > 0) {
        message += `，成功解绑${successCount}个设备`
      }
      if (alreadyUnboundCount > 0) {
        message += `，${alreadyUnboundCount}个设备已解绑`
      }
      
      uni.showToast({
        title: message,
        icon: 'success'
      })
      
      // 清除选择状态
      selectedBoundDevices.value = []
      
      // 重新加载绑定信息以确保数据同步
      await loadScriptBindings(script)
      await loadAvailableDevices(script)
    } else {
      throw new Error(response.message || '解绑失败')
    }
    
  } catch (error) {
    console.error('解绑设备失败:', error)
    uni.showToast({
      title: error.message || '解绑失败',
      icon: 'error'
    })
  } finally {
    unbindingLoading.value = false
  }
}

const toggleBoundDeviceSelection = (binding: any) => {
  const bindingId = binding.device_id
  const index = selectedBoundDevices.value.indexOf(bindingId)
  
  if (index > -1) {
    // 如果已选中，则取消选择
    selectedBoundDevices.value.splice(index, 1)
  } else {
    // 添加到选择列表
    selectedBoundDevices.value.push(bindingId)
  }
}

const isAllBoundDevicesSelected = computed(() => {
  return selectedScript.value.deviceBindings.length === selectedBoundDevices.value.length
})

const toggleSelectAllAvailableDevices = () => {
  if (isAllAvailableDevicesSelected.value) {
    // 如果已全选，则取消全选
    selectedDevices.value = []
  } else {
    // 如果未全选，则全选
    selectedDevices.value = availableDevices.value.map(device => device.id)
  }
}

const isAllAvailableDevicesSelected = computed(() => {
  return availableDevices.value.length === selectedDevices.value.length
})

// 升级套餐相关方法
const showUpgradeDialog = async (script: any) => {
  // 关闭下拉菜单
  script.showMoreDropdown = false
  
  selectedScript.value = script
  selectedPackage.value = null
  upgradePackages.value = []
  
  // 显示弹窗
  upgradePopup.value.open()
  
  // 加载升级套餐
  await loadUpgradePackages(script.id)
}

const loadUpgradePackages = async (scriptId: number) => {
  upgradeLoading.value = true
  try {
    const response = await paymentAPI.getUpgradePackages(scriptId)
    if (response.code === 0) {
      upgradePackages.value = response.data || []
    } else {
      uni.showToast({
        title: response.message || '加载升级套餐失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载升级套餐失败:', error)
    uni.showToast({
      title: '加载升级套餐失败',
      icon: 'none'
    })
  } finally {
    upgradeLoading.value = false
  }
}

const selectPackage = (pkg: any) => {
  selectedPackage.value = pkg
}

const confirmUpgrade = async () => {
  if (!selectedPackage.value || !selectedScript.value) {
    return
  }
  
  upgradeOrderLoading.value = true
  try {
    const response = await paymentAPI.createUpgradeOrder({
      script_id: selectedScript.value.id,
      package_id: selectedPackage.value.id,
      pay_type: 'alipay' // 默认使用支付宝
    })
    
    if (response.code === 0) {
      const order = response.data
      
      // 关闭升级弹窗
      closeUpgradeDialog()
      
      // 跳转到支付页面
      uni.navigateTo({
        url: `/pages/payment/index?order_no=${order.order_no}&pay_url=${encodeURIComponent(order.pay_url)}&amount=${order.amount}&pay_type=${order.pay_type}&created_at=${encodeURIComponent(order.created_at)}&type=upgrade`
      })
    } else {
      uni.showToast({
        title: response.message || '创建升级订单失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('创建升级订单失败:', error)
    uni.showToast({
      title: '创建升级订单失败',
      icon: 'none'
    })
  } finally {
    upgradeOrderLoading.value = false
  }
}

const closeUpgradeDialog = () => {
  upgradePopup.value.close()
  selectedScript.value = null
  selectedPackage.value = null
  upgradePackages.value = []
}

// 续费相关方法
const showRenewalDialog = async (script: any) => {
  // 关闭下拉菜单
  script.showMoreDropdown = false
  
  selectedScript.value = script
  
  // 获取套餐信息，确保续费价格准确
  if (script.package_id) {
    try {
      const packageResponse = await scriptAPI.getScriptPackage(script.package_id)
      if (packageResponse.code === 0) {
        const packageInfo = packageResponse.data
        // 更新脚本的续费价格
        selectedScript.value.renewal_price = packageInfo.price
      }
    } catch (error) {
      console.warn('获取套餐信息失败，使用默认价格:', error)
      // 使用默认价格
      selectedScript.value.renewal_price = getRenewalPrice(script)
    }
  } else {
    // 没有套餐ID，使用默认价格
    selectedScript.value.renewal_price = getRenewalPrice(script)
  }
  
  renewalPopup.value.open()
}

const confirmRenewal = async () => {
  if (!selectedScript.value) {
    return
  }
  
  renewalOrderLoading.value = true
  try {
    // 先获取套餐信息，确保价格准确
    let packageInfo = null
    if (selectedScript.value.package_id) {
      try {
        const packageResponse = await scriptAPI.getScriptPackage(selectedScript.value.package_id)
        if (packageResponse.code === 0) {
          packageInfo = packageResponse.data
        }
      } catch (error) {
        console.warn('获取套餐信息失败，使用默认价格:', error)
      }
    }
    
    const response = await deviceAPI.createScriptRenewalOrder({
      script_id: selectedScript.value.id,
      pay_type: 'alipay' // 默认使用支付宝
    })
    
    if (response.code === 0) {
      const order = response.data
      
      // 关闭续费弹窗
      closeRenewalDialog()
      
      // 跳转到支付页面
      uni.navigateTo({
        url: `/pages/payment/index?order_no=${order.order_no}&pay_url=${encodeURIComponent(order.pay_url)}&amount=${order.amount}&pay_type=${order.pay_type}&created_at=${encodeURIComponent(order.created_at)}&type=script_renewal`
      })
    } else {
      uni.showToast({
        title: response.message || '创建续费订单失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('创建续费订单失败:', error)
    uni.showToast({
      title: '创建续费订单失败',
      icon: 'none'
    })
  } finally {
    renewalOrderLoading.value = false
  }
}

const closeRenewalDialog = () => {
  renewalPopup.value.close()
  selectedScript.value = null
}

// 删除脚本相关方法
const showDeleteScriptDialog = (script: any) => {
  // 关闭下拉菜单
  script.showMoreDropdown = false
  
  selectedScript.value = script
  deleteScriptPopup.value.open()
}

const closeDeleteScriptDialog = () => {
  deleteScriptPopup.value.close()
  selectedScript.value = null
}

const confirmDeleteScript = async () => {
  if (!selectedScript.value) {
    return
  }
  
  deleteScriptLoading.value = true
  try {
    const response = await scriptAPI.deleteUserScript(selectedScript.value.id)
    
    if (response.code === 0) {
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      })
      
      // 关闭删除弹窗
      closeDeleteScriptDialog()
      
      // 重新加载脚本列表
      await loadScripts()
    } else {
      uni.showToast({
        title: response.message || '删除失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('删除脚本失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    })
  } finally {
    deleteScriptLoading.value = false
  }
}

// 格式化价格，保留2位小数
const formatPrice = (price: number | string) => {
  // 转换为数字
  const numPrice = typeof price === 'string' ? parseFloat(price) : price
  
  if (isNaN(numPrice)) {
    return '0.00'
  }
  
  return numPrice.toFixed(2)
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return '未知'
  const date = new Date(dateTimeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 更新脚本相关方法
const showUpdateDialog = async (script: any) => {
  // 关闭下拉菜单
  script.showMoreDropdown = false
  
  selectedScript.value = script
  updateLoading.value = true
  
  try {
    // 检查脚本更新
    const response = await scriptAPI.checkScriptUpdate(script.id)
    
    if (response.code === 0) {
      updateInfo.value = response.data
      updateScriptPopup.value.open()
    } else {
      uni.showToast({
        title: response.message || '检查更新失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('检查脚本更新失败:', error)
    uni.showToast({
      title: '检查更新失败',
      icon: 'none'
    })
  } finally {
    updateLoading.value = false
  }
}

const closeUpdateDialog = () => {
  updateScriptPopup.value.close()
  selectedScript.value = null
  updateInfo.value = null
}

const confirmUpdateScript = async () => {
  if (!selectedScript.value || !updateInfo.value) {
    return
  }
  
  updateLoading.value = true
  try {
    const response = await scriptAPI.updateScript(selectedScript.value.id)
    
    if (response.code === 0) {
      uni.showToast({
        title: '更新成功',
        icon: 'success'
      })
      
      // 关闭更新弹窗
      closeUpdateDialog()
      
      // 重新加载脚本列表
      await loadScripts()
    } else {
      uni.showToast({
        title: response.message || '更新失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('更新脚本失败:', error)
    uni.showToast({
      title: '更新失败',
      icon: 'none'
    })
  } finally {
    updateLoading.value = false
  }
}

// 直接更新脚本方法
const updateScript = async (script: any) => {
  try {
    uni.showLoading({
      title: '正在更新...'
    })
    
    const response = await scriptAPI.updateScript(script.id)
    
    uni.hideLoading()
    
    if (response.code === 0) {
      uni.showToast({
        title: '更新成功',
        icon: 'success'
      })
      
      // 重新加载脚本列表
      await loadScripts()
    } else {
      uni.showToast({
        title: response.message || '更新失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('更新脚本失败:', error)
    uni.showToast({
      title: '更新失败',
      icon: 'none'
    })
  }
}


</script>

<style lang="scss" scoped>
.script-page {
  min-height: 100vh;
  background: var(--background-color);
  padding-bottom: env(safe-area-inset-bottom);
}

// 模块区域样式
.modules-section {
  padding: 16px;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.module-item {
  background: var(--card-background);
  border-radius: 12px;
  padding: 16px 8px;
  text-align: center;
  box-shadow: 0 2px 8px var(--shadow-color);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px var(--shadow-color);
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
    
    &:hover {
      transform: none;
      box-shadow: 0 2px 8px var(--shadow-color);
    }
    
    &:active {
      transform: none;
    }
    
    .module-icon {
      background: #f5f5f5 !important;
    }
    
    .module-name {
      color: #999;
    }
    
    .module-status {
      display: block;
      font-size: 10px;
      color: #ff6b35;
      font-weight: 500;
      margin-top: 4px;
      background: #fff2e8;
      border-radius: 8px;
      padding: 2px 6px;
      width: fit-content;
      margin: 4px auto 0;
    }
  }
}

.module-icon {
  width: 40px;
  height: 40px;
  margin: 0 auto 8px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  
  &.config-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  &.order-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }
  
  &.module1-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
  
  &.module2-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }
}

.module-name {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.script-content {
  padding: 0 16px;
}

.script-list {
  // 保持原有样式
}

.script-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  @media (min-width: 1200px) {
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 24px;
  }
}

.script-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
  
  &.expired {
    opacity: 0.6;
    border-color: #ffccc7;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.script-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

  .script-details {
    flex: 1;
    min-width: 0;
    
    .script-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      
      .script-name {
        font-size: 18px;
        font-weight: 600;
        color: #1a1a1a;
        line-height: 1.2;
      }
      
      .update-scroll-indicator {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-left: 8px;
        
        .scroll-bar {
          width: 20px;
          height: 4px;
          background: #f0f0f0;
          border-radius: 2px;
          position: relative;
          overflow: hidden;
          
          .scroll-dot {
            width: 6px;
            height: 4px;
            background: linear-gradient(90deg, #ff6b35, #ff8c42);
            border-radius: 2px;
            position: absolute;
            left: 0;
            top: 0;
            animation: scrollMove 2s ease-in-out infinite;
          }
        }
        
        .update-hint {
          font-size: 11px;
          color: #ff6b35;
          font-weight: 500;
          white-space: nowrap;
        }
      }
      
      .update-badge {
        display: flex;
        align-items: center;
        gap: 4px;
        background: #fff2e8;
        border: 1px solid #ffd591;
        border-radius: 12px;
        padding: 2px 8px;
        font-size: 11px;
        font-weight: 500;
        color: #ff6b35;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
          background: #ffe7ba;
          border-color: #ffc53d;
        }
      }
    }
    
    .script-meta {
      .meta-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        
        .meta-tag {
          font-size: 11px;
          font-weight: 500;
          padding: 2px 8px;
          border-radius: 8px;
          border: 1px solid transparent;
          
          &.version {
            background: #e6f7ff;
            color: #1890ff;
            border-color: #91d5ff;
          }
          
          &.package {
            background: #f6ffed;
            color: #52c41a;
            border-color: #b7eb8f;
          }
          
          &.expiry {
            background: #fff7e6;
            color: #fa8c16;
            border-color: #ffd591;
            
            &.expired {
              background: #fff1f0;
              color: #ff4d4f;
              border-color: #ffa39e;
            }
          }
          
          &.binding {
            background: #f9f0ff;
            color: #722ed1;
            border-color: #d3adf7;
          }
        }
      }
    }
  }

  .script-icon-wrapper {
    position: relative;
    
    .script-icon {
      width: 48px;
      height: 48px;
      background: #1890ff;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background: #40a9ff;
        transform: scale(1.05);
      }
      
      .icon-text {
        color: white;
        font-size: 20px;
        font-weight: 600;
      }
    }
    
    .update-indicator {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 16px;
      height: 16px;
      background: #ff6b35;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid white;
    }
  }

.card-actions {
  margin-top: 16px;
  
  .action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: space-between;
    
    // 手机端响应式设计
    @media (max-width: 480px) {
      gap: 8px;
    }
    
    .action-btn {
      flex: 0 0 80px;
      width: 80px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      padding: 0 8px;
      border-radius: 6px;
      border: none;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      white-space: nowrap;
      
      // 手机端响应式设计
      @media (max-width: 480px) {
        flex: 0 0 70px;
        width: 70px;
        height: 32px;
        padding: 0 6px;
        font-size: 11px;
        gap: 2px;
        
        .btn-text {
          font-size: 11px;
        }
      }
      
      // 运行按钮样式
      &.run-btn {
        background: #52c41a;
        color: white;
        
        &:hover {
          background: #73d13d;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        &.disabled {
          background: #d9d9d9;
          color: #999;
          cursor: not-allowed;
          
          &:hover {
            transform: none;
            box-shadow: none;
          }
        }
      }
      
      // 停止按钮样式
      &.stop-btn {
        background: #ff4d4f;
        color: white;
        
        &:hover {
          background: #ff7875;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }
      }
      

      
      // 配置和绑定按钮样式
      &.config-btn,
      &.bind-btn {
        background: #1890ff;
        color: white;
        
        &:hover {
          background: #40a9ff;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
      }
      
      &:active {
        transform: translateY(0);
      }
      
      &.more-btn {
        flex: 0 0 36px;
        width: 36px;
        height: 36px;
        padding: 0;
        background: #1890ff;
        color: white;
        
        &:hover {
          background: #40a9ff;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        // 手机端响应式设计
        @media (max-width: 480px) {
          flex: 0 0 32px;
          width: 32px;
          height: 32px;
        }
      }
      
      .btn-text {
        font-size: 12px;
        font-weight: 500;
      }
    }
    
    // 配置菜单
    .config-menu {
      position: relative;
    }
    
    // 绑定菜单
    .bind-menu {
      position: relative;
    }
    
    // 更多菜单
    .more-menu {
      position: relative;
      
      .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        z-index: 1000;
        margin-top: 8px;
        min-width: 200px;
        max-height: 300px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        border: 1px solid #e0e0e0;
        overflow-y: auto;
        overflow-x: hidden;
        animation: slideDown 0.3s ease;
        
        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
          
          &:hover {
            background: #a8a8a8;
          }
        }
      }
      
      .menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:hover {
          background: #f8f9fa;
        }
        
        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          
          &:hover {
            background: transparent;
          }
        }
        
        &.danger {
          color: #ff4d4f;
          
          &:hover {
            background: #fff1f0;
          }
        }
        
        .menu-text {
          font-size: 14px;
          font-weight: 500;
          color: inherit;
        }
      }
    }
    
    // 运行菜单
    .run-menu {
      position: relative;
      
      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1000;
        margin-top: 8px;
        min-width: 200px;
        max-height: 300px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        border: 1px solid #e0e0e0;
        overflow-y: auto;
        overflow-x: hidden;
        animation: slideDown 0.3s ease;
        
        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
          
          &:hover {
            background: #a8a8a8;
          }
        }
      }
      
      .menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:hover {
          background: #f8f9fa;
        }
        
        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          
          &:hover {
            background: transparent;
          }
        }
        
        .menu-text {
          font-size: 14px;
          font-weight: 500;
          color: inherit;
        }
      }
    }
    
    // 停止菜单
    .stop-menu {
      position: relative;
      
      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1000;
        margin-top: 8px;
        min-width: 200px;
        max-height: 300px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        border: 1px solid #e0e0e0;
        overflow-y: auto;
        overflow-x: hidden;
        animation: slideDown 0.3s ease;
        
        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
          
          &:hover {
            background: #a8a8a8;
          }
        }
      }
      
      .menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:hover {
          background: #f8f9fa;
        }
        
        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          
          &:hover {
            background: transparent;
          }
        }
        
        .menu-text {
          font-size: 14px;
          font-weight: 500;
          color: inherit;
        }
      }
    }
    

  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.script-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.script-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 4px;
}

.script-version {
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--background-color);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  line-height: 1.2;
}

.script-package {
  font-size: 12px;
  color: #722ed1;
  background: #f9f0ff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #d3adf7;
  line-height: 1.2;
  font-weight: 500;
}

.script-expiry {
  font-size: 12px;
  color: var(--text-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  line-height: 1.2;
  
  &.expired {
    background: #fff2f0;
    border-color: #ffccc7;
    color: #ff4d4f;
  }
}

.script-devices {
  font-size: 12px;
  color: var(--text-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  background: #f0f5ff;
  border: 1px solid #adc6ff;
  line-height: 1.2;
}



// 运行下拉相关样式
.run-dropdown {
  position: relative;
  display: flex;
  overflow: visible;
}

.device-info {
  flex: 1;
  min-width: 0;
}

.device-name {
  font-size: 14px;
  font-weight: 500;
  color: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.device-status-wrapper {
  margin-left: 12px;
  flex-shrink: 0;
}

.device-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 0;
  line-height: 1.2;
  
  &.status-1 {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }
  
  &.status-2 {
    background: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
  }
  
  &.status-0 {
    background: #f5f5f5;
    color: #bfbfbf;
    border: 1px solid #d9d9d9;
  }
}

.device-arrow {
  font-size: 16px;
  color: var(--text-tertiary);
  margin-left: 8px;
}

.no-devices {
  padding: 16px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.no-devices-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.bind-device-tip-btn {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #40a9ff;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

.run-btn {
  background: var(--success-color);
  color: white;
    
    &:hover {
    background: #52c41a;
  }
}

.stop-btn {
  background: #ff4d4f;
  color: white;
  
  &:hover {
    background: #ff7875;
  }
}

.config-btn {
  background: var(--primary-color);
  color: white;
  
  &:hover {
    background: #1890ff;
  }
}

.edit-btn {
  background: var(--primary-color);
  color: white;
  
  &:hover {
    background: #1890ff;
  }
}

.delete-btn {
  background: #ff4d4f;
  color: white;
  
  &:hover {
    background: #ff7875;
  }
}

// 多配置页面样式
.config-list {
  // 保持原有样式
}

.config-tree {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.script-config-section {
  background: var(--card-background);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px var(--shadow-color);
}

.script-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
  
  .script-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--primary-color), #4096ff);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    flex-shrink: 0;
    
    .icon-text {
      font-size: 20px;
      color: white;
    }
  }
  
  .script-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
  }
  
  .add-config-btn {
    padding: 8px 16px;
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: #52c41a;
      transform: translateY(-1px);
    }
  }
}

.config-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-item {
  background: var(--background-color);
  border-radius: 10px;
  padding: 16px;
  border: 1px solid var(--border-color);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.config-info {
  flex: 1;
}

.config-name {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.config-time {
  font-size: 12px;
  color: var(--text-tertiary);
}

.config-actions {
  display: flex;
  gap: 8px;
}

.device-associations {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 12px;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--card-background);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.device-info {
  flex: 1;
}

.device-name {
  display: block;
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.device-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  
  &.status-1 {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.status-2 {
    background: #fff2e8;
    color: #fa8c16;
  }
  
  &.status-3 {
    background: #fff7e6;
    color: #faad14;
  }
  
  &.status-4 {
    background: #fff1f0;
    color: #ff4d4f;
  }
}

.device-priority {
  display: flex;
  align-items: center;
  gap: 4px;
}

.priority-label {
  font-size: 12px;
  color: var(--text-tertiary);
}

.priority-value {
  font-size: 12px;
  font-weight: 600;
  color: var(--primary-color);
}

.no-devices {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.no-devices-text {
  display: block;
  font-size: 14px;
  color: var(--text-tertiary);
  margin-bottom: 12px;
}

.link-device-btn {
  padding: 6px 12px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #1890ff;
  }
}

.config-modal {
  width: 90vw;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.fixed-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
  border-bottom: 1px solid #f5f5f5;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.modal-close {
  font-size: 16px;
  color: #999;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f5f5f5;
    color: #666;
  }
}

.modal-content-scrollable {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  max-height: calc(80vh - 80px);
}

.modal-footer {
  position: sticky;
  bottom: 0;
  z-index: 10;
  background: white;
  border-top: 1px solid #f5f5f5;
  padding: 8px 16px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  flex: 1;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    background: #f5f5f5;
  }
}

.btn-primary {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(135deg, #1677ff, #4096ff);
  color: white;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    transform: scale(0.98);
  }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  .loading-icon {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }
  
  text {
    font-size: 14px;
    color: var(--text-secondary);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 16px;
    color: var(--text-primary);
    margin-bottom: 8px;
  }
  
  .empty-desc {
    font-size: 14px;
    color: var(--text-secondary);
    text-align: center;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 设备绑定相关样式
.bind-btn {
  background: #52c41a;
  color: white;
  width: 70px;
  min-width: 70px;
  
  &:hover {
    background: #45a716;
  }
}



// 设备绑定弹窗样式
.binding-modal {
  width: 400px;
  max-width: 90vw;
  max-height: 80vh;
  border-radius: 12px;
  background: var(--card-background);
  box-shadow: 0 8px 32px var(--shadow-color);
  overflow: hidden;
}

.binding-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
}

.binding-modal .modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.binding-modal .modal-close {
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  color: #999;
  
  &:active {
    transform: scale(0.9);
  }
}

// 删除脚本弹窗样式
.delete-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
}

.delete-modal .modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.delete-modal .modal-close {
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  color: #999;
  
  &:active {
    transform: scale(0.9);
  }
}

.delete-modal .modal-content {
  padding: 20px;
}

.delete-warning {
  text-align: center;
  padding: 20px 0;
}

.warning-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
}

.warning-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.warning-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: block;
}

.btn-danger {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:active {
    transform: scale(0.98);
  }
  
  &:disabled {
    background: #d9d9d9;
    color: #999;
    cursor: not-allowed;
    transform: none;
  }
}

.binding-modal .modal-content {
  padding: 20px;
  max-height: 500px;
  overflow-y: auto;
}



.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-count {
  font-size: 12px;
  color: var(--primary-color);
  font-weight: normal;
}

.available-devices,
.bound-devices {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  &.compact {
    gap: 2px; // 紧凑布局，减少间隔
    max-height: 200px;
    overflow-y: auto;
  }
}

.device-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
  
  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px var(--shadow-light);
  }
  
  &.bound {
    background: var(--background-light);
    cursor: default;
    border-color: var(--border-color);
    
    &:hover {
      border-color: var(--border-color);
      box-shadow: none;
    }
  }
  
  &.selected {
    border-color: var(--primary-color);
    background: rgba(24, 144, 255, 0.05);
    
    &.bound {
      background: rgba(24, 144, 255, 0.05);
    }
  }
  
  &.bound:not(.selected) {
    border-color: var(--border-color);
  }
  
  &.compact {
    padding: 8px 12px; // 紧凑布局，减少内边距
    border-radius: 4px; // 更小的圆角
    
    // 确保可绑定和已绑定设备在紧凑模式下样式一致
    &.bound {
      background: var(--background-light);
      border-color: var(--border-color);
      
      &:hover {
        border-color: var(--border-color);
        box-shadow: none;
      }
    }
    
    &.selected {
      border-color: var(--primary-color);
      background: rgba(24, 144, 255, 0.05);
      
      &.bound {
        background: rgba(24, 144, 255, 0.05);
      }
    }
    
    &.bound:not(.selected) {
      border-color: var(--border-color);
    }
  }
}

.device-checkbox {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    color: white;
    background: var(--card-background);
    transition: all 0.2s ease;
    flex-shrink: 0;
    
    &.checked {
      background: var(--primary-color);
      border-color: var(--primary-color);
    }
  }
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.2s ease;
  
  .device-item:hover & {
    color: var(--primary-color);
  }
}

.device-status-compact {
  font-size: 11px;
  padding: 1px 4px;
  border-radius: 0;
  margin-right: 6px;
  font-weight: 500;
  flex-shrink: 0;
  
  &.status-1 {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }
  
  &.status-2 {
    background: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  
  &.status-3 {
    background: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
  }
  
  &.status-0 {
    background: #f5f5f5;
    color: #999;
    border: 1px solid #d9d9d9;
  }
}

.no-devices {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
}

.bind-actions {
  margin-top: 16px;
  padding: 12px;
  background: var(--background-light);
  border-radius: 8px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  
  .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &.btn-cancel {
      background: var(--border-color);
      color: var(--text-secondary);
      
      &:hover {
        background: #d9d9d9;
      }
    }
    
    &.btn-primary {
      background: var(--primary-color);
      color: white;
      
      &:hover {
        background: #0958d9;
      }
      
      &:disabled {
        background: var(--border-color);
        color: var(--text-secondary);
        cursor: not-allowed;
      }
    }
  }
}

.binding-modal .modal-footer {
  padding: 16px 20px;
  background: var(--background-light);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-tips {
  flex: 1;
  
  .tip-text {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .modules-section {
    padding: 12px;
  }
  
  .modules-grid {
    gap: 8px;
  }
  
  .module-item {
    padding: 12px 6px;
  }
  
  .module-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
    margin-bottom: 6px;
  }
  
  .module-name {
    font-size: 12px;
  }
}

.section-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn-small {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-danger {
  background: #ff4d4f;
  color: white;
  
  &:hover {
    background: #ff7875;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

// 升级套餐弹窗样式
.upgrade-modal {
  width: 90vw;
  max-width: 500px;
  max-height: 80vh;
  background: var(--card-background);
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.upgrade-modal .modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upgrade-modal .modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.upgrade-modal .modal-close {
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 4px;
  
  &:active {
    transform: scale(0.9);
  }
}

.upgrade-modal .modal-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.current-package-info {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    display: block;
  }
  
  .current-package {
    background: var(--background-light);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    
    .package-name {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      display: block;
      margin-bottom: 8px;
    }
    
    .package-details {
      font-size: 14px;
      color: var(--text-secondary);
      display: block;
    }
  }
}

.upgrade-packages {
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
    display: block;
  }
  
  .loading-packages,
  .no-upgrade-packages {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
    font-size: 14px;
  }
  
  .package-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .package-item {
    background: var(--card-background);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: var(--primary-color);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px var(--shadow-color);
    }
    
    &.selected {
      border-color: var(--primary-color);
      background: #f0f7ff;
    }
    
    .package-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .package-name {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
      }
      
      .package-price {
        font-size: 18px;
        font-weight: 700;
        color: #ff4d4f;
      }
    }
    
    .package-description {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 12px;
      display: block;
    }
    
    .package-features {
      display: flex;
      gap: 16px;
      
      .feature {
        font-size: 12px;
        color: var(--text-secondary);
        background: var(--background-light);
        padding: 4px 8px;
        border-radius: 4px;
      }
    }
  }
}

.upgrade-modal .modal-footer {
  padding: 16px 20px;
  background: var(--background-light);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 12px;
  align-items: center;
}

// 升级按钮样式
.btn-upgrade {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    transform: scale(0.98);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 续费按钮样式
.renewal-btn {
  background: linear-gradient(135deg, #fa8c16 0%, #faad14 100%);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    background: #d9d9d9;
    color: #999;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

// 续费弹窗样式
.renewal-modal {
  width: 420px;
  max-width: 90vw;
  background: var(--card-background);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  
  .modal-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .modal-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
    
    .modal-close {
      font-size: 20px;
      color: #999;
      cursor: pointer;
      padding: 4px;
      
      &:active {
        transform: scale(0.9);
      }
    }
  }
  
  .modal-content {
    padding: 24px;
    
    .script-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 20px;
      border: 1px solid #e9ecef;
      
      .script-header {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .script-icon {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          
          &::before {
            content: '';
            width: 16px;
            height: 20px;
            border: 2px solid white;
            border-top: none;
            border-radius: 0 0 8px 8px;
          }
        }
        
        .script-info {
          flex: 1;
          
          .script-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            display: block;
            margin-bottom: 4px;
          }
          
          .script-status {
            font-size: 12px;
            color: #fa8c16;
            background: #fff7e6;
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
          }
        }
      }
    }
    
    .renewal-details {
      margin-bottom: 20px;
      
      .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .detail-label {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .label-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            border-radius: 50%;
            position: relative;
            
            &::before {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              width: 6px;
              height: 6px;
              background: white;
              border-radius: 50%;
              transform: translate(-50%, -50%);
            }
          }
          
          text {
            font-size: 14px;
            color: var(--text-secondary);
          }
        }
        
        .detail-value {
          font-size: 14px;
          font-weight: 500;
          color: var(--text-primary);
          
          &.price {
            color: #f5222d;
            font-weight: 600;
            font-size: 16px;
          }
        }
      }
    }
    
    .renewal-benefits {
      background: #f6ffed;
      border-radius: 12px;
      padding: 16px;
      border: 1px solid #b7eb8f;
      
      .benefit-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .benefit-icon {
          width: 14px;
          height: 14px;
          background: #52c41a;
          border-radius: 50%;
          position: relative;
          
          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 6px;
            height: 3px;
            border: 2px solid white;
            border-top: none;
            border-right: none;
            transform: translate(-50%, -60%) rotate(-45deg);
          }
        }
        
        .benefit-text {
          font-size: 13px;
          color: var(--text-secondary);
        }
      }
    }
  }
  
  // 更新相关样式
  .update-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: rgba(255, 107, 53, 0.1);
    padding: 2px 6px;
    border-radius: 8px;
    margin-left: 8px;
    border: 1px solid rgba(255, 107, 53, 0.2);
    
    .update-text {
      font-size: 10px;
      color: #ff6b35;
      font-weight: 500;
    }
  }
  
  .update-btn {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
    
    &:hover {
      background: linear-gradient(135deg, #e55a2b, #ff6b35);
    }
  }
  
  // 更多按钮样式
  .more-btn {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 4px;
    
    &:hover {
      background: #e8e8e8;
      border-color: #d0d0d0;
    }
  }
  
  // 更多下拉菜单样式
  .more-dropdown {
    position: relative;
    
    .more-dropdown-overlay {
      position: absolute;
      top: 100%;
      right: 0;
      z-index: 1000;
      margin-top: 4px;
      min-width: 140px;
    }
    
    .more-options {
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      border: 1px solid #e0e0e0;
      overflow: hidden;
    }
    
    .more-option {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background: #f8f9fa;
      }
      
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        
        &:hover {
          background: transparent;
        }
      }
      
      &.danger {
        color: #f5222d;
        
        &:hover {
          background: #fff1f0;
        }
      }
      
      .option-text {
        font-size: 14px;
        color: inherit;
      }
    }
  }
}

// 更新弹窗样式
.update-modal {
  width: 90vw;
  max-width: 400px;
  
  .update-info {
    padding: 20px 0;
    
    .version-comparison {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
      margin-bottom: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 12px;
      
      .version-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        
        .version-label {
          font-size: 12px;
          color: #666;
        }
        
        .version-value {
          font-size: 16px;
          font-weight: 600;
          padding: 4px 8px;
          border-radius: 6px;
          
          &.current {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
          }
          
          &.latest {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
          }
        }
      }
      
      .version-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    
    .update-content {
      margin-bottom: 16px;
      
      .update-title {
        display: block;
        font-size: 14px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 8px;
      }
      
      .update-description {
        display: block;
        font-size: 13px;
        color: var(--text-secondary);
        line-height: 1.5;
        background: #f8f9fa;
        padding: 12px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }
    }
    
    .update-time {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-top: 1px solid #f0f0f0;
      
      .time-label {
        font-size: 13px;
        color: var(--text-secondary);
      }
      
      .time-value {
        font-size: 13px;
        color: var(--text-primary);
        font-weight: 500;
      }
    }
  }
  
  .btn-update {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
    
    &:hover {
      background: linear-gradient(135deg, #e55a2b, #ff6b35);
    }
    
    &:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }
  
  .modal-footer {
    padding: 20px 24px;
    background: var(--background-light);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
  }
}

// 续费按钮样式
.btn-renewal {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(135deg, #1677ff, #4096ff);
  color: white;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:active {
    transform: scale(0.98);
  }
  
  &:disabled {
    background: #d9d9d9;
    color: #999;
    cursor: not-allowed;
    transform: none;
  }
  
  .loading-icon {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes scrollMove {
  0% {
    left: 0;
    opacity: 1;
  }
  50% {
    left: 14px;
    opacity: 0.8;
  }
  100% {
    left: 0;
    opacity: 1;
  }
}
</style>