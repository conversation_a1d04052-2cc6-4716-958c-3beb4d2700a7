<template>
  <view class="payment-page">
    <!-- 支付状态 -->
    <view class="payment-status">
      <view class="status-icon" :class="statusClass">
        <text class="icon-text">{{statusIcon}}</text>
      </view>
      <text class="status-title">{{statusTitle}}</text>
      <text class="status-desc">{{statusDesc}}</text>
    </view>

    <!-- 订单信息 -->
    <view class="order-info" v-if="orderInfo">
      <view class="info-item">
        <text class="label">订单号</text>
        <text class="value">{{orderInfo.order_no}}</text>
      </view>
      <view class="info-item">
        <text class="label">支付金额</text>
        <text class="value amount">¥{{orderInfo.amount}}</text>
      </view>
      <view class="info-item">
        <text class="label">支付方式</text>
        <text class="value">{{payTypeText}}</text>
      </view>
      <view class="info-item">
        <text class="label">创建时间</text>
        <text class="value">{{formatDate(orderInfo.created_at)}}</text>
      </view>
    </view>

    <!-- 支付中状态 -->
    <view class="payment-loading" v-if="paymentStatus === 'loading'">
      <text class="loading-text">
        <template v-if="showPayBtn">请点击下方按钮前往支付</template>
        <template v-else>请等待支付结果...</template>
      </text>
    </view>

    <!-- 支付成功 -->
    <view class="payment-success" v-if="paymentStatus === 'success'">
      <view class="success-actions">
        <button v-if="orderType === 'quota' || (orderNo && orderNo.startsWith('DQ'))" @click="goToDevices" class="pay-action-btn primary">查看我的设备</button>
        <button v-else-if="orderType === 'upgrade' || (orderNo && orderNo.startsWith('SU'))" @click="goToScripts" class="pay-action-btn primary">查看我的脚本</button>
        <button v-else @click="goToScripts" class="pay-action-btn primary">查看我的脚本</button>
        <button @click="goToMarket" class="pay-action-btn secondary">继续浏览</button>
      </view>
    </view>

    <!-- 支付失败 -->
    <view class="payment-failed" v-if="paymentStatus === 'failed'">
      <view class="failed-actions">
        <button @click="retryPayment" class="action-btn primary">重新支付</button>
        <button @click="goToMarket" class="action-btn secondary">返回市场</button>
      </view>
    </view>

    <!-- 支付超时 -->
    <view class="payment-timeout" v-if="paymentStatus === 'timeout'">
      <view class="timeout-actions">
        <button @click="retryPayment" class="action-btn primary">重新支付</button>
        <button @click="goToMarket" class="action-btn secondary">返回市场</button>
      </view>
    </view>

    <!-- 立即支付按钮 -->
    <view v-if="showPayBtn && paymentStatus==='loading'" class="pay-action-bar">
      <button class="pay-action-btn primary" @click="handleGoPay">立即支付</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '@/utils/api'

// 响应式数据
const paymentStatus = ref<'loading' | 'success' | 'failed' | 'timeout'>('loading')
const orderInfo = ref<any>(null)
const payUrl = ref('')
const orderNo = ref('')
const payType = ref('')
const orderType = ref('')
const showPayBtn = ref(false)
// 移除orderId相关逻辑，只用orderNo
// 只隐藏立即支付按钮（showPayBtn=false）
// 其余内容不变

// 计算属性
const statusClass = computed(() => {
  switch (paymentStatus.value) {
    case 'success': return 'success'
    case 'failed': return 'failed'
    case 'timeout': return 'timeout'
    default: return 'loading'
  }
})

const statusIcon = computed(() => {
  switch (paymentStatus.value) {
    case 'success': return '✓'
    case 'failed': return '✗'
    case 'timeout': return '⏰'
    default: return '⋯'
  }
})

const statusTitle = computed(() => {
  switch (paymentStatus.value) {
    case 'success': return '支付成功'
    case 'failed': return '支付失败'
    case 'timeout': return '支付超时'
    default: return '正在处理'
  }
})

const statusDesc = computed(() => {
  switch (paymentStatus.value) {
    case 'success': 
      // 根据订单类型显示不同的成功信息
      // 优先使用orderType参数，如果没有则根据订单号前缀判断
      const isQuotaOrder = orderType.value === 'quota' || (orderNo.value && orderNo.value.startsWith('DQ'))
      const isUpgradeOrder = orderType.value === 'upgrade' || (orderNo.value && orderNo.value.startsWith('SU'))
      if (isQuotaOrder) {
        return '您的设备配额已购买成功，可以继续配对设备了'
      } else if (isUpgradeOrder) {
        return '您的脚本已升级成功，可以享受更多功能了'
      }
      return '您的脚本已安装成功，可以开始使用了'
    case 'failed': return '支付过程中出现错误，请重新尝试'
    case 'timeout': return '支付超时，请重新发起支付'
    default: return '正在跳转到支付页面...'
  }
})

const payTypeText = computed(() => {
  return '支付宝'
})

// 生命周期
onMounted(() => {
  initPayment()
})

// 方法
const initPayment = () => {
  try {
    // 获取页面参数
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const options = currentPage.options
    payUrl.value = decodeURIComponent(options.pay_url || '')
    orderType.value = options.type || ''
    orderNo.value = options.order_no || ''
    // 新增：直接用参数渲染订单信息（如果有）
    if (options.amount) {
      orderInfo.value = {
        order_no: orderNo.value,
        amount: options.amount,
        pay_type: options.pay_type || 'alipay',
        created_at: options.created_at || '',
      }
      payType.value = options.pay_type || 'alipay'
    }
    if (!orderNo.value) {
      paymentStatus.value = 'failed'
      return
    }
    showPayBtn.value = !!payUrl.value
    // 不再自动请求订单状态接口
    if (!payUrl.value) {
      startPayment()
    }
  } catch (error) {
    console.error('初始化支付失败:', error)
    paymentStatus.value = 'failed'
  }
}

// handleGoPay 只新开窗口，不做本页跳转
const handleGoPay = () => {
  // #ifdef H5
  window.open(payUrl.value, '_blank')
  // #endif
  // #ifdef APP-PLUS
  plus.runtime.openURL(payUrl.value)
  // #endif
  showPayBtn.value = false
  setTimeout(() => {
    startPayment()
  }, 5000)
}

const startPayment = async () => {
  try {
    // 查询订单状态
    if (!orderNo.value) {
      paymentStatus.value = 'failed'
      return
    }
    const orderRes = await api.getOrderDetail(orderNo.value)
    const order = orderRes.data
    
    if (!order) {
      paymentStatus.value = 'failed'
      return
    }
    
    orderInfo.value = order
    payType.value = order.pay_type
    
    // 检查是否已经支付成功
    if (order.status === 1) {
      paymentStatus.value = 'success'
      // 确保orderType在支付成功后仍然保持
      console.log('支付成功，订单类型:', orderType.value)
      return
    }
    
    // 跳转到支付页面
    await jumpToPayment()
    
    // 开始轮询支付状态
    startPollingPaymentStatus()
    
  } catch (error) {
    console.error('启动支付失败:', error)
    paymentStatus.value = 'failed'
  }
}

// 删除 jumpToPayment 里所有 window.location.href 跳转逻辑，只保留 handleGoPay 新开窗口
const jumpToPayment = () => {
  return new Promise((resolve) => {
    // 不做任何跳转，直接 resolve
    resolve(true)
  })
}

const startPollingPaymentStatus = () => {
  let pollCount = 0
  const maxPolls = 60 // 最多轮询60次（5分钟）
  const pollInterval = 5000 // 每5秒轮询一次
  
  const poll = async () => {
    try {
      pollCount++
      if (!orderNo.value) {
        paymentStatus.value = 'failed'
        return
      }
      const orderRes = await api.getOrderDetail(orderNo.value)
      const order = orderRes.data
      
      if (order && order.status === 1) {
        paymentStatus.value = 'success'
        return
      }
      
      if (pollCount >= maxPolls) {
        paymentStatus.value = 'timeout'
        return
      }
      
      // 继续轮询
      setTimeout(poll, pollInterval)
      
    } catch (error) {
      console.error('轮询支付状态失败:', error)
      if (pollCount >= maxPolls) {
        paymentStatus.value = 'timeout'
      } else {
        setTimeout(poll, pollInterval)
      }
    }
  }
  
  // 开始轮询
  setTimeout(poll, pollInterval)
}

const retryPayment = () => {
  paymentStatus.value = 'loading'
  startPayment()
}

const goToScripts = () => {
  uni.switchTab({
    url: '/pages/script/index'
  })
}

const goToDevices = () => {
  // 跳转到设备页面并传递刷新标记
  uni.switchTab({
    url: '/pages/device/index?refresh=true'
  })
}

const goToMarket = () => {
  uni.navigateBack()
}

const formatDate = (date: string) => {
  if (!date) return '未知'
  try {
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) {
      return '未知'
    }
    return dateObj.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    console.error('格式化日期失败:', error)
    return '未知'
  }
}
</script>

<style lang="scss" scoped>
.payment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 40rpx;
}

.payment-status {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
  
  &.loading {
    background-color: #f0f8ff;
    border: 4rpx solid #007aff;
  }
  
  &.success {
    background-color: #f0fff0;
    border: 4rpx solid #52c41a;
  }
  
  &.failed {
    background-color: #fff0f0;
    border: 4rpx solid #ff4d4f;
  }
  
  &.timeout {
    background-color: #fff7e6;
    border: 4rpx solid #faad14;
  }
}

.icon-text {
  font-size: 48rpx;
  font-weight: bold;
  
  .loading & {
    color: #007aff;
  }
  
  .success & {
    color: #52c41a;
  }
  
  .failed & {
    color: #ff4d4f;
  }
  
  .timeout & {
    color: #faad14;
  }
}

.status-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.status-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

.order-info {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  
  &.amount {
    font-weight: bold;
    color: #ff6b35;
  }
}

.payment-loading,
.payment-success,
.payment-failed,
.payment-timeout {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  margin: 0 auto 30rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.success-actions,
.failed-actions,
.timeout-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  
  &.primary {
    background-color: #007aff;
    color: white;
  }
  
  &.secondary {
    background-color: #f5f5f5;
    color: #666;
  }
}

.pay-action-bar {
  margin: 40rpx 0 0 0;
  text-align: center;
}
.pay-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80%;
  height: 88rpx;
  margin: 0 auto 20rpx auto;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 8rpx;
  box-sizing: border-box;
  border: none;
}
.pay-action-btn.primary {
  background: #007aff;
  color: #fff;
  border: none;
}
.pay-action-btn.secondary {
  background: #f5f5f5;
  color: #007aff;
  border: 2rpx solid #007aff;
}
</style> 