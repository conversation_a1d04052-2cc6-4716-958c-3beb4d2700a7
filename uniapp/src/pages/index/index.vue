<template>
  <view class="home">
    <!-- 顶部标签页 -->
    <view class="tab-header">
      <view class="tab-list">
        <view 
          v-for="tab in tabs" 
          :key="tab.key"
          :class="['tab-item', { active: currentTab === tab.key }]"
          @click="switchTab(tab.key)"
        >
          <text class="tab-text">{{ tab.name }}</text>
        </view>
      </view>
    </view>

    <!-- 标签页内容 -->
    <view class="tab-content">
      <!-- 脚本市场 -->
      <view v-if="currentTab === 'market'" class="market-tab">
        <!-- 搜索栏 -->
        <view class="search-section">
          <view class="search-bar">
            <uni-icons type="search" size="16" color="#999"></uni-icons>
            <input 
              v-model="searchKeyword"
              placeholder="搜索脚本..."
              class="search-input"
              @confirm="searchScripts"
            />
            <button @click="searchScripts" class="search-btn">搜索</button>
          </view>
        </view>

        <!-- 脚本列表 -->
        <view class="script-list-section">
          <view class="section-header">
            <text class="section-title">脚本市场</text>
            <view class="filter-tabs">
              <text 
                :class="['filter-tab', { active: currentFilter === 'all' }]"
                @click="setFilter('all')"
              >
                全部
              </text>
              <text 
                :class="['filter-tab', { active: currentFilter === 'featured' }]"
                @click="setFilter('featured')"
              >
                推荐
              </text>
            </view>
          </view>

          <view class="script-list">
            <view 
              v-for="script in scripts"
              :key="script.id"
              class="script-item"
              @click="viewScriptDetail(script.id)"
            >
              <image :src="script.icon || '/static/logo.png'" class="script-icon" />
              <view class="script-info">
                <view class="script-header">
                  <text class="script-name">{{script.name}}</text>
                  <view class="script-badges">
                    <text v-if="script.is_featured" class="badge featured">推荐</text>
                  </view>
                </view>
                <text class="script-desc">{{script.summary || getPlainDescription(script.description)}}</text>
                <view class="script-meta">
                  <text class="script-author">{{script.author}}</text>
                  <text class="script-downloads">{{script.downloads}}次下载</text>
                  <text class="script-rating">⭐{{script.rating}}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 加载更多 -->
          <view v-if="hasMore" class="load-more" @click="loadMore">
            <text>加载更多</text>
          </view>
          <view v-else-if="scripts.length > 0" class="no-more">
            <text>没有更多了</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="scripts.length === 0 && !loading" class="empty-state">
          <image src="/static/logo.png" class="empty-icon" />
          <text class="empty-text">暂无脚本</text>
        </view>
      </view>

      <!-- 新手入门 -->
      <view v-if="currentTab === 'guide'" class="guide-tab">
        <view class="guide-content">
          <view class="guide-section">
            <text class="guide-subtitle">快速上手，轻松管理您的设备</text>
          </view>
          
          <view class="guide-steps">
            <view class="step-item">
              <view class="step-number">1</view>
              <view class="step-content">
                <text class="step-title">注册账号</text>
                <text class="step-desc">创建您的专属账号，开始使用群控系统</text>
              </view>
            </view>
            
            <view class="step-item">
              <view class="step-number">2</view>
              <view class="step-content">
                <text class="step-title">绑定设备</text>
                <text class="step-desc">在设备管理页面绑定您的设备</text>
              </view>
            </view>
            
            <view class="step-item">
              <view class="step-number">3</view>
              <view class="step-content">
                <text class="step-title">选择脚本</text>
                <text class="step-desc">在脚本市场选择合适的脚本</text>
              </view>
            </view>
            
            <view class="step-item">
              <view class="step-number">4</view>
              <view class="step-content">
                <text class="step-title">开始使用</text>
                <text class="step-desc">配置脚本参数，开始自动化操作</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 下载 -->
      <view v-if="currentTab === 'download'" class="download-tab">
        <view class="download-content">
          <view class="download-section">
            <text class="download-subtitle">选择适合您设备的客户端版本</text>
          </view>
          
          <view class="download-list">
            <view class="download-item">
              <view class="download-icon">📱</view>
              <view class="download-info">
                <text class="download-name">安卓运行端</text>
                <text class="download-desc">支持 Android 7.0 及以上版本</text>
              </view>
              <button class="download-btn">下载</button>
            </view>
            
            <view class="download-item">
              <view class="download-icon">🎮</view>
              <view class="download-info">
                <text class="download-name">安卓控制端</text>
                <text class="download-desc">支持 Android 7.0 及以上版本</text>
              </view>
              <button class="download-btn">下载</button>
            </view>
          </view>
        </view>
      </view>

      <!-- 注册 -->
      <view v-if="currentTab === 'register'" class="register-tab">
        <view class="register-content">
          <view class="register-section">
            <text class="register-subtitle">加入我们，开启自动化之旅</text>
          </view>
          
          <view class="register-actions">
            <button @click="goToRegister" class="register-btn">前往注册</button>
            <button @click="goToLogin" class="login-btn">已有账号？去登录</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '@/utils/api'

// 标签页配置
const tabs = [
  { key: 'market', name: '脚本市场' },
  { key: 'guide', name: '新手入门' },
  { key: 'download', name: '下载' },
  { key: 'register', name: '注册' }
]

// 当前标签页
const currentTab = ref('market')

// 响应式数据
const searchKeyword = ref('')
const selectedCategory = ref('')
const currentFilter = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const scripts = ref<any[]>([])
const total = ref(0)
const loading = ref(false)
const hasMore = ref(true)





// 计算属性
const filterParams = computed(() => {
  const params: any = {
    page: currentPage.value,
    page_size: pageSize.value,
    keyword: searchKeyword.value
  }

  if (currentFilter.value === 'featured') {
    params.is_featured = true
  }

  return params
})

// 生命周期
onMounted(() => {
  // 默认加载脚本市场
  loadScripts()
})

// 方法

// 切换标签页
const switchTab = (tabKey: string) => {
  currentTab.value = tabKey
  if (tabKey === 'market' && scripts.value.length === 0) {
    loadScripts()
  }
}

// 跳转到注册页面
const goToRegister = () => {
  uni.navigateTo({
    url: '/pages/register/index'
  })
}

// 跳转到登录页面
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/index'
  })
}

const loadScripts = async (reset = false) => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    if (reset) {
      currentPage.value = 1
      scripts.value = []
    }

    const res = await api.getMarketScripts(filterParams.value)
    const newScripts = res.data.list || []
    
    if (reset) {
      scripts.value = newScripts
    } else {
      scripts.value.push(...newScripts)
    }
    
    total.value = res.data.total
    hasMore.value = scripts.value.length < total.value
  } catch (error) {
    console.error('加载脚本列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const searchScripts = () => {
  loadScripts(true)
}



const setFilter = (filter: string) => {
  currentFilter.value = filter
  loadScripts(true)
}

const loadMore = () => {
  if (hasMore.value && !loading.value) {
    currentPage.value++
    loadScripts()
  }
}

const viewScriptDetail = (scriptId: number) => {
  uni.navigateTo({
    url: `/pages/script-market/detail?id=${scriptId}`
  })
}

// 安装功能已移除，现在通过脚本详情页面的支付流程来安装

// 处理描述文本，去除Markdown语法并限制长度
const getPlainDescription = (description: string) => {
  if (!description) return ''
  
  // 去除Markdown语法
  let plainText = description
    .replace(/>!?\*\*.*?\*\*/g, '') // 去除 >!**重要提示** 等格式
    .replace(/>.*?/g, '') // 去除引用格式 >
    .replace(/\*\*.*?\*\*/g, '') // 去除粗体 **
    .replace(/\*.*?\*/g, '') // 去除斜体 *
    .replace(/`.*?`/g, '') // 去除行内代码 `
    .replace(/\[.*?\]\(.*?\)/g, '') // 去除链接 [text](url)
    .replace(/!\[.*?\]\(.*?\)/g, '') // 去除图片 ![alt](url)
    .replace(/#{1,6}\s/g, '') // 去除标题 #
    .replace(/\n/g, ' ') // 将换行替换为空格
    .replace(/\s+/g, ' ') // 将多个空格替换为单个空格
    .trim()
  
  // 限制长度，最多显示50个字符
  if (plainText.length > 50) {
    plainText = plainText.substring(0, 50) + '...'
  }
  
  return plainText
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 标签页样式 */
.tab-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-list {
  display: flex;
  padding: 0 16px;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 8px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1677ff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: #1677ff;
  border-radius: 1px;
}

.tab-text {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #1677ff;
  font-weight: 600;
}

.tab-content {
  flex: 1;
}

/* 新手入门样式 */
.guide-content {
  padding: 20px 16px;
}

.guide-section {
  text-align: center;
  margin-bottom: 32px;
}

.guide-title {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.guide-subtitle {
  display: block;
  font-size: 14px;
  color: #666;
}

.guide-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.step-number {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #1677ff, #4096ff);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.step-desc {
  display: block;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 下载样式 */
.download-content {
  padding: 20px 16px;
}

.download-section {
  text-align: center;
  margin-bottom: 32px;
}

.download-title {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.download-subtitle {
  display: block;
  font-size: 14px;
  color: #666;
}

.download-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.download-item {
  display: flex;
  align-items: center;
  gap: 16px;
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.download-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.download-info {
  flex: 1;
}

.download-name {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.download-desc {
  display: block;
  font-size: 14px;
  color: #666;
}

.download-btn {
  background: linear-gradient(135deg, #1677ff, #4096ff);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.download-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
}

/* 注册样式 */
.register-content {
  padding: 20px 16px;
}

.register-section {
  text-align: center;
  margin-bottom: 32px;
}

.register-title {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.register-subtitle {
  display: block;
  font-size: 14px;
  color: #666;
}

.register-actions {
  background: #fff;
  border-radius: 12px;
  padding: 32px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.register-btn {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #1677ff, #4096ff);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.register-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
}

.login-btn {
  width: 100%;
  height: 48px;
  background: transparent;
  color: #1677ff;
  border: 1px solid #1677ff;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn:hover {
  background: #f0f8ff;
  transform: translateY(-1px);
}

/* 搜索栏 - 与设备页面保持一致 */
.search-section {
  padding: 8px 12px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.search-bar {
  position: relative;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 16px;
  padding: 0 12px;
  height: 32px;
}

.search-input {
  flex: 1;
  height: 32px;
  border: none;
  outline: none;
  font-size: 13px;
  color: #333;
  background: transparent;
  margin-left: 6px;
}

.search-btn {
  background: #1677ff;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 4px 12px;
  font-size: 12px;
  height: 24px;
  line-height: 16px;
}



.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #667eea;
}

.script-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  margin-bottom: 15rpx;
}

.script-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  white-space: normal;
}

.script-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 15rpx;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.script-meta {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
}

/* 脚本列表 */
.script-list-section {
  background: white;
  margin: 8px 0;
  padding: 12px;
}

.filter-tabs {
  display: flex;
  gap: 20px;
}

.filter-tab {
  font-size: 14px;
  color: #666;
  padding: 6px 0;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.filter-tab.active {
  color: #1677ff;
  border-bottom-color: #1677ff;
}

.script-list {
  margin-top: 16px;
}

.script-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.script-item:last-child {
  border-bottom: none;
}

.script-item .script-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-right: 12px;
}

.script-info {
  flex: 1;
}

.script-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.script-header .script-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-right: 12px;
}

.script-badges {
  display: flex;
  gap: 10rpx;
}

.badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
}

.badge.featured {
  background: #52c41a;
}

.script-item .script-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.script-item .script-meta {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #999;
}



/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}

.load-more {
  color: #667eea;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}


</style>
