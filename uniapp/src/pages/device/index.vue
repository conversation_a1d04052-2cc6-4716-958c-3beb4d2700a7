<template>
  <view class="device-page">
    <!-- 紧凑型头部设计 -->
    <view class="header-section">
      <!-- 状态栏 -->
      <view class="status-bar">
        <view class="ws-status">
          <view :class="['ws-dot', getWsStatusClass()]"></view>
          <text class="ws-text">{{ getWsStatusText() }}</text>
        </view>
        <view class="refresh-btn" @click="loadDevices">
          <uni-icons type="refresh" size="16" color="#ffffff" />
        </view>
      </view>

      <!-- 统计和操作区域 -->
      <view class="stats-actions-row">
        <!-- 统计信息 -->
        <view class="stats-row">
          <view class="stat-item">
            <text class="stat-number">{{ deviceStats?.total || 0 }}</text>
            <text class="stat-label">总数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number online">{{ deviceStats?.online || 0 }}</text>
            <text class="stat-label">在线</text>
          </view>
          <view class="stat-item">
            <text class="stat-number offline">{{ deviceStats?.offline || 0 }}</text>
            <text class="stat-label">离线</text>
          </view>
          <view class="stat-item" v-if="quotaInfo">
            <text class="stat-number quota">{{ quotaInfo.used_quota || 0 }}/{{ quotaInfo.total_quota || 3 }}</text>
            <text class="stat-label">配额</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button class="action-btn primary" @click="startPairing">
            <uni-icons type="plus" size="14" color="#ffffff" />
            <text class="btn-text">配对</text>
          </button>
          <button class="action-btn secondary" @click="buyQuota">
            <uni-icons type="shop" size="14" color="#ffffff" />
            <text class="btn-text">配额</text>
          </button>
          <button class="action-btn tertiary" @click="showRenewalOptions">
            <uni-icons type="refresh" size="14" color="#ffffff" />
            <text class="btn-text">续费</text>
          </button>
        </view>
      </view>



      <!-- 搜索框 -->
      <view class="search-container">
      <view class="search-box">
        <uni-icons type="search" size="16" color="#999" />
        <input 
          class="search-input" 
          v-model="searchKeyword" 
          placeholder="搜索设备名称或标签"
          @input="onSearchInput"
        />
        <uni-icons v-if="searchKeyword" type="close" size="16" color="#999" @click="clearSearch" />
        </view>
      </view>
    </view>

    <!-- 设备列表 -->
    <view class="device-list">
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <view v-else-if="!filteredDevices || filteredDevices.length === 0" class="empty-container">
        <uni-icons type="phone" size="48" color="#ccc" />
        <text class="empty-title">{{ searchKeyword ? '未找到匹配的设备' : '暂无设备' }}</text>
        <text class="empty-desc">{{ searchKeyword ? '请尝试其他搜索关键词' : '点击上方按钮配对新设备' }}</text>
      </view>
      
      <view v-else class="device-grid">
        <view 
          v-for="device in filteredDevices" 
          :key="device.id"
          class="device-card"
        >
          <!-- 设备头部 -->
          <view class="device-header">
            <view class="device-info">
              <text class="device-name">{{ device.name || '未命名设备' }}</text>
              <text class="device-id">{{ device.device_id }}</text>
            </view>
            <view class="device-status">
              <view 
                :class="['status-badge', getStatusClass(device.status)]"
              >
                <view :class="['status-dot', getStatusClass(device.status)]"></view>
                <text class="status-text">{{ getStatusText(device.status) }}</text>
              </view>
            </view>
          </view>
          
          <!-- 设备详情 -->
          <view class="device-details">
            <view class="detail-row">
              <text class="detail-label">活跃：</text>
                              <text class="detail-value">{{ formatRelativeTime(device.last_active) }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">配额：</text>
              <view class="quota-info">
                <text v-if="device.quota_info?.quota_type === 'expired'" class="quota-expired">已过期</text>
                <text v-else-if="device.quota_info?.is_free" class="quota-free">免费</text>
                <text v-else-if="device.quota_info?.expires_at" class="quota-paid">
                  {{ formatQuotaExpiry(device.quota_info.expires_at) }}
                </text>
                <text v-else class="quota-unknown">未知</text>
              </view>
            </view>
          </view>
          

          
          <!-- 操作按钮 -->
          <view class="device-actions">
                                    <button 
                          class="device-action-btn stop-btn"
                          :disabled="operatingDevices.has(device.device_id) || device.status !== DEVICE_STATUS.ONLINE"
                          @click.stop="stopScript(device)"
                          :title="getStopButtonTitle(device)"
                        >
                          <uni-icons type="stop" size="18" color="#ffffff" />
                        </button>
            <button 
              class="device-action-btn edit-btn"
              :disabled="operatingDevices.has(device.device_id)"
              @click.stop="editDevice(device)"
              title="编辑设备"
            >
              <uni-icons type="compose" size="18" color="#ffffff" />
            </button>
            <button 
              class="device-action-btn delete-btn"
              :disabled="operatingDevices.has(device.device_id)"
              @click.stop="deleteDevice(device)"
              :title="operatingDevices.has(device.device_id) ? '删除中...' : '删除设备'"
            >
              <uni-icons type="trash" size="18" color="#ffffff" />
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 配对状态弹窗 -->
    <view v-if="pairingStep !== 'idle'" class="pairing-modal">
      <view class="modal-mask" @click="cancelPairing"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">设备配对</text>
          <uni-icons v-if="pairingStep !== 'success'" type="close" size="20" color="#999" class="modal-close" @click="cancelPairing" />
        </view>
        
        <view class="modal-body">
          <view v-if="pairingStep === 'generating'" class="pairing-step">
            <uni-icons type="spinner-cycle" size="48" color="#1677ff" class="step-icon loading" />
            <text class="step-title">正在生成配对码...</text>
          </view>
          
          <view v-else-if="pairingStep === 'waiting'" class="pairing-step">
            <view class="code-section">
              <text class="code-title">配对码</text>
              <view class="code-value" @click.stop="copyPairingCode">
                <text class="code-text">{{ pairingCode }}</text>
              </view>
              <text class="code-desc">点击配对码可复制，请在设备端输入此配对码</text>
              <!-- 复制成功提示 -->
              <view v-if="showCopySuccess" class="copy-success-tip">
                <uni-icons type="checkmarkempty" size="16" color="#52c41a" />
                <text class="copy-success-text">配对码已复制</text>
              </view>
            </view>
            <view class="status-section">
              <uni-icons type="spinner-cycle" size="24" color="#1677ff" class="step-icon loading" />
              <text class="status-text">等待设备连接...</text>
            </view>
          </view>
          
          <view v-else-if="pairingStep === 'success'" class="pairing-step">
            <uni-icons type="checkmarkempty" size="48" color="#52c41a" class="step-icon success" />
            <text class="step-title">配对成功！</text>
            <text class="step-desc">设备已成功添加到您的账户</text>
            <button class="complete-btn" @click="cancelPairing">
              完成
            </button>
          </view>
        </view>
      </view>
    </view>
    </view>
  
  <!-- 编辑设备弹窗 -->
  <view v-if="showEditModal" class="edit-modal-overlay" @click="closeEditModal">
    <view class="edit-modal" @click.stop>
      <view class="edit-modal-header">
        <text class="edit-modal-title">编辑设备</text>
        <view class="edit-modal-close" @click="closeEditModal">
          <uni-icons type="close" size="20" />
        </view>
      </view>
      
      <view class="edit-modal-content">
        <view class="form-item">
          <text class="form-label">设备名称</text>
          <input 
            class="form-input" 
            v-model="editForm.name" 
            placeholder="请输入设备名称"
            maxlength="50"
          />
        </view>
        

      </view>
      
      <view class="edit-modal-footer">
        <button class="btn-cancel" @click="closeEditModal">取消</button>
        <button class="btn-confirm" @click="submitEdit">确定</button>
      </view>
    </view>
  </view>
  
  <!-- 删除设备弹窗 -->
  <view v-if="showDeleteModal" class="delete-modal-overlay" @click="closeDeleteModal">
    <view class="delete-modal" @click.stop>
      <view class="delete-modal-header">
        <text class="delete-modal-title">确认删除</text>
        <view class="delete-modal-close" @click="closeDeleteModal">
          <uni-icons type="close" size="20" />
        </view>
      </view>
      
      <view class="delete-modal-content">
        <view class="delete-icon">
          <uni-icons type="info" size="48" color="#f5222d" />
        </view>
        <text class="delete-message">
          确定要删除设备 "{{ deletingDevice?.name || deletingDevice?.device_id }}" 吗？
        </text>
        <text class="delete-warning">此操作不可撤销，请谨慎操作。</text>
      </view>
      
      <view class="delete-modal-footer">
        <button class="btn-cancel" @click="closeDeleteModal">取消</button>
        <button class="btn-delete" @click="confirmDelete">删除</button>
      </view>
    </view>
  </view>

  <!-- 配额购买弹窗 -->
  <view v-if="showQuotaModal" class="quota-modal-overlay" @click="closeQuotaModal">
    <view class="quota-modal" @click.stop>
      <view class="quota-modal-header">
        <text class="quota-modal-title">购买设备配额</text>
        <view class="quota-modal-close" @click="closeQuotaModal">
          <uni-icons type="close" size="20" />
        </view>
      </view>
      
      <view class="quota-modal-content">
        <!-- 选择配额数量 -->
        <view class="quota-selection">
          <text class="section-title">选择购买数量</text>
          
          <!-- 快速选择 -->
          <view class="quota-options">
            <view 
              v-for="count in quotaOptions" 
              :key="count"
              :class="['quota-option', { active: selectedQuotaCount === count }]"
              @click="selectQuotaCount(count)"
            >
              <text class="option-count">{{ count }}个</text>
              <text class="option-price">¥{{ count }}</text>
            </view>
          </view>
          
          <!-- 自定义数量 -->
          <view class="custom-quota">
            <text class="custom-label">自定义数量：</text>
            <input 
              class="custom-input" 
              v-model="customQuotaCount" 
              type="number" 
              placeholder="请输入数量"
              min="1"
              max="999"
              @input="onCustomQuotaInput"
            />
            <text class="custom-price">¥{{ customQuotaPrice }}</text>
          </view>
        </view>

        <!-- 支付按钮 -->
        <view class="payment-section">
          <button class="payment-btn alipay" @click="createQuotaOrder('alipay')">
            <uni-icons type="wallet" size="20" color="#1677ff" />
            <text class="payment-text">立即购买 ¥{{ selectedQuotaCount }}</text>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 配额续费弹窗 -->
  <view v-if="showRenewalModal" class="quota-modal-overlay" @click="closeRenewalModal">
    <view class="quota-modal" @click.stop>
      <view class="quota-modal-header">
        <text class="quota-modal-title">配额续费</text>
        <view class="quota-modal-close" @click="closeRenewalModal">
          <uni-icons type="close" size="20" />
        </view>
      </view>
      
      <view class="quota-modal-content">
        <!-- 选择续费配额 -->
        <view class="quota-selection">
          <text class="section-title">选择要续费的配额</text>
          
          <view class="renewal-quotas">
            <view 
              v-for="quota in renewableQuotas" 
              :key="quota.id"
              :class="['renewal-quota-item', { active: selectedQuotaForRenewal?.id === quota.id }]"
              @click="selectQuotaForRenewal(quota)"
            >
              <view class="quota-info">
                <text class="quota-count">{{ quota.total_quota }}个配额</text>
                <view class="quota-details">
                  <view class="detail-item">
                    <uni-icons type="calendar" size="16" color="#666" />
                    <text class="detail-text">到期时间：{{ formatDateTime(quota.expires_at) }}</text>
                  </view>
                  <view class="detail-item">
                    <uni-icons type="calendar" size="16" color="#4CAF50" />
                    <text class="detail-text renewal-text">续费后到期：{{ formatDateTime(calculateRenewalExpiry(quota.expires_at)) }}</text>
                  </view>
                </view>
              </view>
              <view class="quota-price">
                <text class="price-text">续费价格：¥{{ quota.total_quota }}</text>
                <text class="price-desc">（30天）</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 订单信息显示 -->
        <view class="order-info-section" v-if="renewalOrderNo">
          <text class="section-title">订单信息</text>
          <view class="order-info-card">
            <view class="order-info-item">
              <uni-icons type="receipt" size="16" color="#666" />
              <text class="order-label">订单号：</text>
              <text class="order-value">{{ renewalOrderNo }}</text>
            </view>
            <view class="order-info-item">
              <uni-icons type="clock" size="16" color="#666" />
              <text class="order-label">订单状态：</text>
              <text class="order-status" :class="renewalOrderStatus">
                {{ getOrderStatusText(renewalOrderStatus) }}
              </text>
            </view>
          </view>
        </view>

        <!-- 支付按钮 -->
        <view class="payment-section" v-if="selectedQuotaForRenewal && !renewalOrderNo">
          <button class="payment-btn alipay" @click="createRenewalOrder('alipay')">
            <uni-icons type="wallet" size="20" color="#1677ff" />
            <text class="payment-text">立即续费 ¥{{ selectedQuotaForRenewal.total_quota }}</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
// 已移除 Icon 导入，使用 uni-icons 替代
import { deviceAPI, pairingAPI, api } from '@/utils/api'
import { getWebSocketService } from '@/utils/websocket'
import { 
  formatRelativeTime, 
  formatDateTime, 
  formatQuotaExpiry, 
  calculateRenewalExpiry 
} from '@/utils/date'

// 生成UUID函数
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}
import { useDeviceStore } from '@/stores/device'
import { useUserStore } from '@/stores/user'



// 设备状态常量
const DEVICE_STATUS = {
  OFFLINE: 0,
  ONLINE: 1
} as const

// 使用store
const deviceStore = useDeviceStore()
const userStore = useUserStore()

// 响应式数据
const searchKeyword = ref('')
const pairingStep = ref<'idle' | 'generating' | 'waiting' | 'success'>('idle')
const pairingCode = ref('')
const pairingTimer = ref<number | null>(null)
const lastRefreshTime = ref(0)
const operatingDevices = ref<Set<string>>(new Set()) // 正在操作的设备ID集合
const showCopySuccess = ref(false) // 显示复制成功提示

// 配额相关状态
const showQuotaModal = ref(false)
const quotaInfo = ref<any>(null)
const selectedQuotaCount = ref(5)
const quotaOptions = [1, 5, 10, 20, 50, 100]
const customQuotaCount = ref('')
const customQuotaPrice = ref(0)

// 续费相关状态
const showRenewalModal = ref(false)
const renewableQuotas = ref<any[]>([])
const selectedQuotaForRenewal = ref<any>(null)
const renewalOrderNo = ref('')
const renewalOrderStatus = ref('')
const renewalOrderTimer = ref<number | null>(null)

// 从store获取数据
const devices = computed(() => deviceStore.devices)
const loading = computed(() => deviceStore.loading)
const deviceStats = computed(() => deviceStore.deviceStats)
const websocketStatus = computed(() => deviceStore.websocketStatus)

// 计算属性
const filteredDevices = computed(() => {
  // 确保 devices.value 是数组
  const deviceList = Array.isArray(devices.value) ? devices.value : []
  
  if (!searchKeyword.value) {
    return deviceList
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return deviceList.filter(device => 
    device?.name?.toLowerCase().includes(keyword) ||
    device?.device_id?.toLowerCase().includes(keyword) ||

    device?.device_model?.toLowerCase().includes(keyword)
  )
})

const getStatusClass = (status: number) => {
  switch (status) {
    case DEVICE_STATUS.ONLINE: return 'status-online'
    case DEVICE_STATUS.OFFLINE: return 'status-offline'
    case 2: return 'status-expired'
    default: return 'status-offline'
  }
}

const getStatusText = (status: number) => {
  switch (status) {
    case DEVICE_STATUS.ONLINE: return '在线'
    case DEVICE_STATUS.OFFLINE: return '离线'
    case 2: return '已过期'
    default: return '离线'
  }
}

// WebSocket状态相关方法
const getWsStatusClass = () => {
  switch (websocketStatus.value) {
    case 'connected': return 'ws-connected'
    case 'connecting': return 'ws-connecting'
    case 'reconnecting': return 'ws-reconnecting'
    default: return 'ws-disconnected'
  }
}

const getWsStatusText = () => {
  switch (websocketStatus.value) {
    case 'connected': return '实时'
    case 'connecting': return '连接中'
    case 'reconnecting': return '重连中'
    default: return '离线'
  }
}

// 获取停止按钮的提示文本
const getStopButtonTitle = (device: any) => {
  if (operatingDevices.value.has(device.device_id)) {
    return '操作中...'
  }
  if (device.status !== DEVICE_STATUS.ONLINE) {
    return '设备离线，无法停止脚本'
  }
  return '停止脚本'
}

// 方法
const loadDevices = async () => {
  const result = await deviceStore.fetchDevices()
  if (!result.success) {
    uni.showToast({
      title: result.message || '加载失败',
      icon: 'none'
    })
  }
  
  // 同时加载配额信息
  await loadQuotaInfo()
}

const loadQuotaInfo = async () => {
  try {
    console.log('开始加载配额信息...')
    const response = await deviceAPI.getQuotaInfo()
    console.log('配额信息响应:', response)
    if (response.code === 0) {
      quotaInfo.value = response.data
      console.log('配额信息已设置:', quotaInfo.value)
    } else {
      console.error('配额信息加载失败:', response.message)
    }
  } catch (error) {
    console.error('加载配额信息失败:', error)
  }
}

const startPairing = async () => {
  try {
    // 先检查配额信息
    if (!quotaInfo.value) {
      await loadQuotaInfo()
    }
    
    // 检查是否有可用配额
    if (quotaInfo.value && quotaInfo.value.available_quota <= 0) {
      uni.showToast({
        title: '设备配额不足，请购买更多配额',
        icon: 'none',
        duration: 3000
      })
      return
    }
    
    // 有配额时才开始生成配对码
    pairingStep.value = 'generating'
    generatePairingCode()
  } catch (error) {
    console.error('检查配额失败:', error)
    uni.showToast({
      title: '检查配额失败，请重试',
      icon: 'none'
    })
  }
}

const onSearchInput = () => {
  // 搜索功能已通过计算属性实现
}

const clearSearch = () => {
  searchKeyword.value = ''
}



const buyQuota = async () => {
  // 如果配额信息未加载，先加载
  if (!quotaInfo.value) {
    await loadQuotaInfo()
  }
  
  // 显示购买弹窗
  showQuotaModal.value = true
}

// 续费相关方法
const showRenewalOptions = async () => {
  try {
    uni.showLoading({ title: '加载中...' })
    
    const response = await deviceAPI.getRenewableQuotas()
    
    uni.hideLoading()
    
    if (response.code === 0) {
      renewableQuotas.value = response.data || []
      if (renewableQuotas.value.length === 0) {
        uni.showToast({
          title: '没有可续费的配额',
          icon: 'none'
        })
        return
      }
      showRenewalModal.value = true
    } else {
      throw new Error(response.message || '获取可续费配额失败')
    }
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '获取可续费配额失败',
      icon: 'none'
    })
  }
}

const closeRenewalModal = () => {
  showRenewalModal.value = false
  renewableQuotas.value = []
  selectedQuotaForRenewal.value = null
  renewalOrderNo.value = ''
  renewalOrderStatus.value = ''
  
  // 清除定时器
  if (renewalOrderTimer.value) {
    clearInterval(renewalOrderTimer.value)
    renewalOrderTimer.value = null
  }
}

const selectQuotaForRenewal = (quota: any) => {
  selectedQuotaForRenewal.value = quota
}

const createRenewalOrder = async (payType: string) => {
  if (!selectedQuotaForRenewal.value) {
    uni.showToast({
      title: '请选择要续费的配额',
      icon: 'none'
    })
    return
  }
  
  try {
    uni.showLoading({ title: '创建续费订单中...' })
    
    const response = await deviceAPI.createQuotaRenewalOrder({
      quota_id: selectedQuotaForRenewal.value.id,
      pay_type: payType
    })
    
    uni.hideLoading()
    
    if (response.code === 0) {
      const order = response.data
      if (!order || !order.pay_url) {
        uni.showToast({ title: '创建续费订单失败', icon: 'none' })
        return
      }
      
      renewalOrderNo.value = order.order_no
      renewalOrderStatus.value = 'pending'
      
      // 跳转到支付页面
      uni.navigateTo({
        url: `/pages/payment/index?pay_url=${encodeURIComponent(order.pay_url)}&order_no=${order.order_no}&amount=${order.amount}&pay_type=${order.pay_type}&created_at=${encodeURIComponent(order.created_at || '')}&type=quota_renewal`
      })
      
      // 开始轮询订单状态
      startRenewalOrderPolling()
    } else {
      throw new Error(response.message || '创建续费订单失败')
    }
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '创建续费订单失败',
      icon: 'none'
    })
  }
}

const startRenewalOrderPolling = () => {
  if (renewalOrderTimer.value) {
    clearInterval(renewalOrderTimer.value)
  }
  
  renewalOrderTimer.value = setInterval(async () => {
    if (!renewalOrderNo.value) {
      clearInterval(renewalOrderTimer.value!)
      renewalOrderTimer.value = null
      return
    }
    
    try {
      const response = await api.getOrderDetail(renewalOrderNo.value)
      
      if (response.code === 0) {
        const order = response.data
        if (order.status === 1) { // 已支付
          renewalOrderStatus.value = 'paid'
          clearInterval(renewalOrderTimer.value!)
          renewalOrderTimer.value = null
          
          uni.showToast({
            title: '续费成功！',
            icon: 'success'
          })
          
          // 刷新配额信息
          await loadQuotaInfo()
          
          // 关闭续费弹窗
          setTimeout(() => {
            closeRenewalModal()
          }, 1500)
        }
      }
    } catch (error) {
      console.error('轮询续费订单状态失败:', error)
    }
  }, 3000) // 每3秒轮询一次
}



const closeQuotaModal = () => {
  showQuotaModal.value = false
  quotaInfo.value = null
  selectedQuotaCount.value = 5
}

const selectQuotaCount = (count: number) => {
  selectedQuotaCount.value = count
  customQuotaCount.value = '' // 清空自定义输入
}

const onCustomQuotaInput = () => {
  const count = parseInt(customQuotaCount.value)
  if (count && count > 0 && count <= 999) {
    selectedQuotaCount.value = count
    customQuotaPrice.value = count
  } else {
    customQuotaPrice.value = 0
  }
}

const createQuotaOrder = async (payType: string) => {
  try {
    uni.showLoading({ title: '创建订单中...' })
    
    const response = await deviceAPI.createQuotaOrder({
      quota_count: selectedQuotaCount.value,
      pay_type: payType
    })
    
    uni.hideLoading()
    
    if (response.code === 0) {
      const order = response.data
      if (!order || !order.pay_url) {
        uni.showToast({ title: '创建订单失败', icon: 'none' })
        return
      }
      
      // 跳转到支付页面，传递所有订单详情参数
      uni.navigateTo({
        url: `/pages/payment/index?pay_url=${encodeURIComponent(order.pay_url)}&order_no=${order.order_no}&amount=${order.amount}&pay_type=${order.pay_type}&created_at=${encodeURIComponent(order.created_at || '')}&type=quota`
      })
      
      closeQuotaModal()
    } else {
      throw new Error(response.message || '创建订单失败')
    }
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '创建订单失败',
      icon: 'none'
    })
  }
}



const stopScript = async (device: any) => {
  // 检查设备是否在线
  if (device.status !== DEVICE_STATUS.ONLINE) {
    uni.showToast({
      title: '设备离线，无法停止脚本',
      icon: 'none'
    })
    return
  }
  
  // 检查设备是否正在操作中
  if (operatingDevices.value.has(device.device_id)) {
    uni.showToast({
      title: '设备正在操作中，请稍候',
      icon: 'none'
    })
    return
  }
  
  // 标记设备正在操作中
  operatingDevices.value.add(device.device_id)
  
  try {
    // 使用WebSocket直接发送停止命令（更快速）
    const wsService = getWebSocketService()
    if (wsService && wsService.isConnected()) {
      // 发送停止脚本消息
      wsService.send({
        type: 'SCRIPT_STOP',
        seq: generateUUID(),
        timestamp: Date.now(),
        payload: {
          device_ids: [device.device_id], // 使用device_ids数组格式
          action: 'stop_all' // 停止设备上的所有脚本
        }
      })
      
      uni.showToast({
        title: '停止命令已发送',
        icon: 'success'
      })
    } else {
      // WebSocket不可用时，降级到HTTP方式
      const response = await api.stopScript({ device_ids: [device.device_id] })
      if (response.code === 0) {
        uni.showToast({
          title: '停止命令已发送',
          icon: 'success'
        })
      } else {
        throw new Error(response.message || '停止失败')
      }
    }
  } catch (error) {
    uni.showToast({
      title: `停止失败: ${error.message || '未知错误'}`,
      icon: 'none'
    })
  } finally {
    // 移除操作状态
    operatingDevices.value.delete(device.device_id)
  }
}

// 编辑相关状态
const showEditModal = ref(false)
const editingDevice = ref<any>(null)
const editForm = ref({
  name: ''
})

// 删除相关状态
const showDeleteModal = ref(false)
const deletingDevice = ref<any>(null)

const editDevice = (device: any) => {
  // 设置编辑数据
  editingDevice.value = device
  editForm.value.name = device.name
  
  // 显示编辑弹窗
  showEditModal.value = true
}

const closeEditModal = () => {
  showEditModal.value = false
  editingDevice.value = null
  editForm.value = { name: '' }
}

const submitEdit = async () => {
  if (!editForm.value.name.trim()) {
    uni.showToast({
      title: '设备名称不能为空',
      icon: 'none'
    })
    return
  }
  
  // 调用API更新设备信息
  await updateDeviceInfo(editingDevice.value.device_id, editForm.value.name.trim())
  
  // 关闭弹窗
  closeEditModal()
}

const updateDeviceInfo = async (deviceId: string, name: string) => {
  try {
    uni.showLoading({ title: '更新中...' })
    
    const result = await deviceStore.updateDeviceInfo(deviceId, name)
    
    uni.hideLoading()
    
    if (result.success) {
      uni.showToast({
        title: '更新成功',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: result.message || '更新失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '更新失败',
      icon: 'none'
    })
  }
}

const deleteDevice = async (device: any) => {
  if (!device.device_id) {
    uni.showToast({
      title: '设备ID不存在',
      icon: 'none'
    })
    return
  }
  
  // 检查设备是否正在操作中
  if (operatingDevices.value.has(device.device_id)) {
    uni.showToast({
      title: '设备正在操作中，请稍候',
      icon: 'none'
    })
    return
  }
  
  // 设置删除数据
  deletingDevice.value = device
  
  // 显示删除弹窗
  showDeleteModal.value = true
}

const closeDeleteModal = () => {
  showDeleteModal.value = false
  deletingDevice.value = null
}

const confirmDelete = async () => {
  if (!deletingDevice.value) return
  
  const device = deletingDevice.value
  
  // 立即关闭弹窗
  closeDeleteModal()
  
  // 标记设备正在操作中
  operatingDevices.value.add(device.device_id)
  
  try {
    const result = await deviceStore.deleteDevice(device.device_id)
    
    if (result.success) {
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      })
      // 删除成功后刷新设备列表和配额信息
      await loadDevices()
    } else {
      uni.showToast({
        title: result.message || '删除失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    })
  } finally {
    // 移除操作状态
    operatingDevices.value.delete(device.device_id)
  }
}

const generatePairingCode = async () => {
  try {
    const response = await pairingAPI.generatePairingCode()
    if (response.code === 0) {
      pairingCode.value = response.data.pairing_code
      pairingStep.value = 'waiting'
      startPairingPolling()
    } else {
      throw new Error(response.message || '生成配对码失败')
    }
  } catch (error) {
    // 显示具体的错误信息
    const errorMessage = error.message || '生成配对码失败'
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    })
    pairingStep.value = 'idle'
  }
}

const startPairingPolling = () => {
  if (pairingCode.value) {
    pairingTimer.value = setInterval(async () => {
      try {
        const response = await pairingAPI.getPairingStatus({ pairing_code: pairingCode.value })
        
        if (response.code === 0) {
          const pairingData = response.data.pairing
          const status = pairingData.pairing_status
          
          if (status === 2) { // 配对成功
            clearInterval(pairingTimer.value!)
            pairingStep.value = 'success'
            uni.showToast({
              title: '配对成功！',
              icon: 'success'
            })
            // 配对成功后立即刷新设备列表
            setTimeout(() => {
              loadDevices()
            }, 1000)
          } else if (status === 3) { // 配对失败或过期
            clearInterval(pairingTimer.value!)
            pairingStep.value = 'idle'
            uni.showToast({
              title: '配对失败或已过期',
              icon: 'none'
            })
          }
          // status === DEVICE_STATUS.ONLINE 表示等待配对，继续轮询
        } else {
          // API返回错误
          clearInterval(pairingTimer.value!)
          pairingStep.value = 'idle'
          uni.showToast({
            title: response.message || '配对失败',
            icon: 'none'
          })
        }
      } catch (error) {
        // 网络错误时不停止轮询，继续尝试
      }
    }, 2000) // 每2秒检查一次
  }
}

const cancelPairing = () => {
  console.log('cancelPairing 被调用')
  if (pairingTimer.value) {
    clearInterval(pairingTimer.value)
    pairingTimer.value = null
  }
  pairingStep.value = 'idle'
  pairingCode.value = ''
  showCopySuccess.value = false
}

const copyPairingCode = () => {
  console.log('copyPairingCode 被调用，配对码:', pairingCode.value)
  
  if (!pairingCode.value) {
    uni.showToast({
      title: '配对码为空',
      icon: 'none'
    })
    return
  }

  // 直接复制，不使用延迟
  uni.setClipboardData({
    data: pairingCode.value,
    success: () => {
      console.log('复制成功')
      // 显示内部成功提示
      showCopySuccess.value = true
      // 3秒后隐藏提示
      setTimeout(() => {
        showCopySuccess.value = false
      }, 3000)
    },
    fail: (err) => {
      console.error('复制失败:', err)
      uni.showToast({
        title: '复制失败，请手动复制',
        icon: 'none',
        duration: 2000
      })
    }
  })
}



// 使用统一的日期格式化函数

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '待支付'
    case 'paid':
      return '已支付'
    case 'failed':
      return '支付失败'
    case 'timeout':
      return '支付超时'
    default:
      return '未知状态'
  }
}

// 生命周期
onMounted(() => {
  loadDevices()
})

// 页面显示时刷新数据（tab切换时触发）
onShow(() => {
  const now = Date.now()
  
  // 检查是否有强制刷新标记
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options
  
  // 如果有刷新标记或距离上次刷新超过5秒，则进行刷新
  if (options.refresh === 'true' || now - lastRefreshTime.value > 5000) {
    loadDevices()
    lastRefreshTime.value = now
    
    // 清除刷新标记
    if (options.refresh === 'true') {
      delete options.refresh
    }
  }
})

onUnmounted(() => {
  if (pairingTimer.value) {
    clearInterval(pairingTimer.value)
  }
  
  // 清理续费订单轮询定时器
  if (renewalOrderTimer.value) {
    clearInterval(renewalOrderTimer.value)
  }
})
</script>

<style lang="scss" scoped>
// 全局按钮重置
button {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  background: transparent;
  
  &::before,
  &::after {
    display: none !important;
  }
}

.device-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: env(safe-area-inset-bottom);
}

// 紧凑型头部设计
.header-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 12px 16px;
  border-bottom: 1px solid #dee2e6;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

// 状态栏
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.page-title {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ws-status {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.05);
  padding: 3px 6px;
  border-radius: 8px;
}

.ws-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  
  &.ws-connected {
    background: #52c41a;
    box-shadow: 0 0 6px rgba(82, 196, 26, 0.6);
    animation: pulse 2s infinite;
  }
  
  &.ws-connecting {
    background: #faad14;
    box-shadow: 0 0 6px rgba(250, 173, 20, 0.6);
    animation: pulse 1s infinite;
  }
  
  &.ws-reconnecting {
    background: #faad14;
    box-shadow: 0 0 6px rgba(250, 173, 20, 0.6);
    animation: pulse 0.5s infinite;
  }
  
  &.ws-disconnected {
    background: #ff4d4f;
    box-shadow: 0 0 6px rgba(255, 77, 79, 0.6);
  }
}

.ws-text {
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

.refresh-btn {
  width: 28px;
  height: 28px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.95);
    background: rgba(0, 0, 0, 0.1);
  }
}

// 统计和操作区域
.stats-actions-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

// 统计行
.stats-row {
  display: flex;
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #333;
  line-height: 1;
  margin-bottom: 2px;
  
  &.online {
    color: #52c41a;
  }
  
  &.offline {
    color: #ff4d4f;
  }
  
  &.quota {
    color: #52c41a;
  }
}

.stat-label {
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

// 操作按钮
.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0 12px;
  min-width: 70px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  
  &:active {
    transform: scale(0.95);
  }
  
  &.primary {
    background: linear-gradient(135deg, #0050d0, #1677ff);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.5);
  }
}

  &.secondary {
    background: linear-gradient(135deg, #389e0d, #52c41a);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
      box-shadow: 0 4px 12px rgba(82, 196, 26, 0.5);
    }
  }

  &.tertiary {
    background: linear-gradient(135deg, #fa8c16, #faad14);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
      box-shadow: 0 4px 12px rgba(250, 173, 20, 0.5);
    }
  }
  

}



.btn-text {
  font-size: 13px;
  font-weight: 700;
  color: #ffffff;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

// 搜索容器
.search-container {
  margin-bottom: 4px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 0 12px;
  height: 32px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.search-input {
  flex: 1;
  height: 32px;
  border: none;
  outline: none;
  font-size: 13px;
  color: #333;
  background: transparent;
  margin-left: 6px;
}

// 设备列表 - 更紧凑
.device-list {
  padding: 6px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f0f0f0;
  border-top: 3px solid #1677ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}



.empty-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #999;
}

// 设备网格 - 更紧凑的布局
.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 6px;
}

// 响应式布局 - 优化移动端显示
@media (max-width: 480px) {
  .device-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .device-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
  }
}

@media (min-width: 769px) {
  .device-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
  }
}

.device-card {
  background: #fff;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.06);
  min-width: 0; // 防止内容溢出
  word-wrap: break-word; // 长文本自动换行
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06);
    border-color: rgba(22, 119, 255, 0.2);
  }
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 3px;
}

.device-info {
  flex: 1;
  min-width: 0; // 允许flex子项收缩
  overflow: hidden; // 防止内容溢出
}

.device-name {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.2s ease;
  
  .device-card:hover & {
    color: #1677ff;
  }
}

.device-id {
  display: block;
  font-size: 9px;
  color: #666;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: rgba(0, 0, 0, 0.04);
  padding: 1px 4px;
  border-radius: 3px;
  display: inline-block;
}

.device-status {
  margin-left: 8px;
}

.status-badge {
  display: flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &.status-online {
    background: linear-gradient(135deg, rgba(82, 196, 26, 0.15) 0%, rgba(82, 196, 26, 0.08) 100%);
    color: #52c41a;
    border: 1px solid rgba(82, 196, 26, 0.2);
  }
  
  &.status-offline {
    background: linear-gradient(135deg, rgba(153, 153, 153, 0.15) 0%, rgba(153, 153, 153, 0.08) 100%);
    color: #999;
    border: 1px solid rgba(153, 153, 153, 0.2);
  }
  
  &.status-expired {
    background: linear-gradient(135deg, rgba(245, 34, 45, 0.15) 0%, rgba(245, 34, 45, 0.08) 100%);
    color: #f5222d;
    border: 1px solid rgba(245, 34, 45, 0.2);
  }
  
  .device-card:hover & {
    transform: scale(1.05);
  }
}

.status-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  margin-right: 3px;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8);
  
  &.status-online {
    background: #52c41a;
    animation: pulse 2s infinite;
  }
  
  &.status-offline {
    background: #999;
  }
  
  &.status-expired {
    background: #f5222d;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header-section {
  // 移除向上动画
}

.stat-card {
  // 移除向上动画
}

.status-text {
  font-size: 11px;
  font-weight: 500;
}

// 设备详情 - 更紧凑
.device-details {
  margin-bottom: 6px;
  padding: 6px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 9px;
  color: #666;
  width: 28px;
  flex-shrink: 0;
  font-weight: 500;
}

.detail-value {
  font-size: 9px;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 400;
}

.quota-info {
  display: flex;
  align-items: center;
}

.quota-free {
  font-size: 9px;
  color: #52c41a;
  font-weight: 500;
  background: rgba(82, 196, 26, 0.1);
  padding: 1px 4px;
  border-radius: 3px;
}

.quota-paid {
  font-size: 9px;
  color: #1677ff;
  font-weight: 500;
  background: rgba(22, 119, 255, 0.1);
  padding: 1px 4px;
  border-radius: 3px;
}

.quota-expired {
  font-size: 9px;
  color: #f5222d;
  font-weight: 500;
  background: rgba(245, 34, 45, 0.1);
  padding: 1px 4px;
  border-radius: 3px;
}

.quota-unknown {
  font-size: 9px;
  color: #999;
  font-weight: 400;
}



// 操作按钮 - 更紧凑
.device-actions {
  display: flex;
  justify-content: center;
  gap: 6px;
  margin-top: 8px;
  padding: 6px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.device-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0;
  margin: 0;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  
  &:hover::before {
    opacity: 1;
  }
  
  &.stop-btn {
    background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
    box-shadow: 0 1px 4px rgba(250, 173, 20, 0.3);
    
    &:hover {
      box-shadow: 0 2px 8px rgba(250, 173, 20, 0.4);
    }
  }
  
  &.edit-btn {
    background: linear-gradient(135deg, #1677ff 0%, #4096ff 100%);
    box-shadow: 0 1px 4px rgba(22, 119, 255, 0.3);
    
    &:hover {
      box-shadow: 0 2px 8px rgba(22, 119, 255, 0.4);
    }
  }
  
  &.delete-btn {
    background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%);
    box-shadow: 0 1px 4px rgba(245, 34, 45, 0.3);
    
    &:hover {
      box-shadow: 0 2px 8px rgba(245, 34, 45, 0.4);
    }
  }
  
  &:active:not(:disabled) {
    transform: translateY(0) scale(0.95);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
  
  // 图标样式
  .uni-icons {
    color: #ffffff !important;
    transition: transform 0.2s ease;
  }
  
  &:hover .uni-icons {
    transform: scale(1.1);
  }
}





// 配对弹窗
.pairing-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 320px;
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 24px;
  color: #999;
  padding: 4px;
  cursor: pointer;
}

.modal-body {
  padding: 20px;
}

.pairing-step {
  text-align: center;
}

.step-icon {
  margin-bottom: 16px;
  
  &.loading {
    animation: spin 1s linear infinite;
  }
  
  &.success {
    color: #52c41a;
  }
}



.step-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.step-desc {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.code-section {
  margin-bottom: 20px;
}

.code-title {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.code-value {
  display: block;
  margin-bottom: 12px;
  text-align: center;
  background: rgba(22, 119, 255, 0.08);
  padding: 20px 24px;
  border-radius: 8px;
  border: 1px solid rgba(22, 119, 255, 0.15);
  user-select: all;
  -webkit-user-select: all;
  -moz-user-select: all;
  -ms-user-select: all;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
    background: rgba(22, 119, 255, 0.12);
    border-color: rgba(22, 119, 255, 0.25);
  }
}

.code-text {
  display: block;
  font-size: 40px;
  font-weight: 600;
  color: #1677ff;
  font-family: 'Arial', 'Helvetica', sans-serif;
  letter-spacing: 6px;
  line-height: 1.2;
}

.code-desc {
  display: block;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin-top: 8px;
}

.copy-success-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin-top: 12px;
  padding: 8px 16px;
  background: rgba(82, 196, 26, 0.1);
  border: 1px solid rgba(82, 196, 26, 0.2);
  border-radius: 6px;
  animation: fadeIn 0.3s ease;
}

.copy-success-text {
  font-size: 14px;
  color: #52c41a;
  font-weight: 500;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-text {
  font-size: 14px;
  color: #666;
}

.complete-btn {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #1677ff, #4096ff);
  border-radius: 22px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  margin-top: 16px;
  
  &:active {
    transform: scale(0.98);
  }
}

// 编辑弹窗样式
.edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-modal {
  width: 320px;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.edit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.edit-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.edit-modal-close {
  padding: 4px;
  cursor: pointer;
  color: #999;
  
  &:active {
    transform: scale(0.9);
  }
}

.edit-modal-content {
  padding: 20px;
}

.form-item {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  background: #fff;
  outline: none;
  
  &:focus {
    border-color: #1677ff;
    box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
  }
  
  &::placeholder {
    color: #999;
  }
}

.form-tip {
  display: block;
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.edit-modal-footer {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
}

.btn-cancel {
  flex: 1;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    background: #f5f5f5;
  }
}

.btn-confirm {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(135deg, #1677ff, #4096ff);
  color: white;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    transform: scale(0.98);
  }
}

// 删除弹窗样式
.delete-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-modal {
  width: 320px;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.delete-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.delete-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.delete-modal-close {
  padding: 4px;
  cursor: pointer;
  color: #999;
  
  &:active {
    transform: scale(0.9);
  }
}

.delete-modal-content {
  padding: 24px 20px;
  text-align: center;
}

.delete-icon {
  margin-bottom: 16px;
}

.delete-message {
  display: block;
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.5;
}

.delete-warning {
  display: block;
  font-size: 13px;
  color: #f5222d;
  line-height: 1.4;
}

.delete-modal-footer {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
}

.btn-delete {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(135deg, #f5222d, #ff4d4f);
  color: white;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    transform: scale(0.98);
  }
}

// 配额购买弹窗样式
.quota-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quota-modal {
  width: 360px;
  max-height: 80vh;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.quota-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.quota-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.quota-modal-close {
  padding: 4px;
  cursor: pointer;
  color: #999;
  
  &:active {
    transform: scale(0.9);
  }
}

.quota-modal-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}



.section-title {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.quota-selection {
  margin-bottom: 20px;
}

.quota-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.quota-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.95);
  }
  
  &.active {
    border-color: #1677ff;
    background: rgba(22, 119, 255, 0.05);
  }
}

.option-count {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.option-price {
  font-size: 12px;
  color: #1677ff;
  font-weight: 500;
}

.custom-quota {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 12px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.custom-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
}

.custom-input {
  flex: 1;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 14px;
  color: #333;
  background: #fff;
  outline: none;
  
  &:focus {
    border-color: #1677ff;
  }
}

// 续费配额样式
.renewal-quotas {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.renewal-quota-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fafafa;
  
  &:active {
    transform: scale(0.98);
  }
  
  &.active {
    border-color: #1677ff;
    background: rgba(22, 119, 255, 0.05);
  }
}

.quota-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.quota-count {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.quota-expiry {
  font-size: 12px;
  color: #666;
}

.quota-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.detail-text {
  font-size: 12px;
  color: #666;
}

.renewal-text {
  color: #4CAF50;
  font-weight: 500;
}

.quota-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.price-text {
  font-size: 14px;
  font-weight: 600;
  color: #1677ff;
}

.price-desc {
  font-size: 11px;
  color: #999;
}

// 订单信息样式
.order-info-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.order-info-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
  border: 1px solid #e9ecef;
}

.order-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.order-label {
  font-size: 13px;
  color: #666;
  min-width: 60px;
}

.order-value {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  font-family: 'Courier New', monospace;
  background: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.order-status {
  font-size: 13px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  
  &.pending {
    color: #faad14;
    background: rgba(250, 173, 20, 0.1);
  }
  
  &.paid {
    color: #52c41a;
    background: rgba(82, 196, 26, 0.1);
  }
  
  &.failed {
    color: #ff4d4f;
    background: rgba(255, 77, 79, 0.1);
  }
  
  &.timeout {
    color: #8c8c8c;
    background: rgba(140, 140, 140, 0.1);
  }
}

.custom-price {
  font-size: 14px;
  color: #1677ff;
  font-weight: 600;
  white-space: nowrap;
}

.payment-section {
  margin-bottom: 20px;
}

.payment-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 44px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #1677ff, #4096ff);
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  padding: 0 16px;
  
  &:active {
    transform: scale(0.98);
    background: linear-gradient(135deg, #0958d9, #1677ff);
  }
}

.payment-text {
  font-size: 14px;
  font-weight: 500;
}
</style>


