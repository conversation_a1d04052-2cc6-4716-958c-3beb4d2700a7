<template>
  <view class="me-page">
    <!-- 用户信息卡片 -->
    <view class="user-card card" @click="showProfileDetail">
      <view class="user-header">
        <view class="avatar-wrapper">
          <image 
            v-if="userInfo.avatar && userInfo.avatar.trim()" 
            :src="userInfo.avatar" 
            class="avatar"
            mode="aspectFill"
          />
          <view v-else class="avatar-placeholder">
            <text class="avatar-text">{{ (userInfo.nickname || userInfo.username || 'U').charAt(0).toUpperCase() }}</text>
          </view>
        </view>
        <view class="user-info">
          <view class="user-name">{{ userInfo.nickname || userInfo.username || '用户' }}</view>
          <view class="user-username">@{{ userInfo.username || 'username' }}</view>
        </view>
        <view class="user-arrow">
          <text class="arrow-text">></text>
        </view>
      </view>
    </view>

                    <!-- 功能菜单 -->
                <view class="menu-section">
                  <view class="menu-group card">
                    <view class="menu-item" @click="goToOrders">
                      <view class="menu-icon">
                        <uni-icons type="receipt" size="20" color="#667eea" />
                      </view>
                      <view class="menu-content">
                        <text class="menu-title">订单</text>
                      </view>
                      <view class="menu-arrow">></view>
                    </view>
        
        <view class="menu-item" @click="showAccountSecurity">
          <view class="menu-icon">
            <uni-icons type="shield" size="20" color="#667eea" />
          </view>
          <view class="menu-content">
            <text class="menu-title">账户安全</text>
          </view>
          <view class="menu-arrow">></view>
        </view>
      </view>

      <view class="menu-group card">
        <view class="menu-item" @click="showHelpCenter">
          <view class="menu-icon">
            <uni-icons type="help" size="20" color="#667eea" />
          </view>
          <view class="menu-content">
            <text class="menu-title">帮助中心</text>
          </view>
          <view class="menu-arrow">></view>
        </view>
        
        <view class="menu-item" @click="showAbout">
          <view class="menu-icon">
            <uni-icons type="info" size="20" color="#667eea" />
          </view>
          <view class="menu-content">
            <text class="menu-title">关于我们</text>
          </view>
          <view class="menu-arrow">></view>
        </view>
        
        <view class="menu-item" @click="showFeedback">
          <view class="menu-icon">
            <uni-icons type="chat" size="20" color="#667eea" />
          </view>
          <view class="menu-content">
            <text class="menu-title">意见反馈</text>
          </view>
          <view class="menu-arrow">></view>
        </view>
      </view>

      <view class="menu-group card">
        <view class="menu-item" @click="clearCache">
          <view class="menu-icon">
            <uni-icons type="trash" size="20" color="#667eea" />
          </view>
          <view class="menu-content">
            <text class="menu-title">清除缓存</text>
          </view>
          <view class="menu-arrow">></view>
        </view>
        
        <view class="menu-item" @click="checkUpdate">
          <view class="menu-icon">
            <uni-icons type="refresh" size="20" color="#667eea" />
          </view>
          <view class="menu-content">
            <text class="menu-title">检查更新</text>
          </view>
          <view class="menu-arrow">></view>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section">
      <button class="btn btn-large logout-btn" @click="logout">
        退出登录
      </button>
    </view>

    <!-- 个人资料编辑弹窗 -->
    <view v-if="showProfileModal" class="modal-overlay" @click="closeProfileModal">
      <view class="modal-content card" @click.stop>
        <view class="modal-header">
          <text class="modal-title">个人资料</text>
          <uni-icons type="close" size="20" color="#999" class="modal-close" @click="closeProfileModal" />
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">用户名</text>
            <text class="form-value">{{ userInfo.username }}</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">昵称</text>
            <input 
              class="form-input" 
              v-model="editForm.nickname"
              placeholder="请输入昵称"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">邮箱</text>
            <input 
              class="form-input" 
              v-model="editForm.email"
              placeholder="请输入邮箱"
              type="email"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">注册时间</text>
            <text class="form-value">{{ formatRelativeTime(userInfo.created_at) }}</text>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn btn-secondary" @click="closeProfileModal">
            取消
          </button>
          <button class="btn btn-primary" @click="saveProfile">
            保存
          </button>
        </view>
      </view>
    </view>

  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
// 已移除 vue-icons-plus 依赖，使用 uni-icons 替代
import { useUserStore } from '@/stores/user'
import { authAPI, userAPI } from '@/utils/api'
import { formatRelativeTime } from '@/utils/date'

// 响应式数据
const userInfo = ref<any>({})
const appVersion = ref('1.0.0')
const lastRefreshTime = ref(0)
const showProfileModal = ref(false)
const editForm = ref({
  nickname: '',
  email: ''
})

// 用户状态管理
const userStore = useUserStore()

// 方法
const loadUserInfo = async () => {
  try {
    const response = await userAPI.getUserProfile()
    if (response.code === 0) {
      userInfo.value = response.data
      console.log('用户信息加载成功:', userInfo.value)
    } else {
      console.log('用户信息加载失败:', response)
      // 设置默认用户信息
      userInfo.value = {
        username: '用户',
        nickname: '用户',
        avatar: null
      }
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    // 设置默认用户信息
    userInfo.value = {
      username: '用户',
      nickname: '用户',
      avatar: null
    }
  }
}





const showProfileDetail = () => {
  // 打开个人资料编辑弹窗
  editForm.value.nickname = userInfo.value.nickname || ''
  editForm.value.email = userInfo.value.email || ''
  showProfileModal.value = true
}

const closeProfileModal = () => {
  showProfileModal.value = false
}

const saveProfile = async () => {
  try {
    const response = await userAPI.updateUserProfile({
      nickname: editForm.value.nickname,
      email: editForm.value.email
    })
    if (response.code === 0) {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
      loadUserInfo() // 重新加载用户信息
      closeProfileModal()
    } else {
      uni.showToast({
        title: response.message || '保存失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('保存个人资料失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}

// 使用统一的日期格式化函数



const goToOrders = () => {
  uni.showModal({
    title: '订单管理',
    content: '订单管理功能开发中...',
    showCancel: false
  })
}



const showAccountSecurity = () => {
  uni.showModal({
    title: '账户安全',
    content: '密码修改功能开发中...',
    showCancel: false
  })
}

const showHelpCenter = () => {
  uni.showModal({
    title: '帮助中心',
    content: '帮助中心功能开发中...',
    showCancel: false
  })
}

const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content: `v${appVersion.value}\n\n一个现代化的设备管理和脚本执行平台\n\n技术支持：<EMAIL>`,
    showCancel: false
  })
}

const showFeedback = () => {
  uni.showModal({
    title: '意见反馈',
    content: '意见反馈功能开发中...',
    showCancel: false
  })
}

const clearCache = () => {
  uni.showModal({
    title: '清除缓存',
    content: '确定要清除本地缓存数据吗？',
    success: (res) => {
      if (res.confirm) {
        uni.clearStorageSync()
        uni.showToast({
          title: '缓存已清除',
          icon: 'success'
        })
      }
    }
  })
}

const checkUpdate = () => {
  uni.showModal({
    title: '检查更新',
    content: `当前版本：${appVersion.value}\n\n已是最新版本`,
    showCancel: false
  })
}

const logout = () => {
  uni.showModal({
    title: '退出登录',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await authAPI.userLogout()
          // 清除本地存储
          uni.clearStorageSync()
          // 清除用户状态
          userStore.logout()
          // 跳转到登录页
          uni.reLaunch({
            url: '/pages/login/index'
          })
        } catch (error) {
          console.error('退出登录失败:', error)
          // 即使API调用失败，也清除本地数据并跳转
          uni.clearStorageSync()
          userStore.logout()
          uni.reLaunch({
            url: '/pages/login/index'
          })
        }
      }
    }
  })
}



// 生命周期
onMounted(() => {
  loadUserInfo()
})

// 页面显示时刷新数据（tab切换时触发）
onShow(() => {
  const now = Date.now()
  // 如果距离上次刷新超过5秒，才进行刷新
  if (now - lastRefreshTime.value > 5000) {
    loadUserInfo()
    lastRefreshTime.value = now
  }
})
</script>

<style lang="scss" scoped>
.me-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: env(safe-area-inset-bottom);
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.user-card {
  margin: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    background: #f8f9fa;
    transform: scale(0.98);
  }
  
  .user-header {
    display: flex;
    align-items: center;
    padding: 20px;
  }
  
  .avatar-wrapper {
    margin-right: 16px;
  }
  
  .avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }
  
  .avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  }
  
  .avatar-text {
    font-size: 24px;
    font-weight: 600;
    color: white;
  }
  
  .user-info {
    flex: 1;
  }
  
  .user-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }
  
  .user-username {
    font-size: 14px;
    color: #666;
  }
  
  .user-arrow {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
  }
  
  .arrow-text {
    font-size: 16px;
    color: #999;
    font-weight: 500;
  }
}

.menu-section {
  padding: 0 16px;
}

.menu-group {
  margin-bottom: 12px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f9fa;
  }
}

.menu-icon {
  font-size: 20px;
  margin-right: 16px;
  width: 24px;
  text-align: center;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.menu-arrow {
  font-size: 16px;
  color: #ccc;
}

.logout-section {
  padding: 20px 16px;
}

.btn {
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &.btn-small {
    padding: 8px 16px;
    font-size: 12px;
  }
  
  &.btn-large {
    width: 100%;
    height: 48px;
    font-size: 16px;
  }
  
  &.btn-secondary {
    background: #f0f0f0;
    color: #666;
    
    &:active {
      background: #e0e0e0;
    }
  }
  
  &.btn-primary {
    background: #667eea;
    color: white;
    
    &:active {
      background: #5a6fd8;
    }
  }
  
  &.logout-btn {
    background: #ff4d4f;
    color: white;
    
    &:active {
      background: #cf1322;
    }
  }
}

// 弹窗样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 90vw;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  
  &:active {
    background: #f0f0f0;
  }
}

.modal-body {
  padding: 20px;
}

.form-item {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
  }
  
  &::placeholder {
    color: #ccc;
  }
}

.form-value {
  display: block;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  border: 1px solid #e0e0e0;
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  
  .btn {
    flex: 1;
    height: 44px;
    font-size: 16px;
  }
}
</style>

