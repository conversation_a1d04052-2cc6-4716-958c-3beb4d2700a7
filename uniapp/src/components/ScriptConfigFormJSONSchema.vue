<template>
  <view class="script-config-form">
    <!-- 使用 @jsonforms/vue 渲染 JSON Schema -->
    <div v-if="configSchema" class="json-schema-form">
      <!-- 这里可以集成 @jsonforms/vue 或其他 JSON Schema 渲染库 -->
      <div class="schema-info">
        <h3>配置结构</h3>
        <pre>{{ JSON.stringify(configSchema, null, 2) }}</pre>
      </div>
    </div>
    
    <!-- 自定义渲染的配置表单 -->
    <div v-else-if="configFields.length > 0" class="custom-form">
      <div 
        v-for="field in configFields" 
        :key="field.name"
        class="config-field"
      >
        <div class="field-label">
          <span class="field-title">{{ field.label }}</span>
          <span v-if="field.help" class="field-help">{{ field.help }}</span>
        </div>
        
        <!-- 根据字段类型渲染不同的控件 -->
        <component 
          :is="getFieldComponent(field.type)"
          :field="field"
          :value="configValues[field.name]"
          @update="updateField(field.name, $event)"
        />
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <view class="form-actions">
      <button class="btn btn-secondary" @click="$emit('cancel')">取消</button>
      <button class="btn btn-primary" @click="handleSubmit">保存配置</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { api } from '@/utils/api'

// 异步加载字段组件
const TextField = defineAsyncComponent(() => import('./fields/TextField.vue'))
const NumberField = defineAsyncComponent(() => import('./fields/NumberField.vue'))
const RadioField = defineAsyncComponent(() => import('./fields/RadioField.vue'))
const CheckboxField = defineAsyncComponent(() => import('./fields/CheckboxField.vue'))

// Props
interface Props {
  scriptId: number
  configId?: number
  initialValues?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  submit: [config: any]
  cancel: []
}>()

// 响应式数据
const configSchema = ref<any>(null)
const configFields = ref<any[]>([])
const configValues = ref<any>({})

// 方法
const loadScriptConfigSchema = async () => {
  try {
    const res = await api.getScriptConfigSchema(props.scriptId)
    
    // 如果返回的是数组格式，转换为字段配置
    if (Array.isArray(res.data)) {
      configFields.value = res.data
      configSchema.value = null
    } else {
      // 如果是JSON Schema格式
      configSchema.value = res.data
      configFields.value = convertSchemaToFields(res.data)
    }
    
    // 设置默认值或初始值
    if (props.initialValues) {
      configValues.value = { ...props.initialValues }
    } else {
      configValues.value = getDefaultValues(configFields.value)
    }
  } catch (error) {
    console.error('加载脚本配置结构失败:', error)
    uni.showToast({
      title: '加载配置结构失败',
      icon: 'none'
    })
  }
}

const convertSchemaToFields = (schema: any) => {
  const fields: any[] = []
  
  // 如果是数组格式，直接返回
  if (Array.isArray(schema)) {
    return schema
  }
  
  // 如果是旧的schema格式
  if (schema && schema.properties) {
    for (const [fieldName, field] of Object.entries(schema.properties)) {
      fields.push({
        name: fieldName,
        type: field.type,
        label: field.title || fieldName,
        help: field.description,
        value: field.default,
        options: field.enum ? field.enum.reduce((acc: any, val: string) => {
          acc[val] = val
          return acc
        }, {}) : undefined
      })
    }
  }
  
  return fields
}

const getDefaultValues = (fields: any[]) => {
  const defaults: any = {}
  
  for (const field of fields) {
    if (field.type === 'checkbox') {
      defaults[field.name] = []
    } else if (field.value !== undefined) {
      defaults[field.name] = field.value
    } else if (field.default !== undefined) {
      defaults[field.name] = field.default
    }
  }
  
  return defaults
}

const getFieldComponent = (type: string) => {
  switch (type) {
    case 'text':
      return TextField
    case 'number':
      return NumberField
    case 'radio':
      return RadioField
    case 'checkbox':
      return CheckboxField
    default:
      return TextField
  }
}

const updateField = (fieldName: string, value: any) => {
  configValues.value[fieldName] = value
}

const handleSubmit = async () => {
  const configData = {
    script_id: props.scriptId,
    config_values: configValues.value
  }
  
  // 如果是编辑模式，添加配置ID
  if (props.configId) {
    configData.config_id = props.configId
  } else {
    configData.config_name = `配置_${Date.now()}`
  }
  
  emit('submit', configData)
}

// 生命周期
onMounted(() => {
  loadScriptConfigSchema()
})
</script>

<style lang="scss" scoped>
.script-config-form {
  padding: 20px;
}

.json-schema-form {
  margin-bottom: 20px;
}

.schema-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #1677ff;
  
  h3 {
    margin: 0 0 12px 0;
    color: #333;
  }
  
  pre {
    margin: 0;
    font-size: 12px;
    color: #666;
    white-space: pre-wrap;
  }
}

.custom-form {
  margin-bottom: 20px;
}

.config-field {
  margin-bottom: 20px;
}

.field-label {
  margin-bottom: 8px;
}

.field-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.field-help {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  
  &.btn-primary {
    background: #1677ff;
    color: white;
  }
  
  &.btn-secondary {
    background: #f5f5f5;
    color: #666;
  }
}
</style> 