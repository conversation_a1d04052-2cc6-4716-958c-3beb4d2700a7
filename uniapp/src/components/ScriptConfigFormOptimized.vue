<template>
  <view class="script-config-form">
    <view class="config-form">
      <view 
        v-for="field in configFields" 
        :key="field.name"
        class="form-item"
      >
        <text class="form-label">{{ field.label }}</text>
        
        <!-- 文本输入 -->
        <input 
          v-if="field.type === 'text'"
          v-model="configValues[field.name]"
          :placeholder="`请输入${field.label}`"
          :maxlength="field.maxLength || 100"
          class="form-input"
          @input="onInputChange(field.name, $event)"
        />
        
        <!-- 数字输入 -->
        <input 
          v-else-if="field.type === 'number'"
          v-model="configValues[field.name]"
          :placeholder="`请输入${field.label}`"
          type="number"
          :min="String(field.min || 0)"
          :max="String(field.max || 999999)"
          :step="String(field.step || 1)"
          class="form-input"
          @input="onNumberChange(field.name, $event)"
        />
        
        <!-- 滑块 -->
        <slider
          v-else-if="field.type === 'slider'"
          :value="configValues[field.name] || field.min || 0"
          :min="String(field.min || 0)"
          :max="String(field.max || 100)"
          :step="String(field.step || 1)"
          :show-value="true"
          :range="field.range || false"
          class="form-slider"
          @change="onSliderChange($event, field.name)"
        />
        
        <!-- 日期选择 -->
        <picker
          v-else-if="field.type === 'date'"
          mode="date"
          :value="configValues[field.name]"
          @change="onDateChange($event, field.name)"
          class="form-picker"
        >
          <view class="picker-text">
            {{ configValues[field.name] || `请选择${field.label}` }}
          </view>
        </picker>
        
        <!-- 时间选择 -->
        <picker
          v-else-if="field.type === 'time'"
          mode="time"
          :value="configValues[field.name]"
          @change="onTimeChange($event, field.name)"
          class="form-picker"
        >
          <view class="picker-text">
            {{ configValues[field.name] || `请选择${field.label}` }}
          </view>
        </picker>
        
        <!-- 单选 -->
        <radio-group 
          v-else-if="field.type === 'radio'"
          class="form-radio-group"
          @change="onRadioChange($event, field.name)"
        >
          <label 
            v-for="(label, value) in field.options" 
            :key="value"
            class="radio-item"
          >
            <radio :value="value" :checked="configValues[field.name] === value" />
            <text class="radio-label">{{ label }}</text>
          </label>
        </radio-group>
        
        <!-- 多选 -->
        <checkbox-group 
          v-else-if="field.type === 'checkbox'"
          @change="onCheckboxChange($event, field.name)"
          class="form-checkbox-group"
        >
          <label 
            v-for="(label, value) in field.options" 
            :key="value"
            class="checkbox-item"
          >
            <checkbox 
              :value="value" 
              :checked="Array.isArray(configValues[field.name]) && configValues[field.name].includes(value)"
            />
            <text class="checkbox-label">{{ label }}</text>
          </label>
        </checkbox-group>
        
        <!-- 开关 -->
        <switch
          v-else-if="field.type === 'switch'"
          :checked="configValues[field.name]"
          @change="onSwitchChange($event, field.name)"
          class="form-switch"
        />
        
        <!-- 文本域 -->
        <textarea
          v-else-if="field.type === 'textarea'"
          v-model="configValues[field.name]"
          :placeholder="`请输入${field.label}`"
          :maxlength="field.maxLength || 500"
          :rows="field.rows || 3"
          class="form-textarea"
        />
        
        <!-- 默认文本输入 -->
        <input 
          v-else
          v-model="configValues[field.name]"
          :placeholder="`请输入${field.label}`"
          class="form-input"
        />
        
        <!-- 帮助文本 -->
        <view v-if="field.help" class="field-help">
          <view class="help-icon-container">
            <text class="help-icon">i</text>
          </view>
          <text class="help-text">{{ field.help }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { api } from '@/utils/api'

// Props
interface Props {
  scriptId: number
  configId?: number
  initialValues?: any
  scriptConfig?: any // 脚本的配置结构
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  submit: [config: any]
  cancel: []
}>()

// 响应式数据
const configFields = ref<any[]>([])
const configValues = ref<any>({})
const submitting = ref(false)

// 方法
const loadScriptConfigSchema = async () => {
  try {
    let schemaData
    
    // 如果传入了脚本配置，直接使用
    if (props.scriptConfig) {
      // 解析脚本的config字段
      try {
        if (typeof props.scriptConfig === 'string') {
          schemaData = JSON.parse(props.scriptConfig)
        } else {
          schemaData = props.scriptConfig
        }
      } catch (e) {
        console.error('解析脚本配置失败:', e)
        schemaData = null
      }
    }
    
    if (!schemaData) {
      // 否则从API获取
      const res = await api.getScriptConfigSchema(props.scriptId)
      schemaData = res.data
    }
    
    // 如果返回的是数组格式，直接使用
    if (Array.isArray(schemaData)) {
      configFields.value = schemaData
    } else {
      // 如果是旧的schema格式，转换为新格式
      configFields.value = convertSchemaToFields(schemaData)
    }
    
    // 设置默认值或初始值（由watch处理，这里不需要手动设置）
    console.log('loadScriptConfigSchema完成，configFields已设置')
    // configValues的设置交由watch处理，确保时序正确
  } catch (error) {
    console.error('加载脚本配置结构失败:', error)
    uni.showToast({
      title: '加载配置结构失败',
      icon: 'none'
    })
  }
}

const convertSchemaToFields = (schema: any) => {
  const fields: any[] = []
  
  // 如果是数组格式，直接返回
  if (Array.isArray(schema)) {
    return schema
  }
  
  // 如果是旧的schema格式
  if (schema && schema.properties) {
    // 保持原始顺序，不使用Object.entries
    const propertyNames = Object.keys(schema.properties)
    for (const fieldName of propertyNames) {
      const field = schema.properties[fieldName]
      
      // 映射 JSON Schema 字段类型到前端表单类型
      let fieldType = field.type
      if (field.type === 'string' && field.enum) {
        fieldType = 'radio' // 枚举字符串使用单选
      } else if (field.type === 'string' && field.format === 'time') {
        fieldType = 'time' // 时间格式
      } else if (field.type === 'string' && field.format === 'date') {
        fieldType = 'date' // 日期格式
      } else if (field.type === 'string') {
        fieldType = 'text' // 普通文本
      } else if (field.type === 'integer') {
        fieldType = 'number' // 数字类型
      } else if (field.type === 'boolean') {
        fieldType = 'switch' // 布尔值使用开关
      } else if (field.type === 'array') {
        fieldType = 'checkbox' // 数组使用多选
      }
      
      // 处理选项
      let options = undefined
      if (field.enum) {
        options = field.enum.reduce((acc: any, val: string) => {
          acc[val] = val
          return acc
        }, {})
      }
      
      fields.push({
        name: fieldName,
        type: fieldType,
        label: field.title || fieldName,
        help: field.description,
        value: field.default,
        default: field.default, // 添加默认值字段
        min: field.minimum,
        max: field.maximum,
        step: field.multipleOf,
        options: options
      })
    }
  }
  
  return fields
}

const getDefaultValues = (fields: any[]) => {
  const defaults: any = {}
  
  for (const field of fields) {
    const fieldName = field.name
    
    // 优先使用 default 字段，然后是 value 字段
    const defaultValue = field.default !== undefined ? field.default : field.value
    
    if (field.type === 'checkbox' || field.type === 'multiselect') {
      // 数组类型字段
      defaults[fieldName] = Array.isArray(defaultValue) ? defaultValue : []
    } else if (field.type === 'switch') {
      // 开关类型字段
      defaults[fieldName] = defaultValue !== undefined ? Boolean(defaultValue) : false
    } else if (field.type === 'number') {
      // 数字类型字段
      defaults[fieldName] = defaultValue !== undefined ? Number(defaultValue) : 0
    } else if (defaultValue !== undefined) {
      // 其他类型字段
      defaults[fieldName] = defaultValue
    } else {
      // 没有默认值时的处理
      if (field.type === 'number') {
        defaults[fieldName] = 0
      } else if (field.type === 'switch') {
        defaults[fieldName] = false
      } else if (field.type === 'checkbox') {
        defaults[fieldName] = []
      } else {
        defaults[fieldName] = ''
      }
    }
  }
  
  console.log('生成的默认值:', defaults)
  return defaults
}

const handleSubmit = async () => {
  submitting.value = true
  
  try {
    const configData = {
      script_id: props.scriptId,
      config_name: props.configId ? undefined : `配置_${Date.now()}`,
      config_values: configValues.value,
      device_ids: [] // 可以根据需要添加设备ID
    }
    
    // 如果是编辑模式，添加配置ID
    if (props.configId) {
      configData.config_id = props.configId
    }
    
    emit('submit', configData)
  } finally {
    submitting.value = false
  }
}

// 暴露配置数据给父组件
const getConfigData = () => {
  const data = {
    script_id: props.scriptId,
    config_values: configValues.value,
    device_ids: [] // 可以根据需要添加设备ID
  }
  
  // 如果是编辑模式，添加配置ID（不需要config_name，后端会保持原有名称）
  if (props.configId) {
    data.config_id = props.configId
  } else {
    // 如果是新建模式，不提供config_name，让后端生成默认名称
    // data.config_name = `配置_${Date.now()}` // 注释掉，让后端生成默认名称
  }
  
  console.log('getConfigData 返回数据:', data)
  return data
}

// 暴露给父组件
defineExpose({
  getConfigData
})

// 日期选择事件处理
const onDateChange = (event: any, fieldName: string) => {
  configValues.value[fieldName] = event.detail.value
}

// 时间选择事件处理
const onTimeChange = (event: any, fieldName: string) => {
  configValues.value[fieldName] = event.detail.value
}

// 开关事件处理
const onSwitchChange = (event: any, fieldName: string) => {
  configValues.value[fieldName] = event.detail.value
}

// 单选事件处理
const onRadioChange = (event: any, fieldName: string) => {
  configValues.value[fieldName] = event.detail.value
  console.log(`单选字段 ${fieldName} 变化为:`, event.detail.value)
}

// 多选事件处理
const onCheckboxChange = (event: any, fieldName: string) => {
  configValues.value[fieldName] = event.detail.value
  console.log(`多选字段 ${fieldName} 变化为:`, event.detail.value)
  console.log('当前配置值:', configValues.value)
}

// 输入事件处理
const onInputChange = (fieldName: string, event: any) => {
  const newValue = event.detail?.value || event.target?.value
  configValues.value[fieldName] = newValue
  console.log(`字段 ${fieldName} 输入变化:`, newValue)
  console.log('当前配置值:', configValues.value)
}

// 数字输入事件处理
const onNumberChange = (fieldName: string, event: any) => {
  const newValue = event.detail?.value || event.target?.value
  configValues.value[fieldName] = newValue !== '' ? Number(newValue) : 0
  console.log(`数字字段 ${fieldName} 输入变化:`, configValues.value[fieldName])
  console.log('当前配置值:', configValues.value)
}

// 滑块事件处理
const onSliderChange = (event: any, fieldName: string) => {
  configValues.value[fieldName] = event.detail.value
  console.log(`滑块字段 ${fieldName} 变化为:`, event.detail.value)
  console.log('当前配置值:', configValues.value)
}

// 生命周期
onMounted(() => {
  loadScriptConfigSchema()
})

// 监听 initialValues 变化
watch(() => props.initialValues, (newValues) => {
  if (newValues && Object.keys(newValues).length > 0 && configFields.value.length > 0) {
    console.log('初始值变化:', newValues)
    // 重新获取默认值并合并
    const defaultValues = getDefaultValues(configFields.value)
    
    // 确保checkbox字段的数据类型正确
    const processedValues = { ...newValues }
    configFields.value.forEach(field => {
      if (field.type === 'checkbox' && processedValues[field.name]) {
        if (!Array.isArray(processedValues[field.name])) {
          console.warn(`字段 ${field.name} 应该是数组，但获得:`, processedValues[field.name])
          processedValues[field.name] = []
        }
      }
    })
    
    configValues.value = { ...defaultValues, ...processedValues }
    
    console.log('重新设置配置值 - 默认值:', defaultValues)
    console.log('重新设置配置值 - 初始值:', newValues)
    console.log('重新设置配置值 - 处理后:', processedValues)
    console.log('重新设置配置值 - 合并后:', configValues.value)
  } else if (configFields.value.length > 0) {
    // 如果没有初始值，使用字段的默认值
    console.log('没有初始值，使用字段默认值')
    const defaultValues = getDefaultValues(configFields.value)
    configValues.value = defaultValues
    console.log('使用字段默认值:', configValues.value)
  }
}, { deep: true, immediate: true })

// 监听 configFields 变化，确保在配置结构加载后再设置初始值
watch(() => configFields.value, (newFields) => {
  if (newFields && newFields.length > 0) {
    console.log('配置结构加载完成')
    const defaultValues = getDefaultValues(newFields)
    
    if (props.initialValues && Object.keys(props.initialValues).length > 0) {
      console.log('配置结构加载完成，合并初始值')
      
      // 确保checkbox字段的数据类型正确
      const processedInitialValues = { ...props.initialValues }
      newFields.forEach(field => {
        if (field.type === 'checkbox' && processedInitialValues[field.name]) {
          if (!Array.isArray(processedInitialValues[field.name])) {
            console.warn(`字段 ${field.name} 应该是数组，但获得:`, processedInitialValues[field.name])
            processedInitialValues[field.name] = []
          }
        }
      })
      
      configValues.value = { ...defaultValues, ...processedInitialValues }
      console.log('合并结果:', configValues.value)
    } else {
      console.log('配置结构加载完成，使用默认值')
      configValues.value = defaultValues
    }
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.script-config-form {
  padding: 12px;
  background: #fff;
  min-width: 300px;
}

.config-form {
  margin-bottom: 12px;
  width: 100%;
}

.form-item {
  margin-bottom: 10px;
  padding: 6px 0;
  width: 100%;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.3;
  word-break: break-word;
}

.form-input {
  width: 100%;
  min-width: 350px;
  height: 40px;
  padding: 10px 14px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 15px;
  background: #fff;
  color: #333;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  line-height: 1.4;
  
  &:focus {
    outline: none;
    border-color: #1677ff;
  }
  
  &::placeholder {
    color: #bfbfbf;
    font-size: 14px;
  }
}

.form-slider {
  width: 100%;
  margin: 4px 0;
}

.form-picker {
  width: 100%;
}

.picker-text {
  width: 100%;
  min-width: 350px;
  height: 40px;
  padding: 10px 14px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  font-size: 15px;
  color: #333;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  line-height: 1.4;
  display: flex;
  align-items: center;
  
  &:active {
    border-color: #1677ff;
  }
}

.form-radio-group,
.form-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 1px;
  padding: 0;
}

.radio-item,
.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 1px 0;
}

.radio-label,
.checkbox-label {
  font-size: 13px;
  color: #333;
  flex: 1;
  line-height: 1.3;
  user-select: none;
}

.form-switch {
  margin: 4px 0;
}

.form-textarea {
  width: 100%;
  min-width: 350px;
  padding: 10px 14px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 15px;
  background: #fff;
  color: #333;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  line-height: 1.4;
  
  &:focus {
    outline: none;
    border-color: #1677ff;
  }
  
  &::placeholder {
    color: #bfbfbf;
    font-size: 14px;
  }
}

.field-help {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  margin-top: 4px;
  padding: 4px 0;
  
  .help-icon-container {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #1677ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 1px;
  }
  
  .help-icon {
    font-size: 10px;
    color: white;
    font-weight: bold;
  }
  
  .help-text {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    flex: 1;
    word-break: break-word;
  }
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  
  &:active {
    transform: translateY(1px);
  }
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
  
  &:hover {
    background: #e8e8e8;
  }
}

.btn-primary {
  background: #1677ff;
  color: white;
  
  &:hover {
    background: #0958d9;
  }
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .script-config-form {
    padding: 12px;
    min-width: 350px;
  }
  
  .form-item {
    margin-bottom: 10px;
    padding: 6px 0;
  }
  
  .form-label {
    font-size: 13px;
    margin-bottom: 5px;
  }
  
  .form-input,
  .form-textarea,
  .picker-text {
    min-width: 320px;
    height: 38px;
    padding: 8px 12px;
    font-size: 14px;
  }
  
  .form-textarea {
    min-height: 70px;
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .script-config-form {
    background: #1f1f1f;
    color: #fff;
  }
  
  .form-item {
    border-bottom-color: #333;
  }
  
  .form-label {
    color: #fff;
  }
  
  .form-input,
  .form-textarea,
  .picker-text {
    background: #2a2a2a;
    border-color: #404040;
    color: #fff;
    
    &::placeholder {
      color: #666;
    }
  }
  
  .radio-item,
  .checkbox-item {
    &:hover {
      background: #2a2a2a;
    }
  }
}
</style> 