<template>
  <view class="script-config-form">
    <el-form 
      :model="configValues" 
      label-width="120px"
      class="config-form"
    >
      <el-form-item 
        v-for="field in configFields" 
        :key="field.name"
        :label="field.label"
        :prop="field.name"
      >
        <!-- 文本输入 -->
        <el-input 
          v-if="field.type === 'text'"
          v-model="configValues[field.name]"
          :placeholder="`请输入${field.label}`"
          clearable
        />
        
        <!-- 数字输入 -->
        <el-input-number 
          v-else-if="field.type === 'number'"
          v-model="configValues[field.name]"
          :placeholder="`请输入${field.label}`"
          :min="0"
          controls-position="right"
        />
        
        <!-- 单选 -->
        <el-radio-group 
          v-else-if="field.type === 'radio'"
          v-model="configValues[field.name]"
        >
          <el-radio 
            v-for="(label, value) in field.options" 
            :key="value"
            :label="value"
          >
            {{ label }}
          </el-radio>
        </el-radio-group>
        
        <!-- 多选 -->
        <el-checkbox-group 
          v-else-if="field.type === 'checkbox'"
          v-model="configValues[field.name]"
        >
          <el-checkbox 
            v-for="(label, value) in field.options" 
            :key="value"
            :label="value"
          >
            {{ label }}
          </el-checkbox>
        </el-checkbox-group>
        
        <!-- 帮助文本 -->
        <div v-if="field.help" class="field-help">
          {{ field.help }}
        </div>
      </el-form-item>
    </el-form>
    
    <!-- 操作按钮 -->
    <view class="form-actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存配置</el-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { api } from '@/utils/api'

// Props
interface Props {
  scriptId: number
  configId?: number
  initialValues?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  submit: [config: any]
  cancel: []
}>()

// 响应式数据
const configFields = ref<any[]>([])
const configValues = ref<any>({})

// 方法
const loadScriptConfigSchema = async () => {
  try {
    const res = await api.getScriptConfigSchema(props.scriptId)
    
    // 如果返回的是数组格式，直接使用
    if (Array.isArray(res.data)) {
      configFields.value = res.data
    } else {
      // 如果是旧的schema格式，转换为新格式
      configFields.value = convertSchemaToFields(res.data)
    }
    
    // 设置默认值或初始值
    if (props.initialValues) {
      configValues.value = { ...props.initialValues }
    } else {
      configValues.value = getDefaultValues(configFields.value)
    }
  } catch (error) {
    console.error('加载脚本配置结构失败:', error)
    uni.showToast({
      title: '加载配置结构失败',
      icon: 'none'
    })
  }
}

const convertSchemaToFields = (schema: any) => {
  const fields: any[] = []
  
  // 如果是数组格式，直接返回
  if (Array.isArray(schema)) {
    return schema
  }
  
  // 如果是旧的schema格式
  if (schema && schema.properties) {
    for (const [fieldName, field] of Object.entries(schema.properties)) {
      fields.push({
        name: fieldName,
        type: field.type,
        label: field.title || fieldName,
        help: field.description,
        value: field.default,
        options: field.enum ? field.enum.reduce((acc: any, val: string) => {
          acc[val] = val
          return acc
        }, {}) : undefined
      })
    }
  }
  
  return fields
}

const getDefaultValues = (fields: any[]) => {
  const defaults: any = {}
  
  for (const field of fields) {
    if (field.type === 'checkbox') {
      defaults[field.name] = []
    } else if (field.value !== undefined) {
      defaults[field.name] = field.value
    } else if (field.default !== undefined) {
      defaults[field.name] = field.default
    }
  }
  
  return defaults
}

const handleSubmit = async () => {
  const configData = {
    script_id: props.scriptId,
    config_values: configValues.value
  }
  
  // 如果是编辑模式，添加配置ID
  if (props.configId) {
    configData.config_id = props.configId
  } else {
    configData.config_name = `配置_${Date.now()}`
  }
  
  emit('submit', configData)
}

// 生命周期
onMounted(() => {
  loadScriptConfigSchema()
})
</script>

<style lang="scss" scoped>
.script-config-form {
  padding: 20px;
}

.config-form {
  margin-bottom: 20px;
}

.field-help {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
</style> 