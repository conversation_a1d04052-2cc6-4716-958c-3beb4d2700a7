<template>
  <view class="script-config-form">
    <!-- 配置表单 -->
    <view class="config-section" v-if="configFields.length > 0">
      <view class="section-title">配置参数</view>
      <view class="config-form">
        <view 
          v-for="field in configFields" 
          :key="field.name"
          class="config-field"
        >
          <!-- 字段标题 -->
          <view class="field-label">
            <text class="field-title">{{ field.label }}</text>
            <text v-if="field.help" class="field-desc">{{ field.help }}</text>
          </view>

          <!-- 文本类型 -->
          <input 
            v-if="field.type === 'text'"
            class="field-input"
            :value="configValues[field.name] || field.value || ''"
            :placeholder="`请输入${field.label}`"
            @input="updateField(field.name, $event.detail.value)"
          />

          <!-- 数字类型 -->
          <input 
            v-else-if="field.type === 'number'"
            class="field-input"
            type="number"
            :value="configValues[field.name] || field.value || ''"
            :placeholder="`请输入${field.label}`"
            @input="updateField(field.name, parseFloat($event.detail.value) || 0)"
          />

          <!-- 单选类型 -->
          <view v-else-if="field.type === 'radio'" class="field-radio">
            <view 
              v-for="(label, value) in field.options" 
              :key="value"
              class="radio-item"
              :class="{ selected: configValues[field.name] === value }"
              @click="updateField(field.name, value)"
            >
              <view class="radio-dot" :class="{ active: configValues[field.name] === value }"></view>
              <text class="radio-label">{{ label }}</text>
            </view>
          </view>

          <!-- 多选类型 -->
          <view v-else-if="field.type === 'checkbox'" class="field-checkbox">
            <view 
              v-for="(label, value) in field.options" 
              :key="value"
              class="checkbox-item"
              :class="{ selected: isOptionSelected(field.name, value) }"
              @click="toggleCheckboxOption(field.name, value)"
            >
              <view class="checkbox-box" :class="{ active: isOptionSelected(field.name, value) }">
                <text v-if="isOptionSelected(field.name, value)" class="checkbox-check">✓</text>
              </view>
              <text class="checkbox-label">{{ label }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 设备关联 -->
    <view class="config-section" v-if="showDeviceSelector">
      <view class="section-title">关联设备</view>
      <view class="device-selector">
        <view 
          v-for="device in availableDevices" 
          :key="device.id"
          class="device-item"
          :class="{ selected: selectedDevices.includes(device.id) }"
          @click="toggleDevice(device.id)"
        >
          <view class="device-info">
            <text class="device-name">{{ device.name }}</text>
            <text class="device-id">{{ device.device_id }}</text>
          </view>
          <view class="device-status">
            <text class="status-dot" :class="device.status === 1 ? 'online' : 'offline'"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button class="btn btn-secondary" @click="$emit('cancel')">取消</button>
      <button class="btn btn-primary" @click="handleSubmit">保存配置</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { api } from '@/utils/api'

// Props
interface Props {
  scriptId: number
  configId?: number
  initialValues?: any
  showDeviceSelector?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showDeviceSelector: true
})

// Emits
const emit = defineEmits<{
  submit: [config: any]
  cancel: []
}>()

// 响应式数据
const configFields = ref<any[]>([])
const configValues = ref<any>({})
const availableDevices = ref<any[]>([])
const selectedDevices = ref<number[]>([])
const loading = ref(false)

// 计算属性
const isEdit = computed(() => !!props.configId)

// 方法
const loadScriptConfigSchema = async () => {
  try {
    const res = await api.getScriptConfigSchema(props.scriptId)
    
    // 如果返回的是数组格式，直接使用
    if (Array.isArray(res.data)) {
      configFields.value = res.data
    } else {
      // 如果是旧的schema格式，转换为新格式
      configFields.value = convertSchemaToFields(res.data)
    }
    
    // 设置默认值或初始值
    if (props.initialValues) {
      configValues.value = { ...props.initialValues }
    } else {
      configValues.value = getDefaultValues(configFields.value)
    }
  } catch (error) {
    console.error('加载脚本配置结构失败:', error)
    uni.showToast({
      title: '加载配置结构失败',
      icon: 'none'
    })
  }
}

const convertSchemaToFields = (schema: any) => {
  const fields: any[] = []
  
  // 如果是数组格式，直接返回
  if (Array.isArray(schema)) {
    return schema
  }
  
  // 如果是旧的schema格式
  if (schema && schema.properties) {
    for (const [fieldName, field] of Object.entries(schema.properties)) {
      fields.push({
        name: fieldName,
        type: field.type,
        label: field.title || fieldName,
        help: field.description,
        value: field.default,
        options: field.enum ? field.enum.reduce((acc: any, val: string) => {
          acc[val] = val
          return acc
        }, {}) : undefined
      })
    }
  }
  
  return fields
}

const loadDevices = async () => {
  try {
    const res = await api.getUserDevices()
    availableDevices.value = res.data?.devices || []
  } catch (error) {
    console.error('加载设备列表失败:', error)
  }
}

const getDefaultValues = (fields: any[]) => {
  const defaults: any = {}
  
  for (const field of fields) {
    if (field.type === 'checkbox') {
      defaults[field.name] = []
    } else if (field.value !== undefined) {
      defaults[field.name] = field.value
    } else if (field.default !== undefined) {
      defaults[field.name] = field.default
    }
  }
  
  return defaults
}

const updateField = (fieldName: string, value: any) => {
  configValues.value[fieldName] = value
}

const isOptionSelected = (fieldName: string, optionValue: string) => {
  const selectedValues = configValues.value[fieldName] || []
  return selectedValues.includes(optionValue)
}

const toggleCheckboxOption = (fieldName: string, optionValue: string) => {
  if (!configValues.value[fieldName]) {
    configValues.value[fieldName] = []
  }
  
  const selectedValues = configValues.value[fieldName]
  const index = selectedValues.indexOf(optionValue)
  
  if (index > -1) {
    selectedValues.splice(index, 1)
  } else {
    selectedValues.push(optionValue)
  }
}

const toggleDevice = (deviceId: number) => {
  const index = selectedDevices.value.indexOf(deviceId)
  if (index > -1) {
    selectedDevices.value.splice(index, 1)
  } else {
    selectedDevices.value.push(deviceId)
  }
}

const handleSubmit = async () => {
  if (configFields.value.length === 0) {
    uni.showToast({
      title: '配置结构未加载',
      icon: 'none'
    })
    return
  }
  
  const configData = {
    script_id: props.scriptId,
    config_values: configValues.value,
    device_ids: selectedDevices.value
  }
  
  // 如果是编辑模式，添加配置ID
  if (props.configId) {
    configData.config_id = props.configId
  } else {
    configData.config_name = `配置_${Date.now()}`
  }
  
  emit('submit', configData)
}

// 生命周期
onMounted(() => {
  loadScriptConfigSchema()
  if (props.showDeviceSelector) {
    loadDevices()
  }
})

// 监听初始值变化
watch(() => props.initialValues, (newValues) => {
  if (newValues) {
    configValues.value = { ...newValues }
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.script-config-form {
  padding: 20rpx;
}

.config-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.config-field {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.field-label {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.field-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.field-desc {
  font-size: 24rpx;
  color: #666;
}

.field-input {
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: white;
  
  &:focus {
    border-color: #1677ff;
  }
}

.field-radio {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: white;
  transition: all 0.2s ease;
  
  &.selected {
    border-color: #1677ff;
    background: rgba(22, 119, 255, 0.05);
  }
}

.radio-dot {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 50%;
  position: relative;
  
  &.active {
    border-color: #1677ff;
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 10rpx;
      height: 10rpx;
      background: #1677ff;
      border-radius: 50%;
    }
  }
}

.radio-label {
  font-size: 28rpx;
  color: #333;
}

.field-checkbox {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: white;
  transition: all 0.2s ease;
  
  &.selected {
    border-color: #1677ff;
    background: rgba(22, 119, 255, 0.05);
  }
}

.checkbox-box {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.active {
    border-color: #1677ff;
    background: #1677ff;
  }
}

.checkbox-check {
  color: white;
  font-size: 16rpx;
  font-weight: bold;
}

.checkbox-label {
  font-size: 28rpx;
  color: #333;
}

.device-selector {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: white;
  transition: all 0.2s ease;
  
  &.selected {
    border-color: #1677ff;
    background: rgba(22, 119, 255, 0.05);
  }
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.device-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.device-id {
  font-size: 24rpx;
  color: #666;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  
  &.online {
    background: #52c41a;
  }
  
  &.offline {
    background: #d9d9d9;
  }
}

.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.btn {
  flex: 1;
  padding: 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  
  &.btn-secondary {
    background: #f5f5f5;
    color: #666;
  }
  
  &.btn-primary {
    background: #1677ff;
    color: white;
  }
}
</style> 