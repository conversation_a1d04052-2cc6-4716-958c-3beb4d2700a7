server:
  port: "9090"
  mode: "debug"
  env: "dev"

jwt:
  secret_key: "ZM8888XTM"
  third_secret_key: "THIRD_API_SECRET_KEY_2024"
  expires_time: 2h

database:
  driver: "mysql"
  host: "***************"
  #host: "localhost"
  port: 3306
  username: "goadmin"
  password: "Zzzz123!"
  dbname: "goadmin"
  max_idle_conns: 10
  max_open_conns: 100

github:
  token: "*********************************************************************************************"  # GitHub API token, 留空则不使用token
  webhook_secret: "github_autojs_webhook_888999"  # GitHub webhook密钥，用于验证请求签名
  branch_mapping:  # 环境对应的分支映射
    main: main
    online: online