server:
  port: "9000"
  mode: "debug"
  env: "dev"

jwt:
  secret_key: "ZM8888XTM"
  expires_time: 2h

database:
  driver: "mysql"
  host: "***************"  
  port: 3306
  username: "root"  
  password: "Zzzz123!"  
  dbname: "goadmin"
  max_idle_conns: 10
  max_open_conns: 100

github:
  token: "*********************************************************************************************"  
  webhook_secret: "${AUTOJS_WEBHOOK_SECRET}"  
  branch_mapping:  
    main: main
    online: online

zpay:
  api_url: "https://z-pay.cn/mapi.php"  
  app_id: "20240702175700"  
  app_secret: "3cfEeN7xh9c9SZdRpAOBA9ro6qYkbOaJ"  
  notify_url: "http://localhost:9000/api/v1/payment/notify"  

redis:
  addr: "***************:6379"  
  password: "Zzzz123!"  
  db: 0  
  pool_size: 20

websocket:
  allowed_origins:
    - "https://app.l888.eu.org"
    - "https://www.app.l888.eu.org"
    - "https://yourdomain.com"
    - "https://www.yourdomain.com"
    - "http://localhost:5173"  # UniApp 开发环境
    - "http://localhost:9000"  # 其他开发环境
    - "http://*************:5173"  # UniApp 开发环境
