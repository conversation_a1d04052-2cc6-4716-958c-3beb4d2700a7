name: Deploy to Server

on:
  push:
    branches: [ main ]  # main 分支推送
  pull_request:
    branches: [ online ]  # online 分支的 PR

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: Debug Info
      run: |
        echo "Event name: ${{ github.event_name }}"
        echo "Branch ref: ${{ github.ref }}"
        echo "Base ref: ${{ github.base_ref }}"
        echo "Current branch: $(git branch --show-current)"

    - name: Set environment variables
      run: |
        if [[ "${{ github.event_name }}" == "push" ]] && [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
          echo "ENV=dev" >> $GITHUB_ENV
          echo "TAG=main" >> $GITHUB_ENV
          echo "PORT=9000" >> $GITHUB_ENV
          echo "WEB_PATH=/www/wwwroot/admin-main" >> $GITHUB_ENV
          echo "CONFIG_FILE=config.yaml" >> $GITHUB_ENV
          echo "Running in main branch deployment"
        elif [[ "${{ github.event_name }}" == "pull_request" ]] && [[ "${{ github.base_ref }}" == "online" ]]; then
          echo "ENV=prod" >> $GITHUB_ENV
          echo "TAG=online" >> $GITHUB_ENV
          echo "PORT=9001" >> $GITHUB_ENV
          echo "WEB_PATH=/www/wwwroot/admin" >> $GITHUB_ENV
          echo "CONFIG_FILE=config.prod.yaml" >> $GITHUB_ENV
          echo "Running in online branch deployment"
        else
          echo "Skipping deployment - no matching conditions"
          exit 0
        fi

    # 设置 Node.js 环境
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: web/package-lock.json

    # 构建前端项目
    - name: Build Frontend
      run: |
        cd web
        npm ci --prefer-offline --no-audit
        npm run build
        cd dist
        tar -czf ../../dist.tar.gz .
        cd ../..
        chmod 644 dist.tar.gz

    # 构建UniApp项目
    - name: Build UniApp
      run: |
        cd uniapp
        npm ci --prefer-offline --no-audit
        # 根据环境切换UniApp配置
        if [[ "${ENV}" == "prod" ]]; then
          npm run env:prod
        else
          npm run env:test
        fi
        npm run build:h5
        cd dist/build/h5
        tar -czf ../../../../uniapp-dist.tar.gz .
        cd ../../../../
        chmod 644 uniapp-dist.tar.gz

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: ${{ secrets.DOCKERHUB_USERNAME }}/goadmin:${{ env.TAG }}

    - name: Upload Frontend Dist
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        password: ${{ secrets.SERVER_PASSWORD }}
        port: ${{ secrets.SERVER_PORT }}
        source: "dist.tar.gz"
        target: "/tmp"

    - name: Upload UniApp Dist
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        password: ${{ secrets.SERVER_PASSWORD }}
        port: ${{ secrets.SERVER_PORT }}
        source: "uniapp-dist.tar.gz"
        target: "/tmp"

    - name: Upload Config Files
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        password: ${{ secrets.SERVER_PASSWORD }}
        port: ${{ secrets.SERVER_PORT }}
        source: "config.yaml,config.prod.yaml"
        target: "/tmp"

    - name: Deploy Application
      uses: appleboy/ssh-action@master
      env:
        ENV: ${{ env.ENV }}
        TAG: ${{ env.TAG }}
        PORT: ${{ env.PORT }}
        CONFIG_FILE: ${{ env.CONFIG_FILE }}
        DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
        AUTOJS_WEBHOOK_SECRET: ${{ secrets.AUTOJS_WEBHOOK_SECRET }}
        WEB_PATH: ${{ env.WEB_PATH }}
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        password: ${{ secrets.SERVER_PASSWORD }}
        port: ${{ secrets.SERVER_PORT }}
        envs: ENV,TAG,PORT,CONFIG_FILE,DOCKERHUB_USERNAME,AUTOJS_WEBHOOK_SECRET,WEB_PATH
        script: |
          # 部署后端
          DEPLOY_DIR="/www/wwwroot/admin-api-${TAG}"
          mkdir -p ${DEPLOY_DIR}
          cd ${DEPLOY_DIR}

          # 复制配置文件
          cp /tmp/${CONFIG_FILE} ./config.yaml
          
          # 根据环境更新配置文件中的敏感信息
          if [[ "${ENV}" == "prod" ]]; then
            # 生产环境：只替换webhook secret
            sed -i "s/\${AUTOJS_WEBHOOK_SECRET}/${AUTOJS_WEBHOOK_SECRET}/g" config.yaml
          else
            # 开发环境：只替换webhook secret
            sed -i "s/\${AUTOJS_WEBHOOK_SECRET:-github_autojs_webhook_888999}/${AUTOJS_WEBHOOK_SECRET}/g" config.yaml
          fi

          mkdir -p logs
          chmod 777 logs
          chmod 644 config.yaml

          # Docker Compose 配置
          cat > docker-compose.yml << EOL
          version: '3.8'

          services:
            app:
              image: ${DOCKERHUB_USERNAME}/goadmin:${TAG}
              container_name: goadmin-${TAG}
              restart: always
              network_mode: host
              environment:
                - TZ=Asia/Shanghai
                - PORT=${PORT}
              volumes:
                - ./config.yaml:/app/config.yaml
                - ./logs:/app/logs
          EOL

          # 部署容器
          docker-compose pull
          docker-compose down || true
          docker-compose up -d

          # 等待容器启动
          sleep 5

          # 确保日志目录权限正确
          chmod -R 777 logs

          # 清理悬空镜像
          docker image prune -f --filter "dangling=true"

          # 显示运行状态
          docker ps | grep goadmin

          # 部署前端
          mkdir -p ${WEB_PATH}
          cd ${WEB_PATH}
          rm -rf ./*
          tar -xzf /tmp/dist.tar.gz
          rm -f /tmp/dist.tar.gz
          chown -R www:www .
          chmod -R 755 .
          echo "Frontend deployment completed"

          # 部署UniApp
          UNIAPP_PATH="/www/wwwroot/uniapp-${TAG}"
          mkdir -p ${UNIAPP_PATH}
          cd ${UNIAPP_PATH}
          rm -rf ./*
          tar -xzf /tmp/uniapp-dist.tar.gz
          rm -f /tmp/uniapp-dist.tar.gz
          chown -R www:www .
          chmod -R 755 .
          echo "UniApp deployment completed"

          # 清理临时文件
          rm -f /tmp/config.yaml /tmp/config.prod.yaml
