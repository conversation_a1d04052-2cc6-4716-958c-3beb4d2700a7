name: Deploy to Server

on:
  push:
    branches: [ main ]  # main 分支推送
  pull_request:
    branches: [ online ]  # online 分支的 PR

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: Debug Info
      run: |
        echo "Event name: ${{ github.event_name }}"
        echo "Branch ref: ${{ github.ref }}"
        echo "Base ref: ${{ github.base_ref }}"
        echo "Current branch: $(git branch --show-current)"

    - name: Set environment variables
      run: |
        if [[ "${{ github.event_name }}" == "push" ]] && [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
          echo "PORT=9000" >> $GITHUB_ENV
          echo "TAG=main" >> $GITHUB_ENV
          echo "MYSQL_HOST=***************" >> $GITHUB_ENV
          echo "SERVER_MODE=debug" >> $GITHUB_ENV
          echo "WEB_PATH=/www/wwwroot/admin-main" >> $GITHUB_ENV
          echo "Running in main branch deployment"
        elif [[ "${{ github.event_name }}" == "pull_request" ]] && [[ "${{ github.base_ref }}" == "online" ]]; then
          echo "PORT=9001" >> $GITHUB_ENV
          echo "TAG=online" >> $GITHUB_ENV
          echo "MYSQL_HOST=localhost" >> $GITHUB_ENV
          echo "SERVER_MODE=release" >> $GITHUB_ENV
          echo "WEB_PATH=/www/wwwroot/admin" >> $GITHUB_ENV
          echo "Running in online branch deployment"
        else
          echo "Skipping deployment - no matching conditions"
          exit 0
        fi

    # 设置 Node.js 环境
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    # 构建前端项目
    - name: Build Frontend
      run: |
        cd web
        npm install
        npm run build
        cd dist
        tar -czf ../../dist.tar.gz .
        cd ../..
        chmod 644 dist.tar.gz

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: ${{ secrets.DOCKERHUB_USERNAME }}/goadmin:${{ env.TAG }}

    - name: Upload Frontend Dist
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        password: ${{ secrets.SERVER_PASSWORD }}
        port: ${{ secrets.SERVER_PORT }}
        source: "dist.tar.gz"
        target: "/tmp"

    - name: Deploy Application
      uses: appleboy/ssh-action@master
      env:
        SERVER_MODE: ${{ env.SERVER_MODE }}
        PORT: ${{ env.PORT }}
        TAG: ${{ env.TAG }}
        MYSQL_HOST: ${{ env.MYSQL_HOST }}
        DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
        AUTOJS_WEBHOOK_SECRET: ${{ secrets.AUTOJS_WEBHOOK_SECRET }}
        MYSQL_PORT: 3306
        MYSQL_USER: goadmin
        MYSQL_PASSWORD: Zzzz123!
        MYSQL_DATABASE: goadmin
        JWT_SECRET_KEY: ZM8888XTM
        GITHUB_API_TOKEN: *********************************************************************************************
        WEB_PATH: ${{ env.WEB_PATH }}
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        password: ${{ secrets.SERVER_PASSWORD }}
        port: ${{ secrets.SERVER_PORT }}
        envs: PORT,TAG,DOCKERHUB_USERNAME,AUTOJS_WEBHOOK_SECRET,MYSQL_HOST,MYSQL_PORT,MYSQL_USER,MYSQL_PASSWORD,MYSQL_DATABASE,JWT_SECRET_KEY,GITHUB_API_TOKEN,WEB_PATH,SERVER_MODE
        script: |
          # 部署后端
          DEPLOY_DIR="/www/wwwroot/admin-api-${TAG}"
          mkdir -p ${DEPLOY_DIR}
          cd ${DEPLOY_DIR}

          # 配置文件处理
          cat > config.yaml << EOL
          server:
            port: ${PORT}
            mode: ${SERVER_MODE}
            env: ${TAG}  # 使用TAG作为环境标识（main或online）

          jwt:
            secret_key: ZM8888XTM
            expires_time: 2h

          github:
            token: *********************************************************************************************
            webhook_secret: ${AUTOJS_WEBHOOK_SECRET} # GitHub webhook密钥
            branch_mapping:  # 环境对应的分支映射
              main: main
              online: online

          database:
            driver: mysql
            host: ${MYSQL_HOST}
            port: 3306
            username: goadmin
            password: Zzzz123!
            dbname: goadmin
            max_idle_conns: 10
            max_open_conns: 100

          log:
            level: debug
            filename: /app/logs/app.log
            maxsize: 100
            maxbackups: 10
            maxage: 7
          EOL

          mkdir -p logs
          chmod 777 logs
          chmod 644 config.yaml

          # Docker Compose 配置
          cat > docker-compose.yml << EOL
          version: '3.8'

          services:
            app:
              image: ${DOCKERHUB_USERNAME}/goadmin:${TAG}
              container_name: goadmin-${TAG}
              restart: always
              network_mode: host
              environment:
                - TZ=Asia/Shanghai
                - PORT=${PORT}
              volumes:
                - ./config.yaml:/app/config.yaml
                - ./logs:/app/logs
          EOL

          # 部署容器
          docker-compose pull
          docker-compose down || true
          docker-compose up -d

          # 等待容器启动
          sleep 5

          # 确保日志目录权限正确
          chmod -R 777 logs

          # 清理悬空镜像
          docker image prune -f --filter "dangling=true"

          # 显示运行状态
          docker ps | grep goadmin

          # 部署前端
          mkdir -p ${WEB_PATH}
          cd ${WEB_PATH}
          rm -rf ./*
          tar -xzf /tmp/dist.tar.gz
          rm -f /tmp/dist.tar.gz
          chown -R www:www .
          chmod -R 755 .
          echo "Frontend deployment completed"
