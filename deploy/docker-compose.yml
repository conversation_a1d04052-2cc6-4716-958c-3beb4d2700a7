version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ../
      dockerfile: deploy/Dockerfile.backend
    container_name: goadmin-backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=goadmin
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mysql
    volumes:
      - ./config.yaml:/app/config.yaml
      - ./logs:/app/logs
    networks:
      - app-network

  # 前端服务
  frontend:
    build:
      context: ../console-app
      dockerfile: ../deploy/Dockerfile.frontend
    container_name: goadmin-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
    networks:
      - app-network

  # 数据库服务（开发环境）
  mysql:
    image: mysql:8.0
    container_name: goadmin-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
      - MYSQL_DATABASE=goadmin
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: goadmin-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  mysql_data:
  redis_data:

networks:
  app-network:
    driver: bridge
