# 群控系统部署文档

## 🚀 快速部署

### 1. 准备工作

#### 服务器要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 40GB SSD
- **操作系统**: CentOS 7+ / Ubuntu 18+
- **网络**: 公网IP，5Mbps带宽

#### 域名准备
- 主域名: `yourdomain.com`
- API域名: `api.yourdomain.com`
- 控制台域名: `console.yourdomain.com`

### 2. 环境安装

```bash
# 安装Docker
curl -fsSL https://get.docker.com | bash
systemctl start docker
systemctl enable docker

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 3. 项目部署

```bash
# 克隆项目
git clone <your-repo-url>
cd goadmin

# 配置环境变量
cp deploy/.env.prod.example deploy/.env.prod
vim deploy/.env.prod

# 执行部署
chmod +x deploy/deploy.sh
./deploy/deploy.sh prod deploy
```

### 4. SSL证书配置

#### 方式一：Let's Encrypt自动证书
```bash
# 安装certbot
yum install -y certbot

# 获取证书
certbot certonly --standalone -d yourdomain.com -d api.yourdomain.com -d console.yourdomain.com

# 复制证书
cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem deploy/ssl/cert.pem
cp /etc/letsencrypt/live/yourdomain.com/privkey.pem deploy/ssl/key.pem

# 设置自动续期
echo "0 2 * * * certbot renew --quiet && docker-compose -f /path/to/deploy/docker-compose.yml restart frontend" | crontab -
```

#### 方式二：手动上传证书
```bash
# 将证书文件放置到指定位置
mkdir -p deploy/ssl
# 上传 cert.pem 和 key.pem 到 deploy/ssl/ 目录
```

### 5. 数据库配置

#### 使用阿里云RDS（推荐）
1. 创建RDS MySQL实例
2. 配置安全组，允许ECS访问
3. 更新 `.env.prod` 中的数据库配置

#### 使用自建MySQL
```bash
# 数据库已在docker-compose中配置
# 默认会自动创建和初始化
```

## 📊 监控和维护

### 查看服务状态
```bash
# 查看容器状态
docker-compose -f deploy/docker-compose.yml ps

# 查看日志
./deploy/deploy.sh prod logs

# 查看特定服务日志
docker-compose -f deploy/docker-compose.yml logs -f backend
docker-compose -f deploy/docker-compose.yml logs -f frontend
```

### 更新应用
```bash
# 更新到最新版本
./deploy/deploy.sh prod update

# 重启服务
docker-compose -f deploy/docker-compose.yml restart
```

### 备份数据
```bash
# 备份数据库
docker-compose -f deploy/docker-compose.yml exec mysql mysqldump -u root -p goadmin > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d_%H%M%S).tar.gz deploy/
```

## 🔧 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :443
netstat -tlnp | grep :8080

# 检查防火墙
systemctl status firewalld
firewall-cmd --list-all
```

#### 2. 数据库连接失败
```bash
# 检查数据库容器
docker-compose -f deploy/docker-compose.yml logs mysql

# 测试数据库连接
docker-compose -f deploy/docker-compose.yml exec backend ping mysql
```

#### 3. SSL证书问题
```bash
# 检查证书文件
ls -la deploy/ssl/
openssl x509 -in deploy/ssl/cert.pem -text -noout

# 测试HTTPS
curl -I https://yourdomain.com
```

### 性能优化

#### 1. 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_user_username ON users(username);
CREATE INDEX idx_device_status ON devices(status);
CREATE INDEX idx_task_created_at ON tasks(created_at);
```

#### 2. 缓存配置
```bash
# 启用Redis缓存
# 在docker-compose.yml中已配置Redis服务
```

#### 3. 负载均衡
```bash
# 如需要多实例部署，可以配置多个backend容器
# 修改docker-compose.yml，增加backend副本
```

## 🔐 安全配置

### 1. 防火墙设置
```bash
# 只开放必要端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=22/tcp
firewall-cmd --reload
```

### 2. 定期更新
```bash
# 系统更新
yum update -y

# Docker镜像更新
docker-compose -f deploy/docker-compose.yml pull
```

### 3. 日志监控
```bash
# 设置日志轮转
cat > /etc/logrotate.d/docker-containers << EOF
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size=1M
    missingok
    delaycompress
    copytruncate
}
EOF
```

## 📞 技术支持

如遇到部署问题，请提供以下信息：
1. 服务器配置和操作系统版本
2. 错误日志内容
3. 部署步骤和配置文件
4. 网络环境信息

联系方式：
- 邮箱: <EMAIL>
- 技术文档: https://docs.yourdomain.com
