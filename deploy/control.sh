#!/bin/bash

# 服务控制脚本
APP_NAME="goadmin"
APP_PATH=$(cd $(dirname $0); pwd)
PID_FILE="${APP_PATH}/${APP_NAME}.pid"
LOG_PATH="${APP_PATH}/logs"

# 确保日志目录存在
mkdir -p ${LOG_PATH}

# 获取进程PID
get_pid() {
    if [ -f ${PID_FILE} ]; then
        cat ${PID_FILE}
    fi
}

# 启动服务
start() {
    pid=$(get_pid)
    if [ ! -z "${pid}" ] && kill -0 ${pid} 2>/dev/null; then
        echo "${APP_NAME} already running"
        return
    fi

    echo "Starting ${APP_NAME}..."
    nohup ${APP_PATH}/${APP_NAME} > ${LOG_PATH}/${APP_NAME}.log 2>&1 &
    echo $! > ${PID_FILE}
    
    sleep 1
    pid=$(get_pid)
    if kill -0 ${pid} 2>/dev/null; then
        echo "${APP_NAME} started successfully"
    else
        echo "${APP_NAME} failed to start"
        tail -n 10 ${LOG_PATH}/${APP_NAME}.log
    fi
}

# 停止服务
stop() {
    pid=$(get_pid)
    if [ -z "${pid}" ]; then
        echo "${APP_NAME} not running"
        return
    fi

    echo "Stopping ${APP_NAME}..."
    kill ${pid}
    rm -f ${PID_FILE}
    
    sleep 2
    if kill -0 ${pid} 2>/dev/null; then
        echo "Force killing..."
        kill -9 ${pid}
    fi
    echo "${APP_NAME} stopped"
}

# 重启服务
restart() {
    stop
    sleep 1
    start
}

# 查看状态
status() {
    pid=$(get_pid)
    if [ -z "${pid}" ]; then
        echo "${APP_NAME} not running"
        return
    fi
    
    if kill -0 ${pid} 2>/dev/null; then
        echo "${APP_NAME} running with pid ${pid}"
        echo "Log file: ${LOG_PATH}/${APP_NAME}.log"
    else
        echo "${APP_NAME} not running"
        rm -f ${PID_FILE}
    fi
}

# 查看日志
log() {
    tail -f ${LOG_PATH}/${APP_NAME}.log
}

# 命令行参数处理
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    log)
        log
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|log}"
        exit 1
        ;;
esac

exit 0 