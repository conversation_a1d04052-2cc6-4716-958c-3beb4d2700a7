// AutoJS SDK for GoAdmin API
const BASE_URL = 'https://admintest.l888.eu.org/api/v1'

export default class GoAdminSDK {
    constructor() {
        this.token = null
        this.deviceId = device.getAndroidId() // AutoJS 获取设备ID
        this.heartbeatTimer = null
        this.cardKey = null
    }

    /**
     * 设置认证Token
     * @param {string} token JWT token
     */
    setToken(token) {
        this.token = token
    }

    /**
     * 发送HTTP请求
     * @param {string} path 请求路径
     * @param {object} data 请求数据
     * @param {boolean} needAuth 是否需要认证
     * @returns {Promise<object>}
     */
    async request(path, data, needAuth) {
        var headers = {
            'Content-Type': 'application/json'
        }
        
        if (needAuth && this.token) {
            headers['Authorization'] = 'Bearer ' + this.token
        }

        try {
            var res = await http.post(BASE_URL + path, {
                headers: headers,
                body: JSON.stringify(data || {})
            })

            // AutoJS http 响应处理
            var result = res.body.json()
            if (res.statusCode !== 200) {
                throw new Error(result.message || '请求失败')
            }
            return result
        } catch (err) {
            console.error('请求失败: ' + err.message)
            throw err
        }
    }

    /**
     * 卡密登录
     * @param {string} cardKey 卡密
     * @returns {Promise<object>} 登录结果，包含token
     */
    async login(cardKey) {
        try {
            var result = await this.request('/login', {
                card_key: cardKey,
                device_id: this.deviceId
            })
            if (result.data && result.data.token) {
                this.setToken(result.data.token)
                this.cardKey = cardKey
            }
            return result
        } catch (err) {
            console.error('登录失败: ' + err.message)
            throw err
        }
    }

    /**
     * 绑定设备
     * @param {string} cardKey 卡密
     * @returns {Promise<object>}
     */
    async bindDevice(cardKey) {
        return await this.request('/bindings/bind', {
            card_key: cardKey,
            device_id: this.deviceId
        })
    }

    /**
         * 解绑所有设备
         * @returns {Promise<object>}
         */
    async unbindAllDevices(cardKey) {

        return await this.request('/bindings/unbind_all', {
            card_key: cardKey
        }, true)
    }

    /**
     * 解绑设备
     * @returns {Promise<object>}
     */
    async unbindDevice() {
        if (!this.cardKey) {
            throw new Error('未登录')
        }
        return await this.request('/bindings/unbind', {
            card_key: this.cardKey,
            device_ids: [this.deviceId]
        }, true)
    }

    /**
     * 获取配置
     * @param {string} key 配置键名
     * @returns {Promise<object>}
     */
    async getConfig(key) {
        return await this.request('/config', {
            key: key
        }, true)
    }

    /**
     * 启动活跃状态保持（通过定期调用登录接口）
     * @param {number} interval 间隔，单位毫秒，默认30秒
     */
    startKeepAlive(interval) {
        interval = interval || 30000
        
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer)
        }
        
        if (!this.cardKey) {
            console.error('未登录，无法启动活跃状态保持')
            return
        }
        
        var self = this
        this.heartbeatTimer = setInterval(async function() {
            try {
                await self.login(self.cardKey)
                console.log('活跃状态更新成功')
            } catch (err) {
                console.error('活跃状态更新失败: ' + err.message)
            }
        }, interval)
    }

    /**
     * 停止活跃状态保持
     */
    stopKeepAlive() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer)
            this.heartbeatTimer = null
        }
    }
}

// 使用示例
/*
import GoAdminSDK from './autojs-sdk.js'
const sdk = new GoAdminSDK()

// 登录示例
async function login() {
    try {
        var result = await sdk.login('your-card-key')
        console.log('登录成功:', result)
        // 启动活跃状态保持
        sdk.startKeepAlive()
    } catch (err) {
        console.error('登录失败:', err)
    }
}

// 获取配置示例
async function getConfig() {
    try {
        var result = await sdk.getConfig('config-key')
        console.log('配置:', result)
    } catch (err) {
        console.error('获取配置失败:', err)
    }
}
*/ 