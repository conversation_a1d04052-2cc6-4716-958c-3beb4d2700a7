-- 用户日志表
CREATE TABLE IF NOT EXISTS `user_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` VARCHAR(255) NOT NULL DEFAULT '',
    `robot_id` VARCHAR(255) NOT NULL DEFAULT '',
    `plan_name` VARCHAR(255) NOT NULL DEFAULT '',
    `claims` TEXT NOT NULL DEFAULT '',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin; 

-- 卡密表
CREATE TABLE IF NOT EXISTS `card_keys` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `card_key` VARCHAR(32) NOT NULL UNIQUE COMMENT '卡密',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-未使用 2-已使用 3-已过期',
    `type` TINYINT NOT NULL COMMENT '类型：1-时间卡 2-次数卡',
    `value` INT NOT NULL COMMENT '卡密值（时间卡为天数，次数卡为次数）',
    `max_devices` INT NOT NULL DEFAULT 1 COMMENT '最大可同时在线设备数',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expired_at` TIMESTAMP NULL COMMENT '过期时间',
    `used_at` TIMESTAMP NULL COMMENT '使用时间',
    `parent_id` VARCHAR(32) NULL COMMENT '充值目标卡密ID',
    `recharge_time` TIMESTAMP NULL COMMENT '充值时间',
    INDEX (`card_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- 卡密设备绑定表
CREATE TABLE IF NOT EXISTS `card_device_bindings` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `card_key` VARCHAR(32) NOT NULL COMMENT '卡密',
    `device_id` VARCHAR(64) NOT NULL COMMENT '设备ID',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常 2-离线',
    `last_active` TIMESTAMP NULL COMMENT '最后活跃时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY `idx_card_device` (`card_key`, `device_id`),
    INDEX (`card_key`),
    INDEX (`device_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_last_active` (`last_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- 管理员表
CREATE TABLE IF NOT EXISTS `admin_users` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `username` VARCHAR(32) NOT NULL COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码（bcrypt加密）',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用 2-禁用',
    `last_login` TIMESTAMP NULL COMMENT '最后登录时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` TIMESTAMP NULL,
    UNIQUE KEY `uk_username` (`username`),
    INDEX `idx_status` (`status`),
    INDEX `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='管理员表';

-- 初始化超级管理员账号（如果不存在）
-- 默认账号：admin，密码：admin123
DELETE FROM `admin_users` WHERE `username` = 'admin';
INSERT INTO `admin_users` (`username`, `password`, `status`)
VALUES ('admin', '$2a$10$8lPGyOODVPxsPg31cqa.r.QAL2JJecUpv835lbVeTC3sEeacrZ.L.', 1);

-- 系统配置表
CREATE TABLE IF NOT EXISTS `system_configs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `key` VARCHAR(64) NOT NULL COMMENT '配置键名',
    `value` TEXT NOT NULL COMMENT '配置值',
    `type` VARCHAR(10) NOT NULL DEFAULT 'STRING' COMMENT '配置值类型：STRING/JSON/YAML',
    `desc` VARCHAR(255) DEFAULT '' COMMENT '配置描述',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_key` (`key`),
    INDEX `idx_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='系统配置表';

-- 登录日志表
CREATE TABLE IF NOT EXISTS `login_logs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `card_key` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '卡密',
    `device_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '设备ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_card_key` (`card_key`),
    INDEX `idx_device_id` (`device_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='登录日志表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS `operation_logs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `path` VARCHAR(255) NOT NULL COMMENT '请求路径',
    `method` VARCHAR(10) NOT NULL COMMENT '请求方法',
    `params` TEXT COMMENT '请求参数',
    `response` TEXT COMMENT '响应结果',
    `ip` VARCHAR(64) DEFAULT '' COMMENT '请求IP',
    `user_agent` VARCHAR(255) DEFAULT '' COMMENT '用户代理',
    `status` INT DEFAULT 0 COMMENT 'HTTP响应状态码',
    `code` INT DEFAULT 0 COMMENT '业务响应码',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_path` (`path`),
    INDEX `idx_status` (`status`),
    INDEX `idx_code` (`code`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='操作日志表';

-- GitHub脚本表
CREATE TABLE IF NOT EXISTS `github_scripts` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `branch` VARCHAR(50) NOT NULL COMMENT '分支名称',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
    `script_type` VARCHAR(20) NOT NULL COMMENT '脚本类型',
    `content` LONGTEXT NOT NULL COMMENT '脚本内容',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_branch` (`branch`),
    INDEX `idx_script_type` (`script_type`),
    INDEX `idx_file_name` (`file_name`),
    UNIQUE KEY `uk_branch_script_type` (`branch`, `script_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='GitHub脚本表';

-- 第三方API密钥表
CREATE TABLE IF NOT EXISTS `third_api_keys` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `secret_id` VARCHAR(32) NOT NULL COMMENT 'SecretId',
    `secret_key` VARCHAR(64) NOT NULL COMMENT 'SecretKey',
    `name` VARCHAR(100) NOT NULL COMMENT '密钥名称/描述',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用 2-禁用',
    `permissions` TEXT NOT NULL COMMENT '权限列表，JSON格式',
    `last_used_at` TIMESTAMP NULL COMMENT '最后使用时间',
    `expires_at` TIMESTAMP NULL COMMENT '过期时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_secret_id` (`secret_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='第三方API密钥表';

-- 卡密配置关联表
CREATE TABLE IF NOT EXISTS `card_configs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `card_key` VARCHAR(32) NOT NULL COMMENT '卡密',
    `config` LONGTEXT NOT NULL COMMENT 'JSON配置项',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_card_key` (`card_key`),
    INDEX `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='卡密配置关联表';

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希',
    `email` VARCHAR(100) COMMENT '邮箱',
    `nickname` VARCHAR(50) COMMENT '昵称',
    `avatar` VARCHAR(255) COMMENT '头像',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常 2-禁用',
    `last_login_at` TIMESTAMP NULL COMMENT '最后登录时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户表';

-- 刷新令牌表
CREATE TABLE IF NOT EXISTS `refresh_tokens` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `token` VARCHAR(255) NOT NULL COMMENT '令牌',
    `user_id` INT UNSIGNED COMMENT '用户ID',
    `device_id` BIGINT UNSIGNED COMMENT '设备ID',
    `type` VARCHAR(20) NOT NULL COMMENT '类型：user, device',
    `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_token` (`token`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_device_id` (`device_id`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='刷新令牌表';

-- 脚本市场表
CREATE TABLE IF NOT EXISTS `market_scripts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '脚本名称',
  `summary` varchar(100) DEFAULT '' COMMENT '脚本简介，用于列表页面显示（最多30个中文字符）',
  `description` text COMMENT '脚本描述',
  `content` longtext NOT NULL COMMENT '脚本内容',
  `config` text COMMENT 'JSON格式的配置参数 (JSON Schema)',
  `default_values` text COMMENT 'JSON格式的默认配置值（从Config Schema中提取的default值）',
  `icon` varchar(255) DEFAULT NULL COMMENT '脚本图标URL',
  `version` varchar(20) DEFAULT '1.0.0' COMMENT '脚本版本',
  `tags` text COMMENT '标签，JSON数组格式',
  `author` varchar(100) NOT NULL COMMENT '作者名称',
  `author_id` INT UNSIGNED DEFAULT NULL COMMENT '作者ID',
  `downloads` int DEFAULT '0' COMMENT '下载次数',
  `rating` decimal(3,2) DEFAULT '0.00' COMMENT '评分',
  `rating_count` int DEFAULT '0' COMMENT '评分人数',
  `status` tinyint DEFAULT '1' COMMENT '状态：1-上架 2-下架 3-审核中',
  `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐脚本',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='脚本市场表';

-- 脚本评分表
CREATE TABLE IF NOT EXISTS `market_script_ratings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `market_script_id` bigint unsigned NOT NULL COMMENT '市场脚本ID',
  `user_id` INT UNSIGNED NOT NULL COMMENT '用户ID',
  `rating` tinyint NOT NULL COMMENT '评分：1-5',
  `comment` text COMMENT '评论',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_script_user` (`market_script_id`,`user_id`),
  KEY `idx_market_script_id` (`market_script_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_rating` (`rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='脚本评分表';

-- 市场脚本套餐表
CREATE TABLE IF NOT EXISTS `market_script_packages` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL COMMENT '套餐名称',
    `description` TEXT COMMENT '套餐描述',
    `price` DECIMAL(10,2) NOT NULL COMMENT '价格',
    `device_limit` INT NOT NULL DEFAULT 1 COMMENT '设备数量限制',
    `duration` INT NOT NULL DEFAULT 30 COMMENT '有效期（天）',
    `market_script_id` BIGINT UNSIGNED NOT NULL COMMENT '关联的市场脚本ID',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用 2-禁用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_market_script_id` (`market_script_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='市场脚本套餐表';

-- 支付订单表
CREATE TABLE IF NOT EXISTS `payment_orders` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `user_id` INT UNSIGNED NOT NULL COMMENT '用户ID',
    `package_id` INT UNSIGNED NOT NULL COMMENT '套餐ID',
    `quota_id` INT NULL COMMENT '配额ID（用于配额续费订单）',
    `script_id` INT UNSIGNED NULL COMMENT '脚本ID（用于脚本续费订单）',
    `amount` DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    `pay_type` VARCHAR(20) NOT NULL COMMENT '支付方式：alipay/wxpay',
    `order_type` VARCHAR(20) NOT NULL DEFAULT 'script' COMMENT '订单类型：script/quota/upgrade/quota_renewal/script_renewal',
    `status` TINYINT DEFAULT 0 COMMENT '支付状态：0-未支付 1-已支付 2-已取消 3-已退款',
    `payment_platform` VARCHAR(20) COMMENT '支付平台：zpay/alipay/wxpay等',
    `third_party_order_no` VARCHAR(100) COMMENT '第三方支付平台订单号',
    `notify_data` TEXT COMMENT '支付回调数据',
    `paid_at` TIMESTAMP NULL COMMENT '支付时间',
    `expired_at` TIMESTAMP NOT NULL COMMENT '订单过期时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_order_no` (`order_no`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_package_id` (`package_id`),
    INDEX `idx_quota_id` (`quota_id`),
    INDEX `idx_script_id` (`script_id`),
    INDEX `idx_order_type` (`order_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_expired_at` (`expired_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='支付订单表';

-- 用户脚本表
CREATE TABLE IF NOT EXISTS `user_scripts` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT UNSIGNED NOT NULL COMMENT '用户ID',
    `market_script_id` BIGINT UNSIGNED NOT NULL COMMENT '市场脚本ID',
    `package_id` INT UNSIGNED NULL COMMENT '套餐ID（可选，用于记录购买时的套餐信息）',
    `package_name` VARCHAR(100) NULL COMMENT '套餐名称',
    `name` VARCHAR(100) NOT NULL COMMENT '脚本名称',
    `content` LONGTEXT COMMENT '脚本内容',
    `config` TEXT COMMENT '配置参数(JSON Schema)',
    `default_values` TEXT COMMENT 'JSON格式的默认配置值（从Config Schema中提取的default值）',
    `version` VARCHAR(20) DEFAULT '1.0.0' COMMENT '脚本版本',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用 2-禁用',
    `max_devices` INT DEFAULT 1 COMMENT '最大设备限制数',
    `expired_at` TIMESTAMP NULL COMMENT '脚本可用截止时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_market_script_id` (`market_script_id`),
    INDEX `idx_package_id` (`package_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_expired_at` (`expired_at`),
    UNIQUE KEY `uk_user_market_script` (`user_id`, `market_script_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户脚本表';

-- 设备表
CREATE TABLE IF NOT EXISTS `devices` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `device_id` VARCHAR(100) NOT NULL COMMENT '设备ID',
    `name` VARCHAR(100) COMMENT '设备名称',
    `device_model` VARCHAR(100) DEFAULT '' COMMENT '设备型号',
    `android_version` VARCHAR(20) DEFAULT '' COMMENT '安卓版本',
    `autox_version` VARCHAR(20) DEFAULT '' COMMENT 'AutoX版本',
    `status` INT DEFAULT 0 COMMENT '状态：0-离线 1-在线',
    `last_active` TIMESTAMP NULL COMMENT '最后活跃时间',
    `user_id` INT UNSIGNED COMMENT '所属用户ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `pairing_code` VARCHAR(10) DEFAULT NULL COMMENT '配对码',
    `pairing_status` INT DEFAULT 0 COMMENT '配对状态：0-未配对 1-配对中 2-已配对 3-已过期',
    `pairing_expires_at` TIMESTAMP NULL DEFAULT NULL COMMENT '配对过期时间',
    `token` VARCHAR(255) DEFAULT NULL COMMENT 'WebSocket认证token',
    `token_expires_at` TIMESTAMP NULL DEFAULT NULL COMMENT 'Token过期时间',
    UNIQUE KEY `uk_device_id` (`device_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_pairing_code` (`pairing_code`),
    INDEX `idx_pairing_status` (`pairing_status`),
    INDEX `idx_pairing_expires_at` (`pairing_expires_at`),
    INDEX `idx_token` (`token`),
    INDEX `idx_token_expires_at` (`token_expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='设备表';

-- 用户脚本设备绑定关系表
CREATE TABLE IF NOT EXISTS `user_script_device_bindings` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_script_id` BIGINT UNSIGNED NOT NULL COMMENT '用户脚本ID',
    `device_id` BIGINT UNSIGNED NOT NULL COMMENT '设备ID',
    `status` TINYINT DEFAULT 1 COMMENT '绑定状态：1-正常 2-禁用',
    `last_active` TIMESTAMP NULL COMMENT '最后活跃时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_user_script_id` (`user_script_id`),
    INDEX `idx_device_id` (`device_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_last_active` (`last_active`),
    UNIQUE KEY `uk_user_script_device` (`user_script_id`, `device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户脚本设备绑定关系表';

-- 用户脚本配置表 - 存储用户的具体配置值
CREATE TABLE IF NOT EXISTS `user_script_configs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT UNSIGNED NOT NULL COMMENT '用户ID',
    `script_id` BIGINT UNSIGNED NOT NULL COMMENT '脚本ID',
    `config_name` VARCHAR(100) NOT NULL COMMENT '配置名称',
    `config_values` JSON NOT NULL COMMENT '配置值(JSON格式)',
    `device_ids` JSON COMMENT '关联的设备ID列表',
    `is_active` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_user_script` (`user_id`, `script_id`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户脚本配置表';

-- 设备配置关联表 - 管理设备与配置的关联关系
CREATE TABLE IF NOT EXISTS `device_config_relations` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `device_id` BIGINT UNSIGNED NOT NULL COMMENT '设备ID',
    `user_config_id` BIGINT UNSIGNED NOT NULL COMMENT '用户配置ID',
    `priority` INT DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_device_config` (`device_id`, `user_config_id`),
    INDEX `idx_device_id` (`device_id`),
    INDEX `idx_user_config_id` (`user_config_id`),
    INDEX `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='设备配置关联表';

-- 设备配额表 - 管理用户的设备配额
CREATE TABLE IF NOT EXISTS `device_quotas` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT UNSIGNED NOT NULL COMMENT '用户ID',
    `total_quota` INT NOT NULL DEFAULT 1 COMMENT '总配额数量',
    `used_quota` INT NOT NULL DEFAULT 0 COMMENT '已使用配额数量',
    `available_quota` INT NOT NULL DEFAULT 1 COMMENT '可用配额数量',
    `price_per_quota` DECIMAL(10,2) DEFAULT 1.00 COMMENT '每个配额的价格',
    `total_amount` DECIMAL(10,2) NOT NULL COMMENT '总金额',
    `expires_at` TIMESTAMP NULL COMMENT '过期时间',
    `order_no` VARCHAR(50) NULL COMMENT '关联的订单号',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_expires_at` (`expires_at`),
    INDEX `idx_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='设备配额表';

-- 设备配额使用记录表 - 记录配额的分配
CREATE TABLE IF NOT EXISTS `device_quota_usage` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT UNSIGNED NOT NULL COMMENT '用户ID',
    `quota_id` BIGINT UNSIGNED NOT NULL COMMENT '配额ID',
    `device_id` BIGINT UNSIGNED NOT NULL COMMENT '设备ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_quota_id` (`quota_id`),
    INDEX `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='设备配额使用记录表';


-- 插入示例数据
INSERT INTO `market_script_packages` (`name`, `description`, `price`, `device_limit`, `duration`, `market_script_id`, `status`) VALUES
('基础版', '支持1个设备运行，有效期30天', 9.90, 1, 30, 1, 1),
('标准版', '支持3个设备运行，有效期30天', 19.90, 3, 30, 1, 1),
('专业版', '支持10个设备运行，有效期30天', 49.90, 10, 30, 1, 1),
('基础版', '支持1个设备运行，有效期30天', 15.90, 1, 30, 2, 1),
('标准版', '支持3个设备运行，有效期30天', 29.90, 3, 30, 2, 1),
('专业版', '支持10个设备运行，有效期30天', 79.90, 10, 30, 2, 1);




-- 插入脚本市场示例数据
INSERT INTO `market_scripts` (`name`, `summary`, `description`, `content`, `config`, `default_values`, `icon`, `version`, `tags`, `author`, `author_id`, `downloads`, `rating`, `rating_count`, `status`, `is_featured`) VALUES
('自动签到脚本', '自动完成每日签到任务，支持多种平台', '自动完成每日签到任务，支持多种平台', 'console.log("开始自动签到");\n// 签到逻辑\nconsole.log("签到完成");', '{"type":"object","properties":{"platform":{"type":"string","title":"签到平台","enum":["weibo","zhihu","bilibili","douyin"],"default":"weibo"},"signin_time":{"type":"string","title":"签到时间","format":"time","default":"09:00"},"auto_retry":{"type":"boolean","title":"自动重试","default":true},"retry_count":{"type":"integer","title":"重试次数","minimum":1,"maximum":10,"default":3}},"required":["platform","signin_time"]}', '{"platform":"weibo","signin_time":"09:00","auto_retry":true,"retry_count":3}', '', '1.0.0', '["签到", "自动化", "日常"]', '官方团队', 1, 150, 4.5, 30, 1, 1),
('数据采集助手', '智能数据采集工具，支持多种网站', '智能数据采集工具，支持多种网站', '// 数据采集逻辑\nfunction collectData() {\n  // 采集实现\n}', '{"type":"object","properties":{"target_url":{"type":"string","title":"目标网站","format":"uri","default":"https://example.com"},"interval":{"type":"integer","title":"采集间隔","minimum":1000,"maximum":60000,"default":5000},"max_pages":{"type":"integer","title":"最大页数","minimum":1,"maximum":1000,"default":100},"data_fields":{"type":"array","title":"数据字段","items":{"type":"string"},"default":["title","content","author"]},"save_format":{"type":"string","title":"保存格式","enum":["json","csv","excel"],"default":"json"}},"required":["target_url","interval"]}', '{"target_url":"https://example.com","interval":5000,"max_pages":100,"data_fields":["title","content","author"],"save_format":"json"}', '', '1.2.0', '["数据", "采集", "爬虫"]', '官方团队', 1, 89, 4.2, 15, 1, 1),
('文件管理工具', '高效的文件批量处理工具', '高效的文件批量处理工具', '// 文件处理逻辑\nfunction processFiles() {\n  // 处理实现\n}', '{"type":"object","properties":{"path":{"type":"string","title":"文件路径","default":"/downloads"},"type":{"type":"string","title":"文件类型","enum":["image","video","document"],"default":"image"},"recursive":{"type":"boolean","title":"递归处理","default":false},"max_size":{"type":"integer","title":"最大文件大小(MB)","minimum":1,"maximum":1000,"default":100}},"required":["path","type"]}', '{"path":"/downloads","type":"image","recursive":false,"max_size":100}', '', '1.1.0', '["文件", "管理", "批量"]', '开发者小王', 2, 67, 4.0, 12, 1, 0),
('定时任务管理器', '灵活的定时任务调度工具', '灵活的定时任务调度工具', '// 定时任务逻辑\nfunction scheduleTask() {\n  // 调度实现\n}', '{"type":"object","properties":{"tasks":{"type":"array","title":"任务列表","items":{"type":"object"}},"timezone":{"type":"string","title":"时区","default":"Asia/Shanghai"},"auto_start":{"type":"boolean","title":"自动启动","default":true},"log_level":{"type":"string","title":"日志级别","enum":["debug","info","warn","error"],"default":"info"}},"required":["tasks"]}', '{"tasks":[],"timezone":"Asia/Shanghai","auto_start":true,"log_level":"info"}', '', '1.3.0', '["定时", "任务", "调度"]', '开发者小李', 3, 45, 4.3, 8, 1, 0),
('网络监控脚本', '实时监控网络状态和连接', '实时监控网络状态和连接', '// 网络监控逻辑\nfunction monitorNetwork() {\n  // 监控实现\n}', '{"type":"object","properties":{"interval":{"type":"integer","title":"监控间隔(毫秒)","minimum":1000,"maximum":60000,"default":1000},"alert":{"type":"boolean","title":"启用告警","default":true},"threshold":{"type":"number","title":"延迟阈值(ms)","minimum":0,"maximum":10000,"default":1000},"targets":{"type":"array","title":"监控目标","items":{"type":"string"},"default":["8.8.8.8","114.114.114.114"]}},"required":["interval"]}', '{"interval":1000,"alert":true,"threshold":1000,"targets":["8.8.8.8","114.114.114.114"]}', '', '1.0.1', '["网络", "监控", "状态"]', '官方团队', 1, 123, 4.4, 25, 1, 0);

