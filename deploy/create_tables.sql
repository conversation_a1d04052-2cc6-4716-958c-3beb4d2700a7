-- 用户日志表
CREATE TABLE IF NOT EXISTS `user_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` VARCHAR(255) NOT NULL DEFAULT '',
    `robot_id` VARCHAR(255) NOT NULL DEFAULT '',
    `plan_name` VARCHAR(255) NOT NULL DEFAULT '',
    `claims` TEXT NOT NULL DEFAULT '',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin; 

-- 卡密表
CREATE TABLE IF NOT EXISTS `card_keys` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `card_key` VARCHAR(32) NOT NULL UNIQUE COMMENT '卡密',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-未使用 2-已使用 3-已过期',
    `type` TINYINT NOT NULL COMMENT '类型：1-时间卡 2-次数卡',
    `value` INT NOT NULL COMMENT '卡密值（时间卡为天数，次数卡为次数）',
    `max_devices` INT NOT NULL DEFAULT 1 COMMENT '最大可同时在线设备数',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expired_at` TIMESTAMP NULL COMMENT '过期时间',
    `used_at` TIMESTAMP NULL COMMENT '使用时间',
    INDEX (`card_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- 向card_keys表添加parent_id和recharge_time字段
ALTER TABLE `card_keys`
ADD COLUMN `parent_id` VARCHAR(32) NULL COMMENT '充值目标卡密ID' AFTER `used_at`,
ADD COLUMN `recharge_time` TIMESTAMP NULL COMMENT '充值时间' AFTER `parent_id`;

-- 卡密设备绑定表
CREATE TABLE IF NOT EXISTS `card_device_bindings` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `card_key` VARCHAR(32) NOT NULL COMMENT '卡密',
    `device_id` VARCHAR(64) NOT NULL COMMENT '设备ID',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常 2-离线',
    `last_active` TIMESTAMP NULL COMMENT '最后活跃时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY `idx_card_device` (`card_key`, `device_id`),
    INDEX (`card_key`),
    INDEX (`device_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_last_active` (`last_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- 管理员表
CREATE TABLE IF NOT EXISTS `admin_users` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `username` VARCHAR(32) NOT NULL COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码（bcrypt加密）',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用 2-禁用',
    `last_login` TIMESTAMP NULL COMMENT '最后登录时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` TIMESTAMP NULL,
    UNIQUE KEY `uk_username` (`username`),
    INDEX `idx_status` (`status`),
    INDEX `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='管理员表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `system_configs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `key` VARCHAR(64) NOT NULL COMMENT '配置键名',
    `value` TEXT NOT NULL COMMENT '配置值',
    `type` VARCHAR(10) NOT NULL DEFAULT 'STRING' COMMENT '配置值类型：STRING/JSON/YAML',
    `desc` VARCHAR(255) DEFAULT '' COMMENT '配置描述',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_key` (`key`),
    INDEX `idx_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='系统配置表';

-- 登录日志表
CREATE TABLE IF NOT EXISTS `login_logs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `card_key` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '卡密',
    `device_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '设备ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_card_key` (`card_key`),
    INDEX `idx_device_id` (`device_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='登录日志表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS `operation_logs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `path` VARCHAR(255) NOT NULL COMMENT '请求路径',
    `method` VARCHAR(10) NOT NULL COMMENT '请求方法',
    `params` TEXT COMMENT '请求参数',
    `response` TEXT COMMENT '响应结果',
    `ip` VARCHAR(64) DEFAULT '' COMMENT '请求IP',
    `user_agent` VARCHAR(255) DEFAULT '' COMMENT '用户代理',
    `status` INT DEFAULT 0 COMMENT '响应状态码',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_path` (`path`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='操作日志表';

-- GitHub脚本表
CREATE TABLE IF NOT EXISTS `github_scripts` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `branch` VARCHAR(50) NOT NULL COMMENT '分支名称',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
    `script_type` VARCHAR(20) NOT NULL COMMENT '脚本类型',
    `content` LONGTEXT NOT NULL COMMENT '脚本内容',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_branch` (`branch`),
    INDEX `idx_script_type` (`script_type`),
    INDEX `idx_file_name` (`file_name`),
    UNIQUE KEY `uk_branch_script_type` (`branch`, `script_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='GitHub脚本表';

-- 第三方API密钥表
CREATE TABLE IF NOT EXISTS `third_api_keys` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `secret_id` VARCHAR(32) NOT NULL COMMENT 'SecretId',
    `secret_key` VARCHAR(64) NOT NULL COMMENT 'SecretKey',
    `name` VARCHAR(100) NOT NULL COMMENT '密钥名称/描述',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用 2-禁用',
    `permissions` TEXT NOT NULL COMMENT '权限列表，JSON格式',
    `last_used_at` TIMESTAMP NULL COMMENT '最后使用时间',
    `expires_at` TIMESTAMP NULL COMMENT '过期时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_secret_id` (`secret_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='第三方API密钥表';

-- 卡密配置关联表
CREATE TABLE IF NOT EXISTS `card_configs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `card_key` VARCHAR(32) NOT NULL COMMENT '卡密',
    `config` LONGTEXT NOT NULL COMMENT 'JSON配置项',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_card_key` (`card_key`),
    INDEX `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='卡密配置关联表';

-- 初始化超级管理员账号（如果不存在）
-- 默认账号：admin，密码：admin123
DELETE FROM `admin_users` WHERE `username` = 'admin';
INSERT INTO `admin_users` (`username`, `password`, `status`)
VALUES ('admin', '$2a$10$8lPGyOODVPxsPg31cqa.r.QAL2JJecUpv835lbVeTC3sEeacrZ.L.', 1);
