# 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production --legacy-peer-deps

# 复制源代码
COPY . .

# 构建应用
RUN npm run build:h5

# 运行阶段
FROM nginx:alpine

# 安装必要的包
RUN apk add --no-cache ca-certificates

# 复制构建产物
COPY --from=builder /app/dist/build/h5 /usr/share/nginx/html

# 复制nginx配置
COPY ../deploy/nginx.conf /etc/nginx/nginx.conf

# 创建SSL证书目录
RUN mkdir -p /etc/nginx/ssl

# 暴露端口
EXPOSE 80 443

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
