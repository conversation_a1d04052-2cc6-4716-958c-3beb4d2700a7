#!/bin/bash

# 群控系统部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev/staging/prod
# 操作: deploy/update/rollback/logs

set -e

# 配置变量
PROJECT_NAME="goadmin"
DOCKER_REGISTRY="registry.cn-hangzhou.aliyuncs.com/yournamespace"
DEPLOY_ENV=${1:-prod}
ACTION=${2:-deploy}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 设置环境变量
setup_env() {
    log_info "设置环境变量..."
    
    # 从环境文件加载配置
    if [ -f ".env.${DEPLOY_ENV}" ]; then
        export $(cat .env.${DEPLOY_ENV} | xargs)
    else
        log_error "环境配置文件 .env.${DEPLOY_ENV} 不存在"
        exit 1
    fi
    
    # 设置镜像标签
    export IMAGE_TAG=${IMAGE_TAG:-latest}
    export BACKEND_IMAGE="${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:${IMAGE_TAG}"
    export FRONTEND_IMAGE="${DOCKER_REGISTRY}/${PROJECT_NAME}-frontend:${IMAGE_TAG}"
    
    log_info "环境变量设置完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker build -f deploy/Dockerfile.backend -t ${BACKEND_IMAGE} .
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker build -f deploy/Dockerfile.frontend -t ${FRONTEND_IMAGE} ./console-app
    
    log_info "镜像构建完成"
}

# 推送镜像
push_images() {
    log_info "推送镜像到仓库..."
    
    docker push ${BACKEND_IMAGE}
    docker push ${FRONTEND_IMAGE}
    
    log_info "镜像推送完成"
}

# 部署应用
deploy_app() {
    log_info "部署应用..."
    
    # 创建必要的目录
    mkdir -p logs ssl
    
    # 生成配置文件
    envsubst < deploy/docker-compose.yml.template > deploy/docker-compose.yml
    
    # 停止旧容器
    docker-compose -f deploy/docker-compose.yml down
    
    # 启动新容器
    docker-compose -f deploy/docker-compose.yml up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 健康检查
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_info "后端服务启动成功"
    else
        log_error "后端服务启动失败"
        exit 1
    fi
    
    if curl -f http://localhost/ > /dev/null 2>&1; then
        log_info "前端服务启动成功"
    else
        log_error "前端服务启动失败"
        exit 1
    fi
    
    log_info "应用部署完成"
}

# 更新应用
update_app() {
    log_info "更新应用..."
    
    # 拉取最新镜像
    docker-compose -f deploy/docker-compose.yml pull
    
    # 重启服务
    docker-compose -f deploy/docker-compose.yml up -d
    
    log_info "应用更新完成"
}

# 回滚应用
rollback_app() {
    log_info "回滚应用..."
    
    # 这里可以实现回滚逻辑
    # 例如：切换到上一个版本的镜像
    
    log_warn "回滚功能待实现"
}

# 查看日志
show_logs() {
    log_info "查看应用日志..."
    docker-compose -f deploy/docker-compose.yml logs -f
}

# SSL证书配置
setup_ssl() {
    log_info "配置SSL证书..."
    
    if [ ! -f "ssl/cert.pem" ] || [ ! -f "ssl/key.pem" ]; then
        log_warn "SSL证书不存在，请手动配置证书文件："
        log_warn "  ssl/cert.pem - SSL证书文件"
        log_warn "  ssl/key.pem - SSL私钥文件"
        log_warn "或使用Let's Encrypt自动获取证书"
    fi
}

# 数据库初始化
init_database() {
    log_info "初始化数据库..."
    
    # 等待数据库启动
    sleep 10
    
    # 执行数据库迁移
    docker-compose -f deploy/docker-compose.yml exec backend ./main migrate
    
    log_info "数据库初始化完成"
}

# 主函数
main() {
    log_info "开始部署群控系统 - 环境: ${DEPLOY_ENV}, 操作: ${ACTION}"
    
    check_dependencies
    setup_env
    
    case ${ACTION} in
        "deploy")
            build_images
            push_images
            deploy_app
            setup_ssl
            init_database
            ;;
        "update")
            update_app
            ;;
        "rollback")
            rollback_app
            ;;
        "logs")
            show_logs
            ;;
        *)
            log_error "未知操作: ${ACTION}"
            log_info "支持的操作: deploy, update, rollback, logs"
            exit 1
            ;;
    esac
    
    log_info "操作完成"
}

# 执行主函数
main "$@"
