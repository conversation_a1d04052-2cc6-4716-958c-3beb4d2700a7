package utils

import (
	"time"
)

// GetCurrentTime 获取当前时间
func GetCurrentTime() time.Time {
	return time.Now()
}

// FormatTime 格式化时间
func FormatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// ParseTime 解析时间字符串
func ParseTime(timeStr string) (time.Time, error) {
	return time.Parse("2006-01-02 15:04:05", timeStr)
}

// GetTimeStamp 获取时间戳
func GetTimeStamp() int64 {
	return time.Now().Unix()
}

// GetMilliTimeStamp 获取毫秒时间戳
func GetMilliTimeStamp() int64 {
	return time.Now().UnixMilli()
}

// IsExpired 检查时间是否过期
func IsExpired(t time.Time) bool {
	return time.Now().After(t)
}

// AddDuration 添加时间间隔
func AddDuration(t time.Time, duration time.Duration) time.Time {
	return t.Add(duration)
}

// DiffDays 计算两个时间相差的天数
func DiffDays(t1, t2 time.Time) int {
	diff := t2.Sub(t1)
	return int(diff.Hours() / 24)
}

// StartOfDay 获取一天的开始时间
func StartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// EndOfDay 获取一天的结束时间
func EndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999999999, t.Location())
}
