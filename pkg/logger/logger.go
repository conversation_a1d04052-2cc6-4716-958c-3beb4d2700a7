package logger

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"time"

	"goadmin/pkg/ctxutil"
	"gopkg.in/natefinch/lumberjack.v2"
)

type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
)

var (
	currentLevel = DEBUG
	logger       *log.Logger
)

// InitLogger 初始化日志配置
func InitLogger(filename string, maxSize, maxBackups, maxAge int) {
	// 设置日志输出到文件
	lumberjackLogger := &lumberjack.Logger{
		Filename:   filename,   // 日志文件路径
		MaxSize:    maxSize,    // 每个日志文件最大尺寸，单位MB
		MaxBackups: maxBackups, // 保留的旧日志文件最大数量
		MaxAge:     maxAge,     // 保留的旧日志文件最大天数
		Compress:   true,       // 是否压缩旧日志文件
	}

	// 同时输出到文件和标准输出
	multiWriter := io.MultiWriter(os.Stdout, lumberjackLogger)
	logger = log.New(multiWriter, "", 0)
}

func SetLogLevel(level LogLevel) {
	currentLevel = level
}

func logWithLevel(ctx context.Context, level LogLevel, format string, v ...interface{}) {
	if level < currentLevel {
		return
	}

	// Get caller file and line
	_, file, line, ok := runtime.Caller(2)
	if !ok {
		file = "unknown"
		line = 0
	}
	// Extract just the file name without the full path
	file = filepath.Base(file)

	// Get request ID from context
	requestID := ""
	if ctx != nil {
		// 尝试从自定义上下文键获取请求ID
		if reqID, ok := ctx.Value(ctxutil.RequestIDKey).(string); ok && reqID != "" {
			requestID = reqID
		} else if reqID, ok := ctx.Value("requestID").(string); ok && reqID != "" {
			// 向后兼容，也尝试从旧的键获取
			requestID = reqID
		}
	}

	// Format timestamp
	timestamp := time.Now().Format("2006-01-02 15:04:05")

	// Format message
	message := fmt.Sprintf(format, v...)

	// Format the log entry according to the specified format:
	// timestamp [LEVEL] [reqid][file:line] message
	logEntry := fmt.Sprintf("%s [%s] [%s][%s:%d] %s",
		timestamp,
		levelToString(level),
		requestID,
		file,
		line,
		message,
	)

	logger.Output(3, logEntry)
}

func levelToString(level LogLevel) string {
	switch level {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	default:
		return "UNKNOWN"
	}
}

func Debug(ctx context.Context, format string, v ...interface{}) {
	logWithLevel(ctx, DEBUG, format, v...)
}

func Info(ctx context.Context, format string, v ...interface{}) {
	logWithLevel(ctx, INFO, format, v...)
}

func Warn(ctx context.Context, format string, v ...interface{}) {
	logWithLevel(ctx, WARN, format, v...)
}

func Error(ctx context.Context, format string, v ...interface{}) {
	logWithLevel(ctx, ERROR, format, v...)
}

// LogRequest logs request parameters
func LogRequest(ctx context.Context, params interface{}) {
	Info(ctx, "Request parameters: %+v", params)
}

// LogResponse logs response parameters with truncation for long responses
func LogResponse(ctx context.Context, response interface{}) {
	// Convert response to string for truncation
	respStr := fmt.Sprintf("%+v", response)

	// Truncate if too long (more than 500 characters)
	const maxLength = 500
	if len(respStr) > maxLength {
		// Keep the first 200 and last 200 characters with ellipsis in between
		truncated := respStr[:200] + "... [truncated " + fmt.Sprintf("%d", len(respStr)-maxLength) + " characters] ..." + respStr[len(respStr)-200:]
		Info(ctx, "Response parameters (truncated): %s", truncated)
	} else {
		Info(ctx, "Response parameters: %s", respStr)
	}
}
