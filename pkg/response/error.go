package response

// Error 自定义错误
type Error struct {
	Code    int    // 错误码
	Message string // 错误信息
}

func (e *Error) Error() string {
	return e.Message
}

// NewError 创建错误
func NewError(code int, message string) *Error {
	return &Error{
		Code:    code,
		Message: message,
	}
}

// IsError 判断错误类型
func IsError(err error, code int) bool {
	if e, ok := err.(*Error); ok {
		return e.Code == code
	}
	return false
}
