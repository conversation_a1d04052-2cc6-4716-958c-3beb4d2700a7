package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code      int         `json:"code"`       // 业务码
	Message   string      `json:"message"`    // 提示信息
	Data      interface{} `json:"data"`       // 数据
	RequestID string      `json:"request_id"` // 请求ID
}

// 预定义错误码
const (
	// 通用错误码
	SUCCESS          = 0    // 成功
	ERROR            = 1000 // 一般错误
	INVALID_PARAMS   = 1001 // 参数错误
	UNAUTHORIZED     = 1002 // 未授权
	FORBIDDEN        = 1003 // 权限不足
	DB_ERROR         = 1004 // 数据库错误
	API_ERROR        = 1005 // API错误

	// 业务错误码
	CARD_NOT_FOUND   = 2001 // 卡密不存在
	CARD_USED        = 2002 // 卡密已使用
	CARD_EXPIRED     = 2003 // 卡密已过期
	CARD_DISABLED    = 2004 // 卡密已禁用
	DEVICE_NOT_FOUND = 2005 // 设备不存在
	BIND_LIMIT       = 2006 // 绑定数量超限
	BIND_NOT_FOUND   = 2007 // 未绑定卡密
	CONFIG_NOT_FOUND = 2008 // 配置不存在
	
)

var codeMessages = map[int]string{
	SUCCESS:          "成功",
	ERROR:            "服务器内部错误",
	INVALID_PARAMS:   "参数错误",
	UNAUTHORIZED:     "未授权访问",
	CARD_NOT_FOUND:   "卡密不存在",
	CARD_USED:        "卡密已使用",
	CARD_EXPIRED:     "卡密已过期",
	DEVICE_NOT_FOUND: "设备不存在",
	BIND_LIMIT:       "绑定数量超限,请解绑",
	BIND_NOT_FOUND:   "未绑定卡密",
	CONFIG_NOT_FOUND: "配置不存在",
}

// getRequestID 获取请求ID
func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("requestID"); exists {
		if reqID, ok := requestID.(string); ok {
			return reqID
		}
	}
	return ""
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:      SUCCESS,
		Message:   codeMessages[SUCCESS],
		Data:      data,
		RequestID: getRequestID(c),
	})
}

// Fail 错误响应
func Fail(c *gin.Context, code int, message string) {
	if message == "" {
		message = codeMessages[code]
	}
	c.JSON(http.StatusOK, Response{
		Code:      code,
		Message:   message,
		Data:      nil,
		RequestID: getRequestID(c),
	})
}
