package response

import (
    "net/http"

    "github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
    Code    int         `json:"code"`    // 业务码
    Message string      `json:"message"` // 提示信息
    Data    interface{} `json:"data"`    // 数据
}

// 预定义错误码
const (
    SUCCESS          = 0    // 成功
    ERROR            = 1000 // 一般错误
    INVALID_PARAMS   = 1001 // 参数错误
    UNAUTHORIZED     = 1002 // 未授权
    CARD_NOT_FOUND   = 2001 // 卡密不存在
    CARD_USED        = 2002 // 卡密已使用
    CARD_EXPIRED     = 2003 // 卡密已过期
    CARD_DISABLED    = 2004 // 卡密已禁用
    DEVICE_NOT_FOUND = 3001 // 设备不存在
    DEVICE_DISABLED  = 3002 // 设备已禁用
    BIND_LIMIT       = 4001 // 绑定数量超限
    BIND_NOT_FOUND   = 4002 // 未绑定卡密
    CONFIG_NOT_FOUND = 5001 // 配置不存在
    FORBIDDEN        = 6001 // 禁止访问
)

var codeMessages = map[int]string{
    SUCCESS:          "success",
    ERROR:            "error",
    INVALID_PARAMS:   "invalid params",
    UNAUTHORIZED:     "unauthorized",
    CARD_NOT_FOUND:   "card key not found",
    CARD_USED:        "card key already used",
    CARD_EXPIRED:     "card key expired",
    DEVICE_NOT_FOUND: "device not found",
    DEVICE_DISABLED:  "device disabled",
    BIND_LIMIT:       "binding limit exceeded",
    BIND_NOT_FOUND:   "binding not found",
    CONFIG_NOT_FOUND: "config not found",
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
    c.JSON(http.StatusOK, Response{
        Code:    SUCCESS,
        Message: codeMessages[SUCCESS],
        Data:    data,
    })
}

// Fail 错误响应
func Fail(c *gin.Context, code int, message string) {
    if message == "" {
        message = codeMessages[code]
    }
    c.JSON(http.StatusOK, Response{
        Code:    code,
        Message: message,
        Data:    nil,
    })
}
