package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisClient Redis客户端
type RedisClient struct {
	client *redis.Client
}

// NewRedisClient 创建Redis客户端
func NewRedisClient(addr, password string, db int) *RedisClient {
	client := redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     password,
		DB:           db,
		PoolSize:     20,              // 连接池大小
		DialTimeout:  5 * time.Second, // 连接超时
		ReadTimeout:  3 * time.Second, // 读取超时
		WriteTimeout: 3 * time.Second, // 写入超时
		MaxRetries:   3,               // 最大重试次数
	})

	return &RedisClient{
		client: client,
	}
}

// Ping 测试连接
func (r *RedisClient) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// Close 关闭连接
func (r *RedisClient) Close() error {
	return r.client.Close()
}

// GetClient 获取原始客户端
func (r *RedisClient) GetClient() *redis.Client {
	return r.client
}

// 设备状态管理相关方法

// SetDeviceOnline 设置设备在线状态
func (r *RedisClient) SetDeviceOnline(ctx context.Context, userID uint, deviceID string) error {
	key := fmt.Sprintf("device:online:%d", userID)
	return r.client.HSet(ctx, key, deviceID, time.Now().Unix()).Err()
}

// SetDeviceOffline 设置设备离线状态
func (r *RedisClient) SetDeviceOffline(ctx context.Context, userID uint, deviceID string) error {
	key := fmt.Sprintf("device:online:%d", userID)
	return r.client.HDel(ctx, key, deviceID).Err()
}

// IsDeviceOnline 检查设备是否在线
func (r *RedisClient) IsDeviceOnline(ctx context.Context, userID uint, deviceID string) (bool, error) {
	key := fmt.Sprintf("device:online:%d", userID)
	exists, err := r.client.HExists(ctx, key, deviceID).Result()
	return exists, err
}

// GetOnlineDevices 获取用户在线设备列表
func (r *RedisClient) GetOnlineDevices(ctx context.Context, userID uint) (map[string]int64, error) {
	key := fmt.Sprintf("device:online:%d", userID)
	result, err := r.client.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	devices := make(map[string]int64)
	for deviceID, timestampStr := range result {
		var timestamp int64
		fmt.Sscanf(timestampStr, "%d", &timestamp)
		devices[deviceID] = timestamp
	}

	return devices, nil
}

// UpdateDeviceHeartbeat 更新设备心跳时间
func (r *RedisClient) UpdateDeviceHeartbeat(ctx context.Context, userID uint, deviceID string) error {
	key := fmt.Sprintf("device:heartbeat:%d", userID)
	return r.client.HSet(ctx, key, deviceID, time.Now().Unix()).Err()
}

// GetDeviceHeartbeat 获取设备心跳时间
func (r *RedisClient) GetDeviceHeartbeat(ctx context.Context, userID uint, deviceID string) (int64, error) {
	key := fmt.Sprintf("device:heartbeat:%d", userID)
	result, err := r.client.HGet(ctx, key, deviceID).Result()
	if err != nil {
		return 0, err
	}

	var timestamp int64
	fmt.Sscanf(result, "%d", &timestamp)
	return timestamp, nil
}

// SetDeviceConnection 设置设备连接信息
func (r *RedisClient) SetDeviceConnection(ctx context.Context, userID uint, deviceID, connectionID string) error {
	key := fmt.Sprintf("device:connection:%d", userID)
	return r.client.HSet(ctx, key, deviceID, connectionID).Err()
}

// RemoveDeviceConnection 移除设备连接信息
func (r *RedisClient) RemoveDeviceConnection(ctx context.Context, userID uint, deviceID string) error {
	key := fmt.Sprintf("device:connection:%d", userID)
	return r.client.HDel(ctx, key, deviceID).Err()
}

// UpdateUserConnectionCount 更新用户连接统计
func (r *RedisClient) UpdateUserConnectionCount(ctx context.Context, userID uint, deviceCount, uniappCount int) error {
	key := fmt.Sprintf("user:connections:%d", userID)
	data := map[string]interface{}{
		"device_count": deviceCount,
		"uniapp_count": uniappCount,
		"total_count":  deviceCount + uniappCount,
		"updated_at":   time.Now().Unix(),
	}

	return r.client.HMSet(ctx, key, data).Err()
}

// GetUserConnectionCount 获取用户连接统计
func (r *RedisClient) GetUserConnectionCount(ctx context.Context, userID uint) (map[string]int, error) {
	key := fmt.Sprintf("user:connections:%d", userID)
	result, err := r.client.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	counts := make(map[string]int)
	for field, value := range result {
		if field == "updated_at" {
			continue
		}
		var count int
		fmt.Sscanf(value, "%d", &count)
		counts[field] = count
	}

	return counts, nil
}

// SetDeviceInfo 缓存设备信息
func (r *RedisClient) SetDeviceInfo(ctx context.Context, deviceID string, info map[string]interface{}) error {
	key := fmt.Sprintf("device:info:%s", deviceID)

	// 设置24小时过期
	return r.client.HMSet(ctx, key, info).Err()
}

// GetDeviceInfo 获取设备信息
func (r *RedisClient) GetDeviceInfo(ctx context.Context, deviceID string) (map[string]string, error) {
	key := fmt.Sprintf("device:info:%s", deviceID)
	return r.client.HGetAll(ctx, key).Result()
}

// CleanupExpiredConnections 清理过期连接
func (r *RedisClient) CleanupExpiredConnections(ctx context.Context, timeoutSeconds int64) error {
	// 获取所有用户的在线设备
	pattern := "device:online:*"
	keys, err := r.client.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("获取Redis键失败: %v", err)
	}

	now := time.Now().Unix()
	totalCleaned := 0

	for _, key := range keys {
		// 从key中提取userID
		var userIDStr string
		fmt.Sscanf(key, "device:online:%s", &userIDStr)

		// 解析userID
		var userID uint
		fmt.Sscanf(userIDStr, "%d", &userID)

		// 获取该用户的在线设备
		onlineDevices, err := r.GetOnlineDevices(ctx, userID)
		if err != nil {
			continue
		}

		// 检查每个设备的最后心跳时间
		for deviceID, lastHeartbeat := range onlineDevices {
			// 如果超过超时时间，则设置为离线
			if now-lastHeartbeat > timeoutSeconds {
				if err := r.SetDeviceOffline(ctx, userID, deviceID); err != nil {
					continue
				}
				totalCleaned++
			}
		}
	}

	if totalCleaned > 0 {
		fmt.Printf("清理了 %d 个过期连接", totalCleaned)
	}

	return nil
}

// BatchUpdateDeviceStatus 批量更新设备状态到数据库
func (r *RedisClient) BatchUpdateDeviceStatus(ctx context.Context, userID uint) (map[string]int, error) {
	onlineDevices, err := r.GetOnlineDevices(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 这里应该调用数据库服务批量更新
	// 暂时返回统计信息
	stats := map[string]int{
		"online_count":  len(onlineDevices),
		"updated_count": 0,
	}

	return stats, nil
}

// 脚本市场缓存相关方法

// SetMarketScriptList 缓存脚本市场列表
func (r *RedisClient) SetMarketScriptList(ctx context.Context, key string, data interface{}, expiration time.Duration) error {
	return r.client.Set(ctx, key, data, expiration).Err()
}

// GetMarketScriptList 获取缓存的脚本市场列表
func (r *RedisClient) GetMarketScriptList(ctx context.Context, key string) (string, error) {
	return r.client.Get(ctx, key).Result()
}

// SetMarketScriptDetail 缓存脚本详情
func (r *RedisClient) SetMarketScriptDetail(ctx context.Context, scriptID uint, data interface{}, expiration time.Duration) error {
	key := fmt.Sprintf("market_script:detail:%d", scriptID)
	return r.client.Set(ctx, key, data, expiration).Err()
}

// GetMarketScriptDetail 获取缓存的脚本详情
func (r *RedisClient) GetMarketScriptDetail(ctx context.Context, scriptID uint) (string, error) {
	key := fmt.Sprintf("market_script:detail:%d", scriptID)
	return r.client.Get(ctx, key).Result()
}

// DeleteMarketScriptCache 删除脚本相关缓存
func (r *RedisClient) DeleteMarketScriptCache(ctx context.Context, scriptID uint) error {
	// 删除脚本详情缓存
	detailKey := fmt.Sprintf("market_script:detail:%d", scriptID)
	r.client.Del(ctx, detailKey)

	// 删除列表缓存（使用通配符删除所有列表缓存）
	pattern := "market_script:list:*"
	keys, err := r.client.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}

	if len(keys) > 0 {
		return r.client.Del(ctx, keys...).Err()
	}

	return nil
}

// DeleteAllMarketScriptCache 删除所有脚本市场缓存
func (r *RedisClient) DeleteAllMarketScriptCache(ctx context.Context) error {
	patterns := []string{
		"market_script:list:*",
		"market_script:detail:*",
	}

	for _, pattern := range patterns {
		keys, err := r.client.Keys(ctx, pattern).Result()
		if err != nil {
			continue
		}

		if len(keys) > 0 {
			r.client.Del(ctx, keys...)
		}
	}

	return nil
}
