package ctxutil

import (
	"context"

	"github.com/gin-gonic/gin"
)

// ContextKey 定义上下文键类型
type ContextKey string

const (
	// RequestIDKey 请求ID的键
	RequestIDKey ContextKey = "requestID"
)

// GetRequestContext 从Gin上下文获取带有请求ID的context.Context
func GetRequestContext(c *gin.Context) context.Context {
	ctx := c.Request.Context()

	// 从Gin上下文获取请求ID
	if requestID, exists := c.Get("requestID"); exists {
		if reqID, ok := requestID.(string); ok && reqID != "" {
			// 将请求ID添加到context.Context
			ctx = context.WithValue(ctx, RequestIDKey, reqID)
		}
	}

	return ctx
}
