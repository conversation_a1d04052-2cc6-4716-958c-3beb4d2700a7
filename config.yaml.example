# GoAdmin 配置文件示例
# 复制此文件为 config.yaml 并修改相应配置

# 服务器配置
server:
  port: "8080"                    # 服务端口
  mode: "debug"                   # 运行模式: debug, release
  read_timeout: 60                # 读取超时时间(秒)
  write_timeout: 60               # 写入超时时间(秒)

# 数据库配置
database:
  host: "localhost"               # 数据库主机
  port: 3306                      # 数据库端口
  username: "root"                # 数据库用户名
  password: "your_password"       # 数据库密码
  database: "goadmin"             # 数据库名称
  charset: "utf8mb4"              # 字符集
  max_idle_conns: 10              # 最大空闲连接数
  max_open_conns: 100             # 最大打开连接数
  conn_max_lifetime: 3600         # 连接最大生存时间(秒)

# JWT配置
jwt:
  secret: "your_jwt_secret_key"   # JWT密钥(请修改为随机字符串)
  expire_hours: 24                # 令牌过期时间(小时)
  issuer: "goadmin"               # 签发者

# 第三方JWT配置(用于客户端认证)
third_jwt:
  secret: "your_third_jwt_secret" # 第三方JWT密钥
  expire_hours: 168               # 令牌过期时间(小时，默认7天)
  issuer: "goadmin-third"         # 签发者

# GitHub集成配置(可选)
github:
  token: "your_github_token"      # GitHub Personal Access Token
  owner: "your_username"          # GitHub用户名或组织名
  repo: "your_repository"         # 仓库名称
  branch: "main"                  # 默认分支

# 日志配置
log:
  level: "info"                   # 日志级别: debug, info, warn, error
  format: "json"                  # 日志格式: json, text
  output: "stdout"                # 输出方式: stdout, file
  file_path: "logs/app.log"       # 日志文件路径(当output为file时)
  max_size: 100                   # 日志文件最大大小(MB)
  max_backups: 5                  # 保留的日志文件数量
  max_age: 30                     # 日志文件保留天数

# Redis配置(可选，用于缓存和会话)
redis:
  host: "localhost"               # Redis主机
  port: 6379                      # Redis端口
  password: ""                    # Redis密码
  database: 0                     # Redis数据库编号
  pool_size: 10                   # 连接池大小

# 系统配置
system:
  # 设备绑定配置
  device_binding:
    max_devices: 3                # 默认最大设备绑定数
    heartbeat_interval: 300       # 心跳间隔(秒)
    offline_timeout: 600          # 离线超时时间(秒)
  
  # 卡密配置
  cardkey:
    default_type: "time"          # 默认卡密类型: time, count
    min_value: 1                  # 最小值
    max_value: 365                # 最大值
  
  # API限流配置
  rate_limit:
    enabled: true                 # 是否启用限流
    requests_per_minute: 60       # 每分钟请求数限制
    burst: 10                     # 突发请求数

# 邮件配置(可选)
email:
  smtp_host: "smtp.gmail.com"     # SMTP服务器
  smtp_port: 587                  # SMTP端口
  username: "<EMAIL>" # 邮箱用户名
  password: "your_email_password" # 邮箱密码
  from: "GoAdmin <<EMAIL>>" # 发件人

# 文件上传配置
upload:
  max_size: 10                    # 最大文件大小(MB)
  allowed_types:                  # 允许的文件类型
    - "image/jpeg"
    - "image/png"
    - "image/gif"
  upload_path: "uploads"          # 上传目录

# 安全配置
security:
  # 密码策略
  password:
    min_length: 6                 # 最小长度
    require_uppercase: false      # 是否需要大写字母
    require_lowercase: false      # 是否需要小写字母
    require_numbers: false        # 是否需要数字
    require_symbols: false        # 是否需要特殊字符
  
  # IP白名单(可选)
  ip_whitelist:
    enabled: false                # 是否启用IP白名单
    ips:                          # 允许的IP列表
      - "127.0.0.1"
      - "***********/24"
  
  # CORS配置
  cors:
    enabled: true                 # 是否启用CORS
    allowed_origins:              # 允许的源
      - "http://localhost:5173"
      - "http://localhost:3000"
    allowed_methods:              # 允许的方法
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allowed_headers:              # 允许的头部
      - "Origin"
      - "Content-Type"
      - "Authorization"

# 监控配置
monitoring:
  # Prometheus指标
  prometheus:
    enabled: false                # 是否启用Prometheus指标
    path: "/metrics"              # 指标路径
  
  # 健康检查
  health_check:
    enabled: true                 # 是否启用健康检查
    path: "/health"               # 健康检查路径

# 开发环境配置
development:
  # 是否启用调试模式
  debug: true
  
  # 是否启用热重载
  hot_reload: true
  
  # 是否启用SQL日志
  sql_log: true
  
  # 是否启用性能分析
  pprof: true
