server:
  port: "9001"
  mode: "release"  
  env: "prod"     

jwt:
  secret_key: "ZM8888XTM"  
  expires_time: 2h

database:
  driver: "mysql"
  host: "localhost"  
  port: 3306
  username: "goadmin"  
  password: "Zzzz123!"  
  dbname: "goadmin"
  max_idle_conns: 10
  max_open_conns: 100

github:
  token: "*********************************************************************************************" 
  webhook_secret: "${AUTOJS_WEBHOOK_SECRET}"  
  branch_mapping:  
    main: main
    online: online

zpay:
  api_url: "https://z-pay.cn/mapi.php"  
  app_id: "20240702175700"  
  app_secret: "3cfEeN7xh9c9SZdRpAOBA9ro6qYkbOaJ"  
  notify_url: "https://app.l888.eu.org/api/v1/payment/notify"  

redis:
  addr: "localhost:6379"  
  password:  
  db: 1  
  pool_size: 20

websocket:
  allowed_origins:
    - "https://app.l888.eu.org"
    - "https://www.app.l888.eu.org" 
