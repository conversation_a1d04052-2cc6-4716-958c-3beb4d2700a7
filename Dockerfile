# 后端构建阶段
FROM golang:1.23.5-alpine AS go-builder
WORKDIR /build
# 先复制依赖文件
COPY go.* ./
RUN go mod download
# 复制整个项目代码
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o goadmin main.go

# 最终运行阶段
FROM debian:bullseye-slim
WORKDIR /app

# 安装必要的依赖和设置时区
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    ca-certificates \
    tzdata \
    && rm -rf /var/lib/apt/lists/* \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

# 从构建阶段复制文件
COPY --from=go-builder /build/goadmin /app/

# 创建启动脚本
COPY scripts/start.sh /app/
RUN chmod +x /app/start.sh

CMD ["/app/start.sh"] 